files:
  # If this file is edited, it must be removed from EC2 instance prior to deploy.
  "/opt/elasticbeanstalk/hooks/appdeploy/pre/09_yarn_install.sh" :
    mode: "000775"
    owner: root
    group: users
    content: |
      #!/usr/bin/env bash

      set -xe

      EB_APP_STAGING_DIR=$(/opt/elasticbeanstalk/bin/get-config container -k app_staging_dir)
      EB_APP_USER=$(/opt/elasticbeanstalk/bin/get-config container -k app_user)

      echo "I am: `whoami`"
      echo "App user is $EB_APP_USER"

      # If yarn is not detected, install it.
      if [ `yarn --version` == '1.9.4' ]; then
        echo "Skipping installation of yarn -- yarn already installed."
        echo "yarn --version: `yarn --version`"
      else
        echo "which yarn: `which yarn`"
        echo "Yarn is not installed and accessible."
        echo "Installing yarn..."
        # Consider that the EC2 instance is managed by AWS Elastic Beanstalk.
        # Changes made via SSH WILL BE LOST if the instance is replaced by auto-scaling.
        # QUESTION: Will this script be run on new instances that are created by auto-scaling?
        # QUESTION: Should installation be moved to a rake task?

        # Download the yarn repo
        sudo wget https://dl.yarnpkg.com/rpm/yarn.repo -O /etc/yum.repos.d/yarn.repo
        # Confirm that it downloaded
        file /etc/yum.repos.d/yarn.repo

        # If node is not detected, install it.
        if [ `node --version` == 'v8.11.3' ]; then
          echo "Skipping installation of node -- node already installed."
          echo "node --version: `node --version`"
        else
          echo "Installing Node v8x ..."
          # Download the Node v8 setup script
          curl --location https://rpm.nodesource.com/setup_8.x > /home/<USER>/node_install.sh
          # Confirm that it downloaded
          file /home/<USER>/node_install.sh
          # Run the Node v8 setup script
          sudo bash /home/<USER>/node_install.sh
          # Install nodejs
          sudo yum install -y nodejs
          node --version
          echo "... and finished installing Node v8.11.3"
        fi

        # install yarn
        sudo yum install -y yarn
        yarn --version

        echo "... and finished installing yarn."
      fi

      echo "Change directory to $EB_APP_STAGING_DIR"
      cd $EB_APP_STAGING_DIR

      # yarn install
      echo "Running yarn install."
      ./bin/yarn install