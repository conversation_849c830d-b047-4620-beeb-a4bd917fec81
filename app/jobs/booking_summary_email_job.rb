

class BookingSummaryEmailJob < ApplicationJob

  queue_as :default

  def perform()
    events = Event.where('(temp_flag = false or temp_flag IS NULL) AND datetimeto >= ? AND live = true AND booking_summary_email = true', Date.today)

    events.each do |event|

      end_time = DateTime.now.change({hour: 17})
      start_time = end_time - 1.day

      bookings = event.event_bookings.uncancelled.fully_booked.where('booking_date BETWEEN ? AND ?', start_time, end_time)

      if bookings.size > 0
        NotificationMailer.send_daily_booking_list(event, bookings).deliver_now
      end
    end
  end

end
