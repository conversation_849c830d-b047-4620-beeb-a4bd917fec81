class DeleteInvitesJob < ApplicationJob

	def perform(ev_id, job_id)

		Rails.cache.write(job_id, false)

		event = Event.find(ev_id)

		no_records_to_delete = event.registered_users.includes(:event_booking, :org_user_list).
        where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and (declined = false or declined IS NULL)").count

        no_iterations = (no_records_to_delete.to_f / 200).ceil

        (1..no_iterations).each do |i|
	    	event.registered_users.includes(:event_booking, :org_user_list).
        	where("event_bookings.booking_count = 0 and event_bookings.cancelled_at IS NULL and (declined = false or declined IS NULL)").limit(200).destroy_all
		end

        Rails.cache.write(job_id, true)	

	end	

end
