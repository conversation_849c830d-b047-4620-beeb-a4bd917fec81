class EventBookingCleanUpJob < ApplicationJob

  queue_as :default

  def perform(event_booking_id)
    # COmmented so doesn't run in background before deletion
  #   eb = EventBooking.find(event_booking_id)

  #   if eb.event.chargeable == true && (eb.payment_type == "card" || eb.payment_type.blank?) &&
  #                                   !eb.free_booking &&
  #                                    eb.payment_status != 'paid' &&
  #                                    ((eb.created_at + 10.minutes) < Time.zone.now)

  #      if eb.registered_user.invite_sent == true
  #       eb.package_bookings.destroy_all
  #       eb.update(
  #           booking_date: nil,
  #           booking_amendment: nil,
  #           booking_count: 0,
  #           total_cost: nil,
  #           waiting_list_id: nil,
  #           payment_type: nil,
  #           discount_code_id: nil,
  #           discount_percentage: nil,
  #           app_fees_total: nil,
  #           free_booking: nil,
  #           timed_out: true
  #         )
  #      else                              
  #       eb.destroy
  #      end
  #   end
  end

end
