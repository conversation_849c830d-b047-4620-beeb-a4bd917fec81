class CreateInvitesJob < ApplicationJob

  def perform(user_ids, ev_id, job_id, last_id)
    added_users = []
    user_list = OrgUserList.where("org_user_lists.id in (?)", user_ids).joins(:registered_users)
    .where("NOT EXISTS (SELECT 1 FROM org_user_lists import_contacts INNER JOIN registered_users rus ON rus.org_user_list_id = import_contacts.id WHERE org_user_lists.id = rus.org_user_list_id AND rus.deleted_at IS NULL AND rus.event_id IS NOT NULL AND rus.event_id = #{ev_id})").select('org_user_list_id', 'org_user_lists.email', 'org_user_lists.forename', 'org_user_lists.surname').distinct
    user_list.each do |contact|
      reg_user = RegisteredUser.new(event_id: ev_id, org_user_list_id: contact.org_user_list_id, email: contact.email, forename: contact.forename, surname: contact.surname)
      if reg_user.new_record?
        reg_user.build_event_booking(event_id: ev_id)
        reg_user.save
        added_users << reg_user.id
      end  
    end
    added_users
    count = Rails.cache.read("#{job_id}_count")
    Rails.cache.write("#{job_id}_count", count + added_users.size)
  end

  after_perform do |job|
    if job.arguments.first.last == job.arguments.last
      Rails.cache.write(job.arguments[2], true)
    end  
  end  

end  