class ImportContactsJob < ApplicationJob

  def perform(org_id, ev_id, ev_title, tag, ev_status, from, to, job_id, opted_out = false)
    # TODO set cache flag
    Rails.cache.write(job_id, false)
    Rails.cache.write("#{job_id}_count", 0)

    org = Organisation.find(org_id)
    users_list = org.org_user_lists.company_contacts
    has_event_atts = ev_title != 0 || ev_status != 0 || from.present? || to.present?
    events_list = org.org_user_lists if has_event_atts

    if opted_out
      users_list = users_list.where("opted_out IS NOT null")
    else
      users_list = users_list.where("opted_out IS null")
    end

    # users_list = users_list.by_tag(tag) if tag.present?

    if tag.present?
      tags = tag.split(',')
       users_list = users_list.by_tags(tags)
    end 

    if ev_title != 0
      events_list = events_list.by_event(ev_title)
    end  

    # EventFilter 0 all contacts registered on an event
    if ev_status != 0
      if ev_status == 1 # attended events
        events_list = events_list.has_attended_events
      elsif ev_status == 2 # unattended events
        events_list = events_list.has_unattended_events
      else # cancelled events
        events_list = events_list.has_cancelled_events
      end
    end

    events_list = events_list.has_event_after(from) if from.present?
    events_list = events_list.has_event_before(to) if to.present?

    user_list_ids = users_list.pluck(:id)

    user_list_ids = user_list_ids & events_list.pluck(:id) if has_event_atts

    last_id = user_list_ids.last

    user_list_ids.each_slice(100) do |batch|
      CreateInvitesJob.set(queue: job_id).perform_later(batch, ev_id, job_id, last_id)
    end 

    Rails.cache.write(job_id, true) if user_list_ids.blank? 

  end



end