module InvoiceHelper

  def vat_inc(item_amount, rate)
    rate = rate || 20
    return (item_amount - (item_amount / (1 + (rate.to_f / 100)))).round(2)
  end

  def ticket_cost_after_discount(event_booking, package_booking)
    cost = event_booking.early_bird_valid? ? package_booking.package.cost_a : package_booking.package.cost_b
    if package_booking.discount_code_id
      if (package_booking.percentage?)
        ticketCost = cost - ((package_booking.discount_amount / 100) * cost)
        return ticketCost
      else
        ticketCost = cost - package_booking.discount_amount 
        if (ticketCost < 0)
          return 0
        else
          return ticketCost
        end
      end
    else
      cost
    end
  end

  def get_ticket_cost(event_booking, package_booking)
    cost = event_booking.early_bird_valid? ? package_booking.package.cost_a : package_booking.package.cost_b
  end

  def ticket_discount_message(package_booking)
    if package_booking.discount_code_id
      if (package_booking.percentage?)
        return "( " +package_booking.discount_amount.to_s + "% discount )" 
      else
        return "( £ " + package_booking.discount_amount.to_s + " discount )"
      end
      ""
    end
  end

end
