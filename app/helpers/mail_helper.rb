module MailHelper

  # TODO deprecated
  def price_plus_vat_per_ticket(ticket, discounted)
    vat_rate_factor = 0.2
    cost = discounted ? ticket.cost_a : ticket.cost_b
    if ticket.vat_rate
      vat_rate_factor = ticket.vat_rate.rate / 100
    end
    cost + (vat_rate_factor * cost)
  end

  # TODO moved to packages as a class method
  def vat_amount_per_ticket(ticket, discounted)
    vat_rate_factor = 0.2
    cost = discounted ? ticket.cost_a : ticket.cost_b
    if ticket.vat_rate
      vat_rate_factor = ticket.vat_rate.rate / 100
    end
    vat_rate_factor * cost
  end

end
