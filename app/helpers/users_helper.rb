module Users<PERSON>elper

    def show_data_if_set(label, val, add_class = nil)
    "<div>
          <span class=\"label\">#{h(label)}</span>
          <span class=\"data_value #{add_class}\">#{h(val)}</span>
    </div>".html_safe if val.present?
  end

def mkt_opt_in_for_form(contact_form, invited = false)
   contact = contact_form.object
   out = ["<div>


      <p>
      We'd love to send you the latest information on our service,
      offers and news from Hospitality Guaranteed by email or phone
      and other electronic means.<br/>
      We'll always treat your personal details with the utmost care and will
      never sell them to other companies for marketing purposes.
      </p>
       "]
         contact.mkt_opt_in = (contact.mkt_opted_in_at.present? || contact.mkt_opt_in == '1') ? "1" : "0"

        out << " #{
        contact_form.input :mkt_opt_in, :as => :radio_buttons, :label => false,
         :collection => [["Yes please, I'd like to hear about offers, news and services.","1" ], ["No thanks, I don't want to hear about offers, news and services.", "0"]]
      }

   </div> "
   out.join().html_safe
 end

end
