json.extract! @event, :id, :title, :description, :datetimefrom, :datetimeto, :datetime_eb,
                    :vat_exclusive, :show_tickets_remaining, :phcolor, :phcolour, :complete, :live, :is_public

json.booking_url @event.booking_url
json.type "event_preview"
json.logo_url @logo_url

json.sponsors @event.sponsors do |sponsor|
  json.extract! sponsor, :id, :name, :logo_url, :website_url
end

json.event_address do
  if @event.event_address
    json.extract! @event.event_address, :id, :address1, :address2, :town, :county, :postcode, :country
  end
end

json.payment_option do
  if @event.payment_option
    json.extract! @event.payment_option, :id, :payment_type, :account_name, :account_number, :sort_code
  end
end

json.registration_fields @event.registration_fields do |field|
  json.extract! field, :id, :field_name, :field_type, :required, :options
end

json.discount_codes @event.discount_codes do |code|
  json.extract! code, :id, :code, :discount_type, :discount_amount, :use_limit, :uses_count
end

json.tickets @event.tickets do |ticket|
  json.extract! ticket, :id, :details, :cost_a, :cost_b, :start_time, :end_time, :ticket_no, :max_allowed, :group_amount
  json.tickets_remaining ticket.tickets_remaining

  json.child_tickets ticket.child_tickets do |child_ticket|
    json.extract! child_ticket, :id, :details, :cost_a, :cost_b, :start_time, :end_time, :ticket_no, :max_allowed
    json.tickets_remaining child_ticket.tickets_remaining
  end

  json.package_options ticket.package_options do |option|
    json.extract! option, :id, :option_name, :option_type

    json.package_sub_options option.package_sub_options do |sub_option|
      json.extract! sub_option, :id, :sub_option_name, :cost
    end
  end
end

json.ticket_groups @event.ticket_groups do |ticket_group|
  json.extract! ticket_group, :id, :description, :group_type

  json.packages ticket_group.packages do |package|
    json.extract! package, :id, :details, :cost_a, :cost_b, :start_time, :end_time, :ticket_no, :max_allowed, :group_amount
    json.tickets_remaining package.tickets_remaining

    json.child_tickets package.child_tickets do |child_ticket|
      json.extract! child_ticket, :id, :details, :cost_a, :cost_b, :start_time, :end_time, :ticket_no, :max_allowed
      json.tickets_remaining child_ticket.tickets_remaining
    end

    json.package_options package.package_options do |option|
      json.extract! option, :id, :option_name, :option_type

      json.package_sub_options option.package_sub_options do |sub_option|
        json.extract! sub_option, :id, :sub_option_name, :cost
      end
    end
  end
end
