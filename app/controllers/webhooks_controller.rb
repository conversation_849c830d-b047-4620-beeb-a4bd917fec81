class WebhooksController < ApplicationController
    skip_before_action :verify_authenticity_token
    # skip_before_action :authenticate_basically

    def receive
      event = nil
      payload = request.body.read
      begin
        event = Stripe::Webhook.construct_event(payload, request.env['HTTP_STRIPE_SIGNATURE'], ENV['STRIPE_WEBHOOK_SECRET'])
      rescue JSON::ParserError => e
        # Invalid payload
        status 400
        return
      rescue Stripe::SignatureVerificationError => e
        # Invalid signature
        status 400
        return
      end

      begin
        case event.type
        when 'payment_intent.created'
          handle_payment_intent_created(event.data.object)
        when 'payment_intent.processing'
          handle_payment_intent_processing(event.data.object)
        when 'payment_intent.succeeded'
          handle_payment_succeeded(event.data.object)
        when 'payment_intent.payment_failed'
          handle_payment_failed(event.data.object)
        else
          # raise "Unhandled event type: #{event.type}"
        end
      rescue StandardError => e
        Rollbar.error(e, 'Webhook eventstop error')
        render json: { error: e.message }, status: :unprocessable_entity and return
      end

      render json: { message: 'Webhook received successfully' }, status: :ok
    end

    private

    def handle_payment_intent_created(payment_intent)
    end

    def handle_payment_intent_processing(payment_intent)
      # update payment_type on bacs payments
      event_booking = EventBooking.find(payment_intent.metadata[:event_booking_id])
      event_booking.update(payment_status: EventBooking.payment_statuses[:processing])
    end

    def handle_payment_succeeded(payment_intent)
      event_booking = EventBooking.find(payment_intent.metadata[:event_booking_id])

      charge_id = payment_intent.latest_charge
      charge = StripePayment.get_charge_with_balance_transaction(event_booking, charge_id)

      payment_type = BookingPayment.payment_types[:card]
      if charge.payment_method_details.type != 'card'
        payment_type = BookingPayment.payment_types[:bacs]
      end

      amount = payment_intent.amount.to_f / 100 # annoyingly we record in pounds when stripe records in pence

      total_fees = (charge.balance_transaction.fee.to_f / 100)
      stripe_fee_details = charge.balance_transaction.fee_details.find{|fee| fee.type == 'stripe_fee'}
      application_fee_details = charge.balance_transaction.fee_details.find{|fee| fee.type == 'application_fee'}
      stripe_fees = (stripe_fee_details.amount.to_f / 100)
      application_fees = (application_fee_details.amount.to_f / 100)

      EventBooking.transaction do
        payments = event_booking.booking_payments.find_or_create_by!(
          event_id: event_booking.event.id,
          event_booking_id: event_booking.id,
        ) do |payment|
          payment.stripe_charge_id = payment_intent.id
          payment.amount = amount
          payment.net_amount = amount - total_fees
          payment.total_fees = total_fees
          payment.gross_amount = amount
          payment.registered_user_id = event_booking.registered_user_id
          payment.fees_included = false
          payment.application_fees = application_fees
          payment.stripe_fees = stripe_fees
          payment.vat_included = false
          payment.payment_datetime = Time.at(charge.created).to_datetime
          payment.payment_type = payment_type
          payment.deleted_at = nil
        end

        event_booking.update!(payment_status: EventBooking.payment_statuses[:paid], payment_type: BookingPayment.payment_types.key(payment_type), payment_error: nil)
      end

      send_notification(event_booking)
      send_confirmation(event_booking)
    end

    def handle_payment_failed(payment_intent)
      EventBooking.transaction do
        event_booking = EventBooking.find(payment_intent.metadata[:event_booking_id])

        # only update the payment status if it hasn't already been paid, as we don't want to overwrite the paid status
        if event_booking.payment_status != EventBooking.payment_statuses[:paid]

          failed_reason = payment_intent.last_payment_error.present? ? payment_intent.last_payment_error.message : 'Unknown'
          # For incompletes, we want to ignore as not a real failure
          if payment_intent.last_payment_error.present?
            event_booking.update(
              payment_status: EventBooking.payment_statuses[:payment_failed],
              payment_error: failed_reason
            )

            send_payment_failure(event_booking)
          end

        end
      end
    end

    def send_notification(event_booking)
      event = event_booking.event
      attendee_name = event_booking.registered_user.forename + ' ' + event_booking.registered_user.surname
      event_name = event.title
      event.organisation.notifications.create(event_id: event.id, event_name: event_name, attendee_name: attendee_name)
    end

    def send_confirmation(event_booking)
      if event_booking.id
        if Rails.env.development?
          TicketMailer.send_confirmation(event_booking.id).deliver_now
        else
          TicketMailer.send_confirmation(event_booking.id).deliver_later
        end
      end
    end

    def send_payment_failure(event_booking)
      if Rails.env.development?
        TicketMailer.send_payment_failure(event_booking.id).deliver_now
      else
        TicketMailer.send_payment_failure(event_booking.id).deliver_later
      end
    end

  end
