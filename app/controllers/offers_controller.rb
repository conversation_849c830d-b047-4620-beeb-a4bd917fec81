class OffersController < ApplicationController

  before_action :login_required
  before_action :ensure_admin

  def new
    @offer = Offer.new
  end

  def create
    @offer = Offer.new(create_offer)
    if @offer.save
      redirect_to('/hg_admin/offers', flash: { success: 'Offer Created' })
    else
      flash_now!(:error =>"Offer Errors, Check Each field")
      render :new
    end
  end

  def edit
    @offer = Offer.find_by_id(params[:id])
  end

  def update
    @offer = Offer.find(params[:id])
    if @offer.update(update_offer)
      redirect_to('/hg_admin/offers', flash: { success: 'Offer updated' })
    else
      flash_now!(:error =>"Offer not updated")
      render :edit
    end
  end

  def destroy
    offer = Offer.find(params[:id])
    offer.destroy
    redirect_to('/hg_admin/offers', flash: { success: 'Offer Deleted' })
  end

  private

  def create_offer
    params.require(:offer).permit(:offer_name, :offer_title, :offer_date, :offer_body, :image)
  end

  def update_offer
    params.require(:offer).permit(:offer_name, :offer_title, :offer_date, :offer_body, :image)
  end

end
