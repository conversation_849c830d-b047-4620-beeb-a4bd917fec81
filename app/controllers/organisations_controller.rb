# app/controllers/organisations_controller.rb
class OrganisationsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_organisation, only: [:show, :edit, :update, :destroy]
  before_action :ensure_owner, only: [:edit, :update, :destroy]

  respond_to :html, :json

  def index
    @organisations = current_user.all_organisations

    if request.format.json?
      render json: {
        organisations: @organisations.as_json(include: :owner)
      }
    end
  end

  def show
    if request.format.json?
      render json: {
        organisation: @organisation.as_json(include: [:owner, :members])
      }
    end
  end

  def new
    @organisation = Organisation.new
  end

  def create
    @organisation = current_user.owned_organisations.build(organisation_params)

    if @organisation.save
      if request.format.json?
        render json: {
          success: true,
          organisation: @organisation.as_json(include: :owner),
          message: 'organisation created successfully'
        }, status: :created
      else
        redirect_to @organisation, notice: 'Organisation was successfully created.'
      end
    else
      if request.format.json?
        render json: {
          success: false,
          errors: @organisation.errors.full_messages
        }, status: :unprocessable_entity
      else
        render :new
      end
    end
  end

  def edit
  end

  def update
    if @organisation.update(organisation_params)
      if request.format.json?
        render json: {
          success: true,
          organisation: @organisation.as_json(include: :owner),
          message: 'organisation updated successfully'
        }
      else
        redirect_to @organisation, notice: 'organisation was successfully updated.'
      end
    else
      if request.format.json?
        render json: {
          success: false,
          errors: @organisation.errors.full_messages
        }, status: :unprocessable_entity
      else
        render :edit
      end
    end
  end

  def destroy
    @organisation.destroy

    if request.format.json?
      render json: {
        success: true,
        message: 'organisation deleted successfully'
      }
    else
      redirect_to organisations_url, notice: 'organisation was successfully deleted.'
    end
  end

  private

  def set_organisation
    @organisation = organisation.find(params[:id])

    # Ensure user has access to this organisation
    unless current_user.all_organisations.include?(@organisation)
      if request.format.json?
        render json: { success: false, message: 'Access denied' }, status: :forbidden
      else
        redirect_to organisations_path, alert: 'Access denied.'
      end
    end
  end

  def ensure_owner
    unless @organisation.owner == current_user
      if request.format.json?
        render json: { success: false, message: 'Only the owner can perform this action' }, status: :forbidden
      else
        redirect_to @organisation, alert: 'Only the owner can perform this action.'
      end
    end
  end

  def organisation_params
    params.require(:organisation).permit(:name)
  end
end
