class LegalTermsController < ApplicationController

  def index
    terms = current_user.organisation.legal_term
    render json: terms
  end

  def event_org
    event = Event.find_by_id(params[:id])
    authorize! :manage, event if event    
    terms = nil
    terms = if event
      event.organisation.legal_term
    end
    render json: terms
  end

  def show
    event = Event.find_by_id(params[:id])
    if event
      terms = event.legal_term || event.organisation.legal_term || {terms: 'N/A'}
      render json: terms
    else
      render json: {status: 400}
    end
  end

end
