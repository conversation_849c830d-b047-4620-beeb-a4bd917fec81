class ReleasesController < ApplicationController


    def index
       base = Release.for_stop("EventStop").
                    select("releases.id, releases.*, to_char(deployed, 'Mon-YYYY') as monyr ").
                  order("deployed DESC")
        current_page = params[:page] || 1
        per_page = params[:per_page] || 4
        length = base.length

        @rels = Kaminari.paginate_array(base).page(current_page).per(per_page)
        @releases = @rels.sort_by{|x| x.deployed}.reverse.group_by{|x| x.monyr}
        update_user
    end

    def star_check
      if current_user.is_an_administrator?
        render json: {error: "Not needed for admin"}, status: 200 and return
      end
      result = false
      stamp = current_user.release_stamp || current_user.create_release_stamp
      rel = Release.latest_for("EventStop")
      if rel.present?
        session[:release_stamp] ||= stamp.to_hash
        user_date = DateTime.parse(session[:release_stamp]["EventStop"]) if (session[:release_stamp].present? && session[:release_stamp]["EventStop"].present?)
        if !user_date || (user_date.present? && user_date < rel.published_at)
          result = true
        end
      end
      respond_to do |format|
        format.json{
          render json: {result: result}, status: 200
        }
      end
    end

    def update_user
        return if current_user.is_an_administrator?
        stamp = current_user.release_stamp
        if stamp.present?
          rel = Release.latest_for("EventStop")
          user_date = stamp.time_for_stop("EventStop")
          if user_date.nil? || ( rel.present? && user_date && user_date < rel.published_at)
            stamp.update_stop("EventStop", rel.published_at + 1.second ) # avoid rounding errors time > js > time
            stamp.reload
            session[:release_stamp] = stamp.to_hash
          end
        end
    end





end
