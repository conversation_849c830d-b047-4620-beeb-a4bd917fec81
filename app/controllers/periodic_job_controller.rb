class PeriodicJobController < ActionController::Base

  # TODO PLEASE add this TO SSL exclusions on environment files

  protect_from_forgery except: [:send_booking_data_task, :send_check_for_additional_fees_task, :booking_notification_email, :send_list_expired_events_to_accounts]

  before_action :is_authorised, only: :booking_notification_email

  # TODO ***********************************************************
  # TODO NOTE these endpoints need adding to the ssl exceptions list
  # TODO ***********************************************************

  # TODO ***********************************************************
  # TODO NOTE these endpoints need adding to the ssl exceptions list
  # TODO ***********************************************************

  def send_booking_data_task
    # return head(:unauthorized) if ENV['DISABLE_SQS_CONSUMER']

    auth = {username: 'hg-user', password: '#h05p1ta!!ity#'}

    events = Event.where('DATE(datetimeto) = ? AND live = true AND charge_task_complete IS NULL', Date.today)

    events.each do |event|

      # TODO only send event bookings which are free, or bacs or cheques
      booking_count = if event.chargeable
                      #   TODO get bacs cheques etc
                        event.event_bookings.fully_booked.uncancelled.where.not(payment_type: 'card').size
                      else
                        event.event_bookings.fully_booked.uncancelled.size
                      end

      result = HTTParty.post(ENV["CUSTOM_PROVIDER_URL"] + '/api/v1/eventstop_accounting_task',
                             :body => {:event_id => event.id, event_name: event.title, booking_count: booking_count, organisation_id: event.organisation_id, end_date: event.datetimeto}.to_json,
                             :headers => {'Content-Type' => 'application/json'}, basic_auth: auth)

      if result.success?
        event.update_column(:charge_task_complete, true)
      else
        Rollbar.error("Failed to update accounts task for event" + event.id.to_s)
      end

    end

    return head(:ok)
  end


  def send_check_for_additional_fees_task
    # return head(:unauthorized) if ENV['DISABLE_SQS_CONSUMER']

    auth = {username: 'hg-user', password: '#h05p1ta!!ity#'}

    # Sends task to check for additional fees before event is closed so get event when end date is tomorrow
    events = Event.where('DATE(datetimeto) = ? AND live = true AND additional_fees_check_task_complete IS NULL', Date.today + 1.day)
    # This will only succeed for events where there is a corresponding event id in fees on hg

    events.each do |event|

      result = HTTParty.post(ENV["CUSTOM_PROVIDER_URL"] + '/api/v1/eventstop_additional_task',
                             :body => {:event_id => event.id, organisation_id: event.organisation_id}.to_json,
                             :headers => {'Content-Type' => 'application/json'}, basic_auth: auth)

      if result.success?
        event.update_column(:additional_fees_check_task_complete, true)
      else
        Rollbar.error("Failed to update additional task for event" + event.id.to_s)
      end

    end

    return head(:ok)

  end

  def booking_notification_email
    # return head(:unauthorized) if ENV['DISABLE_SQS_CONSUMER']

    events = Event.where('(temp_flag = false or temp_flag IS NULL) AND datetimeto >= ? AND live = true AND booking_summary_email = true', Date.today)

    events.each do |event|

      end_time = DateTime.now.change({hour: 17})
      start_time = end_time - 1.day

      bookings = event.event_bookings.uncancelled.fully_booked.where('booking_date BETWEEN ? AND ?', start_time, end_time)

      if bookings.size > 0
        NotificationMailer.send_daily_booking_list(event, bookings).deliver_now
      end
    end

    return head(:ok)
  end

  def send_list_expired_events_to_accounts
    NotificationMailer.send_list_expired_events_to_accounts.deliver_now
    return head(:ok)
  end

  private

  def is_authorised
    if ENV['DISABLE_SQS_CONSUMER'] || Rails.env == "development"
      authenticate_or_request_with_http_basic do |username, password|
        username == "hg-user" && password == "#h05p1ta!!ity#"
      end
    end
  end

end