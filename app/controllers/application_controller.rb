require "application_responder"

class ApplicationController < ActionController::Base
  include Pagy::Backend

  before_action :set_paper_trail_whodunnit
  # before_action :add_www_subdomain

  before_action :authenticate_user!, unless: :devise_authentication_action?
  before_action :configure_permitted_parameters, if: :devise_controller?

  # Override Devise's default redirect for unauthenticated users
  def authenticate_user!
    # Don't override for Devise controllers - let them handle their own auth
    if devise_controller?
      super
    elsif user_signed_in?
      super
    else
      respond_to do |format|
        format.html { redirect_to "/#/login" }
        format.json { render json: { error: 'You need to sign in or sign up before continuing.' }, status: :unauthorized }
        format.all { redirect_to "/#/login" }
      end
    end
  end

  private

  # Skip authentication for Devise authentication actions
  def devise_authentication_action?
    # Skip for specific non-auth actions
    return true if action_name == 'current_user_info'

    # Skip for Devise authentication actions
    devise_controller? && (
      (controller_name == 'sessions' && %w[new create destroy].include?(action_name)) ||
      (controller_name == 'registrations' && %w[new create].include?(action_name)) ||
      (controller_name == 'passwords' && %w[new create edit update].include?(action_name)) ||
      (controller_name == 'confirmations' && %w[show new create].include?(action_name))
    )
  end

  # Override Devise's default paths
  def after_sign_in_path_for(resource)
    "/#/dashboard"
  end

  def after_sign_out_path_for(resource_or_scope)
    "/#/login"
  end

  def new_session_path(resource_name)
    "/#/login"
  end

  def info_for_paper_trail
    { ip: request.remote_ip, user_agent: request.user_agent }
  end

  self.responder = ApplicationResponder
  respond_to :html

  rescue_from CanCan::AccessDenied do |exception|
    logger.error "CanCan Access Denied Exception: #{exception.message}"
    respond_to do |format|
      format.csv { render body: nil, status: :forbidden }
      format.json { render :json => {'error' => 'Access Denied ... you may need to log in again'}.to_json,  :status => 403 }
      format.html { redirect_to main_app.root_url, alert: exception.message }
    end
  end

  rescue_from ActionController::InvalidAuthenticityToken do
    respond_to do |format|
      format.json { render nothing: true, status: :forbidden }
      # format.html { redirect_to main_app.root_url, alert: exception.message }
    end
  end

  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :exception

  # Simple ping endpoint for connectivity checks
  def ping
    render json: { status: 'ok', timestamp: Time.now.iso8601 }
  end

  # These are from the omniauth client materials
  # def login_required
  #   session[:entry_point] = request.fullpath
  #   session[:entry_point] = stripe_confirm_path if params[:controller] == 'stripe'

  #   unless current_user

  #     respond_to do |format|
  #       format.html do
  #         # TODO ************* FOR DEV PURPOSES ONLY *******************************
  #         if Rails.env == 'development'
  #           # user = User.find_by_email(ENV['DEV_LOGIN_EMAIL'])
  #           user = User.find_by_email('<EMAIL>')# (org 3182)
  #           # user = User.find_by_email('<EMAIL>')
  #           session[:user_id] = user.id
  #           @current_user = user
  #           return
  #         else
  #           redirect_post('/auth/hg', options: {authenticity_token: :auto})
  #           return
  #         end
  #       end
  #       format.json { render :json => {'error' => 'Access Denied'}.to_json }
  #       format.all{
  #         redirect_post('/auth/hg', options: {authenticity_token: :auto})
  #       }
  #     end
  #   end
  # end

  def current_user
    # Try Devise's warden user first (for API and new authentication)
    devise_user = warden.user
    return devise_user if devise_user

    # Fall back to legacy session-based authentication
    return unless session[:user_id]
    @current_user ||= User.find_by_id(session[:user_id])
  end

  helper_method :current_user

  private

  def sign_out
    session[:user_id] = nil
    @current_user = nil
  end

  def admin_only
    unless current_user.is_an_administrator?
      permission_denied
    end
  end

  def permission_denied
    flash[:error] = 'You are not authorized to perform this action.'
    redirect_to(request.referrer || root_path)
  end

  # Allows pagination with the angular paginator and kaminari
  def self.paginated_action(options = {})
    before_action(options) do |controller|
      if request.headers['Range-Unit'] == 'items' &&
          request.headers['Range'].present?

        requested_from = nil
        requested_to = nil

        if request.headers['Range'] =~ /(\d+)-(\d*)/
          requested_from, requested_to = $1.to_i, ($2.present? ? $2.to_i : Float::INFINITY)
        end

        if (requested_from > requested_to)
          response.status = 416
          headers['Content-Range'] = "*/#{total_items}"
          render text: 'invalid pagination range'
          return false
        end

        @kp_per_page = requested_to - requested_from + 1
        @kp_page = requested_to / @kp_per_page + 1
      end
    end

    after_action(options) do |controller|
      results = instance_variable_get("@events") # TODO hard coded as events
      # results = instance_variable_get("@#{controller_name}") # ex @users
      if (results.length > 0)
        response.status = 206
        headers['Accept-Ranges'] = 'items'
        headers['Range-Unit'] = 'items'

        total_items = results.total_count
        per = @kp_per_page

        requested_from = (results.current_page - 1) * per
        available_to = (results.length - 1) + requested_from

        headers['Content-Range'] = "#{requested_from}-#{available_to}/#{total_items < Float::INFINITY ? total_items : '*'}"
      else
        response.status = 200
        # headers['Content-Range'] = "*/0"
      end
    end
  end

  # def ensure_admin
  #   unless current_user && current_user.is_an_administrator?
  #     redirect_to '/404'
  #   end
  # end

  # def add_www_subdomain
  #   unless Rails.env == 'development' ||  Rails.env == 'staging' || /herokuapp/.match(request.host)||  /^www/.match(request.host) || /^staging/.match(request.host) || /^qa/.match(request.host)
  #     redirect_to("#{request.protocol}www.#{request.host_with_port}#{request.fullpath}",status: 301)
  #   end
  # end

  public

  def json_request?
    request.format.json?
  end

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [:email, :password, :password_confirmation])
    devise_parameter_sanitizer.permit(:sign_in, keys: [:email, :password, :remember_me])
  end

  # Make these methods available as helper methods
  helper_method :after_sign_in_path_for, :after_sign_out_path_for, :new_session_path
end
