class Events::TicketsController < ApplicationController
  #  before_action :login_required
  # TODO move from events controller
  def update
  end

  def confirm_options
    event = Event.find_by_id(params[:event])
    authorize! :manage, event

    if event && event.update_column(:confirm_ticket_options, true)
      render json: {status: 200}
    else
      render json: {error: 'Not updated'}, status: 400
    end
  end
end
