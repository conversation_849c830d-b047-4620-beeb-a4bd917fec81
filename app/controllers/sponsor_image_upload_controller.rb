class SponsorImageUploadController < ApplicationController

  def upload_sponsors
    sfile = save_sponsors!
    render json: { fileid:  sfile.id, filename: sfile.sponsor_file }, status: 200
  end

  def update_event
    event = Event.find_by_id(params[:id])
    event.update(sponsor_view_email: params[:email], sponsor_view_delegate: params[:delegate])
    render json: { status: 200 }
  end

  def remove_file
    uploader = SponsorUploader.new
    uploader.event_id = params[:eventid]
    uploader.retrieve_from_store!(params[:filename])
    uploader.remove!
    if params[:destroy] == true
    fu = Sponsor.find_by_id(params[:file_id])
    if fu.present? && fu.destroy
      render json: {status: 200}
     else
      render json: {status: 400}
    end
    end
  end

  private

  def save_sponsors!
    uploader = SponsorUploader.new
    uploader.event_id = params[:id]
    uploader.store!(params[:file])
    sfile = Sponsor.find_by_id(params[:file_id])

    cleanedFileName = params[:file].original_filename.gsub(/[(\/,()\ ]/, '_');
    if sfile.blank?
      sfile = Sponsor.create!(sponsor_file: cleanedFileName, event_id: params[:id])
    else
      sfile.update(sponsor_file: cleanedFileName)
    end
    sfile
  end
end
