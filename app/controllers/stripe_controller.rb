class StripeController < ApplicationController

  include ActionController::Live

  before_action :authenticate_user!

  Mime::Type.register "text/event-stream", :stream

  # Create a manage Stripe account for yourself.
  # Only works on the currently logged in user.
  # See app/services/stripe_managed.rb for details.
  # def managed
  #   connector = StripeManaged.new( current_user )
  #   account = connector.create_account!(
  #       params[:country], params[:tos] == 'on', request.remote_ip
  #   )
  #
  #   if account
  #     flash[:notice] = "Managed Stripe account created! <a target='_blank' rel='platform-account' href='https://dashboard.stripe.com/test/applications/users/#{account.id}'>View in dashboard &raquo;</a>"
  #   else
  #     flash[:error] = "Unable to create Stripe account!"
  #   end
  #   redirect_to user_path( current_user )
  # end

  # Create a standalone Stripe account for yourself.
  # Only works on the currently logged in user.
  # See app/services/stripe_unmanaged.rb for details.
  def standalone
    connector = StripeStandalone.new(current_user)
    account = connector.create_account!(params[:country] || 'GB')

    if account
      @stripe_message = "Standalone Stripe account created! <a target='_blank' rel='platform-account' href='https://dashboard.stripe.com/test/applications/users/#{account.id}'>View in dashboard &raquo;</a>"
    else
      @stripe_message = "Unable to create Stripe account!"
    end
  end


  # Connect yourself to a Stripe account.
  # Only works on the currently logged in user.
  # See app/services/stripe_oauth.rb for #oauth_url details.
  def oauth
    Rails.logger.info "=== Stripe OAuth Request Started ==="
    Rails.logger.info "Current user: #{current_user&.id}"
    Rails.logger.info "STRIPE_CONNECT_CLIENT_ID: #{ENV['STRIPE_CONNECT_CLIENT_ID'].present? ? 'Present' : 'Missing'}"
    Rails.logger.info "STRIPE_SECRET: #{ENV['STRIPE_SECRET'].present? ? 'Present' : 'Missing'}"

    begin
      connector = StripeOauth.new(current_user)
      Rails.logger.info "StripeOauth connector created successfully"

      url, error = connector.oauth_url(redirect_uri: stripe_confirm_url)
      Rails.logger.info "oauth_url method completed"
      Rails.logger.info "Generated URL: #{url}"
      Rails.logger.info "Error: #{error}" if error

      if url.nil?
        Rails.logger.error "OAuth URL generation failed: #{error}"
        flash[:error] = error || "Unable to connect to Stripe. Please try again."
        redirect_to stripe_connected_path
      else
        Rails.logger.info "Redirecting to Stripe: #{url}"
        redirect_to url, allow_other_host: true
      end
    rescue => e
      Rails.logger.error "Exception in oauth method: #{e.class}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      flash[:error] = "Unable to connect to Stripe. Please try again."
      redirect_to stripe_connected_path
    end
  end

  # Confirm a connection to a Stripe account.
  # Only works on the currently logged in user.
  # See app/services/stripe_connect.rb for #verify! details.
  def confirm
    confirm_inner

    redirect_url = root_url + "dashboard#/payment-admin"

    if params[:state]
      eventId = params[:state]
      redirect_url = root_url + "events/#{eventId}/edit#/payments/"
    end

    redirect_to redirect_url
  end

  # Deauthorize the application from accessing
  # the connected Stripe account.
  # Only works on the currently logged in user.
  def deauthorize
    connector = StripeOauth.new(current_user)
    if connector.deauthorize
      flash.now[:success] = "Account disconnected from Stripe."
    else
       flash[:error] = "Account could not be disconnected from Stripe. (It may be disconnected already)"
    end

    redirect_to root_url + "dashboard#/payment-admin"
  end

  private

  def confirm_inner
    connector = StripeOauth.new(current_user)
    if params[:code]
      # If we got a 'code' parameter. Then the
      # connection was completed by the user.
      connector.verify!(params[:code])

    elsif params[:error]
      # If we have an 'error' parameter, it's because the
      # user denied the connection request. Other errors
      # are handled at #oauth_url generation time.
      flash[:error] = "Authorization request denied."
    end
  end

end
