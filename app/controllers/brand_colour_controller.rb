class BrandColourController  < ApplicationController

	respond_to :json

	#before_action :login_required
	before_action :find_current_org

	def update
		respond_to do |format|
			Rails.logger.info "Updating brand colour for org #{@org.id} with hex code #{params[:hex_code]}"
			if @org.brand_colour.update(hex_code: params[:hex_code])
			format.json	{render json: {status: 200}}
			else
				render json: {error: 'Problem updating brand colour'}, status: 400
			end  	
		end	
	end

	def show
		@colour = @org.brand_colour.present? ? @org.brand_colour : BrandColour.create(organisation_id: @org.id, hex_code: '#FF9500')
		render :json => @colour.to_json
	end	

	private

	def find_current_org
		@org = current_user.organisation
	end	

end
