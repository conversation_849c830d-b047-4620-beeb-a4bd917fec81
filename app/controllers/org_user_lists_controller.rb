class OrgUserListsController < ApplicationController

  require 'securerandom'

  def index
    # TODO what if it is hg org
    @org = current_user.organisation

    has_event_filter = params[:eventTitleFilter] != "0" || params[:eventStatusFilter] != "0" || params[:dateFromFilter] != "null" || params[:dateToFilter] != "null"
    @events_list = @org.org_user_lists if has_event_filter

    @users_list = @org.org_user_lists.company_contacts#.joins("INNER JOIN registered_users ON registered_user_id = registered_users.id")

    if params[:usersFilter].present?
      @users_list = @users_list.where("org_user_lists.forename ILIKE ? or org_user_lists.surname ILIKE ? or org_user_lists.email ILIKE ?", '%'+ params[:usersFilter] + '%', '%'+ params[:usersFilter] + '%', '%'+ params[:usersFilter] + '%')
    end

    if params[:optOutFilter] != "0"
      if params[:optOutFilter] == "1"
        @users_list = @users_list.where("opted_out IS NOT null")
      else
        @users_list = @users_list.where("opted_out IS null")
      end
    end

    if params[:tagFilter].present?
      tags = params[:tagFilter].split(',')
       @users_list = @users_list.by_tags(tags)
    end  


    if params[:eventTitleFilter] != "0"
      @events_list = @events_list.by_event(params[:eventTitleFilter].to_i)
    end  

    # EventFilter 0 all contacts registered on an event
    if params[:eventStatusFilter] != "0"
      if params[:eventStatusFilter] == "1" # attended events
        @events_list = @events_list.has_attended_events
      elsif params[:eventStatusFilter] == "2" # unattended events
        @events_list = @events_list.has_unattended_events
      else # cancelled events
        @events_list = @events_list.has_cancelled_events
      end
    end

    if params[:dateFromFilter].present? && params[:dateFromFilter] != "null"
      @events_list = @events_list.has_event_after(params[:dateFromFilter])
    end

    if params[:dateToFilter].present? && params[:dateToFilter] != "null"
      @events_list = @events_list.has_event_before(params[:dateToFilter])
    end

    @users_list = @users_list & @events_list if has_event_filter

    if has_event_filter
      @users_list = @users_list & @events_list
      @user_count = @users_list.count
      @users_list = Kaminari.paginate_array(@users_list).page(params[:page]).per(100)
    else
      @users_list = paginate(@users_list)
    end  

  end

  def update
    org = current_user.organisation.org_user_lists.find_by_id(params[:id])
    savedUpdate = false
    if org
      if params[:is_add_opt_out]
        savedUpdate = org.update(opted_out: DateTime.now)
      else
        savedUpdate = org.update(opted_out: nil)
      end
    end

    if savedUpdate
      render json: {opted_out: org.opted_out}, status: 200
    else
      render json: {errors: "Not Updated"}, status: 400
    end
  end

  def destroy
    # TODO do some checks
    regUser = RegisteredUser.find_by_id(params[:id])

    if regUser && regUser.destroy
      render json: {success: true}, status: 200
    else
      render json: {errors: "Not Updated"}, status: 400
    end
  end

  def add_tag
    org = current_user.organisation.org_user_lists.find_by_id(params[:id])
    @tag = params[:tag]
    if org&.tags&.include?(@tag)
      render json: {errors: "Cannot have duplicate tag"}, status: 400
    else  
      org&.tags&.push(@tag)
      if org && org.save!
        render :json => { :tag => @tag, :id => org.id }, :status => 200
      else
        render json: {errors: "Could not add tag"}, status: 400
      end
    end  
  end

  def delete_tag
    org = current_user.organisation.org_user_lists.find_by_id(params[:id])
    @tag = params[:tag]
    org&.tags&.delete(@tag)
    if org && org.save!
      render :json => { :tag => @tag, :id => org.id }, :status => 200
    else
      render json: {errors: "Not Updated"}, status: 400
    end 
  end  

  def get_filter_options
    if current_user.is_an_administrator? && params[:eventId].present?
      org = Event.find(params[:eventId]).organisation
    else
      org = current_user.organisation
    end  
    @events = org.events.where("title is not null").order("title ASC").map{|e| {id: e.id, title: e.title}}
    @tags = org.org_user_lists.map{|c| c.tags}.flatten.uniq
  end  

  def export_contacts

    @org = current_user.organisation

    @users_list = @org.org_user_lists.company_contacts
    has_event_filter = params[:eventTitleFilter] != "0" || params[:eventStatusFilter] != "0" || params[:dateFromFilter] != "null" || params[:dateToFilter] != "null"
    @events_list = @org.org_user_lists if has_event_filter

    if params[:usersFilter].present?
      @users_list = @users_list.where("org_user_lists.forename LIKE ? or org_user_lists.surname LIKE ? or org_user_lists.email LIKE ?", '%'+ params[:usersFilter] + '%', '%'+ params[:usersFilter] + '%', '%'+ params[:usersFilter] + '%')
    end

    if params[:optOutFilter] != "0"
      if params[:optOutFilter] == "1"
        @users_list = @users_list.where("opted_out IS NOT null")
      else
        @users_list = @users_list.where("opted_out IS null")
      end
    end

    if params[:tagFilter].present?
      tags = params[:tagFilter].split(',')
       @users_list = @users_list.by_tags(tags)
    end 

    if params[:eventTitleFilter] != "0"
      @events_list = @events_list.by_event(params[:eventTitleFilter].to_i)
    end  

    if params[:eventStatusFilter] != "0"
      if params[:eventStatusFilter] == "1"
        @events_list = @events_list.has_attended_events
      elsif params[:eventStatusFilter] == "2"
        @events_list = @events_list.has_unattended_events
      else
        @events_list = @events_list.has_cancelled_events
      end
    end

    if params[:dateFromFilter].present? && params[:dateFromFilter] != "null"
      @events_list = @events_list.has_event_after(params[:dateFromFilter])
    end

    if params[:dateToFilter].present? && params[:dateToFilter] != "null"
      @events_list = @events_list.has_event_before(params[:dateToFilter])
    end

    @users_list = @users_list & @events_list if has_event_filter

    respond_to do |format|
      format.csv {send_data OrgUserList.export_as_csv(@users_list), filename: "org_contacts.csv"}
    end
  end

  def import_contacts
    if current_user.is_an_administrator?
      @event = Event.find(params[:eventId])
      @org = @event.organisation
    else
      @org = current_user.organisation
      @event = @org.events.find(params[:eventId])
    end  
    to = params[:dateTo] == 'null' ? nil : params[:dateTo]
    from = params[:dateFrom] == 'null' ? nil : params[:dateFrom]
    job_id = SecureRandom.uuid
    ImportContactsJob.set(queue: job_id).perform_later(@org.id, @event.id, params[:eventTitle], params[:contactTag].join(","), params[:eventStatus], from, to, job_id)
    render :json => { :queue => job_id }, :status => 200
  end

  def poll_imports
    outcome = params[:job_id].present? ? Rails.cache.read(params[:job_id]) : false
    users_added = params[:job_id].present? ? Rails.cache.read("#{params[:job_id]}_count") : 0
    render :json => { :success => outcome, :no_added =>  users_added}, :status => 200
  end

  def check_import_job
    outcome = Rails.cache.read(params[:job_id]).present?
    render :json => { :success => outcome}, :status => 200
  end  

  private

  def paginate(users_list)
    @user_count = users_list.count
    users_list.page(params[:page]).per(100).order(:id)
  end
end
