class SplashScreenController < ApplicationController

  layout 'home'

  before_action :check_registered
  before_action :news_article
  before_action :latest_offers

  def index

    @show_disabled_notification = params[:referrer].present? && params[:referrer] == "eventstop_disabled"


    if params[:logout]
      @logout = true
    end

    @events = Event.proper.is_public.includes(:organisation).order('id DESC').limit(2)

    testimonials

    if params[:login_failed]
      flash.now[:error] = 'Login failed - Please try again!'
      return
    end

    if params[:eventstop_login].present? && current_user
      return redirect_to "/dashboard"
    end

    # if params[:uuid]
    #   event = Event.find_by(uuid: params[:uuid])
    #   if event.present?
    #     @end_date = event.datetime_eb
    #     @terms = event.legal_term
    #     if event.legal_term.blank?
    #       @terms = event.organisation.legal_term
    #     end
    #   end
    # end

    if params[:eventstop_login]
      flash[:success] = "Successfully Signed In!"
    end
  end

  def testimonials
    @testimonials = Testimonial.all
  end

  def news_article
    @latest_news = NewsArticle.order('id DESC').limit(2)
  end

  def latest_offers
    @latest_offers = Offer.order('id DESC').limit(2)
  end

  def offers
    @offers = Offer.all.page(params[:page]).per(5)
  end

  def knowledgehub
    @kh = KnowledgeHubArticle.where("published").page(params[:page]).per(9).order('id ASC')
  end

  def charity
  end

  def pricing
    @fees = Fees.where(:date_end => nil).last
  end

  def security
  end

  def events
    @events = Event.is_public.bookable.is_live

    if params[:location]
      origin = EventAddress.origin_of_location(params[:location])
      @events = @events.filter_by_location(origin) if origin != false
    else
      @events = @events.order('datetimefrom ASC')
    end

    @events = @events.filter_by_type(params[:type]) if params[:type]
    @events = @events.filter_by_title(params[:title]) if params[:title]
    @events = @events.filter_by_tag(params[:tag]) if params[:tag]
    @events = @events.filter_by_date_on(params[:date_on]) if params[:date_on]
    @events = Event.filter_by_free(@events) if params[:cost] == "free"
    @events = Event.filter_by_paid_for(@events) if params[:cost] == "paid"
    @events_count = @events.count
    respond_to do |format|
      format.json{}
      format.html do
        redirect_to root_path
      end
    end

  end

  def event_type_selector_options
    @selector = EventType.all.order(:name).pluck(:name)
    respond_to do |format|
      format.json { render :json => @selector }
    end
  end

  private

  def check_registered
    if params[:successful_reg]
      flash[:success] = "Registered Successfully!"
    end
    if params[:successful_reg].present? || params[:eventstop_login].present?
      login_required
    end
  end
end
