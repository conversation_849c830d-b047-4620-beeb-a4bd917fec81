class EventsController < ApplicationController
  require 'httparty'

  respond_to :html, only: :create
  respond_to :json
  respond_to :js, only: :destroy

  before_action :get_event, only: [:edit, :edit_old, :update, :destroy, :export_bookings, :export_unbooked_users, :export_menu, :export_payments, :export_attendees, :export_bookings, :update_email_templates, :update_email_congratulations,
                                   :update_packages, :update_colors, :update_payment_options, :add_additional, :change_live_status, :remove_live_status,
                                   :change_public_status, :save_sharing_options, :copy, :close_and_clone, :events_preview_template, :skip_invite_email, :update_step, :toggle_booking_notifications, :toggle_print_tickets]
  before_action :get_event_minimal, only: [:export_data]
  before_action :can_edit_event, only: [:edit, :edit_old]

  def index
    # @conference = Conference.find_by_id(params[:conference_id])

    # !-------For dev test only----------------!
    # @conference = Conference.where(stage: :confirmed)[2]

    # authorize! :manage, @conference if @conference

    # if @conference
    #   url = ENV['CUSTOM_PROVIDER_URL'] + "/api/v1/conference.json?id=#{@conference.id}"

    #   begin
    #     auth = 'Bearer ' + session[:hgonetoken]
    #     response = HTTParty.get(url, headers: { 'Authorization' => auth })
    #     @conference_data = response.parsed_response

    #   rescue Exception => e
    #     Rollbar.error(e, 'Conference Data error')
    #     logger.error "Problem retrieving conference data: #{e.message}"
    #   end
    # end

    @event_user = current_user
    @event_org = current_user.organisation

    # TODO does this tie in with new admin books as org
    @event = if @event_org.events.where("temp_flag = ? and user_id = ?", true, current_user.id).present?
               @event_org.events.where("temp_flag = ? and user_id = ?", true, current_user.id).last
             else
               event = Event.create(user_id: current_user.id, organisation_id: @event_org.id, temp_flag: true, completion_status: "Event Details", ticket_type: 'virtual', ticket_payment_options: 'paid')
             end

    # TODO move to model
    # if @conference_data

    #   if @conference_data['hotel']
    #     location = @conference_data['hotel']['location']
    #   end

    #   opp = @conference_data['opportunity']

    #   if location
    #     @event.location = @conference_data['hotel']['name']
    #     event_address = EventAddress.new(postcode: location['postcode'], address1: location['address_1'], address2:
    #       location['address_2'], city: location['city'], county: location['county'])
    #     @event.event_address = event_address
    #   end

    #   @event.datetimefrom = @conference_data['arrival_date']
    #   @event.datetimeto = @conference_data['departure_date']
    #   # @event.summary = @conference_data.summary

    #   if opp
    #     @event.organiser_email = opp['contact']['email']
    #     @event.organiser = opp['contact']['full_name']
    #     @event.title = opp['description']
    #   end

    #   @event.conference_id = @conference_data['id']
    #   @event.event_type_id = 1
    # else
      unless @event.event_address
        event_address = EventAddress.new()
        @event.event_address = event_address
      end
    # end

    @event.save(validate: false)

    redirect_to action: 'edit', id: @event.id
  end

  # def active_events
  #   events = current_user.organisation.events.proper.alive.select('id', 'title as name', 'datetimeto')
  #   render json: events
  # end

def list
  begin
    # Base query
    events = current_user.organisation.events

    # filter by live status
    if params[:live].present?
      if params[:live] == 'true'
        events = events.where(live: true)
      elsif params[:live] == 'false'
        events = events.where(live: false)
      else
        render json: { error: "Invalid live parameter value: #{params[:live]}" }, status: 400 and return
      end
    end

    # Optional filtering by event type
    if params[:filter].present?
      case params[:filter]
      when 'Alive'
        events = events.proper.alive
      when 'Expired'
        events = events.proper.expired
      when 'All'
        # Load ALL events regardless of status so frontend filters can work
        # Don't apply .proper restriction to show temp events too
        events = events
      else
        render json: { error: "Invalid filter value: #{params[:filter]}" }, status: 400 and return
      end
    else
      events = events.proper
    end

    # Optional search by title
    if params[:search].present?
      unless params[:search].is_a?(String)
        render json: { error: "Search parameter must be a string" }, status: 400 and return
      end
      events = events.where("title ILIKE ?", "%#{params[:search]}%")
    end

     # Filter by tags
    if params[:tags].present?
      tags = params[:tags]
      # todo: add filtering
      tags.each do |tag|
        events = events.filter_by_tag(tag)
      end
    end

    # Optional sorting
    if params[:sort].present?
      sort_column = params[:sort]
      sort_direction = params[:direction] || 'asc'
      if %w[asc desc].exclude?(sort_direction.downcase)
        render json: { error: "Invalid sort direction: #{sort_direction}" }, status: 400 and return
      end
      if Event.column_names.exclude?(sort_column)
        render json: { error: "Invalid sort column: #{sort_column}" }, status: 400 and return
      end
      events = events.order("#{sort_column} #{sort_direction}")
    else
      events = events.order('created_at DESC')
    end

    # Total count before pagination
    total_count = events.count

    # Pagination using Pagy
    pagy, paginated_events = pagy(events, items: params[:per_page] || 20)

    # Select only the fields we need to avoid N+1 queries
    event_data = paginated_events.select('id', 'title', 'datetimefrom', 'datetimeto', 'live', 'user_id', 'event_type_id', 'summary', 'organiser', 'created_at','tags','complete','live')

    # Add an expired boolean field
    event_data = event_data.map do |event|
      event.attributes.merge(expired: event.datetimeto.present? && event.datetimeto < Time.current)
    end

    # Respond with JSON or render a view
    respond_to do |format|
      format.json do
        render json: {
          events: event_data,
          pagy: pagy_metadata(pagy)
        }
      end
      format.html { @events = paginated_events }
    end
  rescue ActiveRecord::RecordNotFound => e
    render json: { error: "Record not found: #{e.message}" }, status: 404
  rescue StandardError => e
    Rails.logger.error "Error in list action: #{e.message}"
    render json: { error: "An unexpected error occurred. Please try again later." }, status: 500
  end
end

  def user_events
    begin
      # Get current user's events from their organization
      events_query = current_user.organisation.events.proper.alive
                      .order(created_at: :desc)

      # Use Pagy for pagination
      pagy, events = pagy(events_query, items: params[:per_page] || 20)

      # Format the response with pagination metadata
      response = {
        events: events.select('id', 'title', 'datetimefrom', 'datetimeto', 'live', 'user_id', 'event_type_id', 'summary', 'organiser'),
        pagy: pagy_metadata(pagy)
      }

      render json: response
    rescue StandardError => e
      Rails.logger.error("Error fetching user events: #{e.message}")
      render json: { error: 'Failed to load events', details: e.message }, status: :internal_server_error
    end
  end

  def expired
    # TODO authorize
    # TODO filter by user type etc
    events = current_user.organisation.events.proper.expired.order(:id)
    respond_with events
  end

  def show
  end

  def image_upload
    if params[:file].present?
      save_event_details_image!
      render json: { filename: 'test', status: 200 }
    else
      render json: { error: 'No file provided', status: 400 }, status: 400
    end
  end

  def image_delete
    delete_event_image!
  end

  def logo_upload
    if params[:file].present?
      get_predominant_logo_colours
      save_event_details_image!('logo')
      render json: { filename: 'test', status: 200 }
    else
      render json: { error: 'No file provided', status: 400 }, status: 400
    end
  end

  def create
    @event = Event.create(secured_params_with_org_and_user)
    @event.update_completion_status("Event Details")
    respond_with @event
  end

  def edit
    if @event.temp_flag
      @event.organiser_email = current_user.email
      @event.organiser = current_user.name
    end
  end

  def edit_old
    if @event.temp_flag
      @event.organiser_email = current_user.email
      @event.organiser = current_user.name
    end
  end

  def update
    # Update event and address
    notify = false
    if @event.temp_flag?
      notify = true
    end
    discount_code = apply_discount_code if params[:event][:promo_code]

    if @event.update(secured_params)
      if notify
        create_new_event_notification
        if Rails.env.development?
          UserMailer.new_event(current_user, @event).deliver_now
        else
          UserMailer.new_event(current_user, @event).deliver_later
        end
      end
      if discount_code
        discount_code.update_attribute(:inactive, true)
      end
      if @event.title.present? && @event.packages.blank?
        @event.update(event_created_date: Time.zone.now) if @event.event_created_date.blank?
        @event.update_completion_status("Ticket Details")
      end
      render json: { event: { one_off_discount_code: @event.one_off_discount_code, one_off_discount_percentage: @event.one_off_discount_percentage, vatable: @event.vatable } }
    else
      render json: { error: @event.errors }, status: 400
    end
  end

  def update_colors
    @event.update(color_params)
    respond_with @event
  end

  # TODO move to own controller Events::PackagesController
  def update_packages
    # If success uses jbuilder to return packages json
    # TODO validate if any tickets have bookings
    @event.update_completion_status("Booking Questions")
    if @event.live
      render json: { errors: ['Cannot amend tickets on a live event'] }, status: 400
      return
    end

    Event.transaction do
      # Clears out old tickets and groups before adding new
      @event.ticket_groups.destroy_all
      @event.packages.destroy_all
      @event.update(package_params)
    end
    if @event.errors.any?
      render json: { errors: @event.errors }, status: 400
    end
  end

  def get_event_tickets
    @event = Event.find_by_id(params[:id])
  end

  def update_email_templates
    @event.update(email_template_params)

    # Checks if an existing has not been updated
    templates = @event.email_templates

    if templates.invite.size > 1
      templates.invite.first.destroy
    end

    if templates.confirmation.size > 1
      templates.confirmation.first.destroy
    end

    respond_with @event
  end

  def update_email_congratulations
    if @event.update(send_congratulations: params[:event][:send_congratulations])
      render json: { status: 200 }
    else
      render json: { error: @event.errors }, status: 400
    end
  end

  def events_preview_template
    if @event.update(template: params[:template])
      render json: { status: 200 }
    else
      render json: { error: @event.errors }, status: 400
    end
  end

  def skip_invite_email
    if @event.step < 2.2
      @event.update(step: 2.2)
    end
    @event.update(show_email_invite: false)

    respond_with @event
  end

  def update_payment_options
    # If success uses jbuilder to return packages json
    unless @event.update(payment_options_params)
      render json: { error: @event.errors }, status: 400
    end
  end

  def update_step
    if params[:event] && params[:event][:cost_code]
      @event.update_column(:cost_code, params[:event][:cost_code])
    end
    if @event.update(step: params[:event][:step])
      render json: { status: 200 }
    else
      render json: { error: @event.errors }, status: 400
    end
  end

  def add_additional
    Event.transaction do
      # Check if we're dealing with individual field management (new UI)
      # or bulk field replacement (legacy UI)

      if params[:event][:registration_fields_attributes].present?
        # New approach: Handle individual field operations
        registration_fields_attrs = params[:event][:registration_fields_attributes]

        registration_fields_attrs.each do |field_attrs|
          # Filter through strong parameters
          permitted_attrs = field_attrs.permit(:id, :title, :_destroy, :question_type, :order_index, :mandatory, :min_length, :max_length, :field_type, options: [])

          if permitted_attrs[:_destroy] == 'true' || permitted_attrs[:_destroy] == true
            # Delete specific field
            field = @event.registration_fields.find_by(id: permitted_attrs[:id])
            field&.destroy
          elsif permitted_attrs[:id].present?
            # Update existing field
            field = @event.registration_fields.find_by(id: permitted_attrs[:id])
            if field
              field.update!(permitted_attrs.except(:id, :_destroy))
            end
          else
            # Create new field
            @event.registration_fields.create!(permitted_attrs.except(:id, :_destroy))
          end
        end
      else
        # Legacy approach: Clear and replace all (for backward compatibility)
        @event.registration_fields.destroy_all
      end

      # Handle static reg field updates
      if params[:event][:static_reg_field_attributes].present?
        permitted_params = additional_params[:static_reg_field_attributes]

        if @event.static_reg_field
          @event.static_reg_field.update!(permitted_params)
        else
          @event.build_static_reg_field(permitted_params)
          @event.static_reg_field.save!
        end
      end

      # Update other event attributes if present
      other_params = additional_params.except(:registration_fields_attributes, :static_reg_field_attributes)
      @event.update!(other_params) if other_params.present?

      @event.update_completion_status("Terms")
    end

    if @event.errors.any?
      render json: { error: @event.errors }, status: 400
    end
  end

  def event_invites_selected
    event_id = params[:id]

    @event = Event.find_by_id(event_id)

    emailParams = params[:emails]

    users = @event.registered_users.includes(:event_booking).opted_in.where("booking_count = 0 and cancelled_at IS NULL and (declined = false or declined IS NULL)")

    if emailParams
      users = users.where(email: emailParams)
    end

    # TODO: NEEDS JOB FOR ALL not just one!!
    users.each do |user|
      booking = user.event_booking

      if Rails.env.development?
        SendInvitesJob.perform_now(event_id, booking.id, booking.uuid, user.forename, user.email)
      else
        SendInvitesJob.perform_later(event_id, booking.id, booking.uuid, user.forename, user.email)
      end

      user.update_attribute(:invite_sent, true)
    end
    # TODO: end of job

    render json: { head: :ok }
  end

  def change_live_status
    if @event.update(live: true, complete: true, completion_status: "Complete", step: params[:step], is_public: params[:is_public])
      render json: { status: 200 }
    else
      render json: { error: @event.errors }, status: 400
    end
  end

  def remove_live_status
    if @event.event_bookings.fully_booked.any?
      render json: { error: "Cannot change status as bookings exist" }, status: 400
      return
    end

    @event.update_attribute(:live, false)

    render json: { status: 200 }
  end

  def change_public_status
    @event.update(is_public: params[:is_public])
    respond_with @event
  end

  def save_sharing_options
    @event.update_attribute(:custom_url, params[:custom_url])
    respond_with @event
  end

  def destroy
    if @event && @event.destroy
      render json: { status: 200 }
    else
      render json: { status: 400, error: 'Event Not Deleted' }
    end
  end

  def export_data
    if params[:load]
      if @event.tocsv.attached?
        if params[:purge]
          @event.tocsv.purge_later
        else
          return render json: {url: url_for(@event.tocsv)}
        end
      else
        return render json: { status: 400, error: 'No Csv data yet' }
      end
    else
      @event.delay.delay_to_csv
    end
    render json: { status: 200 }
  end

  def export_unbooked_users
    respond_to do |format|
      format.csv {send_data @event.unbooked_to_csv, filename: "event-unbooked-users#{Date.today}.csv"}
    end
  end

  def export_menu
    respond_to do |format|
      format.csv { send_data @event.menu_to_csv, filename: "event-menu#{Date.today}.csv" }
    end
  end

  def export_payments
    respond_to do |format|
      format.csv { send_data @event.booking_payments_to_csv, filename: "event-payments#{Date.today}.csv" }
    end
  end

  def export_attendees
    respond_to do |format|
      format.csv { send_data @event.attendees_booked_to_csv, filename: "event-attendees#{Date.today}.csv" }
    end
  end

  def export_bookings
    respond_to do |format|
      format.csv { send_data @event.booker_and_attendees_to_csv, filename: "booking-attendees#{Date.today}.csv" }
    end
  end

  def send_sample_emails
    if params[:invite]
      invite_mailer = SampleMailer.send_invites(params[:id], params[:send_to])
      if Rails.env.development?
        invite_mailer.deliver_now
      else
        invite_mailer.deliver_later
      end
    end
    if params[:confirmation]
      confirmation_mailer = SampleMailer.send_confirmation(params[:id], params[:send_to], params[:payment_type])
      if Rails.env.development?
        confirmation_mailer.deliver_now
      else
        confirmation_mailer.deliver_later
      end
    end
    render json: { status: 200 }
  end

  # TODO this is temporary, only for transitioning events
  def close_and_clone
    new_event = @event.deep_clone include: [:event_address, :legal_term, :static_reg_field, :registration_fields, { tickets: [:child_tickets, package_options: :package_sub_options] }, { ticket_groups: { packages: [:child_tickets, package_options: :package_sub_options] } }]

    if new_event.clean_copy_and_save(@event)

      @event.update(child_id: new_event.id)
      @event.update(live: false)

      message = nil

      begin
        new_event.copy_event_images!(@event)
      rescue Exception => e
        message = "Event Copied Successfully, but Images could not copied"
      end

      render json: { status: 200, event_id: new_event.id, message: message }
    else
      new_event.update(temp_flag: false)
      render json: { status: 400, error: 'Event Not Copied' }
    end
  end

  def copy
    # Removed email templates from the clone
    # pirate.deep_clone include: [ :mateys, { treasures:  [ :matey, :gold_pieces ] } ], use_dictionary: true

    new_event = @event.deep_clone include: [:event_address, :legal_term, :static_reg_field, :registration_fields, { tickets: [:child_tickets, package_options: :package_sub_options] }, { ticket_groups: { packages: [:child_tickets, package_options: :package_sub_options] } }]

    if new_event.clean_copy_and_save(@event)

      message = nil

      begin
        new_event.copy_event_images!(@event)
      rescue Exception => e
        message = "Event Copied Successfully, but Images could not copied"
      end

      render json: { status: 200, event_id: new_event.id, message: message }
    else
      new_event.update(temp_flag: false)
      render json: { status: 400, error: 'Event Not Copied' }
    end
  end

  def toggle_booking_notifications
    if @event.update_attribute(:booking_summary_email, params[:toggle_value])
      render json: { status: 200 }
    else
      render json: { error: 'Not updated' }, status: 400
    end
  end

  def toggle_print_tickets
    if @event.update_attribute(:print_tickets, params[:toggle])
      render json: { status: 200 }
    else
      render json: { error: 'Not updated' }, status: 400
    end
  end

  def get_organisations
    @organisations = Organisation.where('name ILIKE ?', "%#{params[:id]}%").includes(:vat_number)

    unless @organisations
      render json: { status: 400 }
    end
  end

  def typeahead_events
    if current_user.is_an_administrator? && params[:referrer] == 'admin'
      events = Event.all.select(:id, :title)
    else
      events = current_user.organisation.events.select(:id, :title)
    end
    events = events.where("title ILIKE ?", "%#{params[:query]}%").order(:title).limit(10).select(:id, :title)
    render :json => events.to_json
  end

  def preview_event_card
    @event = Event.find(params[:id])
    respond_to do |format|
      format.json {}
    end
  end

  def unified
    # Serve the unified events application
    render template: "events/unified", layout: false
  end

  def stripe_connected
    # Handle Stripe connection success/failure
    # This action is called when users return from Stripe Connect flow

    if params[:stripe_removed]
      flash[:notice] = "Stripe account has been disconnected successfully."
    elsif params[:stripe_connected]
      flash[:notice] = "Stripe account has been connected successfully."
    end

    # Redirect to the payment admin page
    redirect_to root_url + "dashboard#/payment-admin"
  end

  private

  def get_predominant_logo_colours
    org = Event.find(params[:id]).try(:organisation)
    if org.present? && org.brand_colour.blank? && params[:file].present?
      brand_colour = ExtractColoursService.extract_dominant_colors(params[:file].tempfile.path, 1)
      if brand_colour.present? && brand_colour[0].present?
        BrandColour.create(organisation_id: org.id, hex_code: brand_colour[0])
      end
    end
  end

  def save_event_details_image!(image_type = 'image')
    return unless params[:file].present?

    ev = Event.find(params[:id])
    params[:file].original_filename = params[:file].original_filename
    if image_type == 'image'
      uploader = EventImageUploader.new
      delete_existing_image!('image', ev) if ev.image2.present?
    else
      uploader = EventLogoUploader.new
      delete_existing_image!('logo', ev) if ev.image1.present?
    end

    uploader.event_id = params[:id]
    uploader.store!(params[:file])
    authorize! :manage, ev
    if image_type == 'image'
      ev.update_attribute(:image2, uploader.filename)
    else
      ev.update_attribute(:image1, uploader.filename)
    end
  end

  def delete_existing_image!(image_type, event)
    uses_same_image = event.image1 == event.image2
    image_type_name = :image1
    if image_type == 'logo'
      uploader = EventLogoUploader.new
      file_name = event.image1
    else
      uploader = EventImageUploader.new
      image_type_name = :image2
      file_name = event.image2
    end

    unless uses_same_image
      uploader.event_id = event.id
      uploader.retrieve_from_store!(file_name)
      uploader.remove!
    end

    authorize! :manage, event
    if event.update_column(image_type_name, nil)
      return true
    else
      return false
    end
  end

  def delete_event_image!
    event = Event.find_by_id(params[:eventid])
    uses_same_image = event.image1 == event.image2
    image_type_name = :image1
    if params[:logo] == true
      uploader = EventLogoUploader.new
    else
      uploader = EventImageUploader.new
      image_type_name = :image2
    end
    unless uses_same_image
      uploader.event_id = params[:eventid]
      uploader.retrieve_from_store!(params[:image_file])
      uploader.remove!
    end
    authorize! :manage, event
    if event.update_column(image_type_name, nil)
      render json: { status: 200 }
    else
      render json: { status: 400 }
    end
  end

  def secured_params
    params.require(:event)
          .permit(:user_id, :ticket_type, :ticket_payment_options, :close_date, :cost_code, :acc_enabled, :add_sponsors, :datetimefrom, :datetimeto, :custom_url, :location, :sponsor_view_email, :sponsor_view_delegate, :details, :organisation_id, :organiser, :organiser_email,
                  :sponsor_title, :title, :is_public, :conference_id, :email_default, :summary, :event_type_name, :event_type_id, :step, :international, :remove_location, :one_off_discount_code, :one_off_discount_percentage, tags: [],
                  event_address_attributes: [:id, :address1, :address2, :city, :county, :postcode, :short_info, :latitude, :longitude, :country_code], discount_codes_attributes: [:id, :code, :description, :discount_date]).merge(temp_flag: false, seen_popup: true, show_date: true)
  end

  def color_params
    params.require(:event)
          .permit(:id, :phcolour, :buttoncolour, :textcolour, :etcolour, :ehcolour)
  end

  def secured_params_with_org_and_user
    # Make sure this is organisation.id to use eventstop id
    secured_params.merge(user_id: current_user.id, organisation_id: current_user.organisation.id)
  end

  def package_params
    params.require(:event).permit(:id, :datetime_eb, :fees_pass_on, :show_vat, :vat_number, :vat_exclusive, :step, :show_tickets_remaining, :show_add_attendees,
                                  packages_attributes: [:start_time, :end_time, :virtual_link, :meeting_id, :meeting_password, :details, :cost_a, :cost_b, :ticket_no, :max_allowed, :is_new, :package_type, :title, :vat_rate_id, :company_number, :_destroy, :group_amount, :ticket_type, :details_for_all_group_members, :tickets_for_whole_group,
                                                        package_options_attributes: [:title, :description, package_sub_options_attributes: [:title, :description, :_destroy]],
                                                        child_tickets_attributes: [:event_id, :start_time, :end_time, :virtual_link, :meeting_id, :meeting_password, :details, :cost_a, :cost_b, :ticket_no, :max_allowed, :is_new, :package_type, :title, :vat_rate_id, :company_number, :_destroy, :group_amount, :ticket_type, :child_ticket]],
                                  ticket_groups_attributes: [:description, packages_attributes: [:event_id, :start_time, :end_time, :virtual_link, :meeting_id, :meeting_password, :details, :cost_a, :cost_b, :ticket_no, :max_allowed, :is_new, :package_type, :title, :vat_rate_id, :company_number, :_destroy, :group_amount, :ticket_type, :details_for_all_group_members, :tickets_for_whole_group,
                                                                                                 package_options_attributes: [:title, :description, package_sub_options_attributes: [:title, :description]],
                                                                                                 child_tickets_attributes: [:event_id, :start_time, :end_time, :virtual_link, :meeting_id, :meeting_password, :details, :cost_a, :cost_b, :ticket_no, :max_allowed, :is_new, :package_type, :title, :vat_rate_id, :company_number, :_destroy, :group_amount, :ticket_type, :child_ticket]]]
    )
  end

  def email_template_params
    params.require(:event)
          .permit(:step, :ehcolour, email_templates_attributes: [:id, :welcome_text, :show_forename_only, :text_area_1, :text_area_2, :text_area_3, :title1, :title2, :details, :email_type, :ticket_options_title, :email_button_option])
  end

  # TODO: change this, as it's just a simple complete
  def payment_options_params
    params.require(:event).merge(complete: true, completion_status: "Complete")
          .permit(:complete, payment_option_attributes: [:id, :fees_inclusive])
  end

  def additional_params
    params.require(:event).permit(:step, :company_field, :attendee_questions_mandatory, :booker_questions_mandatory, :job_description_field, :phone_field, :address1_field, :address2_field, :town_field, :county_field, :postcode_field, :country_field, :po_number_field, :show_add_attendees,
                                  registration_fields_attributes: [:id, :title, :_destroy, :question_type, :order_index, :mandatory, :min_length, :max_length, :field_type, options: []],
                                  static_reg_field_attributes: [:id, :field_type, :company_enabled, :job_enabled, :phone_enabled, :address1_enabled, :address2_enabled, :town_enabled, :county_enabled, :postcode_enabled, :country_enabled])
  end

  # def custom_field_params
  #   params.require(:event).permit(:id, registration_fields_attributes: [:id, :title, :_destroy, :question_type, :order_index, :mandatory, :min_length, :max_length, :field_type, options: []],
  #                                 static_reg_field_attributes: [:id, :field_type, :company_enabled, :job_enabled, :phone_enabled, :address1_enabled, :address2_enabled, :town_enabled, :county_enabled, :postcode_enabled])
  # end
  #

  def get_event_minimal
    @event = Event.find_by_id(params[:id])
    authorize! :manage, @event
  end

  def get_event
    # TODO we may need to restrict the data pulled in this
    @event = Event.includes(packages: [package_options: :package_sub_options], event_bookings: [package_bookings: [package: :package_options, registered_users: :registered_user_responses]], ticket_groups: [:packages]).includes(:file_uploads).includes(:sponsors).joins('LEFT JOIN registration_fields ON registration_fields.field_type = 1').find_by_id(params[:id])
    authorize! :manage, @event
  end

  def can_edit_event
    # TODO: secure this
    event = Event.find_by_id(params[:id])
    authorize! :manage, event
    # if event.event_bookings && (!event.event_bookings.fully_booked.empty? && event.complete && event.live == true)
    #   unless current_user.is_an_administrator?
    #     redirect_to :back, flash: { error: "You cannot edit this event as it has bookings, please contact Eventstop." }
    #   end
    # end
  end

  def create_new_event_notification
    current_user.organisation.notifications.create(event_id: @event.id, email: current_user.email, details: "New Event Created", admin_only: true)
  end

  def apply_discount_code
    # TODO needs checks to see if in place for other events
    discount_code = params[:event][:promo_code]

    # Gets overrides per event, then org
    fee_overrides = if @event.fee_overrides.blank?
                      @event.organisation.fee_overrides
                    else
                      @event.fee_overrides
                    end

    if fee_overrides.present?
      return
    end

    # So not yet used
    if @event.organisation.events.proper.where(one_off_discount_code: discount_code).size == 0
      dc = DiscountCode.where("one_off_code = true AND code = ? AND (one_off_code_org_id = ? OR one_off_code_org_id IS NULL)", discount_code, @event.organisation_id)
      dc = dc.first if dc
      if dc
        params[:event][:one_off_discount_code] = dc.code
        params[:event][:one_off_discount_percentage] = dc.amount
      end
      dc
    end

  end

end
