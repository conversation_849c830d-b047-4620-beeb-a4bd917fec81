class FeesController < ApplicationController

  before_action :login_required, except: :active_fees
  before_action :admin_only, except: :active_fees

  # Should just be the index method
  def index
    fees = Fees.all.order(id: :desc)
    render json: fees.to_json.html_safe
  end

  def active_fees
    fees = Fees.active_fees
    if fees
      # Gets overridden rates for HG
      get_fee_overrides(fees)
      render json: fees.to_json(:methods => [:fee_cap, :fees_overridden, :standard_fees_card, :standard_fees_add, :charity_fees_card, :charity_fees_add,
                                             :uk_premium_fees_card, :uk_premium_fees_add, :charity_uk_premium_fees_card, :charity_uk_premium_fees_add,
                                             :eu_standard_fees_card, :eu_standard_fees_add, :eu_charity_fees_card, :eu_charity_fees_add,
                                             :int_standard_fees_card, :int_standard_fees_add, :int_charity_fees_card, :int_charity_fees_add,
                                             :stripe_bacs_fees, :stripe_bacs_fees_add, :charity_stripe_bacs_fees, :charity_stripe_bacs_fees_add]).html_safe

      # TODO fees overrides
    else
      render json: { error: 'Could not get active fees' }, status: 400
    end
  end

  def update
    options = params[:options].permit(:stripe_fees_card, :stripe_fees_card_add, :stripe_fees_int, :stripe_fees_int_add, :hg_fees, :hg_fees_add, :hg_fees_charity, :hg_fees_charity_add, :stripe_fees_eu, :stripe_fees_eu_add, :stripe_fees_uk_premium, :stripe_fees_uk_premium_add, :bacs_fees).merge(date_start: Date.today)
    old_fees = Fees.order(:created_at).last

    Fees.transaction do
      fee = Fees.create(options)

      if old_fees
        old_fees.update_attribute(:date_end, Date.today)
      end

      if fee
        render json: { status: 200 }
      else
        render json: { status: 400, error: 'Fees not saved' }
      end
    end
  end

  private

  def get_fee_overrides(fees)
    # Default cap of £19
    # fees.fee_cap = 19.00
    fees.fees_overridden = false
    @fee_overrides = nil
    if params[:event_id].present?
      event = Event.find_by_id(params[:event_id])
      if event.fee_overrides.blank?
        @fee_overrides = event.organisation.fee_overrides
      else
        @fee_overrides = event.fee_overrides
      end
    else
      @fee_overrides = current_user.organisation.fee_overrides
    end

    if @fee_overrides
      fee_override = @fee_overrides.first
      if fee_override
        fees.hg_fees = fee_override.hg_fees if fee_override.hg_fees.present? && fee_override.hg_fees > 0
        fees.hg_fees_add = fee_override.hg_fees_add if fee_override.hg_fees_add.present?
        # fees.fee_cap = fee_override.hg_fee_cap if fee_override.hg_fee_cap.present? && fee_override.hg_fee_cap > 0
        fees.fees_overridden = true
      end
    end
  end
end
