class ApiDebugController < ApplicationController
  before_action :login_required

  def auth_status
    render json: {
      authenticated: current_user.present?,
      user_id: current_user&.id,
      user_email: current_user&.email,
      session_id: session.id,
      session_expires_at: session[:expires_at],
      timestamp: Time.current.to_s,
      rails_env: Rails.env,
      session_data: session.to_h.except('session_id', 'csrf', 'warden.user.user.key') # Don't expose sensitive values
    }
  end
end
