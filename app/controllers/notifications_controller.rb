class NotificationsController < ApplicationController

  # before_action :ensure_admin

  def index
   # if current_user.is_an_administrator?
   #   notifications = Notification.where("(viewed = false OR viewed IS NULL) AND created_at >= ? AND admin_only = true", DateTime.now - 2.days).order('created_at desc')
  #  else
      notifications = current_user.organisation.notifications.where("(viewed = false OR viewed IS NULL) AND created_at >= ? AND admin_only IS NULL", DateTime.now - 2.days).order('created_at desc')
  #  end
    render json: notifications
  end

  def destroy
    Notification.delete(params[:id])
    redirect_to('/manage_notifications', flash: {success: 'Notification Deleted'})
  end

  def update
    notification = Notification.find_by_id(params[:id])
    if notification.update_attribute(:viewed, true)
      render json: {status: 200}
    else
      render json: {status: 400}, status: 400
    end
  end

  def update_all_notifications
    notification = Notification.where('id IN (?)', params[:notification]).update_all(viewed: true)
    if notification
      render json: {status: 200}
    else
      render json: {status: 400}, status: 400
    end
  end

  def notification_count
  #  if current_user.is_an_administrator?
  #    counter = Notification.where("(viewed = false OR viewed IS NULL) AND created_at >= ? AND admin_only = true", DateTime.now - 2.days).count
  #  else
      counter = current_user.organisation.notifications.where("(viewed = false OR viewed IS NULL) AND created_at >= ? AND admin_only IS NULL", DateTime.now - 2.days).count
  #  end
    render json: {count: counter}
  end
end
