class FbPixelCodeController  < ApplicationController

	respond_to :json

	before_action :login_required
	before_action :find_current_org

	def change
		if @org.update(fb_pixel_code: params[:code])
			render json: {status: 200}
		else
			render json: {error: 'Problem updating facebook pixel code'}, status: 400
		end	
	end	

	def show
		@pixel_code = @org.fb_pixel_code
		render :json => {code: @pixel_code}
	end	

	def remove
		if @org.update(fb_pixel_code: nil)
			render json: {status: 200}
		else
			render json: {error: 'Problem removing facebook pixel code'}, status: 400
		end	
	end	

	private

	def find_current_org
		@org = @current_user.organisation
	end	

end