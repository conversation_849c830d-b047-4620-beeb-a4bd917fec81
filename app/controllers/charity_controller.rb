class CharityController < ApplicationController
  require 'httparty'
  require 'nokogiri'

  def index
    charity = CharityNumber.find_by(organisation_id: current_user.organisation.id)

    authorize! :manage, charity if charity

    if charity
      render json: charity.to_json
    else
      render json: {status: 200, charity: nil}
    end
  end

  def create
    regNumber = params[:reg_number].strip

    country_selected = params[:country_selected]

    if country_selected == 0
      checks_url = "https://findthatcharity.uk/orgid/GB-CHC-#{regNumber}.json"
      response = HTTParty.get(checks_url)
      charity_data = JSON.parse(response.body)
      charity_name = charity_data['name']
      charity = save_reg_number regNumber, charity_name
      render json: {status: 200, name: charity_name, id: charity.id}
    else
      checks_url = "https://www.oscr.org.uk/about-charities/search-the-register/charity-details?number=#{regNumber}"

      doc = Nokogiri::HTML(open(checks_url))

      row = doc.css(".subtitle").first

      text = row.text

      if text
        text = text.strip.split(',')[0]
        charity = save_reg_number regNumber, text
        render json: {status: 200, name: text, id: charity.id}
      else
        render json: {error: "invalid reg number"}, status: 400
      end
    end

  rescue JSON::ParserError
    render json: {error: "invalid reg number"}, status: 400
  end


  def destroy
    charity = CharityNumber.find_by_id(params[:id])
    authorize! :manage, charity
    charity.destroy
    render json: {status: 200}
  end

  private

  def save_reg_number(reg_number, full_name)
    charity = CharityNumber.find_or_create_by!(organisation_id: current_user.organisation.id)
    charity.update({
      organisation_id: current_user.organisation.id,
      country_registered: params[:country_selected],
      full_name: full_name,
      registration_number: reg_number
      })

    authorize! :manage, charity

    return charity
  end

end
