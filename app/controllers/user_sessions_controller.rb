class UserSessionsController < ApplicationController
  respond_to :html

  # omniauth callback method
  def create
    omniauth = request.env['omniauth.auth']

    session[:hgonetoken] = omniauth.credentials[:token]

    logger.debug "+++ #{omniauth}"
    user = User.find_by_id(omniauth[:uid])

    unless user.con_enabled?
      redirect_to root_path(:referrer => "eventstop_disabled") and return
    end  

    session[:user_id] = user.id
    redirect_to session.delete(:entry_point) || root_path
  end

  # Omniauth failure callback
  def failure
    flash[:notice] = params[:message]
  end

  # logout - Clear our rack session BUT essentially redirect to the provider
  # to clean up the Devise session from there too !
  def destroy
    session[:user_id] = nil
    session[:add_survey_path] = nil


    @current_user = nil;

    respond_to do |format|
      format.html {
        redirect_to registration_login_path
      }
      format.json {
        render json: { status: 200 }
      }
    end

  end

  def end_all
    session[:user_id] = nil
    session[:add_survey_path] = nil
    @current_user = nil;
    redirect_to ENV['CUSTOM_PROVIDER_URL'] + "/users/sign_out"
  end

  def check_alive
    if current_user
      render json: { status: 200 }
    else
      render json: { status: 400, error: 'User not found' }
    end
  end

end
