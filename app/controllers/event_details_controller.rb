class EventDetailsController < ApplicationController
  respond_to :json

  before_action :get_event, only: [:show, :update]

  layout 'app_new'

  # GET /event_details/new
  # Returns an empty event object for the form
  def new
    # Create a skeleton event object without saving it
    @event = Event.new
    @event.build_event_address

    event_data = @event.as_json
    event_data[:event_address] = @event.event_address.as_json
    event_data[:event_types] = EventType.all.order(:name)

    render json: {
      event: event_data,
      status: 200
    }
  end

  # POST /event_details
  # Creates a new event when the form is saved
  def create
    @event = Event.new(secured_params_with_org_and_user)

    # Set initial values for a new event
    @event.completion_status = "Event Details"
    @event.step = 1.0
    @event.ticket_type = 'physical' unless @event.ticket_type.present?

    if @event.save
      event_data = @event.as_json
      event_data[:event_address] = @event.event_address.as_json if @event.event_address
      event_data[:event_types] = EventType.all.order(:name)

      render json: {
        event: event_data,
        status: 200
      }
    else
      render json: {
        error: @event.errors.full_messages.join(", "),
        status: 400
      }, status: :unprocessable_entity
    end
  end

  # GET /event_details/:id
  def show
    # Include event_types, event_address, registration_fields, and static_reg_field in the response
    event_data = @event.as_json(include: [:registration_fields, :static_reg_field])
    event_data[:event_address] = @event.event_address.as_json if @event.event_address
    event_data[:event_types] = EventType.all.order(:name)

    render json: {
      event: event_data,
      status: 200
    }
  end

  # GET /event_details/event_types
  def event_types
    # Return a list of all event types for the dropdown
    @event_types = EventType.all.order(:name)
    render json: {
      event_types: @event_types,
      status: 200
    }
  end

  # PUT /event_details/:id
  def update
    if @event.update(secured_params)
      if @event.title.present?
        @event.update(event_created_date: Time.zone.now) if @event.event_created_date.blank?
        @event.update_completion_status("Ticket Details")
      end

      event_data = @event.as_json
      event_data[:event_address] = @event.event_address.as_json if @event.event_address
      event_data[:event_types] = EventType.all.order(:name)

      render json: {
        event: event_data,
        status: 200
      }
    else
      render json: {
        error: @event.errors.full_messages.join(", "),
        status: 400
      }, status: :unprocessable_entity
    end
  end

  private

  def get_event
    @event = Event.includes(:registration_fields, :static_reg_field, :event_address).where(organisation_id: current_user.organisation_id).find_by_id(params[:id])
    unless @event
      render json: { error: "Event not found", status: 404 }, status: :not_found
      return
    end
    authorize! :manage, @event
  end

  def secured_params
    params.require(:event)
          .permit(:user_id, :ticket_type, :ticket_payment_options, :close_date, :cost_code,
                  :acc_enabled, :add_sponsors, :datetimefrom, :datetimeto, :custom_url,
                  :location, :sponsor_view_email, :sponsor_view_delegate, :details,
                  :organiser, :organiser_email, :sponsor_title, :title, :is_public,
                  :conference_id, :email_default, :summary, :event_type_name, :event_type_id,
                  :step, :international, :remove_location, tags: [],
                  event_address_attributes: [:id, :address1, :address2, :city, :county,
                                            :postcode, :short_info, :latitude, :longitude,
                                            :country_code])
  end

  def secured_params_with_org_and_user
    params.require(:event).permit(
      :user_id, :ticket_type, :ticket_payment_options, :close_date, :cost_code,
      :acc_enabled, :add_sponsors, :datetimefrom, :datetimeto, :custom_url,
      :location, :sponsor_view_email, :sponsor_view_delegate, :details,
      :organiser, :organiser_email, :sponsor_title, :title, :is_public,
      :conference_id, :email_default, :summary, :event_type_name, :event_type_id,
      :step, :international, :remove_location, tags: [],
      event_address_attributes: [:id, :address1, :address2, :city, :county,
                                :postcode, :short_info, :latitude, :longitude,
                                :country_code]
    ).merge(
      organisation_id: current_user.organisation.id,
      user_id: current_user.id,
      temp_flag: false,
      seen_popup: true,
      show_date: true
    )
  end
end
