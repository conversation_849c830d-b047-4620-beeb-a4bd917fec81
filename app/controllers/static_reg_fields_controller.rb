class StaticRegFieldsController < ApplicationController

  # before_action :login_required 
  # TODO need these on bookings, requires some minimal security

  def index
    # TODO cancan stuff
    @static_fields = StaticRegField.find_by_event_id(params[:event_id])

    if @static_fields
      render json: @static_fields.to_json
    else
      render json: {error: 'Static Reg Fields Not returned'}, :status => 400
    end
  end
end
