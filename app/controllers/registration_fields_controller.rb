class RegistrationFieldsController < ApplicationController

  # before_action :login_required// Needed by attendees in booking

  def index
    # TODO cancan stuff
    @registration_fields = RegistrationField.where(:event_id => params[:event_id])

    if params[:field_type]
      @registration_fields = @registration_fields.where(field_type: params[:field_type])
    end

    if @registration_fields
      render json: @registration_fields.to_json
    else
      render json: {status: 400, error: 'Reg Fields Not returned'}
    end
  end
end
