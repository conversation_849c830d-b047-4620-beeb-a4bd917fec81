class ForecastsController < ApplicationController
  before_action :login_required
  before_action :ensure_admin

  def index
    if params[:year]
      @current_year = TargetYear.find_by_id(params[:year])
      @forecast_target = @current_year.forecast_target
    else
      @target_years = TargetYear.all
      @current_year = @target_years.where("date_from <= ? and date_to >= ?", Date.today, Date.today).first
      @forecast_target = ForecastTarget.where(target_year: @current_year).first
    end

    get_event_fee_data(@current_year)

    respond_to do |format|
      format.csv {send_data generate_fees_csv(), filename: "eventfees-#{Date.today}.csv"}
      format.json {}
    end
  end

  private

  def get_event_fee_data(target_year)
    # TODO Should we not also be getting events without payments - hence the left join
    @events = Event.joins("LEFT JOIN booking_payments ON events.id = booking_payments.event_id AND refunded IS NULL").
        where("datetimeto <= ? and datetimeto >= ?", target_year.date_to + 1.month, target_year.date_from).
        select("events.id as id, events.organisation_id, events.title as event_name, to_char(datetimeto, 'FMmonth') AS month, sum(application_fees) as hg_fees").
        group('1').order(:datetimeto, :id)

    @events_details = []

    set_summary_monthly_data

    details_block = []

    # # TODO get the admin fees too!
    @events.each do |ev|

      if (!details_block.empty? && details_block.last[:month] != ev[:month] && @forecast_target)

        ev_summary_hash = {organisation_name: '', month: details_block.last[:month].upcase, forecast_target: @forecast_target[details_block.last[:month].to_sym], summary_row: true, hg_fees: @hg_fees_summary,
                           free_event_fee: @free_event_fee_summary, advanced_payments_client_managed: @advanced_payments_client_managed_summary,
                           advanced_payments_hg_managed: @advanced_payments_hg_managed_summary, setup_fee: @setup_fee_summary, badging_cost: @badging_cost_summary, equipment_cost: @equipment_cost_summary,
                           management_fee: @management_fee_summary, total: @total_summary}

        @events_details << ev_summary_hash
        set_summary_monthly_data #Resets the monthly summary

        @events_details.concat(details_block)

        details_block = []
      end

      ev_hash = {organisation_name: ev.organisation.try(:name), month: ev[:month], id: ev[:id], event_name: ev[:event_name], hg_fees: ev[:hg_fees] || 0, summary_row: false}

      ev_hash[:total] = 0
      @hg_fees_summary += ev_hash[:hg_fees]

      admin_fee = EventFee.find_by(eventstop_event_id: ev.id)

      if admin_fee
        ev_hash[:free_event_fee] = admin_fee.free_event_fee
        ev_hash[:advanced_payments_client_managed] = admin_fee.advanced_payments_client_managed
        ev_hash[:advanced_payments_hg_managed] = admin_fee.advanced_payments_hg_managed
        ev_hash[:setup_fee] = admin_fee.setup_fee
        ev_hash[:management_fee] = admin_fee.management_fee
        ev_hash[:badging_cost] = admin_fee.badging_cost
        ev_hash[:equipment_cost] = admin_fee.equipment_cost
        ev_hash[:total] = ev_hash[:hg_fees] +
            ev_hash[:free_event_fee] +
            ev_hash[:advanced_payments_client_managed] +
            ev_hash[:advanced_payments_hg_managed] +
            ev_hash[:setup_fee] +
            ev_hash[:management_fee] +
            ev_hash[:badging_cost] +
            ev_hash[:equipment_cost]

        @free_event_fee_summary += ev_hash[:free_event_fee]
        @advanced_payments_client_managed_summary += ev_hash[:advanced_payments_client_managed]
        @advanced_payments_hg_managed_summary += ev_hash[:advanced_payments_hg_managed]
        @setup_fee_summary += ev_hash[:setup_fee]
        @management_fee_summary += ev_hash[:management_fee]
        @badging_cost_summary += ev_hash[:badging_cost]
        @equipment_cost_summary += ev_hash[:equipment_cost]
      else
        ev_hash[:free_event_fee] = 0
        ev_hash[:advanced_payments_client_managed] = 0
        ev_hash[:advanced_payments_hg_managed] = 0
        ev_hash[:setup_fee] = 0
        ev_hash[:management_fee] = 0
        ev_hash[:equipment_cost] = 0
        ev_hash[:badging_cost] = 0
        ev_hash[:total] = ev_hash[:hg_fees]
      end

      details_block << ev_hash

      @total_summary += ev_hash[:total]

    end

  end

  def set_summary_monthly_data
    @hg_fees_summary = 0
    @free_event_fee_summary = 0
    @advanced_payments_client_managed_summary = 0
    @advanced_payments_hg_managed_summary = 0
    @setup_fee_summary = 0
    @management_fee_summary = 0
    @badging_cost_summary = 0
    @equipment_cost_summary = 0
    @total_summary = 0
  end

  def generate_fees_csv()

    details_array = []

    header = ['Month', 'Event Name', 'Target Revenue', 'Debit/Credit Fees',
              'Free Event Fees', 'Adv Payments - Client Managed', 'Adv Payments - HG Managed', 'Setup Fee',
              'Event Mgt Fee', 'Badging Fees', 'Equipment Fees', 'Total Fees', 'Percent To Target']

    details_array << header

    @events_details.each do |details|

      percent_by_target = if details[:forecast_target]
                            if details[:total] == 0 || details[:forecast_target] == 0
                              0
                            else
                              "%.2f" % (details[:total] / details[:forecast_target])
                            end
                          else
                            'n/a'
                          end

      row = [details[:month], details[:event_name], details[:forecast_target], details[:hg_fees], details[:free_event_fee],
             details[:advanced_payments_client_managed], details[:advanced_payments_hg_managed], details[:setup_fee], details[:management_fee],
             details[:badging_cost], details[:equipment_cost], details[:total], percent_by_target]

      details_array << row

    end

    CSV.generate(headers: true) do |csv|
      details_array.each do |arr|
        csv << arr
      end
    end

  end


end
