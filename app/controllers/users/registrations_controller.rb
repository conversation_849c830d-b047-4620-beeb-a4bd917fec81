# app/controllers/users/registrations_controller.rb
class Users::RegistrationsController < Devise::RegistrationsController
  respond_to :html, :json

  def create
    # Handle organisation creation in a transaction
    ActiveRecord::Base.transaction do
      if params[:user][:organisation_name].present?

        # Create organisation first without owner
        organisation = Organisation.new(
          name: params[:user][:organisation_name],
          skip_owner_validation: true
        )

        organisation.save!

        # Create user with organisation_id
        build_resource(sign_up_params.except(:organisation_name).merge(organisation_id: organisation.id))

        if resource.save
          # Now update organisation with owner_id
          organisation.update!(owner_id: resource.id)

          # Set legacy session for compatibility
          session[:user_id] = resource.id

          yield resource if block_given?

          if request.format.json?
            if resource.active_for_authentication?
              render json: {
                success: true,
                user: resource.as_json(except: [:encrypted_password]),
                organisation: organisation.as_json(only: [:id, :name]),
                message: 'Account created successfully'
              }
            else
              # User needs to confirm email
              render json: {
                success: true,
                user: resource.as_json(except: [:encrypted_password]),
                organisation: organisation.as_json(only: [:id, :name]),
                message: 'Account created successfully! Please check your email to confirm your account.',
                requires_confirmation: true
              }
            end
          else
            if resource.active_for_authentication?
              set_flash_message! :notice, :signed_up
              sign_up(resource_name, resource)
              respond_with resource, location: after_sign_up_path_for(resource)
            else
              set_flash_message! :notice, :"signed_up_but_#{resource.inactive_message}"
              expire_data_after_sign_up!
              respond_with resource, location: after_inactive_sign_up_path_for(resource)
            end
          end
        else
          clean_up_passwords resource
          set_minimum_password_length
          if request.format.json?
            render json: {
              success: false,
              errors: resource.errors.full_messages
            }, status: :unprocessable_entity
          else
            respond_with resource
          end
        end
      else
        # No organisation name provided - this should not happen as organisation is required
        if request.format.json?
          render json: {
            success: false,
            errors: ['Organisation name is required']
          }, status: :unprocessable_entity
        else
          redirect_to new_user_registration_path, alert: 'Organisation name is required'
        end
      end
    end
  rescue ActiveRecord::RecordInvalid => e
    if request.format.json?
      render json: {
        success: false,
        errors: [e.message]
      }, status: :unprocessable_entity
    else
      redirect_to new_user_registration_path, alert: e.message
    end
  rescue => e
    Rails.logger.error "Registration error: #{e.message}"
    if request.format.json?
      render json: {
        success: false,
        errors: ['An error occurred during registration']
      }, status: :internal_server_error
    else
      redirect_to new_user_registration_path, alert: 'An error occurred during registration'
    end
  end

  private

  def sign_up_params
    params.require(:user).permit(:email, :password, :password_confirmation, :organisation_name)
  end

  def account_update_params
    params.require(:user).permit(:email, :password, :password_confirmation, :current_password, :organisation_name)
  end
end
