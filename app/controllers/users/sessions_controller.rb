# app/controllers/users/sessions_controller.rb
class Users::SessionsController < Devise::SessionsController
  respond_to :html, :json
  protect_from_forgery with: :null_session, if: -> { request.format.json? }

  def create
    resource = warden.authenticate!(auth_options)

    if resource
      # Set legacy session for compatibility with existing code
      session[:user_id] = resource.id
      set_flash_message!(:notice, :signed_in) if is_flashing_format?
      sign_in(resource_name, resource)

      if request.format.json?
        render json: {
          success: true,
          user: resource.as_json(except: [:encrypted_password]),
          message: 'Signed in successfully'
        }
      else
        respond_with resource, location: after_sign_in_path_for(resource)
      end
    end
  rescue Warden::NotAuthenticated
    if request.format.json?
      render json: {
        success: false,
        message: failure_message
      }, status: :unauthorized
    else
      redirect_to new_user_session_path, alert: failure_message
    end
  end

  def respond_to_on_destroy
    if request.format.json?
      render json: {
        success: true,
        message: 'Signed out successfully'
      }
    else
      super
    end
  end

  protected

  def auth_options
    { scope: resource_name, recall: "#{controller_path}#new" }
  end

  private

  def failure_message
    # Check if user exists but is unconfirmed
    if params[:user] && params[:user][:email] && (user = User.find_by(email: params[:user][:email])) && !user.confirmed?
      'Your email address has not been confirmed. Please check your email for confirmation instructions.'
    else
      'Invalid email or password.'
    end
  end
end
