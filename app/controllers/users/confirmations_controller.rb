# app/controllers/users/confirmations_controller.rb
class Users::ConfirmationsController < Devise::ConfirmationsController
  respond_to :html, :json

  # GET /users/confirmation/new
  def new
    super
  end

  # POST /users/confirmation
  def create
    self.resource = resource_class.send_confirmation_instructions(resource_params)
    yield resource if block_given?

    if successfully_sent?(resource)
      if request.format.json?
        render json: {
          success: true,
          message: 'Confirmation instructions sent to your email'
        }
      else
        respond_with({}, location: after_resending_confirmation_instructions_path_for(resource_name))
      end
    else
      if request.format.json?
        render json: {
          success: false,
          errors: resource.errors.full_messages
        }, status: :unprocessable_entity
      else
        respond_with(resource)
      end
    end
  end

  # GET /users/confirmation?confirmation_token=abcdef
  def show
    self.resource = resource_class.confirm_by_token(params[:confirmation_token])
    yield resource if block_given?

    if resource.errors.empty?
      set_flash_message!(:notice, :confirmed)
      
      if request.format.json?
        render json: {
          success: true,
          message: 'Your email address has been successfully confirmed.',
          user: resource.as_json(except: [:encrypted_password])
        }
      else
        respond_with_navigational(resource){ redirect_to after_confirmation_path_for(resource_name, resource) }
      end
    else
      if request.format.json?
        render json: {
          success: false,
          message: 'Confirmation token is invalid or has expired'
        }, status: :unprocessable_entity
      else
        respond_with_navigational(resource.errors, status: :unprocessable_entity){ render :new }
      end
    end
  end

  protected

  def after_resending_confirmation_instructions_path_for(resource_name)
    is_navigational_format? ? new_session_path(resource_name) : root_path
  end

  def after_confirmation_path_for(resource_name, resource)
    if sign_in_after_confirm?
      after_sign_in_path_for(resource)
    else
      new_session_path(resource_name)
    end
  end

  private

  def sign_in_after_confirm?
    true # You can customize this logic
  end
end
