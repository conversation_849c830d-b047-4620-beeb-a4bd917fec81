# app/controllers/users/passwords_controller.rb
class Users::PasswordsController < Devise::PasswordsController
  respond_to :html, :json

  protect_from_forgery with: :null_session, if: -> { request.format.json? }

  skip_before_action :authenticate_user!, only: [:create, :edit, :update]

  # POST /users/password
  def create
    self.resource = resource_class.send_reset_password_instructions(resource_params)
    yield resource if block_given?

    if successfully_sent?(resource)
      if request.format.json?
        render json: {
          success: true,
          message: 'Password reset instructions sent to your email'
        }
      else
        respond_with({}, location: after_sending_reset_password_instructions_path_for(resource_name))
      end
    else
      if request.format.json?
        render json: {
          success: false,
          errors: resource.errors.full_messages
        }, status: :unprocessable_entity
      else
        respond_with(resource)
      end
    end
  end

  # PUT /users/password
  def update
    self.resource = resource_class.reset_password_by_token(resource_params)
    yield resource if block_given?

    if resource.errors.empty?
      # Ensure user is confirmed after password reset
      resource.confirm if !resource.confirmed?

      resource.unlock_access! if unlockable?(resource)

      if request.format.json?
        # For JSON requests, return success without session
        render json: {
          success: true,
          message: 'Password has been reset successfully. You can now log in with your new password.',
          user: resource.as_json(except: [:encrypted_password])
        }
      else
        # For HTML requests, use the normal Devise flow
        if Devise.sign_in_after_reset_password
          flash_message = resource.active_for_authentication? ? :updated : :updated_not_active
          set_flash_message!(:notice, flash_message)
          resource.after_database_authentication
          sign_in(resource_name, resource)
        else
          set_flash_message!(:notice, :updated_not_active)
        end
        respond_with resource, location: after_resetting_password_path_for(resource)
      end
    else
      if request.format.json?
        render json: {
          success: false,
          errors: resource.errors.full_messages
        }, status: :unprocessable_entity
      else
        set_minimum_password_length
        respond_with resource
      end
    end
  end

  # GET /users/password/edit?reset_password_token=abcdef
  def edit
    self.resource = resource_class.new
    set_minimum_password_length
    resource.reset_password_token = params[:reset_password_token]

    if request.format.json?
      # For JSON, just validate the token
      original_token = params[:reset_password_token]
      reset_password_token = Devise.token_generator.digest(resource_class, :reset_password_token, original_token)
      recoverable = resource_class.find_by(reset_password_token: reset_password_token)

      if recoverable && recoverable.reset_password_period_valid?
        render json: {
          success: true,
          reset_password_token: original_token,
          message: 'Token is valid'
        }
      else
        render json: {
          success: false,
          message: 'Reset password token is invalid or has expired'
        }, status: :unprocessable_entity
      end
    else
      respond_with(resource)
    end
  end

  protected

  def after_resetting_password_path_for(resource)
    Devise.sign_in_after_reset_password ? after_sign_in_path_for(resource) : new_session_path(resource_name)
  end

  def after_sending_reset_password_instructions_path_for(resource_name)
    new_session_path(resource_name) if is_navigational_format?
  end
end
