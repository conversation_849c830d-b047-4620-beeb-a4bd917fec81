class PaymentsController < ApplicationController
  def delete
    account_id = current_user.stripe_account&.stripe_user_id
    account = Stripe::Account.retrieve(account_id)
    account.delete
    flash!(success: 'Account deleted Successfully')
  rescue Exception => e
    flash!(error: e.message)
  end

  def register_payment
    payment_params = params[:payment]

    @booking = EventBooking.find_by_id(payment_params[:event_booking_id])

    payment_type = BookingPayment.payment_types[payment_params[:method].to_sym]

    @payment = @booking.booking_payments.create(registered_user_id: payment_params[:user_id],
                                                event_id: @booking.event_id,
                                                amount: payment_params[:amount],
                                                payment_datetime: DateTime.now,
                                                reference: payment_params[:ref],
                                                payment_type: payment_type)

    if !@payment.errors.empty?
      render json: { error: @payment.errors }, status: 400
    else
      @booking.set_payment_status
    end
  end

  def refund_all
    payment_ids = params[:booking][:payments].map { |pay| pay[:payment_id] }
    payments = BookingPayment.where(id: payment_ids)

    BookingPayment.transaction do
      cancel_booking(payments.first) if params[:cancel]
      payments.each do |payment|
        stripe_refund_for_payment!(payment) if payment.stripe_charge_id.present?
      end

      if payments.update_all(refunded: DateTime.now)
        eb = payments.first.event_booking
        eb.set_payment_status

        if Rails.env.development?
          TicketMailer.send_cancellation(eb.id, true).deliver_now
        else
          TicketMailer.send_cancellation(eb.id, true).deliver_later
        end

        render json: { success: 'refunded', status: payments.first.event_booking.payment_status }, status: 200
      else
        render json: { status: 'failed', payment: 'failed' }, status: 400
      end
    end
  end

  def refund
    payment_id = params[:payment_id] || params[:payment][:payment_id]
    payment = BookingPayment.find_by_id(payment_id)

    BookingPayment.transaction do
      stripe_refund_for_payment!(payment) if payment.stripe_charge_id.present?
      if payment.update_attribute(:refunded, DateTime.now)
        eb = payment.event_booking
        eb.set_payment_status

        if Rails.env.development?
          TicketMailer.send_confirmation(eb.id).deliver_now
        else
          TicketMailer.send_confirmation(eb.id).deliver_later
        end
        render json: { success: 'refunded', status: eb.payment_status }, status: 200
      else
        render json: { status: 'failed', payment: 'failed' }, status: 400
      end
    end
  end

  def notes
    payment = BookingPayment.find_by_id(params[:id])
    if payment.update_attribute(:notes, params[:notes])
      render json: { success: 'updates' }, status: 200
    else
      render json: { status: 'failed', notes: 'failed' }, status: 400
    end
  end

  def send_reminders
    event = Event.find_by_id(params[:event_id])
    event.event_bookings.fully_booked.each do |eb|
      if eb.payment_status == 'unpaid' || eb.payment_status == 'part_paid'
        email_reminders(eb)
      end
    end
    render json: { head: :ok }
  end

  def get_payments
    event_booking = EventBooking.where(uuid: (params[:id])).first
    if event_booking
      payments = event_booking.booking_payments
      render json: { payments: event_booking.booking_payments, payment_status: event_booking.payment_status }
    else
      render json: { error: 'could not get payments' }, status: 400
    end
  end

  private

  def stripe_user_id(event)
    event.organisation.stripe_account.stripe_user_id
  end

  def email_reminders(event_booking)
    if event_booking
      ticket_mailer = TicketMailer.send_confirmation(event_booking.id, true)
      if Rails.env.development?
        ticket_mailer.deliver_now
      else
        ticket_mailer.deliver_later
      end
    end
  end

  def check_refund_date
    event = Event.find_by_id(params[:event_id])
    if (event.refund_date && event.refund_date < DateTime.now) || (event.datetimefrom < DateTime.now)
      render json: { status: 'failed', error_msg: 'Cannot refund, expired' }, status: 400
      return false
    end
  end

  def stripe_refund_for_payment!(payment)
    refund = stripe_refund!(payment.stripe_charge_id, payment.event_booking)
    payment.update_attribute(:stripe_refund_id, refund.id)
  end

  def stripe_refund!(payment_intent_id, event_booking)
    pi = StripePayment.get_intent(event_booking, payment_intent_id)
    StripePayment.stripe_refund!(pi.latest_charge, event_booking.event)
  end

  def cancel_booking(payment)
    # TODO: assumes one payment per event at the dayjs
    event_booking = payment.event_booking
    cancel_user = current_user || event_booking.registered_user
    event_booking.cancel_booking(cancel_user)
    event_booking.set_payment_status
  end
end
