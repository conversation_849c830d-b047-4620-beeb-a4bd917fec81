class TestimonialsController < ApplicationController

  before_action :login_required
  before_action :ensure_admin

  def index
    @testimonials = Testimonial.all
  end

  def get_testimonials
     @testimonials = Testimonial.all
     render json: @testimonials
  end

  def new
     @testimonial = Testimonial.new
  end

  def create
    testimonial = Testimonial.new(testimonial_params)
     if testimonial.save
       render json: testimonial
     else
      render json: {error: 'Not updated'}, status: 400
     end
  end

  def edit
    @testimonial = Testimonial.find_by_id(params[:id])
  end

  def update
    testimonial = Testimonial.find(params[:id])
    if testimonial.update(update_testimonial)
      render json: testimonial
    else
       render json: {error: 'Not updated'}, status: 400
    end
  end

  def destroy
    testimonial = Testimonial.find(params[:id])
    if testimonial.destroy
      render json: {success: "Success"}
    else
      render json: {error: 'Not deleted'}, status: 400
    end
  end

  private

  def testimonial_params
    params.require(:testimonial).permit(:name, :testimonial_text, :industry)
  end

  def update_testimonial
    params.require(:testimonial).permit(:name, :testimonial_text, :industry)
  end

end
