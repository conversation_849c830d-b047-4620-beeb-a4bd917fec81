class TicketsController < ApplicationController
    respond_to :json
    # before_action :login_required
    before_action :get_event

    # GET /event_details/:event_detail_id/tickets
    # Returns ticket details for an event
    def index
      render json: {
        tickets: @event.tickets,
        ticket_groups: @event.ticket_groups,
        status: 200
      }
    end

    # GET /event_details/:event_detail_id/tickets/:id
    # Returns a specific ticket details
    def show
      @ticket = @event.tickets.find(params[:id])
      render json: {
        ticket: @ticket,
        status: 200
      }
    end

    # POST /event_details/:event_detail_id/tickets
    # Creates a new ticket for an event
    def create
      @ticket = @event.tickets.new(ticket_params)

      if @ticket.save
        render json: {
          ticket: @ticket,
          status: 200
        }
      else
        render json: {
          error: @ticket.errors.full_messages.join(", "),
          status: 400
        }, status: :unprocessable_entity
      end
    end

    # PUT /event_details/:event_detail_id/tickets/:id
    # Updates an existing ticket
    def update
      @ticket = @event.tickets.find(params[:id])

      if @ticket.update(ticket_params)
        render json: {
          ticket: @ticket,
          status: 200
        }
      else
        render json: {
          error: @ticket.errors.full_messages.join(", "),
          status: 400
        }, status: :unprocessable_entity
      end
    end

    # DELETE /event_details/:event_detail_id/tickets/:id
    # Deletes a ticket
    def destroy
      @ticket = @event.tickets.find(params[:id])
      @ticket.destroy

      render json: {
        message: "Ticket deleted successfully",
        status: 200
      }
    end

    # PUT /event_details/:event_detail_id/tickets/update_all
    # Updates all tickets for an event at once (batch update)
    def update_all
      event_params = params.require(:event).permit(
        packages_attributes: [
          :id, :event_id, :details, :virtual_link, :meeting_id, :meeting_password,
          :start_time, :end_time, :max_allowed, :cost_a, :cost_b, :ticket_no,
          :is_new, :package_type, :vat_rate_id, :group_amount, :ticket_type,
          :details_for_all_group_members, :tickets_for_whole_group,
          { package_options_attributes: [
            :id, :option_title, :option_required,
            { package_sub_options_attributes: [:id, :option_value, :option_cost] }
          ]},
          { child_tickets_attributes: [
            :id, :event_id, :details, :virtual_link, :meeting_id, :meeting_password,
            :start_time, :end_time, :max_allowed, :cost_a, :cost_b, :ticket_no,
            :is_new, :package_type, :vat_rate_id, :group_amount, :ticket_type,
            :details_for_all_group_members, :tickets_for_whole_group, :parent_id, :child_ticket
          ]}
        ],
        ticket_groups_attributes: [
          :id, :description,
          { packages_attributes: [
            :id, :event_id, :details, :virtual_link, :meeting_id, :meeting_password,
            :start_time, :end_time, :max_allowed, :cost_a, :cost_b, :ticket_no,
            :is_new, :package_type, :vat_rate_id, :group_amount, :ticket_type,
            :details_for_all_group_members, :tickets_for_whole_group
          ]}
        ]
      )

      @event.assign_attributes(event_params)

      if @event.save
        @event.update_completion_status("Ticket Details")
        # Mark the event as complete, as it is useable now
        @event.update(complete: true)
        render json: {
          tickets: @event.tickets,
          ticket_groups: @event.ticket_groups,
          status: 200
        }
      else
        render json: {
          error: @event.errors.full_messages.join(", "),
          status: 400
        }, status: :unprocessable_entity
      end
    end

    private

    def get_event
      @event = Event.find_by_id(params[:event_detail_id])
      unless @event
        render json: { error: "Event not found", status: 404 }, status: :not_found
        return
      end
      authorize! :manage, @event
    end

    def ticket_params
      params.require(:ticket).permit(
        :details, :virtual_link, :meeting_id, :meeting_password,
        :start_time, :end_time, :max_allowed, :cost_a, :cost_b, :ticket_no,
        :is_new, :package_type, :vat_rate_id, :group_amount, :ticket_type,
        :details_for_all_group_members, :tickets_for_whole_group
      )
    end
end
