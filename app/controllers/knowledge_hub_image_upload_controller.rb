class KnowledgeHubImageUploadController < ApplicationController

  def upload_knowledge_image
   new_filename = save_knowledge_hub_image!
   render json: {status: 200, filename: new_filename}
  end

  def remove_file
    uploader = KnowledgeHubUploader.new
    uploader.retrieve_from_store!(params[:filename])
    uploader.remove!
    fu = KnowledgeHubArticle.find_by_id(params[:articleId])
    if fu
      fu.image = nil
      fu.save!
      #Ex:- :null => false
      render json: {status: 200}
     else
      render json: {status: 400}
    end
  end

 private

  def save_knowledge_hub_image!()
    article = KnowledgeHubArticle.find(params[:article_id])
    # We only allow this when the article has already been saved:
    if article
      # If there's already a stored image we should remove it from the S3 store to free up space for the new one
      if article.image
        uploader = KnowledgeHubUploader.new
        uploader.retrieve_from_store!(article.image)
        uploader.remove!
      end
      # Now we'll upload the new file...
      uploader = KnowledgeHubUploader.new
      # We pass the model id through to the uploader via a prop to store to the article's own folder:
      uploader.instance_variable_set("@new_file_name", "main_image #{params[:article_id]}")
      # ... And store the file.
      uploader.store!(params[:file])
      # Finally we'll save the new filename to our model's record:
      article.image = uploader.filename
      article.save!
      return uploader.filename
    end
  end
end
