class EventPreviewController < ApplicationController

    def template_change
        event = Event.find_by_id(params[:eid])
        if event
            event.update_column(:template, params[:template])
        end

        render json: {status: 200}
    end

    def colour_changes
        event = Event.find_by_id(params[:eid])

        if event
            event.update(phcolour: params[:phcolour], buttoncolour: params[:buttoncolour], textcolour: params[:textcolour])
        end

        render json: {status: 200}
    end
end