class KnowledgeHubsController < ApplicationController

  before_action :login_required, :except => [:show]
  before_action :ensure_admin, :except => [:show]
  layout "home", only: [:show]

  def new
    @knowledge_hub = KnowledgeHub.new
  end

  def create
    save_knowledge_hub
    render json: {status: 200}
  end

  def edit_contents
    kh = KnowledgeHub.find_by_id(params[:id])

    if kh
      render json: kh.to_json
    else
      render json: {status: 200, vat: nil}
    end
  end

  def edit
    @knowledge_hub = KnowledgeHub.find_by_id(params[:id])
  end

  def case_study
  end

  def show
   render template: "knowledge_hubs/#{params[:page].downcase}"
  end

  def update
    @knowledge_hub = KnowledgeHub.find(params[:id])
    if @knowledge_hub.update(update_knowledge_hub)
      render json: {status: 200}
    else
      flash_now!(:error => "KnowledgeHub not updated")
      render :edit
    end
  end

  def destroy
    knowledge_hub = KnowledgeHub.find(params[:id])
    knowledge_hub.destroy
    redirect_to('/hg_admin/knowledge_hub', flash: {success: 'KnowledgeHub Deleted'})
  end

  private

  def save_knowledge_hub
    KnowledgeHub.find_or_create_by!(id: params[:id]) do |kh|
      kh.title = params[:knowledge][:title]
      kh.meta_description = params[:knowledge][:meta_description]
      kh.icon = params[:knowledge][:icon]
      kh.image = params[:knowledge][:image]
      kh.date = DateTime.now
      kh.body = params[:knowledge][:body]
    end
  end

  def update_knowledge_hub
    params.require(:knowledge_hub).permit(:title, :meta_description, :body, :image, :icon)
  end

end
