class TargetYearsController < ApplicationController

  before_action :login_required
  before_action :ensure_admin

  def index
    @target_years = TargetYear.all
    @current_year = @target_years.where("date_from <= ? and date_to >= ?", Date.today, Date.today).first
    @forecast_target = ForecastTarget.where(target_year: @current_year).first
  end

  def show
    @forecast_target = ForecastTarget.where(target_year_id: params[:id]).first
  end

  def create
    @forecast_target = ForecastTarget.create(target_params)
  end

  def update
    @forecast_target = ForecastTarget.find_by_id(params[:id])
    @forecast_target.update(target_params)
  end

  private

  def target_params
    params.require(:forecast_target).permit(:id, :target_year_id, :april, :may, :june, :july, :august, :september, :october, :november, :december, :january, :february, :march)
  end

end
