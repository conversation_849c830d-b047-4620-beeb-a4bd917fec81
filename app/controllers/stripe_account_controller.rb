class StripeAccountController < ApplicationController

    respond_to :json

    # Create a new Stripe Account for the current user's organisation
  def create
    # TODO: add other stripe fields?
    stripe_account = StripeAccount.new(update_params)
    stripe_account.organisation = current_user.organisation
    stripe_account.user_id = current_user.id

    if stripe_account.save
      render json: { message: 'Stripe account created successfully' }, status: :created
    else
      render json: { errors: stripe_account.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # Show the Stripe details for the current user's organisation
  def show
    @event_user = current_user
    @event_org = current_user.organisation
    stripe_account = @event_org.stripe_account

    if stripe_account
      render json: stripe_account, status: :ok
    else
      render json: { error: 'No Stripe details found for this organisation' }, status: :not_found
    end
  end

  # Delete the Stripe detail for the current user's organisation
  def destroy
    stripe_account = current_user.organisation.stripe_account

    if stripe_account && stripe_account.destroy
      render json: { message: 'Stripe details deleted successfully' }, status: :ok
    else
      render json: { error: 'Failed to delete Stripe details' }, status: :unprocessable_entity
    end
  end

    # Update action to handle updates to stripe_accounts
  def update
    stripe_account = current_user.organisation.stripe_account

    if stripe_account.update(update_params)
      render json: { message: 'Stripe details updated successfully' }, status: :ok
    else
      render json: { errors: stripe_account.errors.full_messages }, status: :unprocessable_entity
    end
  end

    # Check if the organisation has a Stripe account with keys set and not empty
  def check_keys
    stripe_account = current_user.organisation.stripe_account

    if stripe_account && stripe_account.publishable_key.present? && stripe_account.secret_key.present?
      render json: { message: 'success' }, status: :ok
    else
      render json: { error: 'Stripe account or keys are missing or empty' }, status: :ok
    end
  end

  private

  def update_params
    params.require(:stripe_account).permit(:stripe_account_type, :stripe_user_id, :publishable_key, :secret_key, :currency, :stripe_account_status, :test_mode)
  end
end
