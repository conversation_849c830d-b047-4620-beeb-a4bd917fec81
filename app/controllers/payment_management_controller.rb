class PaymentManagementController < ApplicationController

  before_action :login_required, except: [:payment_info_for_event]

  def create
    options = params[:options]

    event = Event.find_by_id(params[:event_id])

    paymentInfo = if event
                    event.update_completion_status("Go Live")
                    # Removed org from here as there should only be one set per event
                    PaymentInfo.find_by(event_id: event.id)
                  else
                    current_user.organisation.payment_info
                  end

    if paymentInfo
      if (paymentInfo.event_id.blank? && !event.id) || (paymentInfo.event_id && paymentInfo.event_id == event.id)
        # Updating the existing one (so if org level) || (if event level)
        res = paymentInfo.update(stripe_enabled: options[:stripe], bacs_enabled: options[:bacs], cheque_enabled: options[:cheque], cheque_payable_name: options[:cheque_payable_name], cheque_payable_address: options[:cheque_payable_address], bacs_instructions: options[:bacs_instructions], event_id: event.id)
      else
        # Create a new one if we have one
        res = PaymentInfo.create(organisation_id: current_user.organisation.id, stripe_enabled: options[:stripe], bacs_enabled: options[:bacs], cheque_enabled: options[:cheque], cheque_payable_name: options[:cheque_payable_name], cheque_payable_address: options[:cheque_payable_address], bacs_instructions: options[:bacs_instructions], event_id: event.id)
      end
    else
      res = PaymentInfo.create(organisation_id: current_user.organisation.id, stripe_enabled: options[:stripe], bacs_enabled: options[:bacs], cheque_enabled: options[:cheque], cheque_payable_name: options[:cheque_payable_name], cheque_payable_address: options[:cheque_payable_address], bacs_instructions: options[:bacs_instructions], event_id: event.id)
    end

    if res
      render json: {status: 200}
    else
      render json: {status: 400, error: 'Payment Info Not Saved'}
    end
  end

  def payment_info
    @payment_info =
        if params[:event_id]
          event = Event.find_by_id(params[:event_id])
          event.update_completion_status("Setup Payments")
          @stripe_details = event.organisation.stripe_account
          authorize! :read, event
          event.payment_info || event.organisation.payment_info
        else
          @stripe_details = current_user.organisation.stripe_account
          current_user.organisation.payment_info
        end
  end

  def payment_info_org
    @stripe_details = current_user.organisation.stripe_account
    @payment_info = current_user.organisation.payment_info
  end

  def delegate_payment_details
    # FIXME only retrieve events which are payable
    @events = current_user.organisation.events.order(datetimefrom: :desc)
  end

  def delegate_event_payments
    @event = Event.includes(:event_bookings).find_by_id(params[:id])

    @event_bookings = @event.event_bookings.where('payment_status != ?', 7).
    where('(booking_count = 1 OR ( booking_count = 0 and cancelled_at IS NOT NULL)) and (payment_type is not null or payment_status = 9 or free_booking is true)')

    if params[:payment_filter] && params[:payment_filter] != 'null'
      if params[:payment_filter] == 'free_booking'
        @event_bookings = @event_bookings.where(free_booking: true)
      else
        @event_bookings = @event_bookings.where(payment_status: EventBooking.payment_statuses[params[:payment_filter]])
      end
    end

    @eb_count = @event_bookings.count

    @event_bookings = @event_bookings.order(booking_date: :desc)

    @event_bookings = @event_bookings.page(params[:page]).per(20)

    authorize! :view, @event
    @payment_info = @event.payment_info || @event.organisation.payment_info
  end

  def delegate_event_booking_payments
    @reg_user = RegisteredUser.find_by_id(params[:id])
    @event = @reg_user.event

    event_booking = @reg_user.event_booking

    @event_bookings = []
    @event_bookings << event_booking

    @payment_info = @event.payment_info || @event.organisation.payment_info
    authorize! :view, @event
  end

  def payment_info_for_event
    event = Event.find_by_uuid(params[:id])
    org = event.organisation
    @stripe_details = org.stripe_account
    @payment_info = event.payment_info || org.payment_info
  end
end
