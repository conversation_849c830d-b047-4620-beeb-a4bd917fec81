class PackagesController < ApplicationController

  before_action :login_required

  # TODO not needed as we don't update tickets excpet via the popup
  before_action :check_changes_allowed, except: [:view_package_bookings_summary, :update, :event_packages]

  def update
    @package = Package.find_by_id(params[:id])

    unless @package
      render json: {errors: ["Could not find package"]}
      return
    end

    unless @package.update(package_options_params)
      render json: {errors: @package.errors.values}, status: :unprocessable_entity
    end

    # Else returns using jbuilder
  end

  def view_package_bookings_summary
    @package = Package.find_by_id(params[:package_id])
  end

  def event_packages
    # TODO security
    # Because of routes package id is actually the event_id
    if params[:package_id]
      @packages = Package.where(event_id: params[:package_id])
    else
      render json: {errors: "No tickets found"}, status: :unprocessable_entity
    end

  end

  private

  def package_options_params
    params.require(:package).permit(:id, :details, :ticket_no, :max_allowed)
  end

  def check_changes_allowed
    @package = Package.find_by_id(params[:id])

    event = @package.event
    if event.event_bookings && (!event.event_bookings.fully_booked.empty? && event.complete && event.live == true)
      unless current_user.is_an_administrator?
        render json: {errors: ["Cannot make changes to live events tickets"]}, status: :unprocessable_entity
        return
      end
    end
  end
end
