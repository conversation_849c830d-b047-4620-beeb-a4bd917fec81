class VatController < ApplicationController

  before_action :login_required

  def index
    vat = if params[:oid]
          VatNumber.find_by(organisation_id: params[:oid])
        else
          VatNumber.find_by(organisation_id: current_user.organisation.id)
        end

    if vat
      authorize! :manage, vat
      render json: vat.to_json
    else
      render json: {status: 200, vat: nil}
    end
  end

  def create
    vat_number = params[:vat_number]
    company_number = params[:company_number]
    company_name = params[:company_name]
    company_address = params[:company_address]

    vat_details = VatNumber.find_or_create_by(organisation_id: current_user.organisation.id) do |vat|
      vat.vat_number = vat_number
      vat.company_number = company_number
      vat.company_name = company_name
      vat.company_address = company_address
    end
    vat_details.update(vat_number: vat_number, company_number: company_number, company_name: company_name, company_address: company_address) if vat_details

    if vat_details.errors.blank?
      render json: {success: 200}, status: 200
    else
      render json: {errors: vat_details.errors.full_messages}, status: 400
    end
  end

  def destroy
    @vat = VatNumber.find_by(vat_number: params[:id])
    if @vat
      events = current_user.organisation.events.is_live.where(vat_exclusive: true)
      if events && events.size > 0
        render json: {errors: "Vat details could not be deleted, as you have vatable events!"}, status: 400
      else
        if @vat.destroy
          render json: {status: 200}
        else
          render json: {errors: "Vat details could not be deleted!"}, status: 400
        end
      end
    else
      render json: {errors: "Vat details could not be found!"}, status: 400
    end
  end

end
