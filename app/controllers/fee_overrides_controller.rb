class FeeOverridesController < ApplicationController

  before_action :login_required
  before_action :admin_only

  def index
    overrides = FeeOverride.joins(:organisation).all
    render json: overrides.to_json(:include => :organisation).html_safe
  end

  def create
    override_params = params[:fee_override].permit(:organisation_id, :event_id, :hg_fees, :hg_fees_add, :hg_fee_cap)

    override = FeeOverride.find_or_initialize_by(:id => params[:id])

    if override.update(override_params)
      overrides = FeeOverride.joins(:organisation).all
      render json: overrides.to_json(:include => :organisation).html_safe
    else
      render json: {messages: override.errors.full_messages}, status: 400
    end
  end

  def destroy
    override = FeeOverride.find_by_id(params[:id])
    if override.destroy
      render json: {status: 200}
    else
      render json: {error: 'Could not delete'}, status: 400
    end
  end

end
