class KnowledgeHubArticlesController < ApplicationController
  before_action :login_required, except: [:show]
  before_action :get_current_user
  before_action :set_knowledge_hub_article, only: [:show, :edit, :update, :destroy]

  respond_to :html


  def get_current_user
    @user = nil
    if session[:user_id]
      @user = User.where("id = ?", session[:user_id]).select("id, name, email, role").first
    end
  end

  def index
    if @user.role == "ADMIN"
      @knowledge_hub_articles = KnowledgeHubArticle.all
    else
      @knowledge_hub_articles = KnowledgeHubArticle.where("user_id = ?",@user.id)
    end
    respond_with(@knowledge_hub_articles)
  end

  def show
    return respond_with(@knowledge_hub_article)
    if @knowledge_hub_article.user_id == @user.id || @user.role == "ADMIN"
      respond_with(@knowledge_hub_article)
    else
      redirect_to knowledge_hub_article_path, alert: "You are not allowed to view the selected article"
    end
  end

  def new
    @knowledge_hub_article = KnowledgeHubArticle.new
    respond_with(@knowledge_hub_article)
  end

  def edit
    @files = get_files
    @publish_status = @knowledge_hub_article.published
  end

  def create
    @knowledge_hub_article = KnowledgeHubArticle.new(knowledge_hub_article_params)
    @knowledge_hub_article.user_id = @user.id
    @knowledge_hub_article.body = "<p></p>"
    @knowledge_hub_article.save
    render action: :edit 
  end

  def update
    if @knowledge_hub_article.user_id == @user.id || @user.role == "ADMIN"
      @knowledge_hub_article.update(knowledge_hub_article_params)
      if params[:knowledge_hub_article][:delete_file].present?
        params[:knowledge_hub_article][:delete_file].each do |filename|
          delete_file filename
        end
      end
      respond_with(@knowledge_hub_article)
    else
      redirect_to knowledge_hub_articles_path, alert: "You are not allowed to update the selected article"
    end
  end

  def destroy
    if @knowledge_hub_article.user_id == @user.id || @user.role == "ADMIN"
      @knowledge_hub_article.destroy
      objects = get_bucket_objects
      objects.delete
      redirect_to action: "index" and return
    else
      redirect_to knowledge_hub_articles_path, alert: "You are not allowed to delete the selected article"
    end
  end

  def upload_article_image
    new_filename = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/knowledgehub/" + save_knowledge_hub_image!
    s3 = Aws::S3::Resource.new
    bucket = s3.bucket(ENV.fetch('APP_IMAGE_BUCKET'))
    prefix = "knowledgehub/article #{params[:article_id]}/"
    files = bucket.objects( prefix: prefix).collect(&:key).map { |file| [ file[13..-1],"https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/#{file}" ]  }.to_h
    render json: {status: 200, filename: new_filename, files: files}
  end

  def remove_file
    uploader = KnowledgeHubUploader.new
    uploader.retrieve_from_store!(params[:filename])
    uploader.remove!
    s3 = Aws::S3::Resource.new
    bucket = s3.bucket(ENV.fetch('APP_IMAGE_BUCKET'))
    prefix = "knowledgehub/article #{params[:article_id]}/"
    files = bucket.objects( prefix: prefix).collect(&:key)
    render json: {status: 200, files: files}
  end

  private
  def save_knowledge_hub_image!()
    article = KnowledgeHubArticle.find(params[:article_id])
    # We only allow this when the article has already been saved:
    if article
      # Now we'll upload the new file...
      uploader = KnowledgeHubUploader.new
      # We pass the model id through to the uploader via a prop to store to the article's own folder:
      uploader.instance_variable_set("@new_file_name", "article #{params[:article_id]}")
      # ... And store the file.
      uploader.store!(params[:file])

      return uploader.filename
    end
  end


  private
    def set_knowledge_hub_article
      @knowledge_hub_article = KnowledgeHubArticle.find(params[:id])
    end

    def knowledge_hub_article_params
      params.require(:knowledge_hub_article).permit(:title, :date, :body, :image, :icon, :meta_description, :user_id, :published, :delete_file)
    end

    def get_bucket
      s3 = Aws::S3::Resource.new
      s3.bucket(ENV.fetch('APP_IMAGE_BUCKET'))
    end

    def get_bucket_objects
      bucket = get_bucket
      prefix = "knowledgehub/article #{@knowledge_hub_article.id}/"
      bucket.objects( prefix: prefix)
    end

    def get_files
      objects = get_bucket_objects
      objects.collect(&:key).map { |file| [ file[13..-1],"https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/#{file}" ]  }.to_h.select {|key, value| key != @knowledge_hub_article.image}
    end

    def delete_file(filename)
      bucket = get_bucket
      prefix = "knowledgehub/#{filename}"
      bucket.objects( prefix: prefix).delete
    end
end

