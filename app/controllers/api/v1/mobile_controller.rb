# TODO this could be events controller or whatever

class Api::V1::MobileController < Api::V1::ApiController

  def get_events
    @events = @current_user.organisation.events.proper.bookable.is_live.select(:id, :title, :image1, :image2, :datetimefrom, :datetimeto)

    unless @events
      render json: {message: "No Events Found"}, status: :not_found
    end
    #   Jbuilder returns the events
  end

  def get_attendees
    @event_bookings = EventBooking.where(:event_id => params[:id])

    unless @event_bookings
      render json: {message: "No Events Found"}, status: :not_found
    end
  end

  def mark_attendee_present
    # TODO should have security check for access to these reg_users
    event_attendee = RegisteredUser.find_by_uuid(params[:uuid])
    if event_attendee && event_attendee.update_attribute(:attending_event, DateTime.now)
      render json: {message: "Success"}, status: :ok
    else
      render json: {message: "Could not update attendee"}, status: 400
    end
  end
end
