class Api::V1::WidgetEventsController < Api::V1::ApiController

  skip_before_action :authenticate_request!

  def public_events
    begin
      find_api_user
      @events = @api_user.events.is_public.bookable.is_live

      if params[:location]
        origin = EventAddress.origin_of_location(params[:location])
        @events = @events.filter_by_location(origin) if origin != false
      else
        @events = @events.order('datetimefrom ASC')
      end

      @events = @events.filter_by_type(params[:type]) if params[:type] && params[:type] != "All"
      @events = @events.filter_by_title(params[:title]) if params[:title]
      @events = @events.filter_by_tag(params[:tag]) if params[:tag]
      @events = @events.filter_by_date_on(params[:date_on]) if params[:date_on]
      @events = Event.filter_by_free(@events) if params[:cost] == "free"
      @events = Event.filter_by_paid_for(@events) if params[:cost] == "paid"
      @events_count = @events.count
      @bgd_filter_colour = @api_user.brand_colour.present? ? @api_user.try(:brand_colour).try(:hex_code) : '#ff9500'

    rescue AbstractController::ActionNotFound, ActionController::ParameterMissing, WidgetAuthError => e
      Rollbar.error(e, 'Unauthorised widget access attempt')
      render json: { error: 'Unauthorised access' }, status: :unauthorized
    end
  end

  def event_type_selector_options
    begin
      find_api_user
      @selector = EventType.all.order(:name).pluck(:name)
      render :json => @selector.to_json
    rescue AbstractController::ActionNotFound, ActionController::ParameterMissing, WidgetAuthError => e
      Rollbar.error(e, 'Unauthorised widget access attempt')
      render json: { error: 'Unauthorised access' }, status: :unauthorized
    end
  end

  private

  def find_api_user
    token = request.headers['Authorization'].split(' ')[1] if request.headers['Authorization'].present?
    decoded_token = WidgetAuthService.decode(token) if token.present?
    type = decoded_token.present? && decoded_token['type'].present? ? decoded_token['type'] : "HgOrganisation"
    @api_user = Organisation.where("parent_id = ? and parent_type = ?", decoded_token['sub'], type).first if decoded_token.present?
    access_token = @api_user.api_tokens.where("token = ?", token).first if @api_user.present?
    raise WidgetAuthError.new("Invalid access token!") if token.blank? || access_token.blank? || access_token.revoked?
  end

end