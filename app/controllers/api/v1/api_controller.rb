require 'json_web_token'

class Api::V1::ApiController < ActionController::API

  before_action :authenticate_request!, except: :login


  def login
    auth = {username: 'hg-user', password: '#h05p1ta!!ity#'}

    if Rails.env == 'staging'
      result = HTTParty.post(ENV["CUSTOM_PROVIDER_URL"] + '/users/sign_in.json',
                             :body => {:user => {:email => params[:email], :password => params[:password]},
                                       :ev_token => ENV['APP_ID']}.to_json, :headers => {'Content-Type' => 'application/json'}, basic_auth: auth)
    else
      result = HTTParty.post(ENV["CUSTOM_PROVIDER_URL"] + '/users/sign_in.json',
                             :body => {:user => {:email => params[:email], :password => params[:password]},
                                       :ev_token => ENV['APP_ID']}.to_json, :headers => {'Content-Type' => 'application/json'})
    end

    if result.success?
      user = User.find_by_id(result['uid'])

      if user
        auth_token = JsonWebToken.encode({user_id: user.id})
        render json: {auth_token: auth_token}, status: :ok
      else
        render json: {message: "Login Failed"}, status: 400
      end

    else
      render json: {message: "Login Failed"}, status: 400
    end
  end


  protected

  # Validates the token and user and sets the @current_user scope
  def authenticate_request!
    if !payload || !JsonWebToken.valid_payload(payload.first)
      return invalid_authentication
    end

    load_current_user!
    invalid_authentication unless @current_user
  end

  # Returns 401 response. To handle malformed / invalid requests.
  def invalid_authentication
    render json: {error: 'Invalid Request'}, status: :unauthorized
  end

  private
  # Deconstructs the Authorization header and decodes the JWT token.
  def payload
    auth_header = request.headers['Authorization']
    token = auth_header.split(' ').last
    JsonWebToken.decode(token)
  rescue
    nil
  end

  # Sets the @current_user with the user_id from payload
  def load_current_user!
    @current_user = User.find_by(id: payload[0]['user_id'])
  end

end
