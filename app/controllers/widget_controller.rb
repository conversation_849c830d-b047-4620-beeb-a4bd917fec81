class WidgetController < ActionController::Base
    content_security_policy false, only: :index
    before_action :find_api_user, only: [:index]
    after_action :allow_iframe, only: :index

    layout 'widget-layout'
    
    def index
        @colour = @api_org.brand_colour.present? ? @api_org.try(:brand_colour).try(:hex_code) : '#ff9500'
    end

    def allow_iframe
        response.headers.except! 'X-Frame-Options'
    end

    def find_api_user
        token = params[:token]
        decoded_token = WidgetAuthService.decode(token) if token.present?
        type = decoded_token.present? && decoded_token['type'].present? ? decoded_token['type'] : "HgOrganisation"

        @api_org = Organisation.where("parent_id = ? and parent_type = ?", decoded_token['sub'], type).first if decoded_token.present?
        access_token = @api_org.api_tokens.where("token = ?", token).first if @api_org.present?
        raise WidgetAuthError.new("Invalid access token!") if token.blank? || access_token.blank? || access_token.revoked?
    end
end