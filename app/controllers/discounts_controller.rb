require 'json_web_token'

class DiscountsController < ApplicationController
  before_action :login_required, except: :check_valid
  before_action :admin_only, only: [:get_one_off_codes, :typeahead_organisations]

  def create
    codes = DiscountCode.create(discount_params)
    if codes && codes.errors.size == 0
      render :json => { :id => codes.id, :code => codes.code, :description => codes.description, :amount => codes.amount, :organisation => codes&.organisation&.name }, :status => 200
    else
      render json: {errors: codes.errors.full_messages}, status: 400
    end
  end

  def index
    # Gets all codes for an org irrespective but not for event based ones
    @codes = DiscountCode.where("(one_off_code IS NULL OR one_off_code = false) AND event_id IS NULL AND organisation_id = ?", current_user.organisation.id)
    @events = current_user.organisation.events.proper.bookable.select('id', 'title as name', 'datetimeto', 'close_date')
  end

  def show
    event = Event.find_by_id(params[:id])
    authorize! :manage, event

    # Only active as dialog doesn't have the checkbox to change these
    codes = event.discount_codes.where(inactive: [false, nil])

    if codes
      render json: codes.to_json(:include => :package)
    else
      render json: {}, status: 400
    end
  end

  def update
    discount = DiscountCode.find_by_id(params[:id])
    if discount.update(discount_params)
      render json: discount
    else
      render json: {}, status: 400
    end
  end

  def destroy
    dc = DiscountCode.find_by_id(params[:id])

    if dc && dc.destroy
      render json: {}
    else
      render json: {}, status: 400
    end
  end

  def check_valid
    # Not for one off codes
    event = Event.find_by_id(params[:id])
    organisation = event.organisation

    d_code = params[:code]
    d_code = d_code.upcase

    codes = event.discount_codes.where("code = ? AND (inactive = false or inactive IS NULL) AND (one_off_code IS NULL OR one_off_code = false)", d_code)

    if codes.count == 0
      codes = organisation.discount_codes.where("code = ? AND (inactive = false or inactive IS NULL) AND (one_off_code IS NULL OR one_off_code = false)", d_code)
    end

    code = codes.find_by_code(d_code)

    if code && code.dates_valid? && code.discount_valid?

      unless checkValidEmailForFreeCode(code)
        render json: {errors: ["Discount Code Not Valid"]}, status: 400
        return
      end

      if code.free_code && code.package_id.blank?
        fees = get_fees_for_free(event)
      else
        # TODO if ticket codes, need to have adjustments for all codes added, maybe bundle and do at once?
        if code.package_id
          unless checkValidMaxTicketsSelected(code)
            render json: {errors: ["You can only book max amount of tickets (#{code.max_tickets_per_discount}) with this discount code.Click the booking link again, purchase the correct amount of tickets for the code to apply, then make another booking for your extra tickets."]}, status: 400
            return
          end

          existing_ticket_discounts = []
          if params[:ticketCodes]
            existing_ticket_discounts = params[:ticketCodes]
          end

          existing_ticket_discounts.push(code.id)

          fees = get_fees_after_ticket_discounts(existing_ticket_discounts)
        else
          fees = get_fees_after_discount(code.id)
        end
      end
      enc_fees = JsonWebToken.encodeBase64(fees)
      render json: {code: {id: code.id, value: code.amount, ticket_id: code.package_id, discount_type: code.discount_type}, fees: fees, booking_token: enc_fees, free_booking: fees[:free_booking]}
    else
      render json: {errors: ["Discount Code Not Valid"]}, status: 400
    end
  end

  def get_one_off_codes
    @codes = DiscountCode.where(one_off_code: true)
  end

  def check_one_off_valid
    event = Event.find_by_id(params[:id])
    organisation = event.organisation

    count_prev = organisation.events.proper.where(one_off_discount_code: params[:code]).size

    codes = DiscountCode.where("one_off_code = true AND code = ? AND (one_off_code_org_id = ? OR one_off_code_org_id IS NULL)", params[:code], event.organisation_id) if count_prev == 0

    # Gets overrides per event, then org
    fee_overrides = if event.fee_overrides.blank?
                      event.organisation.fee_overrides
                    else
                      event.fee_overrides
                    end

    if !fee_overrides.present? && count_prev == 0 && (codes && codes.size > 0)
      code = codes.first
      event.update_columns(one_off_discount_code: code.code, one_off_discount_percentage: code.amount)
      render json: {valid: true, discount_code: code.code, discount_percentage: code.amount}
    else
      render json: {}, status: 400
    end
  end

  def typeahead_organisations
    organisations = Organisation.where("name ILIKE ?", "%#{params[:query]}%").order(:name).limit(10).select(:id, :name)
    render :json => organisations.to_json
  end

  def apply_discount
    discount = DiscountCode.where(event_id: params[:event_booking][:event][:id], code: params[:code].upcase).first

    event_booking = EventBooking.find(params[:event_booking][:id])
    package_booking = event_booking.package_bookings.where(package_id: discount.package_id).first if discount.event_or_ticket == true

    if discount.nil?
      render json: {error: 'Discount code invalid'}, status: 400 and return
    elsif discount.inactive
      render json: {error: 'Discount code inactive'}, status: 400 and return
    elsif (discount.events_only? && discount.max_uses.present? && (discount.max_uses <= discount.use_counter))
      render json: {error: 'Discount code has no more uses'}, status: 400 and return
    elsif discount.free_code && !discount.email_list.split(',').include?(params[:event_booking][:user_email])
      render json: {error: 'You do not have access to this code'}, status: 400 and return
    elsif discount.tickets_only? == true && package_booking.nil?
      render json: {error: 'This code is not valid for this ticket'}, status: 400 and return
    end

    event_booking_discount = {}
    package_booking_discount = {}
    
    if discount.tickets_only? == true  # if ticket discount, nice column name Eamon jeez

      if event_booking.discount_code_id
        render json: {error: 'You cannot add ticket discount codes as you already have an event discount applied'}, status: 400 and return
      end

      ticket = Package.find(discount.package_id) #filter by the one we want and amend
      new_cost = if event_booking.early_bird_valid?
                    ticket.cost_a
                  else
                    ticket.cost_b
                  end
      if discount.discount_type == 'percentage'
       new_cost = if event_booking.early_bird_valid?
                  ticket.cost_a - ((ticket.cost_a.to_f / 100) * discount.amount)
                else
                  new_cost = ticket.cost_b - ((ticket.cost_b.to_f / 100) * discount.amount)
                end
        package_booking.update(discount_type: PackageBooking.discount_types[:percentage])
      elsif discount.discount_type == 'numeric'
        new_cost = if event_booking.early_bird_valid?
                    ticket.cost_a.to_f - discount.amount
                  else
                    ticket.cost_b.to_f - discount.amount
                  end
        package_booking.update(discount_type: PackageBooking.discount_types[:numeric])
      else
        render json: {error: 'Error with discount code'}, status: 400 and return
      end
      package_booking.update(discount_amount: discount.amount)
      package_booking.update(discount_code_id: discount.id)

      package_booking_discount[:id] = package_booking.id
      package_booking_discount[:discount_type] = package_booking.discount_type
      package_booking_discount[:discount_amount] = package_booking.discount_amount
      package_booking_discount[:discount_code_id] = package_booking.discount_code_id
      
    else # if event discount
      # events codes only ever have percentage discounts
      if event_booking.package_bookings.where("discount_code_id IS NOT NULL").size > 0
        render json: {error: 'You cannot add event discount codes as you already have ticket discounts applied'}, status: 400 and return
      end

      event_booking.update(discount_code_id: discount.id)
      event_booking.update(discount_percentage: discount.amount)

      event_booking_discount[:discount_percentage] = event_booking.discount_percentage
      event_booking_discount[:discount_code_id] = event_booking.discount_code_id

      new_cost = discount.amount
    end
    render json: {discount_details: {ticket: ticket, discount: discount, new_cost: new_cost, package_booking_discount: package_booking_discount, event_booking_discount: event_booking_discount, early_bird: event_booking.early_bird_valid?}}

  end

  private

  def checkValidEmailForFreeCode(code)
    if code.free_code && code.email_list.present?
      return code.email_list.downcase.include?(params[:bookerEmail].downcase)
    else
      return true
    end
  end

  def checkValidMaxTicketsSelected(code)
    ticket = Package.find{code.package_id}
    return code.max_tickets_per_discount >= ticket[:quantity_tickets]
  end

  def get_fees_after_discount(discount_code_id)
    EventBooking.get_payment_fees(params[:id], params[:packages], discount_code_id)
  end

  def get_fees_after_ticket_discounts(existing_ticket_discount_codes)
    existing_ticket_discounts = DiscountCode.where(id: existing_ticket_discount_codes)
    EventBooking.get_payment_fees(params[:id], params[:packages], nil, existing_ticket_discounts)
  end

  def get_fees_for_free(event)
     {
      standard: {
          event_amount: 0,
          total_fees: 0,
          stripe_fees: 0
      },
      event_fees: 0,
      vat_amount: 0,
      vat_included: !event.vat_exclusive?,
      discount_diff: 0,
      non_stripe_fees_for_inc: 0,
      free_booking: true
    }
  end

  def discount_params
    org_id = nil
    if params[:discount][:event_id]
      event = Event.find_by_id(params[:discount][:event_id])
      org_id = event.organisation_id
    elsif  params[:discount][:org_name]
      org_id = Organisation.where("name = ?", params[:discount][:org_name]).first&.id 
    else
      org_id = current_user.organisation.id
    end
    params.require(:discount)
        .permit(:id, :event_id, :package_id, :free_code, :event_or_ticket, :description, :code, :end_date, :inactive, :amount, :discount_type, :start_date, :max_uses, :one_off_code_org_id, :one_off_code, :email_list, :max_tickets_per_discount)
        .merge(organisation_id: org_id)
  end
end
