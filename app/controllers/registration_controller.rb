class RegistrationController < ApplicationController

  layout 'home'

  # before_action :permit_confirmation, only: :confirmation

  def index
  end

  def confirmation
    user_id = getUserFromToken(params[:token])

    if user_id
      @user = User.find_by_id(user_id)
      if @user
        email_welcome
        send_verification_email
        EventStopUser.create(user_id: @user.id)
        session[:user_id] = @user.id
        flash.now[:success] = "You have successfully signed up! You will receive an email with login instructions in due course."

        if @user.is_an_administrator?
          redirect_to "/dashboard"
        else
          redirect_to "/events"
        end
      else
        redirect_to "/registration/login"
      end
    else
      redirect_to "/registration/login"
    end

  end

  def login
    if params[:login_failed]
      flash.now[:error] = 'Login failed - Please try again!'
      return
    end
    if params[:acc_locked]
      flash.now[:error] = 'For security reasons this account has now been locked. To re-open your account please call Hospitality Guaranteed on 0344 822 3227.'
      return
    end
  end

  private

  def getUserFromToken(token)
    token_not_base = Base64.urlsafe_decode64(token)

    crypt = ActiveSupport::MessageEncryptor.new(ENV['APP_ID'].truncate(32))

    decrypted_string = crypt.decrypt_and_verify(token_not_base)

    token_hash = Marshal.load(decrypted_string)

    if token_hash[:expires] > DateTime.now
      return token_hash[:user_id]
    else
      return false
    end

  rescue
    return false
  end

  # def permit_confirmation
  #   refererPath = URI(request.referer).path
  #   unless refererPath == '/registration/index' || refererPath == '/registration/login'
  #     redirect_to root_path
  #   end
  # end

  def email_welcome
    user_mailer = UserMailer.welcome(@user)
    create_new_user_notification

    if Rails.env.development?
      user_mailer.deliver_now
    else
      user_mailer.deliver_later
    end
  end

  def send_verification_email
    user_mailer = UserMailer.verify(@user)
    if Rails.env.development?
      user_mailer.deliver_now
    else
      user_mailer.deliver_later
    end
  end

  def create_new_user_notification
    @user.organisation.notifications.create(email: @user.email, details: "New User Registered")
  end
end
