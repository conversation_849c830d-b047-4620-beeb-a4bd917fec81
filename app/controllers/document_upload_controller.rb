class DocumentUploadController < ApplicationController

  def upload_file
    # TODO do cancan for event
   file = save_file!
   render json: {status: 200, id: file.id}
  end

 def remove_file
   email_type = params[:email_type]
   uploader = DocumentUploader.new
   uploader.event_id = params[:eventid]
   uploader.user = current_user
   uploader.type = email_type
   uploader.retrieve_from_store!(params[:filename])
   uploader.remove!
   fu = FileUpload.find_by_id(params[:file_id])
   if fu && fu.destroy
     render json: {status: 200}
    else
     render json: {error: "File not deleted"}, status: 400
   end
 end

 def update
   if @upload.update(post_upload_params)
     redirect_to @upload, notice: 'Upload attachment was successfully updated.'
   else
     render :edit
   end
 end

 private

  def save_file!()
    user = current_user
    email_type = params[:email_type]
    uploader = DocumentUploader.new
    uploader.event_id = params[:event_id]
    uploader.user = user
    uploader.type = email_type
    uploader.store!(params[:file])
    event = Event.find_by_id(params[:event_id])
    file = FileUpload.find_by_id(params[:file_id])

    clean_filename = uploader.filename

    if file.blank?
      file = FileUpload.create!(name: clean_filename, fileable: event, email_type: email_type)
    else
      file.update(name: clean_filename)
    end
    file
  end

end
