class OrgEventsController < ApplicationController

  layout 'home'

  before_action :load_organisation

	def index
		
	end	

	def events
		@org = Organisation.find(params[:id])

		@events = @org.events.is_public.bookable.is_live

    if params[:location]
      origin = EventAddress.origin_of_location(params[:location])
      @events = @events.filter_by_location(origin) if origin != false
    else
      @events = @events.order('datetimefrom ASC')
    end

    @events = @events.filter_by_type(params[:type]) if params[:type]
    @events = @events.filter_by_title(params[:title]) if params[:title]
    @events = @events.filter_by_tag(params[:tag]) if params[:tag]
    @events = Event.filter_by_free(@events) if params[:cost] == "free"
    @events = Event.filter_by_paid_for(@events) if params[:cost] == "paid"
    @events = @events.filter_by_date_on(params[:date_on]) if params[:date_on]
    @events_count = @events.count
	end

  private

  def load_organisation
    @org = Organisation.find(params[:id])
  end  


end