require 'json_web_token'

class EventBookingsController < ApplicationController

   skip_before_action :authenticate_user!, only: [:booking_unavailable, :virtual_booking, :get_all_org_tags,
                 :accept_invite, :invite, :update, :create, :accept_public_invite, :decline, :embed_booking, :event_fees,
                 :cancel, :get_all_events, :setup_pay_card, :create_payment_intent, :get_payment_intent,
                 :update_payment_intent, :cancel_payment_intent, :complete_booking, :show]#, :finish_booking]

  layout 'app_new'

  require 'securerandom'

  before_action :get_event_booking, only: [:view_summary, :invoice]

  respond_to :html, only: [:index, :show]
  respond_to :json

  def show
    # Check if we're looking for an event_booking or an event
    if params[:id].to_s.match?(/^\d+$/) && EventBooking.exists?(params[:id])
      # This is an event_booking ID
      @event_booking = EventBooking.find(params[:id])
      @event = @event_booking.event

      respond_to do |format|
        format.html { render :show }
        format.json { render json: booking_as_json(@event_booking, true) }
      end
    else
      # This is an event ID (original behavior)
      @event = Event.find_by_id(params[:id])

      if @event.image2
        @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image2}"
      elsif @event.image1
        @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
      else
        @logo_url = "https://hgevents-images.s3-eu-west-1.amazonaws.com/event-stop.jpg"
      end

      respond_to do |format|
        format.html
        format.json # This will automatically render show.json.jbuilder
      end
    end
  end

  def get_all_events
    #only used on org-events.vue
    if params[:event_id]
      @org_events = Event.where("organisation_id = ? AND is_public is true AND complete is true AND live is true AND datetimeto > ? AND (close_date > ? OR close_date IS NULL) AND id != ?", params[:id], DateTime.now, DateTime.now, params[:event_id]).order('datetimefrom ASC').limit(3)
      if params[:tags]
        @org_events = @org_events.where('tags && ARRAY[?]::varchar[]', params[:tags])
      end
    else
      get_events
    end
    respond_to do |format|
      format.json {}
    end
  end

  def get_all_org_tags
    @org_live_events = Event.where("organisation_id = ? AND is_public is true AND complete is true AND live is true AND datetimeto > ? AND (close_date > ? OR close_date IS NULL) AND id != ?", params[:id], DateTime.now, DateTime.now, params[:event_id])
    @tags = @org_live_events.map { |e| e.tags }.flatten.uniq
    respond_to do |format|
      format.json {}
    end
  end

  def create
    #payment_status = {payment_passed: false}
    book_params = booking_params #Prevents booking_params method from being called multiple times
    EventBooking.transaction do
      @event_booking = EventBooking.create(book_params)
      # if @event_booking.errors.blank?
      #   EventBookingCleanUpJob.set(wait_until: 12.minutes.from_now).perform_later(@event_booking.id) if @event_booking.event.chargeable && book_params[:free_booking] == false
      # end
      #return unless payment_actions
      # Rollbar.info("Before free booking", param_free_booking: booking_params[:free_booking])
      # @event_booking = @event_booking.reload
      if book_params[:free_booking] == true
        # Rollbar.info("Inside free booking", param_free_booking: booking_params[:free_booking], event_booking_id: @event_booking.id)
        send_confirmation(@event_booking.id)
        send_notification(@event_booking)
      end
    end
    render json: booking_as_json(@event_booking, false)
    #finalize_booking
  end

  def update
    # $$$$$$$$$$$$$$$$$$$ For invites ££££££££££££££££££££££££
    #for update e.g. attendees too
    payment_status = { payment_passed: false }

    book_params = booking_params #Prevents booking_params method from being called multiple times

    @event_booking = EventBooking.find_by(uuid: params[:id])
    if @event_booking
      EventBooking.transaction do
        booking_parameters = if @event_booking.payment_type.blank?
          book_params
        else
          book_params.except(:package_bookings_attributes)
        end
        # if @event_booking.update(booking_params.except(:package_bookings_attributes))
        if @event_booking.update(booking_parameters)
          # if params[:completing] == true
          #   EventBookingCleanUpJob.set(wait_until: 12.minutes.from_now).perform_later(@event_booking.id) if @event_booking.event.chargeable && book_params[:free_booking] == false
          # end
          # If a booking is done again if previously cancelled, it should be marked as not cancelled
          @event_booking.update_columns(cancelled_at: nil, cancelled_by: nil)
          @event_booking.reload

          @payment_status = {:payment_passed => true}
        else
          render json: { errors: @event_booking.errors.full_messages }, status: 400
          return
        end
      end
    end
    if @event_booking.payment_type.blank?
      render json: booking_as_json(@event_booking, true)
    else
      finalize_booking
      return
    end
  end

  def setup_pay_card
    @event_booking = EventBooking.where(uuid: params[:id]).first
    render json: { errors: ['failed - booking timed out'] }, status: 400 and return if @event_booking.blank?
    @event_booking.assign_attributes(booking_params)
    @event_booking.payment_type = params[:booking][:payment_type]
    @event_booking.save
    render json: { errors: ['failed - could not setup payment method'] }, status: 400 and return unless payment_actions
    render json: @payment_status
  end

  # # TODO does this have to go, after webhook implementation as well as finalize_booking
  # def finish_booking
  #   @event_booking = EventBooking.find_by_uuid(params[:id])
  #   completed = StripePayment.stripe_complete_payment(@event_booking, params[:payment_intent_id])

  #   if completed
  #     @payment_status = { payment_passed: true }
  #     finalize_booking
  #   end
  # end

  # new system after webhook implementation
  def complete_booking
    @event_booking = EventBooking.find(params[:id])
    send_confirmation(@event_booking.id)
    send_notification(@event_booking)
    @event_booking.package_bookings.each do |pb|
      dc = pb.discount_code
      dc.increment_use_count if dc
    end
    render json: { success: 'booking completed' }, status: 200
  end

  def cancel
    event_booking = EventBooking.find_by(uuid: params[:id])
    if event_booking && event_booking.cancel_booking(get_cancelled_by(event_booking))
      render json: { success: 'cancelled' }, status: 200
    else
      render json: { status: 'failed', cancellation: 'failed' }, status: 400
    end
  end

  def accept_invite
    @editbyorg = false
    if params[:mode] && params[:mode] = 'edit'
      @editbyorg = true
    end

    token = params[:token]

    @event_booking = EventBooking.find_by(uuid: params[:token])

    # if @event_booking && @event_booking.booking_count > 0 && @event_booking.payment_type.blank? && @event_booking.payment_status == 'unpaid' && !@event_booking.free_booking?
    #   @event_booking.package_bookings.destroy_all if @event_booking.package_bookings.any?
    #   @event_booking.update_columns(:booking_count => 0, :booking_date => nil, :booking_amendment => nil)
    #   @event_booking.reload
    # end

    @event = @event_booking.event if @event_booking

    # If payment failed on previous booking, or was cancelled, create new event booking
    # if @event_booking && (@event_booking.payment_error.present? || @event_booking.cancelled_at.present?)
    #   registered_user = @event_booking.registered_user
    #   token = SecureRandom.uuid
    #   @event_booking = registered_user.build_event_booking(event_id: @event.id, uuid: token)
    #   @event_booking.save
    # end

    # If the event has a child it has been overridden and must book the child
    # if @event && @event.child_id.present?
    #   @event = @event.child
    #   if @event_booking
    #     registered_user = @event_booking.registered_user
    #     token = SecureRandom.uuid
    #     @event_booking = registered_user.build_event_booking(event_id: @event.id, uuid: token)
    #     @event_booking.save
    #   end
    # end

    # TODO move the below up to the finder
    if @event_booking && @event && @event.live?
      redirect_to :action => :invite, :token => token
    else
      redirect_to '/404.html'
    end
  end

  def invite
    @editbyorg = false
    if params[:mode] && params[:mode] = 'edit'
      @editbyorg = true
    end

    @event_booking = EventBooking.find_by(uuid: params[:token])

    @event = @event_booking.event if @event_booking

    # TODO move the below up to the finder
    if @event_booking && @event && @event.live?
      @event_booking_json = booking_as_json(@event_booking, true)
      render :show
    else
      redirect_to '/404.html'
    end
  end

  def accept_public_invite
    @event = Event.find_by(id: params[:id])

    # ES Removed due to strange behaviour where link not working
    # if @event.child_id.present?
    #   # If the event has a child it has been overridden and must book the child
    #   @event = @event.child
    # end

    if @event && @event.complete? && @event.live? && @event.is_public?
      if @event.image2
        @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image2}"
      elsif @event.image1
        @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
      else
        @logo_url = "https://hgevents-images.s3-eu-west-1.amazonaws.com/event-stop.jpg"
      end
      cancelled_booking = @event.event_bookings.find_by(uuid: params[:unavailable]) if params[:unavailable]
      @booker = cancelled_booking.registered_user.full_name if cancelled_booking.present?
      event_booking = @event.event_bookings.build
      @event_booking_json = cancelled_booking.blank? ? booking_as_json(event_booking, false) : booking_as_json(cancelled_booking, true)
      render :show
    else
      redirect_to '/404.html'
    end
  end

  def embed_booking
    @event = Event.find_by(uuid: params[:token])
    if @event && @event.complete? && @event.live?
      event_booking = @event.event_bookings.build
      @event_booking_json = booking_as_json(event_booking, false)
      render :show
    else
      redirect_to '/404.html'
    end
  end

  def decline
    event_booking = EventBooking.find_by(uuid: params[:token])

    @opted_out = params[:optout]

    if event_booking # && event_booking.active?
      @event = event_booking.event
      @event_booking_json = booking_as_json(event_booking, false, true)
      # Sent elsewhere
      # send_decline(event_booking.id)
      render :show
    else
      redirect_to '/404.html'
    end
  end

  def resend_confirmation
    reg_user = RegisteredUser.find_by_id(params[:id])
    if reg_user
      send_confirmation(reg_user.event_booking)
    end
    render json: { head: :ok }
  end

  def view_summary
  end

  def invoice
    @event = @event_booking.event
    @company = @event.organisation.vat_number
    @vatable = @event.vatable
    @vat_number = if @event.organisation.vat_number
                    @event.organisation.vat_number.vat_number
                  end

    # Vat number is from the vat number table
    @company_address = @company ? @company.company_address : ""

    @vatexc = @event.vat_exclusive?
    @vat_percentage = ENV['VAT_RATE']
    @user = @event_booking.registered_user

    # Only returns uncancelled package booking packages
    # @packages = @event_booking.packages.order(:id)

    @package_bookings = @event_booking.package_bookings.order(:package_id)

    @payment_status = @event_booking.payment_status

    org = @event.organisation

    @payment_info = @event.payment_info || org.payment_info

    # @fees = if @event.fees_pass_on && @event_booking.payment_type == 'card'
    #           # If booking refunded show refunded fees
    #           if @event_booking.refunded?
    #             @event_booking.booking_fees_refunded_card
    #           else
    #             # Show un-refunded fees only
    #             @event_booking.booking_fees
    #           end
    #         else
    #           if @event_booking.app_fees_total.present?
    #             @event_booking.app_fees_total / 100
    #           else
    #             0
    #           end
    #         end

    costs = @event_booking.booking_cost

    @net_amount = costs[:cost]

    # @vat_amount = costs[:vat_amount]

    @discount_percentage = @event_booking.discount_percentage if @event_booking.discount_code_id
    @discounted_amount = @net_amount - (@net_amount * (@event_booking.discount_percentage / 100)) if @event_booking.discount_code_id

    @payment = @event_booking.booking_payments.first

    @gross = @net_amount

    # @gross += @fees if @event.fees_pass_on
    # @gross += @vat_amount if @event.vat_exclusive

    # if @event_booking.payment_type == 'card'
    #   payment_id = @event_booking.booking_payments.first.stripe_charge_id
    #   payment = StripePayment.get_charge(@event_booking, payment_id)
    #   intent = StripePayment.get_intent(@event_booking, payment.payment_intent)
    #   fee_details = intent.latest_charge.balance_transaction.fee_details

    #   stripe_fee = fee_details[0].amount.to_f / 100
    #   application_fee = intent.application_fee_amount.to_f / 100

    #   @gross_amount = 0
    #   @package_bookings.each do |pb|
    #     @gross_amount =+ (pb.quantity_tickets * pb.package.cost_b)
    #   end
    #   @net_amount = payment.amount.to_f / 100
    #   # @fees = stripe_fee + application_fee
    # end


    render pdf: 'invoice-' + Date.today.to_s.camelcase,
           disposition: 'attachment',
           layout: 'pdf.erb',
           show_as_html: false,
           page_size: "A4",
           dpi: '300',
           margin: { bottom: 10 }
  end

  def card_invoice
    begin
      event_booking = EventBooking.find(params[:id])
      payment_id = event_booking.booking_payments.first.stripe_charge_id
      receipt_url = StripePayment.get_receipt_url(event_booking, payment_id)
      render json: {receipt_url: receipt_url}, status: 200
    rescue StandardError => e
      render json: {error_message: e.message}, status: :unprocessable_entity
    end
  end

  def event_fees
    # Called from payments in bookings
    fees = EventBooking.get_payment_fees(params[:event_id], params[:selected_packages], params[:discount_code_id])
    enc_fees = JsonWebToken.encodeBase64(fees)
    render json: { fees: fees, booking_token: enc_fees }
  end

  def virtual_booking
    @eb = EventBooking.find_by(uuid: params[:token])
    pb = @eb.package_bookings.find(params[:ticket_id])
    if pb && pb.package.virtual_link.present? && pb.cancelled_at.blank?
      redirect_to pb.package.virtual_link
    elsif pb.cancelled_at.present?
      sanitised_title = @eb.event.title.downcase.gsub(/[!?&|$%@':;.<>=+"\\\/\s]/, '_')
      redirect_to accept_public_invite_path(id: @eb.event.id, name: sanitised_title, unavailable: @eb.uuid)
      #render :booking_unavailable, layout: 'application'
    else
      redirect_to '/404.html'
    end
  end

  def booking_unavailable

  end

  def ics_export
    eb = EventBooking.find_by(uuid: params[:token])
    ical_filename = "#{eb.event.title}.ics"
    send_data eb.generate_ical(params[:ticket_id]), :filename => ical_filename, :type => 'text/calendar', :disposition => 'attachment'
  end

  def get_payment_intent
    event_booking = EventBooking.find(params[:event_booking_id])
    payment = event_booking&.booking_payments&.first
    if payment
      render json: {intent: StripePayment.get_payment_intent(event_booking, payment.stripe_charge_id),
                    connect_account_id: StripePayment.stripe_user_id(event_booking.event)}
    else
      render json: {intent: nil, connect_account_id: nil}
    end
  end

  def create_payment_intent
    event_booking = EventBooking.find(params[:event_booking_id])
    intent = StripePayment.create_payment_intent(event_booking, (event_booking.booking_cost[:cost] * 100).to_i)

    render json: {intent: intent,
                  connect_account_id: StripePayment.stripe_user_id(event_booking.event)}
  end

  def update_payment_intent
    event_booking = EventBooking.find(params[:event_booking_id])
    render json: {intent: StripePayment.update_payment_intent(params[:payment_intent_id], event_booking, params[:amount].to_f)}
  end

  def cancel_payment_intent
    event_booking = EventBooking.find(params[:event_booking_id])
    StripePayment.cancel_payment_intent(params[:payment_intent_id], event_booking)
    render json: {status: 200}
  end

  def unpaid_bookings
    event = Event.find_by_id(params[:id])
    authorize! :manage, event
    @bookings = event.event_bookings.includes(:registered_user).where(payment_type: nil, payment_status: 0, cancelled_at: nil, payment_error: nil).order('created_at DESC')
  end

  def destroy
    event_booking = EventBooking.find_by_id(params[:id])
    authorize! :manage, event_booking
    if event_booking.destroy
      render json: { success: 'deleted' }, status: 200
    else
      render json: { status: 'failed', deletion: 'failed' }, status: 400
    end
  end

  private
  def payment_actions
    if params[:booking] && params[:booking][:payment_type] == 'card'
      # @payment_status = StripePayment.make_payment_intent(@event_booking, @costs)
      logger.debug("[STRIPE] payment status from stripe charge: #{@payment_status.inspect}")
      return true
    else
      # If not Stripe pass regardless and make unpaid (the default status for bacs and cheques)
      if @event_booking.errors.blank?
        @payment_status = { payment_passed: true }
        if @event_booking.payment_type == 'comp'
          @event_booking.update(payment_status: :complimentary, app_fees_total: 0)
        else
          @event_booking.update_column(:payment_status, :unpaid) unless @event_booking.booking_amendment
        end
        return true
      else
        render json: { errors: @event_booking.errors.full_messages }, status: 400
        return false
      end
    end
  end

  def finalize_booking
    if @payment_status[:payment_passed]
      if @event_booking && @event_booking.errors.blank?

        send_confirmation(@event_booking.id)
        send_notification(@event_booking)

        if @event_booking.discount_code && !@event_booking.booking_amendment
          @event_booking.discount_code.increment_use_count
        end

        @event_booking.package_bookings.each do |pb|
          dc = pb.discount_code
          dc.increment_use_count if dc
        end

        @event_booking.set_payment_status_for_free_booking

        render json: booking_as_json(@event_booking, false)
      else
        render json: { errors: @event_booking.errors.full_messages }, status: 400
      end
    else
      render json: { errors: [@payment_status[:card_error_message]] }, status: 400
    end
  end

  def get_event_booking
    @event_booking = EventBooking.find_by_id(params[:id])
  end

  def get_events
    @org_events = Event.where("organisation_id = ? AND is_public = true AND complete = true AND live = true AND (datetimeto > ?  OR close_date > ?)", params[:id], DateTime.now, DateTime.now).order('id ASC').limit(4)
  end

  def get_cancelled_by(event_booking)
    cancel_user = current_user || event_booking.registered_user
    cancel_user.name
  end

  def send_confirmation(event_booking_id)
    if event_booking_id
      if Rails.env.development?
        TicketMailer.send_confirmation(event_booking_id).deliver_now
      else
        TicketMailer.send_confirmation(event_booking_id).deliver_later
      end
    end
  end

  # TODO using reg users, do we need this
  # def send_decline(event_booking_id)
  #   if event_booking_id
  #     if Rails.env.development?
  #       NotificationMailer.send_decline(event_booking_id).deliver_now
  #     else
  #       NotificationMailer.send_decline(event_booking_id).deliver_later
  #     end
  #   end
  # end

  def booking_params
    # Clears cancellation if there is one, for re-booking
    @costs = JsonWebToken.decodeBase64(params[:booking][:booking_token]) if (params[:booking].present? && params[:booking][:booking_token].present?)

    if !@costs.present? && params[:booking_token].present?
      @costs =  JsonWebToken.decodeBase64(params[:booking_token])
    end

    event_fees = 0
    stripe_fees_standard = 0
    non_stripe_fees_for_inc = nil

    free_booking = false

    if @costs
      @costs = @costs.first
      event_fees = @costs["event_fees"] || 0
      # For bacs and cheques, just use standard fees
      stripe_fees_standard = @costs['standard']['stripe_fees'] || 0

      non_stripe_fees_for_inc = @costs['non_stripe_fees_for_inc']

      if @costs['free_booking'] == true
        free_booking = true
      end
    end

    if params[:event_booking][:free_booking]
      free_booking = true
    end

    app_fees_total = if params[:event_booking][:payment_type] != 'card'
                        if non_stripe_fees_for_inc.present?
                          event_fees + (non_stripe_fees_for_inc.to_f * 100)
                        else
                          # For bacs and cheques, just use stripe and event fees as it would be the total amount
                           event_fees + stripe_fees_standard.to_f
                        end
                     else
                       event_fees + stripe_fees_standard.to_f
                     end

    params.require(:event_booking).permit(:id, :event_id, :payment_type, :discount_code_id, :discount_percentage, :uuid, :free_booking, :booking_amendment,
                                          registered_user_attributes: [:id, :event_id, :forename, :surname, :company, :title, :phone, :job_description, :email, :address1, :address2, :town, :county, :postcode, :country, :po_number, :user_type, menu_options_selected: [],
                                                                       registered_user_responses_attributes: [:id, :response_type, :text, :selected_option, :registration_field_id]],
                                          package_bookings_attributes: [:id, :registered_user_id, :package_id, :quantity_tickets, :discount_code_id, :discount_amount, :discount_type, sub_options_selected: [],
                                                                        registered_users_attributes: [:id, :event_id, :forename, :surname, :company, :fullname, :title, :phone, :job_description, :email, :address1, :address2, :town, :county, :postcode, :country, :po_number, :user_type, menu_options_selected: [],
                                                                                                      registered_user_responses_attributes: [:id, :response_type, :text, :selected_option, :registration_field_id],
                                                                                                      assign_subtickets_attributes: [:id, :package_id, :quantity_assigned]
                                                                        ]
                                          ])
          .merge(booking_count: 1, timed_out: false, booking_date: DateTime.now, cancelled_at: nil, cancelled_by: nil, app_fees_total: app_fees_total, free_booking: free_booking).except(:id)
  end

  def send_notification(event_booking)
    event = event_booking.event
    attendee_name = event_booking.registered_user.forename + ' ' + event_booking.registered_user.surname
    event_name = event.title
    event.organisation.notifications.create(event_id: event.id, event_name: event_name, attendee_name: attendee_name)
  end

  def booking_as_json(booking, invited, declined = nil)
    if booking.id
      booking.reload
    end

    booking.as_json(methods: :early_bird_valid, include: [{ package_bookings: { include: { registered_users: { include: { registered_user_responses: {}, assign_subtickets: {} } } } } }, { event: { include: { sponsors: {}, event_address: {}, payment_option: {},
                                                                                                                                                                                    registration_fields: {}, discount_codes: {},
                                                                                                                                                                                    tickets: { methods: :tickets_remaining, include: { child_tickets: { methods: :tickets_remaining }, package_options: { include: { package_sub_options: {} } } } },
                                                                                                                                                                                    ticket_groups: { include: { packages: { methods: :tickets_remaining, include: { child_tickets: { methods: :tickets_remaining }, package_options: { include: { package_sub_options: {} } } } } } } } } }],
    ).merge(type: :event_preview, declined: declined, opted_out: @opted_out, user_email: booking.try(:registered_user).try(:email)).to_json.html_safe

  end

  def allow_iframe
    response.headers.except! 'X-Frame-Options'
  end

  def no_header
    @no_header = true
  end

end
