class DevLoginsController < ApplicationController
  layout false

  def index
    @users = User.where('contact_id IS NOT NULL').joins(:contact).order('contacts.organisation_id') #client
    @users = @users.where("contacts.organisation_id = 3158 or contacts.organisation_id = 3200 or users.email = '<EMAIL>'")
  end

  def create
    user = User.find_by_id(params[:dev_logins][:user_id])
    self.current_user=user
    redirect_to root_path
  end

  # Sets up the current_user for dev logins
  def current_user=(user)
    @current_user = user
    session[:user_id] = user.id
  end
end
