class OrganisationUsersController < ApplicationController
	
  respond_to :json

  before_action :login_required

  def create_contact
  	@org_user = RegisteredUser.create_organisation_contact(contact_params, @current_user.organisation)

  	if @org_user
      render :json => { :id => @org_user.org_user_list_id, :email => @org_user.email, :forename => @org_user.email, :surname => @org_user.surname }, :status => 200
  	else	
      render :json => { :error => "A contact with this email address is already present" }, :status => 400
    end
  end

  def create_contacts
    @org_users = []

    if email_list_params.present?
      email_list_params[:contact_attributes].each do |attrs|
      	org_contact = RegisteredUser.create_organisation_contact(attrs, @current_user.organisation)
      	@org_users << org_contact if org_contact != false
      end
      render :json => @org_users.to_json
    else
      render json: {status: 400, error: 'Not uploaded'}, status: 400
    end
  end

  def remove_contact
  	@result = RegisteredUser.remove_organisation_contact(params[:id])
  	if @result
		  render json: {status: 200}
  	else
		  render :json => { :error => "There was a problem deleting this organisation contact" }, :status => 400
  	end	
  end

  private

  def contact_params
	params.require(:org_contact).permit(:forename, :surname, :email)
  end	

   def email_list_params
    params.permit({contact_attributes: [:forename, :surname, :email]})
  end

end  