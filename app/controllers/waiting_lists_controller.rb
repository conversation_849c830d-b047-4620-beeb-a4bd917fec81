class WaitingListsController < ApplicationController

  def create
    if WaitingList.create(list_params)
      # Sets up job to delete old items from the queue
      WaitingListJob.set(wait_until: Date.tomorrow.midnight).perform_later
      render json: {status: 200}
    else
      render json: {errors: 'not saved'}, status: 400
    end
  end

  def show
    waiting_list = WaitingList.where(event_id: params[:id])
    render json: waiting_list
  end

  def delete
    if WaitingList.find_by_id(params[:id]).destroy
      render json: {status: 200}
    else
      render json: {errors: 'not saved'}, status: 400
    end
  end

  private

  def list_params
    params.require(:waiting_list).permit(:id, :event_id, :forename, :surname, :email).merge(signup_datetime: DateTime.now)
  end

end
