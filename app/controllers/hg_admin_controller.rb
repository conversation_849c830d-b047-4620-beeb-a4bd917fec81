class HgAdminController < ApplicationController
  include ActionView::Helpers::NumberHelper

  before_action :login_required
  before_action :ensure_admin

  def index
  end

  def get_events
    events_proper = Event.proper
    @events_count = events_proper.count
    @created_count = events_proper.where('created_at >= ? and created_at <= ?', Time.now.beginning_of_month, Time.now.end_of_month).size
    @live_count = events_proper.where('live = true').size
    @uncompleted_count = events_proper.where('complete = false').size
    @completed_count = events_proper.where('complete = true').size


    if params[:search] || params[:status] || params[:org] || params[:complete] || params[:expired]
      @text_search = params[:search]
      @events = Event.proper.order('event_created_date DESC NULLS LAST, created_at DESC').includes(:organisation)
      @events = @events.where('title ILIKE ?', "%#{@text_search}%") if @text_search.present?

      # @events = @events.where("datefrom between ? AND ?", @start_date, @end_date) if @start_date.present?
      if @events.blank?
        @events = Event.where(id: [@text_search])
      end

      if params[:tags]
        @events = @events.where('tags && ARRAY[?]::varchar[]', params[:tags])
      end

      if params[:status] != "0"
        if params[:status] == "1" # live
          @events = @events.is_live
        elsif params[:status] == "2" # inactive
          @events = @events.where("live = ?", false)
        end
      end

      if params[:complete] != "0"
        if params[:complete] == "1" # complete
          @events = @events.where("complete = ?", true)
        elsif params[:complete] == "2" # incomplete
          @events = @events.where("complete = ?", false)
        end
      end

      if params[:expired] != "0"
        if params[:expired] == "1" # expired
          @events = @events.where("datetimeto <= ?", Date.today)
        elsif params[:expired] == "2" # non-expired
          @events = @events.where("datetimeto >= ?", Date.today)
        end
      end

      if params[:org] != "0"
        @events = @events.where("events.organisation_id = ?", params[:org].to_i)
      end

      @created_count = @events.where('created_at >= ? and created_at <= ?', Time.now.beginning_of_month, Time.now.end_of_month).size
      @live_count = @events.where('live = true').size
      @uncompleted_count = @events.where('complete = false').size
      @completed_count = @events.where('complete = true').size
      @events_count = @events.count
      @events = @events.page(params[:page]).per(50)
    else
      if params[:tags]
        @events = Event.proper.order('event_created_date DESC NULLS LAST, created_at DESC').includes(:organisation).where('tags && ARRAY[?]::varchar[]', params[:tags])
      else
        @events = Event.proper.order('event_created_date DESC NULLS LAST, created_at DESC').includes(:organisation)
      end

      @events_count = @events.count

      @events = @events.page(params[:page]).per(50)
    end
  end

  def reports
    summary = SummaryReport.all
    @updated_at = summary.first.created_at.strftime("%d/%m/%Y %H:%M")

    if params[:org_id] != "0"
      summary = summary.filter_by_client(params[:org_id])
    end 

    if params[:eventStatusFilter] != "0"
      if params[:eventStatusFilter] == "1"
        summary = summary.is_live.upcoming
      else
        summary = summary.expired
      end
    end

    summary = summary.filter_date_after(params[:dateFromFilter]) if params[:dateFromFilter] != "null"
    summary = summary.filter_date_before(params[:dateToFilter]) if params[:dateToFilter] != "null"
    summary = summary.filter_by_event_name(params[:eventFilter]) if params[:eventFilter] != "null"

    summary_by_client = summary.group_by{|s| s.client}

    @report  = []
    summary_by_client.each do |key, value|
      @report << {
        client_name: key,
        revenue: number_with_precision(value.sum{|p| p.revenue.to_f}, :precision => 2),
        profit: number_with_precision(value.sum{|p| p.profit.to_f}, :precision => 2),
        vat: number_with_precision(value.sum{|p| p.vat.to_f}/100, :precision => 2),
        stripe: number_with_precision(value.sum{|p| p.stripe.to_f}, :precision => 2),
        hg_fees: number_with_precision(value.sum{|p| p.hg_fees.to_f}, :precision => 2),
        total_fees: number_with_precision(value.sum{|p| p.total_fees.to_f}, :precision => 2),
        forecast: number_with_precision(value.sum{|p| p.forecast.to_f}, :precision => 2),
        tickets_sold: value.sum{|p| p.tickets_sold.to_f}
      }
    end

    @total_rows = @report.size

    @grand_totals = {
      revenue: number_with_precision(@report.sum{|r| r[:revenue].to_f}, :precision => 2),
      profit: number_with_precision(@report.sum{|r| r[:profit].to_f}, :precision => 2),
      vat: number_with_precision(@report.sum{|r| r[:vat].to_f}, :precision => 2),
      stripe: number_with_precision(@report.sum{|r| r[:stripe].to_f}, :precision => 2),
      hg_fees: number_with_precision(@report.sum{|r| r[:hg_fees].to_f}, :precision => 2),
      total_fees: number_with_precision(@report.sum{|r| r[:total_fees].to_f}, :precision => 2),
      forecast: number_with_precision(@report.sum{|r| r[:forecast].to_f}, :precision => 2),
      tickets_sold: @report.sum{|r| r[:tickets_sold].to_f},
    }

    @csv_data = @report

    @report = Kaminari.paginate_array(@report).page(params[:page]).per(100)

    respond_to do |format|
      format.json{}
      format.csv {send_data SummaryReport.export_as_csv(@csv_data, @grand_totals, 'admin'), filename: "summary_report.csv"}
      format.html do
        redirect_to root_path
      end
    end  
  end

  def get_org_details
    # Get orgs that have at least one event
    @orgs = Organisation.includes(:events).where(events: {organisation_id: Organisation.all})
                        .order("name ASC").map{|org| {id: org.id, name: org.name}}    
  end

  def get_org_events
    events = Event.where(organisation_id: params[:id]).where.not(title: nil)
    .order("title ASC").map{|event| {id: event.id, name: event.title}}   

    if events.present?
      render json: {events: events}
    else
      render json: {status: 400}, status: 400
    end
  end

  def show_date_toggle
    @event = Event.find(params[:id])
    @event.show_date = !@event.show_date
    @event.save
    respond_to do |format|
      format.json {head :no_content}
    end
  end

  def fees
  end

  def fee_overrides
  end

  def audit
  end

  def news
    @news = NewsArticle.all
  end

  def testimonials
    @testimonial = Testimonial.all
  end

  def offers
    @offer = Offer.all
  end

  def knowledge_hub
    @kh = KnowledgeHub.all
  end

  def administer_users
    @eventstop_users = EventStopUser.all
  end

  def administer_charities
    @charities = CharityNumber.all
    # render :json => @charities
  end

  def promo_codes
  end

  # def verify_user
  #   # TODO no longer used!!
  #   event_user = EventStopUser.find_by_id(params[:user_id])
  #   if event_user.update_attribute(:validated, true)
  #     email_confirmation(event_user)
  #     render json: {status: 200}
  #   else
  #     render json: {status: 400}, status: 400
  #   end
  # end

  def verify_charity
    charity = CharityNumber.find_by_id(params[:charity_id])
    if charity.update_attribute(:verified, params[:verify])
      render json: {status: 200}
    else
      render json: {status: 400}, status: 400
    end
  end

  def manage_notifications
    @notifications = Notification.all
  end

  def get_audits
    # if params[:id] == "audit"
    twoweeksago = DateTime.now - 2.weeks
    @audits = Version.where("created_at >= ?", twoweeksago).order("created_at ASC")
    # else
    #   @audits = Version.where(item_id: params[:id])
    # end
  end


  # These two relate to overall card payments, not just the fees going to Stripe
  def stripe_breakdown
    # Could be cancelled (but not refunded)
    @event_bookings = EventBooking.where(:event_id => params[:id], :payment_status => 2).
        fully_booked.includes(:registered_user).joins(:booking_payments).
        where("booking_payments.stripe_charge_id is not null")

    @total_stripe = 0
    @total_hg = 0

    @event_bookings.each do |eb|
      @total_hg += eb.booking_payments.sum(:stripe_fees)
      @total_stripe += eb.booking_payments.sum(:application_fees)
    end

    render pdf: 'stripe_breakdown-' + Date.today.to_s.camelcase,
           disposition: 'attachment',
           layout: 'pdf.erb',
           show_as_html: false,
           disable_smart_shrinking: true,
           dpi: '300',
           margin: {bottom: 10}
  end

  def stripe_tax_summary
    @event = Event.find_by_id(params[:id])
    @event_bookings = @event.event_bookings.where(:event_id => params[:id], :payment_status => 2).
      fully_booked.includes(:registered_user).joins(:booking_payments).
      where("booking_payments.stripe_charge_id is not null")

    @total_hg = 0
    @total_stripe = 0
    @total_net_revenue = 0
    @stripe_vat = 0
    @hg_fees_vat = 0

    @total_gross_revenue = 0
    @revenue_vat = 0

    @event_bookings.each do |eb|
      @total_hg += eb.booking_payments.sum(:stripe_fees)
      @total_stripe += eb.booking_payments.sum(:application_fees)

      @total_net_revenue += eb.booking_payments.sum(:net_amount)

      if @event.vat_exclusive
        @revenue_vat += eb.booking_payments.sum(:vat_amount) / 100
      # else
      #   @total_gross_revenue += @total_net_revenue
      end
    end

    # @total_net_revenue = @total_net_revenue - @revenue_vat

    @total_gross_revenue += @total_net_revenue

    if @event.vat_exclusive
      @total_net_revenue = @total_net_revenue - @revenue_vat
    else
      @revenue_vat += calc_vat_inc(@total_net_revenue)
      @total_net_revenue = @total_net_revenue - @revenue_vat
    end

    @stripe_vat = calc_vat_inc(@total_stripe)
    @hg_fees_vat = calc_vat_inc(@total_hg)

    render pdf: 'stripe_tax_summary-' + Date.today.to_s.camelcase,
    disposition: 'attachment',
    orientation: 'Landscape',
    layout: 'pdf.erb',
    show_as_html: false,
    disable_smart_shrinking: true,
    dpi: '300',
    margin: {bottom: 10}
  end

  private

  def calc_vat_inc(amount)
    (amount - (amount / 1.2)).round(2)
  end

  def email_confirmation(event_user)
    user = event_user.user
    user_mailer = UserMailer.confirm(user)
    if Rails.env.development?
      user_mailer.deliver_now
    else
      user_mailer.deliver_later
    end
  end

end
