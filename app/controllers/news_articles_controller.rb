class NewsArticlesController < ApplicationController

  before_action :login_required
  before_action :ensure_admin

  def new
    @news = NewsArticle.new
  end

  def create
    @news = NewsArticle.new(news_params)
    if @news.save
      redirect_to('/hg_admin/news', flash: { success: 'News Created' })
    else
      flash_now!(:error =>"News Errors, Check Each field")
      render :new
    end
  end

  def edit
    @news = NewsArticle.find_by_id(params[:id])
  end

  def update
    @news = NewsArticle.find(params[:id])
    if @news.update(news_params)
      redirect_to('/hg_admin/news', flash: { success: 'News Updated' })

    else
      flash_now!(:error =>"News Errors, Check Each field")
      render action: :new
    end
  end

  def destroy
    NewsArticle.find(params[:id]).destroy
    flash[:success] = 'News Deleted'
    redirect_to('/hg_admin/news', flash: { success: 'News Deleted' })
  end

  private

  def news_params
    params.require(:news_article).permit(:news_name, :news_title, :news_date, :news_body, :image)
  end

end
