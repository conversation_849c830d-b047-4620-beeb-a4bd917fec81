class UserMailer < ApplicationMailer

  before_action :setup_aws_bucket

  # TODO these are the user emails
  def dummy_welcome
    @user = User.last

    mail from: "<EMAIL>",
         to: @user.email,
         bcc: "<EMAIL>",
         subject: "Welcome to Eventstop.",
         template_name: "confirm"
  end

  def welcome(user)
    @user = user

    mail from: "<EMAIL>",
         to: @user.email,
         bcc: "<EMAIL>",
         subject: "Welcome to Eventstop."
  end

  def verify(user)
    @user = user

    mail from: "<EMAIL>",
         to: "<EMAIL>",
         bcc: "<EMAIL>",
         subject: "New User on Eventstop."
  end

  def new_event(user, event)
    @user = user
    @event = event
    if user.events.count == 0
      mail from: "<EMAIL>",
           to: "<EMAIL>",
           bcc: "<EMAIL>, <EMAIL>, <EMAIL>",
           subject: "A New User Has Created Their First Event!"
    else
      mail from: "<EMAIL>",
          to: "<EMAIL>",
          bcc: "<EMAIL>, <EMAIL>, <EMAIL>",
          subject: "A User Has Created Their Event!"
   end
  end

  def confirm(user)
    @user = user

    mail from: "<EMAIL>",
         to: @user.email,
         bcc: "<EMAIL>",
         subject: "You have been confirmed as an Eventstop User."
  end

  private

  def setup_aws_bucket
    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"
  end
end
