class NotificationMailer < ApplicationMailer

  before_action :setup_aws_bucket

  def send_daily_booking_list(event, bookings)

    @bookings = bookings.joins(:registered_user)
    @event = event

    mail to: event.organiser_email,
         bcc: "<EMAIL>",
         subject: "Bookings for: #{@event.title}."

  end

  def send_decline(event_booking_id)
    @event_booking = EventBooking.includes(:registered_user, :event).find_by_id(event_booking_id)

    @event = @event_booking.event

    @user = @event_booking.registered_user

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    mail to: @event.organiser_email,
         bcc: "<EMAIL>",
         subject: "#{@user.forename} #{@user.surname} has declined an invite to to #{@event.title}."
  end


  def send_list_expired_events_to_accounts
    @events = Event.where(datetimeto: Time.zone.yesterday.beginning_of_day..Time.zone.yesterday.end_of_day, live: true)

    if @events.present?
      mail to: "<EMAIL>",
        bcc: "<EMAIL>",
        subject: "The events listed have expired yesterday"
    end
  end


  private

  def setup_aws_bucket
    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"
  end

end