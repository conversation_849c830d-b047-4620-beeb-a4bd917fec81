class SampleMailer < ApplicationMailer

  helper InvoiceHelper

  require 'open-uri'

  # TODO use templates for the original mailer and layout

  def send_invites(event_id, email)

    @event = Event.includes(:event_address).find_by_id(event_id)
    @event_address = @event.event_address

    user = Struct.new(:forename, :surname)
    @user = user.new('<PERSON>', '<PERSON>')

    if @event.email_templates.present?
      template = @event.email_templates.invite.first

      if template
        details = template.details || @event.details
      else
        details = @event.details
      end

      details.gsub!("<br>", "<p>&nbsp;</p>") if details

      if template
        @show_forename_only = template.show_forename_only
        @event_invite = {
            welcome_text: template.welcome_text || "Hi",
            title1: template.title1,
            title2: template.title2,
            text_area_1: template.text_area_1,
            text_area_2: template.text_area_2,
            text_area_3: template.text_area_3,
            ticket_options_title: template.ticket_options_title || "Event Details",
            details: details || '',
            button: template.email_button_option
        }
      else
        @event_invite = {
            welcome_text: "Hi",
            title1: "Thank you for booking",
            title2: "Tickets Selected",
            text_area_1: "The event will be soon",
            text_area_2: "As a test",
            text_area_3: "For You",
            ticket_options_title: "Event Details",
            details: details || ''
        }
      end
    else
      details = @event.details
      details.gsub!("<br>", "<p>&nbsp;</p>") if details

      @event_invite = {
          welcome_text: "Hi",
          title1: "Hope you've had a great start to the year, We would like to invite you to...",
          title2: "Please RSVP by clicking the button below before  " + @event.datetimefrom.to_date.try(:strftime, "%A %d %B %Y"),
          text_area_1: "This event will take place at:" + (@event.location || ""),
          text_area_2: @event.summary,
          text_area_3: "If you have an enquiry about this event, please contact the event organiser",
          ticket_options_title: "Event Details",
          details: details || '',
          button: "Register Your interest"
      }
    end

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    if @event.image1
      @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
    end

    if @event.sponsor_view_email
      @sponsors = @event.sponsors
    else
      @sponsors = []
    end

    if @event.sponsor_view_email
      @aws_sponsor_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/sponsors/#{@event.id.to_s}/"
    end


    @fileuploadsinv = @event.file_uploads.where(email_type: "invite")

    if @fileuploadsinv.present?
      begin
        s3 = Aws::S3::Client.new
        @fileuploadsinv.each do |fui|
          key_name = "invite/" + @event.organisation_id.to_s + '/' + @event.id.to_s + '/' + fui.name + ""
          resp3 = s3.get_object(bucket: ENV['S3_DOC_BUCKET'], key: key_name)
          attachments[@event.organisation_id.to_s + '_' + @event.id.to_s + '_' + fui.name] = resp3.body.read
        end
      rescue Exception => e
        Rollbar.error(e, 'Issue with attachments in invites (sample mailer)')
      end
    end

    @name = "Sample"
    @uuid = 'SomeSampleUUID'

    mail to: email,
         reply_to: @event.organiser_email,
         bcc: "<EMAIL>",
         subject: "SAMPLE - You have been invited to #{@event.title}.",
         template_path: "ticket_mailer",
         template_name: "send_invites"
  end

  def send_confirmation(event_id, email, payment_type = '0')
    @event = Event.find_by_id(event_id)

    setup_sample_data(payment_type)

    @uuid = "SAMPLEUUID"
    @event_uuid = @event.uuid

    @event_payment_type = ""
    if payment_type == '0' && @show_paid
      @event_payment_type = "Paid by Card"
    end

    # TODO the payment info should come from the event!!!!!!
    org = @event.organisation
    @payment_info = @event.payment_info || org.payment_info

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    if @event.email_templates.present?
      template = @event.email_templates.confirmation.last
      if template
        @show_forename_only = template.show_forename_only
        @event_confirmation = {
            welcome_text: template.welcome_text || "Hi",
            title1: template.title1 || "Thank you for booking your event at: " + (@event.location || ""),
            title2: template.title2,
            text_area_1: template.text_area_1,
            text_area_2: template.text_area_2,
            text_area_3: template.text_area_3,
            details: template.details
        }
      else
        @event_confirmation = {
            welcome_text: "Hi",
            title1: "Thank you for booking your event at: " + (@event.location || ""),
            title2: "Enjoy the event!",
            text_area_1: "You have confirmed your attendance to",
            text_area_2: "",
            text_area_3: ""
        }
      end
    else
      @event_confirmation = {
          welcome_text: "Hi",
          title1: "Thank you for booking your event at: " + (@event.location || ""),
          title2: "Enjoy the event!",
          text_area_1: "You have confirmed your attendance to",
          text_area_2: "",
          text_area_3: ""
      }
    end


    if @event.image1
      @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
    end

    subject_text = "SAMPLE - Confirmation of: #{@event.title}."

    if @event.sponsor_view_email
      @sponsors = @event.sponsors
    else
      @sponsors = []
    end

    if @event.sponsor_view_email
      @aws_sponsor_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/sponsors/#{@event.id.to_s}/"
    end

    @company_address = ""

    if @net_amount > 0
      @sample = true
      @vatable = @event.vatable
      @vatexc = false
      if !Rails.env.development?
        attachments["invoice.pdf"] = WickedPdf.new.pdf_from_string(render_to_string(:pdf => 'inv', :template => 'event_bookings/invoice.erb', layout: 'pdf.erb'),
                                                                 :dpi => '300')
      end
    end

    if @event.print_tickets && !Rails.env.development?
      attachments["tickets.pdf"] = WickedPdf.new.pdf_from_string(render_to_string(:pdf => 'inv', :template => 'event_bookings/tickets.erb'), :dpi => '300')
    end

    @fileuploadsconf = @event.file_uploads.where(email_type: "confirmation")

    if @fileuploadsconf.present?
      begin
        s3 = Aws::S3::Client.new
        @fileuploadsconf.each do |fuc|
          key_name = "confirmation/" + @event.organisation_id.to_s + '/' + @event.id.to_s + '/' + fuc.name + ""
          resp3 = s3.get_object(bucket: ENV['S3_DOC_BUCKET'], key: key_name)
          attachments[@event.organisation_id.to_s + '_' + @event.id.to_s + '_' + fuc.name] = resp3.body.read
        end
      rescue Exception => e
        Rollbar.error(e, 'Issue with attachments in confirmation emails (sample mailer)')
      end
    end

    mail to: email,
         bcc: '<EMAIL>',
         reply_to: @event.organiser_email,
         subject: subject_text,
         template_path: "ticket_mailer",
         template_name: "send_confirmation"
  end

  private

  def setup_sample_data(payment_type)
    @legal_terms = @event.legal_term || @event.organisation.legal_term
    @event_address = @event.event_address

    user = Struct.new(:forename, :surname, :email, :address1, :address2, :town, :postcode, :county, :po_number, :company, :full_name)
    @user = user.new('John', 'Smith', '<EMAIL>', '1 Example Drive', 'ExampleVille', 'ExampleTown', 'EX1 AMP', nil, nil, 'Test corp', 'John Smith')

    event_booking = Struct.new(:id, :payment_type, :early_bird_valid?, :confirmation_code, :package_bookings, :updated_at, :payment_status, :refunded?, :paid?, :refunded_paid_again?, :free_booking?, :booking_date, :booking_type)

    @package_bookings = []

    @packages = @event.packages
    @packages.each do |package|
      package_booking = Struct.new(:quantity_tickets, :package, :discount_code_id, :id)
      pb = package_booking.new(1, package, nil, 200000)
      @package_bookings << pb
    end

    payment = Struct.new(:id, :payment_type, :amount, :gross_amount)
    @payment = payment.new(1, 0, 1000, 1000) 

    @event_booking = if payment_type == '0'
                       event_booking.new(1, 'card', false, '1234', @package_bookings, Date.today, 'paid', false, true, false, false, DateTime.now, 'mixed')
                     elsif payment_type == '1'
                       event_booking.new(1, 'bacs', false, '1234', @package_bookings, Date.today, 'unpaid', false, false, false, false, DateTime.now, 'mixed')
                     else
                       event_booking.new(1, 'cheque', false, '1234', @package_bookings, Date.today, 'unpaid', false, false, false, false, DateTime.now, 'mixed')
                     end

    @user_responses = []
    @user_sample = @event.registration_fields

    # TODO may be able to get this from the fees code
    costs = booking_cost
    @net_amount = costs[:cost]
    @vat_amount = costs[:vat_amount]

    @fees = if @event.fees_pass_on && @event_booking.payment_type == 'card'
              fees = get_fees
              fees[:standard][:total_fees] / 100
            else
              0
            end

    @show_paid = if @net_amount > 0
                   true
                 else
                   false
                 end

    @vat_number = '123456789'

    # Used by package quantity method to spit out 1 if just a sample
    @sample = true
  end

  # Simulates an event booking cost
  def booking_cost
    # Just gets costs for all tickets
    packages = @event.packages.includes(:vat_rate)

    cost = 0.0
    vat_amount = 0.0

    packages.each do |package|
      # Not doing discount for the sample
      temp_cost = package.cost_b

      cost += temp_cost

      vat_amount += ((temp_cost * package.vat_rate.rate) / 100) if package.vat_rate
    end

    {cost: cost, vat_amount: vat_amount}
  end

  def get_fees
    event = @event
    packages = @event.packages

    discount_valid = event.datetime_eb && (DateTime.now < event.datetime_eb)

    cost = 0.0
    vat_amount = 0.0

    discount_amount = 0

    if event.vat_exclusive?
      packages.each do |p|
        # Parcel off to another method
        package_cost = 0.0

        if discount_valid #In this case, early bird, not from discount codes
          package_cost = p.cost_a
        else
          package_cost = p.cost_b
        end

        # original_cost = original_cost + package_cost
        cost = cost + package_cost

        if p.vat_rate
          vat_amount = vat_amount + ((package_cost * (p.vat_rate.rate.to_f) / 100))
        else
          # uses standard rate as fallback
          vat_amount = vat_amount + ((package_cost * (ENV[:VAT_RATE]) / 100))
        end
      end
    else
      if discount_valid
        packages.each {|p| cost = cost + p.cost_a}
      else
        packages.each {|p| cost = cost + p.cost_b}
      end
    end

    cost_pence = ((cost * 100) + 0.5).to_i

    if event.vat_exclusive?
      vat_pence = ((vat_amount * 100) + 0.5.to_i)
      cost_pence = cost_pence + vat_pence
      cost = cost_pence / 100
    end

    fees = Fees.active_fees
    hg_fees = fees.hg_fees
    hg_fees_add = fees.hg_fees_add

    hg_fees_charity = fees.hg_fees_charity
    hg_fees_charity_add = fees.hg_fees_charity_add

    # Gets the amount we want for HG based upon our fees
    hg_fees_amount = if event.charity?
                       (cost_pence * (hg_fees_charity / 100)) + (hg_fees_charity_add * 100)
                     else
                       (cost_pence * (hg_fees / 100)) + (hg_fees_add * 100)
                     end

    hg_fees_total = (hg_fees_amount + 0.5).to_i # ROUNDS THE FEES

    # Cap of £12
    hg_fees_total = 1200 if hg_fees_total > 1200

    if event.fees_pass_on?
      # if fees ARE NOT included in the price
      internal_charge_standard = ((cost + (hg_fees_total.to_f / 100) + fees.stripe_fees_card_add).to_f / (1 - (fees.stripe_fees_card / 100).to_f))
      charge_standard= ((internal_charge_standard * 100).round).to_i #(Ensures Rounds correctly)
    else
      charge_standard = cost_pence
    end

    stripe_fees_standard = (charge_standard - cost_pence - hg_fees_total)

    {
        standard: {
            event_amount: charge_standard,
            total_fees: stripe_fees_standard + hg_fees_total,
            stripe_fees: stripe_fees_standard
        },
        event_fees: hg_fees_total,
        vat_amount: vat_pence,
        vat_included: !event.vat_exclusive?
    }
  end

end
