class TicketMailer < ApplicationMailer

  helper InvoiceHelper
  require 'open-uri'

  def send_invites(event_id, event_booking_id, uuid, name, email)
    # todo just get the event booking and get the event from that??
    @event = Event.includes(:event_address).includes(:file_uploads).find_by_id(event_id)

    @fileuploadsinv = @event.file_uploads.where(email_type: "invite")

    event_booking = EventBooking.includes(:registered_user).find_by_id(event_booking_id)
    @event_address = @event.event_address
    @user = event_booking.registered_user


    if @event.email_templates.present?
      template = @event.email_templates.invite.first

      details = if template && template.details
                  template.details
                else
                  @event.details
                end

      details.gsub!("<br>", "<p>&nbsp;</p>") if details

      @show_forename_only = template.show_forename_only if template


      if template
        @event_invite = {
            welcome_text: template.welcome_text || "Hi",
            title1: template.title1,
            title2: template.title2,
            text_area_1: template.text_area_1,
            text_area_2: template.text_area_2,
            text_area_3: template.text_area_3,
            ticket_options_title: template.ticket_options_title || "Event Details",
            details: details || '',
            button: template.email_button_option
        }
      else
        @event_invite = {
            welcome_text: "Hi",
            title1: "",
            title2: "",
            text_area_1: "",
            text_area_2: "",
            text_area_3: "",
            ticket_options_title: "Event Details",
            details: "",
            button: ""
        }
      end

    else
      details = @event.details
      details.gsub!("<br>", "<p>&nbsp;</p>")

      @event_invite = {
          welcome_text: "Hi",
          title1: "Hope you've had a great start to the year, We would like to invite you to...",
          title2: "Please RSVP by clicking the button below before  " + @event.datetimefrom.to_date.try(:strftime, "%A %d %B %Y"),
          text_area_1: "This event will take place at:" + (@event.location || ""),
          text_area_2: @event.summary,
          text_area_3: "If you have an enquiry about this event, please contact the event organiser",
          ticket_options_title: "Event Details",
          details: details || '',
          button: "Register Your interest"
      }
    end

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    if @event.image1
      @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
    end

    if @event.sponsor_view_email
      @sponsors = @event.sponsors
    else
      @sponsors = []
    end

    if @event.sponsor_view_email
      @aws_sponsor_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/sponsors/#{@event.id.to_s}/"
    end

    begin
      s3 = Aws::S3::Client.new

      @fileuploadsinv.each do |fui|
        key_name = "invite/" + @event.organisation_id.to_s + '/' + @event.id.to_s + '/' + fui.name + ""
        resp3 = s3.get_object(bucket: ENV['S3_DOC_BUCKET'], key: key_name)
        attachments[@event.organisation_id.to_s + '_' + @event.id.to_s + '_' + fui.name] = resp3.body.read
      end

    rescue Exception => e
      Rollbar.error(e, 'Issue with attachments in invites')
    end

    @name = name
    @uuid = uuid
    mail to: email,
         bcc: "<EMAIL>",
         from: "#{@event.organiser} <<EMAIL>>",
         reply_to: @event.organiser_email,
         subject: "You have been invited to #{@event.title}."
  end

  def send_confirmation(event_booking_id, payment_reminder = false, updated = false)

    @event_booking = EventBooking.includes(:registered_user, event: [:email_templates, :packages], package_bookings: [:package]).find_by_id(event_booking_id)

    unless @event_booking
      Rollbar.log('error', 'This event booking id is not returning an eventbooking:' + event_booking_id.to_s)
    end

    @event_booking = @event_booking.reload

    @package_bookings = @event_booking.package_bookings.uncancelled.try(:order, :package_id)

    setup_initial_data()

    @fileuploadsconf = @event.file_uploads.where(email_type: "confirmation")

    @uuid = @event_booking.uuid
    @event_uuid = @event.uuid

    costs = @event_booking.booking_cost

    @net_amount = costs[:cost]

    # Discount already on the net amount
    @discount_percentage = @event_booking.discount_percentage if @event_booking.discount_code_id

    # TODO only show if event allows it
    @vat_amount = costs[:vat_amount]

    @payment = @event_booking.booking_payments.first

    @event_payment_type = ""
    if @event_booking.payment_type && @net_amount > 0
      @event_payment_type = case @event_booking.payment_type
                              when "card"
                                "Paid by Card"
                              when "cheque"
                                if @event_booking.paid? || @event_booking.refunded_paid_again?
                                  "Paid by Cheque"
                                else
                                  "Due by Cheque"
                                end
                              when "bacs"
                                if @event_booking.paid? || @event_booking.refunded_paid_again?
                                  "Paid by BACS"
                                else
                                  "Due by BACS"
                                end
                            end
    end

    org = @event.organisation

    @vat_number = if org.vat_number
                    org.vat_number.vat_number
                  end

    # Vat number is from the vat number table
    @company = @event.organisation.vat_number
    @company_address = @company ? @company.company_address : ""

    @payment_info = @event.payment_info || org.payment_info

    if ((payment_reminder && @event_booking.payment_type !="card") || !payment_reminder) && !updated
      if (@net_amount > 0 && @event.chargeable) || @event_booking.payment_status == "complimentary"
        @vatable = @event.vatable
        @vatexc = false
        attachments["invoice.pdf"] = WickedPdf.new.pdf_from_string(render_to_string(:pdf => 'inv', :template => 'event_bookings/invoice.erb', layout: 'pdf.erb'),
                                                                   :dpi => '300', :page_size => "A4")
      end

      # This is a duplicate
      if @event.image1
        @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
      end

      if @event.print_tickets
        attachments["tickets.pdf"] = WickedPdf.new.pdf_from_string(render_to_string(:pdf => 'inv', :template => 'event_bookings/tickets.erb'), :disable_smart_shrinking => false, :dpi => '300', print_media_type: true)
      end
    end

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    if @event.email_templates.present?
      template = @event.email_templates.confirmation.last
      if template
        @show_forename_only = template.show_forename_only
        @event_confirmation = {
            welcome_text: template.welcome_text || "Hi",
            title1: template.title1 || "Thank you for booking your event at: " + (@event.location || ""),
            title2: template.title2,
            text_area_1: template.text_area_1,
            text_area_2: template.text_area_2,
            text_area_3: template.text_area_3,
            details: template.details
        }
      else
        @event_confirmation = {
            welcome_text: "Hi",
            title1: "Thank you for booking your event at: " + (@event.location || ""),
            title2: "Enjoy the event!",
            text_area_1: "You have confirmed your attendance to",
            text_area_2: "",
            text_area_3: ""
        }
      end
    else
      @event_confirmation = {
          welcome_text: "Hi",
          title1: "Thank you for booking your event at: " + (@event.location || ""),
          title2: "Enjoy the event!",
          text_area_1: "You have confirmed your attendance to",
          text_area_2: "",
          text_area_3: ""
      }
    end

    if payment_reminder
      set_reminder_data
    end

    if @event.image1
      @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
    end

    if @event.sponsor_view_email
      @sponsors = @event.sponsors
    else
      @sponsors = []
    end

    if @event.sponsor_view_email
      @aws_sponsor_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/sponsors/#{@event.id.to_s}/"
    end

    begin
      s3 = Aws::S3::Client.new

      @fileuploadsconf.each do |fuc|
        key_name = "confirmation/" + @event.organisation_id.to_s + '/' + @event.id.to_s + '/' + fuc.name + ""
        resp3 = s3.get_object(bucket: ENV['S3_DOC_BUCKET'], key: key_name)
        attachments[@event.organisation_id.to_s + '_' + @event.id.to_s + '_' + fuc.name] = resp3.body.read
      end
    rescue Exception => e
      Rollbar.error(e, "Issue with attachments in confirmations")
    end

    subject_text = "Confirmation of: #{@event.title}."

    if payment_reminder
      subject_text = "Payment reminder for: #{@event.title}."
    end

    if updated
      subject_text = "Changes have been made to your details for: #{@event.title}."
    end

    mail to: @user.email.strip,
        bcc: "<EMAIL>",
        from: "#{@event.organiser} <<EMAIL>>",
        reply_to: @event.organiser_email,
        subject: subject_text
  end


  def set_reminder_data
    @payment_reminder = true
    @amount_to_pay = @event_booking.booking_cost[:cost]
    @amount_outstanding = @amount_to_pay - @event_booking.booking_paid
  end

  def send_cancellation(event_booking_id, send_invoice = false)
    @event_booking = EventBooking.includes(:registered_user, event: [:email_templates, :packages], package_bookings: [:package]).find_by_id(event_booking_id)

    @user = @event_booking.registered_user

    setup_initial_data()

    # Duplicate retrieval, needed as these package_bookings will be deleted
    @packages = @event_booking.event.packages

    # @refund_status = event_cancellable?
    # TODO need to have a proper refund information
    @refund_status = false

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    # To send a refund invoice
    if (send_invoice && @event.chargeable)
      @package_bookings =  @event_booking.package_bookings
      @payment = @event_booking.booking_payments.last
      @vatable = @event.vatable
      @vatexc = false
      attachments["invoice.pdf"] = WickedPdf.new.pdf_from_string(render_to_string(:pdf => 'inv', :template => 'event_bookings/invoice.erb', layout: 'pdf.erb'),
                                                               :dpi => '300', :page_size => "A4")
    end

    if @event.image1
      @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
    end
    if @event.sponsor_view_email
      @aws_sponsor_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/sponsors/#{@event.id.to_s}/"
    end

    mail to: @user.email.strip,
          bcc: "<EMAIL>",
          from: "#{@event.organiser} <<EMAIL>>",
          reply_to: @event.organiser_email,
          subject:  "Cancellation of #{@event.title}."
  end

  def send_congratulations(event_booking_id)
    @event_booking = EventBooking.includes(:registered_user, event: [:email_templates, :packages], package_bookings: [:package]).find_by_id(event_booking_id)

    @user = @event_booking.registered_user

    setup_initial_data()

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    mail to: @event.organiser_email,
         bcc: "<EMAIL>",
         subject: "Congratulations, #{@user.forename} #{@user.surname} has signed up to #{@event.title}."
  end

  def send_payment_failure(event_booking_id)
    @event_booking = EventBooking.includes(:registered_user, :event).find_by_id(event_booking_id)

    @user = @event_booking.registered_user

    @event = @event_booking.event

    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"

    if @event.image1
      @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
    end

    @event_address = @event.event_address

    mail to: @user.email,
    cc: "<EMAIL>",
    bcc: "<EMAIL>",
    subject: "Your booking to #{@event.title} has been cancelled, as your payment has failed."
  end

  def send_waiting_list(delegate)
    email = delegate.email
    @event = Event.find_by_id(delegate.event_id)
    @event_address = @event.event_address
    event_booking = EventBooking.where(waiting_list_id: delegate.id)
    @uuid = event_booking.first.uuid


    @aws_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['APP_IMAGE_BUCKET']}/"


    if @event.image1
      @logo_url = "https://s3-eu-west-1.amazonaws.com/#{ENV['S3_BUCKET']}/#{@event.id.to_s}/#{@event.image1}"
    end

    mail to: email,
         bcc: "<EMAIL>",
         reply_to: @event.organiser_email,
         subject: "Congratulations, a space has opened at #{@event.title}."
  end

  private

  def event_cancellable?
    (@event_booking.cancelled_at)
  end

  def setup_initial_data
    @event = @event_booking.event
    @legal_terms = @event.legal_term || @event.organisation.legal_term
    @event_address = @event.event_address
    @user = @event_booking.registered_user

    @packages = @event_booking.packages.order(:id)

    @sponsors = @event.sponsors || []

    # Only want to show fees if they are exclusive; ES they aren't any more
    @fees = 0
    # @fees = if @event.fees_pass_on && @event_booking.payment_type == 'card'
    #           if @event_booking.refunded?
    #             @event_booking.booking_fees_refunded_card
    #           else
    #             # Show un-refunded fees only
    #             @event_booking.booking_fees
    #           end
    #         else
    #           if @event_booking.app_fees_total.present?
    #             @event_booking.app_fees_total / 100
    #           else
    #             0
    #           end
    #         end

    @user_responses = @event_booking.registered_user.registered_user_responses.includes(:registration_field)
    @user_sample = []
  end
end
