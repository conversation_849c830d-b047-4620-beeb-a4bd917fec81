class TicketGroup < ApplicationRecord
  acts_as_paranoid
  
  belongs_to :event
  has_many :packages, -> {order(:id)}

  accepts_nested_attributes_for :packages, allow_destroy: true

  before_destroy :check_package_bookings

  private

  def check_package_bookings
    self.packages.each do |pack|
      if pack.package_bookings.any?
        errors[:base] << "cannot amend or delete tickets where the tickets have bookings"
        return false
      end
    end
  end
end
