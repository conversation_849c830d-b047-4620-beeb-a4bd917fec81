class StripePayment
  require 'securerandom'

  # def self.make_payment_intent(event_booking, costs, update = false)

  #   costs_for_region = costs['standard']

  #   event_amount = costs_for_region['event_amount'].to_i

  #   event_fees = costs['event_fees'].to_i

  #   event = event_booking.event

  #   if event_booking.discount_code_id
  #     charge_metadata = {
  #       :discount_code_id => event_booking.discount_code_id,
  #       :discount_percentage => (event_booking.discount_percentage).to_s + "%",
  #       :cost_code => event.cost_code,
  #       :event_booking_id => event_booking.id,
  #       vat_amount: costs_for_region["vat_amount"],
  #       vat_included: costs_for_region["vat_included"],
  #       discount_amount: costs_for_region["discount_diff"]
  #     }
  #   else
  #     charge_metadata = {
  #       :cost_code => event.cost_code,
  #       :event_booking_id => event_booking.id,
  #       vat_amount: costs_for_region["vat_amount"],
  #       vat_included: costs_for_region["vat_included"],
  #       discount_amount: costs_for_region["discount_diff"]
  #     }
  #   end

  #   begin
  #     charge = Stripe::PaymentIntent.create({
  #                                             :amount => event_amount, # amount in pence
  #                                             :currency => 'gbp',
  #                                             :description => 'Payment for: ' + event.title,
  #                                             :receipt_email => event_booking.registered_user.email,
  #                                             :application_fee_amount => event_fees,
  #                                             :metadata => charge_metadata,
  #                                           },
  #                                           { idempotency_key: SecureRandom.uuid,
  #                                             :stripe_account => stripe_user_id(event) }
  #     )

  #     return { payment_passed: true, card_error_message: nil, client_secret: charge.client_secret, stripe_key: stripe_user_id(event) }
  #   rescue Stripe::InvalidRequestError => stripe_error
  #     Rollbar.error(stripe_error, 'Stripe Invalid Request')
  #     event_booking.update(payment_status: :payment_failed) if event_booking.payment_type == 'card'
  #     return { payment_passed: false, card_error_message: stripe_error.message }
  #   rescue Stripe::CardError => card_error
  #     Rollbar.error(card_error, 'Card error')
  #     event_booking.update(payment_status: :payment_failed) if event_booking.payment_type == 'card'
  #     return { payment_passed: false, card_error_message: card_error.message }
  #   rescue Exception => e
  #     Rollbar.error(e, 'Payment Error non-stripe')
  #     event_booking.update(payment_status: :payment_failed) if event_booking.payment_type == 'card' && event_booking.booking_payments.blank?
  #     return { payment_passed: false, card_error_message: e.message }
  #   end

  # end

  # def self.stripe_complete_payment(event_booking, payment_intent_id)
  #   event = event_booking.event

  #   paymentIntent = Stripe::PaymentIntent.retrieve({
  #                                                    id: payment_intent_id,
  #                                                    expand: ['charges'],
  #                                                  }, { :stripe_account => stripe_user_id(event) })


  #   if paymentIntent && paymentIntent.charges.any?

  #     charge = paymentIntent.charges.first

  #     transactionDetails = getStripeBalanceTransaction(charge, event)

  #     if transactionDetails
  #       fee_details = transactionDetails.fee_details
  #       if fee_details.first[:type] == "stripe_fee"
  #         stripe_fee_details = fee_details.first
  #         app_fee_details = fee_details.last
  #       else
  #         stripe_fee_details = fee_details.last
  #         app_fee_details = fee_details.first
  #       end
  #       app_fee_amount = (app_fee_details.amount.to_f / 100)
  #       stripe_fee_amount = (stripe_fee_details.amount.to_f / 100)
  #       net_amount = (transactionDetails.net.to_f / 100)
  #       gross_amount = (transactionDetails.amount.to_f / 100)
  #       total_fees = (transactionDetails.fee.to_f / 100)

  #       event_booking.booking_payments.create!(registered_user_id: event_booking.registered_user_id,
  #                                              event_id: event.id,
  #                                              stripe_charge_id: charge.id,
  #                                              amount: gross_amount,
  #                                              total_fees: total_fees,
  #                                              application_fees: app_fee_amount,
  #                                              stripe_fees: stripe_fee_amount,
  #                                              net_amount: net_amount,
  #                                              gross_amount: gross_amount,
  #                                              fees_included: !event.fees_pass_on?,
  #                                              vat_amount: paymentIntent.metadata['vat_amount'],
  #                                              vat_included: paymentIntent.metadata['vat_included'],
  #                                              discount_amount: paymentIntent.metadata['discount_diff'],
  #                                              payment_datetime: DateTime.now,
  #                                              payment_type: :stripe)

  #       event_booking.update_column(:payment_status, :paid)
  #       return true
  #     end
  #     return false
  #   end
  #   return false
  # end

  def self.get_charge(event_booking, payment_id)
    return Stripe::Charge.retrieve({id: payment_id}, { stripe_account: stripe_user_id(event_booking.event) })
  end

  def self.get_charge_with_balance_transaction(event_booking, payment_id)
    return Stripe::Charge.retrieve({id: payment_id, expand: ['balance_transaction']}, { stripe_account: stripe_user_id(event_booking.event) })
  end

  def self.get_payment_intent_with_charges(payment_intent_id, event_booking)
    return Stripe::PaymentIntent.retrieve(payment_intent_id, { stripe_account: stripe_user_id(event_booking.event), expand: ['charges'] })
  end

  def self.get_payment_intent_with_charges_and_balance_transaction(payment_intent_id, event_booking)
    return Stripe::PaymentIntent.retrieve(payment_intent_id, { stripe_account: stripe_user_id(event_booking.event), expand: ['charges.balance_transaction'] })
  end

  def self.get_intent(event_booking, intent_id)
    return Stripe::PaymentIntent.retrieve(
      {
        id: intent_id,
        expand: ['latest_charge.balance_transaction'],
      },
      { stripe_account: stripe_user_id(event_booking.event) })
  end

  def self.get_receipt_url(event_booking, payment_id)
    payment = Stripe::Charge.retrieve({id: payment_id}, { stripe_account: stripe_user_id(event_booking.event) })
    return payment.receipt_url
  end

  def self.stripe_refund! charge_id, event
    Stripe::Refund.create({
                            charge: charge_id,
                            refund_application_fee: true
                          },
                          {
                            stripe_account: stripe_user_id(event)
                          })
  end

  def self.getStripeBalanceTransaction charge, event
    begin
      Stripe::BalanceTransaction.retrieve({
                                            id: charge.balance_transaction
                                          },
                                          {
                                            stripe_account: stripe_user_id(event)
                                          }
      )
    rescue Exception => e
      return nil
    end
  end

  def self.stripe_user_id(event)
    event.organisation&.stripe_account&.stripe_user_id
  end

  # NEW STUFF

  def self.get_payment_intent(event_booking, intent_id)
    begin
      Stripe::PaymentIntent.retrieve(
        intent_id,
        { :stripe_account => stripe_user_id(event_booking.event) }
      )
    rescue Stripe::CardError => e
      # Error code will be authentication_required if authentication is needed
      puts "Error is: \#{e.error.code}"
      payment_intent_id = e.error.payment_intent.id
      payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
      puts payment_intent.id
    end
  end

  def self.create_payment_intent(event_booking, amount)
    begin
      description = "Eventstop payment for: " + event_booking.event.title
      description += " This payment intent was initialised on #{Rails.env}" if !Rails.env.production?
      customer = getCustomer(event_booking)
      Stripe::PaymentIntent.create({
          amount: amount, # amount in pence
          currency: 'gbp',
          description: description,
          receipt_email: event_booking.registered_user.email,
          application_fee_amount: calculate_fee_from_amount(event_booking.event, amount),
          metadata: {application: 'Eventstop', :cost_code => event_booking.event.cost_code, :event_booking_id => event_booking.id} ,
          automatic_payment_methods: {enabled: true},
          # payment_method_types: ['card', 'bacs_debit'],
          customer: customer['id'],
        },
        {
          idempotency_key: SecureRandom.uuid,
          stripe_account: stripe_user_id(event_booking.event)
        })
    rescue Stripe::CardError => e
      # Error code will be authentication_required if authentication is needed
      puts "Error is: \#{e.error.code}"
      payment_intent_id = e.error.payment_intent.id
      payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
      puts payment_intent.id
    end
  end

  def self.update_payment_intent(payment_intent_id, event_booking, amount)
    amountInPence = (amount * 100).to_i
    begin
      Stripe::PaymentIntent.update(payment_intent_id,
        {amount: amountInPence},
        {:stripe_account => stripe_user_id(event_booking.event)}
      )
    rescue Stripe::CardError => e
      # Error code will be authentication_required if authentication is needed
      puts "Error is: \#{e.error.code}"
      payment_intent_id = e.error.payment_intent.id
      payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
      puts payment_intent.id
    end
  end

  def self.cancel_payment_intent(payment_intent_id, event_booking)
    begin
      Stripe::PaymentIntent.cancel(payment_intent_id,
        {},
        {:stripe_account => stripe_user_id(event_booking.event)}
      )
    rescue Stripe::CardError => e
      # Error code will be authentication_required if authentication is needed
      puts "Error is: \#{e.error.code}"
      payment_intent_id = e.error.payment_intent.id
      payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
      puts payment_intent.id
    end
  end

  def self.calculate_fee_from_amount(event, amount)
    fees = Fees.active_fees
    # get fee percentage of amount and add to flat rate, return in pence
    if event.charity?
      ( ((fees.hg_fees_charity.to_f / 100) * amount) + (fees.hg_fees_charity_add * 100) ).to_i
    else
      ( ((fees.hg_fees.to_f / 100) * amount) + (fees.hg_fees_add * 100) ).to_i
    end
  end

  def self.getCustomer(event_booking)
    email = event_booking.registered_user.email
    # organisation = "ID: #{ event_booking.event.organisation.id}. Name: #{event_booking.event.organisation.name}"
    # event = "ID: #{event_booking.event.id}. Name: #{event_booking.event.name}"
    # description = "Manager at Organisation #{organisation}, Business Unit #{bu}"


    # should only be one customer per email
    customer = Stripe::Customer.list({email: email},{stripe_account: stripe_user_id(event_booking.event) }).first

    if customer.nil?
      customer = Stripe::Customer.create({
        email: email,
        # description: description,
        name:  event_booking.registered_user.name,
      },
      {
        stripe_account: stripe_user_id(event_booking.event) ,
      })
    end
    return customer
  end

end
