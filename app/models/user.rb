class User < ActiveRecord::Base
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :confirmable

  has_secure_token

  has_one :stripe_account
  has_many :events

  # Association for organisation ownership
  has_one :owned_organisation, class_name: 'Organisation', foreign_key: 'owner_id', dependent: :destroy

  # Association for organisation membership
  belongs_to :organisation, optional: true

  has_one :event_stop_user

  belongs_to :contact, optional: true
  accepts_nested_attributes_for :contact

  attr_accessor :terms_and_conditions, :organisation_name

  # Custom method to get organisation through contact (legacy behavior)
  # def contact_organisation
  #   c = self.contact
  #   return nil unless c

  #   mapping = {"Organisation" => "HgOrganisation", "Hotel" => "HgHotel", "Chain" => "HgChain"}
  #   org = Organisation.where(:parent_type => mapping[c.parent_type], :parent_id => c.parent_id).first
  #   if org.blank? && c.onestop_parent.present?
  #     org = Organisation.create(:parent_type => mapping[c.parent_type], :parent_id => c.parent_id, :name => c.onestop_parent.name)
  #   elsif org.blank?
  #     raise "User Contact has no Parent"
  #   end
  #   org
  # end

  # Override organisation method to use direct association if available, fallback to contact
  # def organisation
  #   # Use direct association if organisation_id is present
  #   return super if organisation_id.present?

  #   # Fallback to contact-based organisation lookup for legacy users
  #   contact_organisation if contact.present?
  # end

  def email_and_org_id
    if contact.present? && organisation.present?
      email + ' ' + organisation.name + ' ' + organisation.id.to_s
    end
  end

  # def is_an_administrator?
  #   role == "ADMIN"
  # end

  # def is_a_client?
  #   ["BOOKER", "MANAGER"].include? role
  # end

  # def is_booker?
  #   role == "BOOKER"
  # end

  # def is_manager?
  #   role == "MANAGER"
  # end

  # def is_a_supplier?
  #   ["CHAIN", "CLUSTER", "HOTEL"].include? role
  # end

  # TODO for local test only
  # def advanced_events
  #   super
  #   true
  # end

  # def free_events
  #   true
  # end

end
