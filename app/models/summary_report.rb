class SummaryReport < ApplicationRecord
  self.primary_key = 'id'
  belongs_to :organisation

  scope :filter_by_client, -> (org_id) { where("client_id = ?", org_id) }
  scope :filter_date_after, -> (date) { where("ev_from >= ?", Time.parse(date).beginning_of_day).or(where("ev_from < ? and ev_to >= ?", Time.parse(date).beginning_of_day, Time.parse(date).beginning_of_day)) }
  scope :filter_date_before, -> (date) { where("ev_to <= ?", Time.parse(date).end_of_day).or(where("ev_to > ? and ev_from <= ?", Time.parse(date).end_of_day, Time.parse(date).end_of_day)) }
  scope :filter_by_event_name, -> (name) { where("event_name = ?", name) }
  scope :is_live, -> { where('is_live = true') }
  scope :expired, -> { where('ev_to < ?', Date.today) }
  scope :upcoming, -> { where('ev_to >= ?', Date.today) }

  def readonly?
    true
  end

  def self.export_as_csv(summary_data, grand_totals, exporter)
    financials = summary_data
    totals = grand_totals

    header_attributes = [exporter == "client" ? "Events" : "Client", "Forecasted Ticket Revenue (If all tickets are full price)", "Stripe Fees", "Application Fees", "Total Fees", "Net Revenue",  "Gross Revenue"]

    sales_details_array = []
    sales_details_array << ['Sales Summary Report']
    sales_details_array << header_attributes

    if financials
      financials.each do |f|
        sales_details_array << [exporter == "client" ? f[:name] : f[:client_name], f[:forecast], f[:profit], f[:stripe], f[:hg_fees], f[:total_fees], f[:revenue]]
      end
      sales_details_array << ["Grand Total", totals[:forecast], totals[:profit], totals[:stripe], totals[:hg_fees], totals[:total_fees], totals[:revenue]]
    end

    SummaryReport.generate_csv(sales_details_array)
  end

  def self.generate_csv(details_array)
    CSV.generate(headers: true) do |csv|
      details_array.each do |arr|
        csv << arr
      end
    end
  end

end	
