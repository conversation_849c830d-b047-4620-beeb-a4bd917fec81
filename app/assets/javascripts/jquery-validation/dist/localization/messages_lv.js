(function( factory ) {
	if ( typeof define === "function" && define.amd ) {
		define( ["jquery", "../jquery.validate"], factory );
	} else if (typeof module === "object" && module.exports) {
		module.exports = factory( require( "jquery" ) );
	} else {
		factory( jQuery );
	}
}(function( $ ) {

/*
 * Translated default messages for the jQuery validation plugin.
 * Locale: LV (Latvian; latviešu valoda)
 */
$.extend( $.validator.messages, {
	required: "Šis lauks ir obligāts.",
	remote: "<PERSON><PERSON><PERSON><PERSON>, pārbaudiet šo lauku.",
	email: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu e-pasta adresi.",
	url: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu URL adresi.",
	date: "<PERSON>ūd<PERSON>, ievadiet derīgu datumu.",
	dateISO: "<PERSON>ūd<PERSON>, ievadiet derīgu datumu (ISO).",
	number: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu numuru.",
	digits: "<PERSON><PERSON><PERSON><PERSON>, ievadiet tikai ciparus.",
	creditcard: "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu kredītkartes numuru.",
	equalTo: "<PERSON>ū<PERSON><PERSON>, ievadiet to pašu vēlreiz.",
	extension: "Lūdzu, ievadiet vērtību ar derīgu paplašinājumu.",
	maxlength: $.validator.format( "Lūdzu, ievadiet ne vairāk kā {0} rakstzīmes." ),
	minlength: $.validator.format( "Lūdzu, ievadiet vismaz {0} rakstzīmes." ),
	rangelength: $.validator.format( "Lūdzu ievadiet {0} līdz {1} rakstzīmes." ),
	range: $.validator.format( "Lūdzu, ievadiet skaitli no {0} līdz {1}." ),
	max: $.validator.format( "Lūdzu, ievadiet skaitli, kurš ir mazāks vai vienāds ar {0}." ),
	min: $.validator.format( "Lūdzu, ievadiet skaitli, kurš ir lielāks vai vienāds ar {0}." )
} );
return $;
}));