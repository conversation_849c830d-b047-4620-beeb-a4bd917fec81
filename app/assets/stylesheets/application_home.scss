@import 'base_styles';
@import "buttons";
@import 'style';
@import "events";
//@import "bootstrap";
@import 'bootstrap_overrides';
@import "font-awesome";
@import "splashscreen";
@import 'cards';
@import 'dialog';
@import 'navbar';
@import 'alertbar';
@import 'tile';
@import 'dropdown';

.uib-datepicker-popup {
  z-index: 9999999 !important;
}
.container-fluid {
  padding-left: 15px;
}
.panel-heading a:after {
  font-family: 'Glyphicons Halflings';
  content: "\e114";
  float: right;
  color: grey;
}
.panel-heading a.collapsed:after {
  content: "\e080";
}
.panel-heading-orange a:after {
  font-family: 'Glyphicons Halflings';
  content: "\e114";
  float: right;
  color: grey;
}
.panel-heading-orange a.collapsed:after {
  content: "\e080";
}

.masthead {
  height: 200px;
  background-image: image-url("HG-Feedback-800x200px.png");
  background-repeat: no-repeat;
  margin-top: 10px;
  margin-bottom: 20px;
}

.buttonlike {
  cursor: pointer;
}

.form-group.required label:after {
  content: "*";
  color: #FF0000;
}
.refunded {
  text-decoration: line-through;
}

.no-gutter > [class*='col-'] {
  padding-left:0 !important;
}
hr.fancy-line {
  border: 0;
}
hr.fancy-line:before {
  top: -0.5em;
  height: 1em;
}

hr.fancy-line:before {
  content: '';
  position: absolute;
  width: 100%;
}

hr.fancy-line, hr.fancy-line:before {
  background: radial-gradient(ellipse at center, rgba(0,0,0,0.1) 0%,rgba(0,0,0,0) 75%);
}
