@import 'pricing';
[class*="fontawesome-"]:before {
  font-family: 'FontAwesome', sans-serif;
}

h1,
h2,
h3,
h4 {
  font-family: Helvetica, Arial, sans-serif;
  font-weight: 900;
}

h5,
h6 {
  font-family: 'Roboto', sans-serif;
}

/*body {
  font-family: 'Open Sans', sans-serif !important;
} */

input {
  border: 0;
  color: inherit;
  font: inherit;
  margin: 0;
  outline: 0;
  padding: 0;
  transition: background-color 0.3s;
}

.heading {
  margin: 20px 0;
}

.pics {
  display: flex;
  width: 100%;
  height: auto;
  clear: both;
  margin-bottom: 20px;
  padding: 0 10px;
}

.staffTable {
  background-color: #f7f7f7;
  margin-left: 1.5em;
  padding-top: 2em;
  padding-bottom: 2em; 
}

.pad {
  padding: 0 10px;
  max-width: 25%; 
}

//The following class is not used, but may be if popovers are added in future
.pop {
  border: solid 2px black;
  height: auto;
  min-height: 16em;
  width: 100%;
  max-width: 16em;
  box-sizing: border-box;
}

// @mixin team_pods($image1, $image2) {
//   height: 100%;
//   width: 100%;
//   display: inline-block;
//   box-sizing: border-box;
//   background: image-url($image1);
//   background-size: 100% 100%;
//   margin-right: 10px;
//   margin-bottom: 10px;
//   -ms-interpolation-mode: bicubic !important;
//   &.displayactive {
//     display: block;
//     // background-image: linear-gradient(rgba(255, 149, 0, 0.3), rgba(255, 149, 0, 0.3)), image-url($image2);
//     background-image: image-url($image2);
//     background-size: 100% 100%;
//     background-position: center;
//     border: solid 2px black;
//     z-index: 99;
//   }
// }

// .amanda {
//   @include team_pods("profiles/Amanda1.jpg", "profiles/Amanda2.jpg")
// }

// .victoria {
//   @include team_pods("profiles/Victoria1.jpg", "profiles/Victoria2.jpg")
// }

// .claire {
//   @include team_pods("profiles/Claire1.jpg", "profiles/Claire2.jpg")
// }

// .eamon {
//   @include team_pods("profiles/Eamon1.jpg", "profiles/Eamon2.jpg")
// }

// .emma {
//   @include team_pods("profiles/Emma1.jpg", "profiles/Emma2.jpg")
// }

// .Rebecca {
//   @include team_pods("profiles/Rebecca1.jpg", "profiles/Rebecca2.jpg")
// }

// .Emily{
//   @include team_pods("profiles/Emily1.jpg", "profiles/Emily2.jpg")
// }

// .lindsay {
//   @include team_pods("profiles/Lindsay1.jpg", "profiles/Lindsay2.jpg")
// }

// .Sarah {
//   @include team_pods("profiles/Sarah1.jpg", "profiles/Sarah2.jpg")
// }

// .Sharon {
//   @include team_pods("profiles/Sharon1.jpg", "profiles/Sharon2.jpg")
// }

// .Ben {
//   @include team_pods("profiles/Ben1.jpg", "profiles/Ben2.jpg")
// }

// .Stacey {
//   @include team_pods("profiles/Stacey1.jpg", "profiles/Stacey2.jpg")
// }

// .Lopez {
//   @include team_pods("profiles/Lopez1.jpg", "profiles/Lopez2.jpg")
// }

// .SarahS {
//   @include team_pods("profiles/SarahS1.jpg", "profiles/SarahS2.jpg")
// }

// .Nat {
//   @include team_pods("profiles/Nat1.jpg", "profiles/Nat2.jpg")
// }

// .James {
//   @include team_pods("profiles/James1.jpg", "profiles/James2.jpg")
// }

// .Karen {
//   @include team_pods("profiles/Karen1.jpg", "profiles/Karen2.jpg")
// }

// .Renata {
//   @include team_pods("profiles/Renata1.jpg", "profiles/Renata2.jpg")
// }

// .Rachel {
//   @include team_pods("profiles/Rachel1.jpg", "profiles/Rachel2.jpg")
// }

.header-image {
  display: block;
  width: 100%;
  text-align: center;
  background: no-repeat center center scroll;
  background-image: image-url("splashscreen-header.jpg");
  -webkit-background-size: cover;
  -moz-background-size: cover;
  background-size: cover;
  -o-background-size: cover;
  z-index: 99;
}

.header-textbox {
  background: rgba(0, 0, 0, 0.4);
}

.header-textbox-inner {
  background: rgba(0, 0, 0, 0);
}

.iframeloading {
  background: no-repeat center center scroll;
  background-image: image-url("ajax-loader.gif");
}

.full-width-image-1 {
  display: block;
  width: 100%;
  //height: 24em;
  text-align: right;
  background: no-repeat center center scroll;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  background-size: cover;
  -o-background-size: cover;
  z-index: 99;
}

.headline {
  padding: 7.5% 5%;
}

.hover-orange li a:hover {
  background-color: #ff9500;
}

.hover-blue li a:hover {
  background-color: #242c33;
}

body,
html {
  width: 100%;
  height: 100%;
}

body {
  margin: 0; // font-size:100%;
  background: rgba(14, 24, 33, 0.95);
  font-family: 'Sentinel A', 'Sentinel B', Georgia, serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 100px;
  background: #7f8c8d;
  display: inline-block;
  text-align: center;
  cursor: pointer;
}

//Payments info with fees
.circlepayment {
  position: relative;
  display: inline-block;
  width: 100%; // max-width: 600px;
  height: 0;
  padding: 13vw 0;
  border-radius: 50%;
  font-family: Helvetica, Arial Black, sans;
  text-align: center;
}

@media only screen and (max-device-width: 736px) {
  .circlepayment {
    position: relative;
    display: inline-block;
    width: 40%;
    max-width: 600px;
    height: 0;
    padding: 18vw 0;
    border-radius: 50%;
    font-family: Helvetica, Arial Black, sans;
    font-size: 1em;
    text-align: center;
  }
}

.iframevideo {
  position: relative;
  padding-bottom: 34.25%;
  margin-bottom: 5%;
  max-width: 768px;
  max-height: 432px;
  padding-top: 25px;
  height: 0;
}

.iframevideo iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@media (max-width: 640px) {
  .iframevideo {
    position: relative;
    padding-bottom: 56.25%;
    /* 16:9 */
    max-width: 768px;
    max-height: 432px;
    padding-top: 25px;
    height: 0;
  }
  .iframevideo iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 768px;
    max-height: 432px;
    height: 100%;
  }
}

#outerdiv {
  width: 950px;
  height: 62em;
  overflow: hidden;
  position: relative;
}

#inneriframe {
  position: absolute;
  top: -41vh;
  left: -160px;
  width: 1280px;
  height: 1400px;
}