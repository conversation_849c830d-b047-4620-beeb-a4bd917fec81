$blue: #00B2F0;

.form-control {
  line-height: 1.5rem;
}

/* Small devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .main-nav {
    display: block;
  }
  .mobile-nav {
    display: none;
  }
}

@media (max-width: 768px) {
  .main-nav {
    display: none;
  }
  .mobile-nav {
    display: block;
  }
}

.pagination-first > a {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: $blue;
  background-color: white;
  border: thin solid #ddd;
}

.pagination-last > a {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: $blue;
  background-color: white;
  border: thin solid #ddd;
}

.pagination-page, .pagination-next, .pagination-prev {
   > a {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: $blue;
    background-color: white;
    border: thin solid #ddd;
  }
}

.nav-pills .nav-link.active, .show > .nav-pills .nav-link {
  color: white;
  background-color: #999;
}

.btn-primary {
  color: white !important;
  background-color: $blue;
  border-color: $blue;
}

.btn-primary:hover {
  color: white;
  background-color: #ff9f18;
  border-color: #ff9f18;
}

.btn-primary:active, .btn-primary.active, .show > .btn-primary.dropdown-toggle {
  color: white;
  background-color: $blue;
  background-image: none;
  border-color: $blue;
}

.btn-primary.disabled, .btn-primary:disabled, .btn-primary:not([disabled]):not(.disabled):active {
  color: white;
  background-color: $blue;
  background-image: none;
  border-color: $blue;
  opacity: 75%;
}

.btn-outline-primary {
  color: $blue;
  background-color: transparent;
  background-image: none;
  border-color: $blue;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: $blue;
  border-color: $blue;
}

.dropdown-item.active, .dropdown-item:active {
  color: white;
  background-color: $blue;
  background-image: none;
  border-color: $blue;
  opacity: 75%;
}

.btn-conf {
  color: white;
  background-color: #B7355F;
  border-color: #B7355F;
}

.btn-use-colour{
  top: -16px;
  position: relative;
  left: 10px;
}

.fade.in {
  opacity: 1;
}

// .modal.in .modal-dialog {
//   -webkit-transform: translate(0, 0);
//   -o-transform: translate(0, 0);
//   transform: translate(0, 0);
// }
//
// .modal-backdrop.in {
//   filter: alpha(opacity=50);
//   opacity: .5;
// }

.card {
  margin: 15px 0;
}

.modal-header {
  border-bottom: medium solid #fb9c40;
}

.hoverlink:hover {
  text-decoration: underline !important;
  cursor: pointer;
}

.hg-underline {
  border-bottom: 4px solid #fb9c40;
  background-color: white;
  font-size: 20px;
  font-weight: bolder !important;
}

.hg-topline {
  border-top: 4px solid #fb9c40;
}

.hg-topline-thin {
  border-top: 2px solid #fb9c40;
}

.hg-bottomline-thin {
  border-bottom: 2px solid #fb9c40;
}

.hg-blank-footer {
  border: 0;
  background-color: white;
}

.errormsg {
  background: maroon;
  color: white;
  padding: 5px;
  border-radius: 5px;
  margin-top: 15px;
  position: relative;
}

.errormsg:after {
  position: absolute;
  top: -10px;
  content: "";
  left: 20px;
  border-style: solid;
  border-color: transparent transparent maroon;
  border-width: 5px;
}

// Gives collpase and expand chevrons to collpsing panel headers
[aria-expanded="false"] i:before{
  content: "\f139";
}

[aria-expanded="true"] i:before{
  content: "\f13a";
}


/// Stof's overrides

@import url('https://fonts.googleapis.com/css?family=Montserrat:100,200,300,400,500,600,700,800,900|Roboto+Slab:100,300,400,700');


body{
  font-family: 'Montserrat', sans-serif !important;
  font-weight: 300 !important;
  color:#878787;
  padding-top: 77px;
}
h1,h2,h3,h4,h5,h6{
  font-family: 'Montserrat', sans-serif !important;
  font-weight: 600 !important;
}

header {
  background:#fff;
  display: none;
}
  .main-nav {
    display: none;
    justify-content: space-between;
    background: #fff;
    padding: 1rem 0;
  }
  .mobile-nav{
    background: #fff;
    padding: 1rem;
    position: relative;
    .navbar-toggler{
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
  }


    .built-logo {
      display: flex;
      width: 70%;
      align-items: center;
      .navbar-brand{
        display: flex;
        align-items: center;
        padding-top: 0;
        padding-bottom: 0;
        width: 50%;
        #logo{
          /*width: auto;
          height: 50px;  */
          width: 100%;
        }
      }
      #btybhg{
        padding-left: 10px;
        //border-left: 2px solid #ae1f5a;
        border-left: 2px solid #5b3768;
        width: 50%;
       img{
          max-width: 100%;
          //width: 100%;
        }
      }
    }
    .call-menu{
      .callus{
        text-align:right;
        font-size: 1.2rem;
        font-weight: 300;
        .number{
          color:$blue;
          font-weight: 600;
        }
      }
      #navbarNav{
        ul{
          li{
            a{
              padding: 0 7px;
              margin: 10px 0;
              border-right: 1px solid $blue;
              color:#000;
              font-size: 14px;
              font-weight: 400;
            }
            a#menuButton{
              cursor:pointer;
            }
          }
          li:last-child{
            a{
              border-right:none;
              padding: 0 0 0 10px;
            }
          }
        }
      }
    }

.carousel-item{
  max-height: 600px;
  max-height: 70vh;
  img{
    /*height: 100vh;
    max-height: 600px;
    object-fit: cover; */
    width: 100%;
    height: auto;
  }
}
.carousel-item.centered-image.carousel-item-left,
.carousel-item.centered-image.carousel-item-right,
.carousel-item.centered-image.active{
  display: flex;
}
.slidestrap{
  position: absolute;
  top: 45%;
  left:5%;
  right:5%;
  margin: 0px auto;
  text-align:center;
  background: rgba(255,255,255,.8);
  padding: 10px;
  font-size: .9rem;
  color: #000;
  border-radius: 20px 0 20px 0;
  font-weight: 700;
  font-family: 'Montserrat', serif !important;
  .orange{
    color:$blue;
  }
  a.confstop{
    color: #ad1a59;
  }

}
.slidestrap.charity{
  top: 15%;
  p{
    font-size: .8rem;
    line-height: 1rem;
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 500 !important;
    margin-bottom: 0;
  }
  .orange a{
    color: #13b09c;
  }
  img.cel{
    max-width: 70px;
    display: inline-block;
    margin-top: -3px;
  }
}

.evt-stop-banner{
  background:#004F59;
  color:#fff;
  text-align:center;
  font-weight:400;
  padding: 4rem 0;
  h2{
    text-transform: uppercase;
    color: $blue;
  }
  p{
    a{
      color:#fff;
      font-weight: bold;
      border-bottom: 2px solid #FFED00;
    }
    a:hover{
      border-bottom: 2px solid #fff;
    }
  }
  .row-buttons{
    justify-content: center;
    a{
      margin: 10px;
    }
  }
}

.evt-steps{
  //background:$blue;
  background: #004F59;
  color:#fff;
  padding:4rem 0;
  text-align:center;
  h2{
    margin: 0 0 4rem 0;
    color: $blue;
  }
  .steps{
    justify-content: space-around;
    font-weight: 600;
    i{
      font-size: 2rem;
      transform: rotate(90deg);
      margin: 2rem 0;
    }
    p{
      margin-bottom:0;
    }
  }
}

#splashscreen{
  padding: 2rem 0;
}
#splashscreen, #app{
  h2{
    margin: 0 0 2rem 0;
    //color:$blue;
    color:$blue;
    text-align:center;
  }
  .filter{
    background: #004F59;
    colour: #fff;
    border-radius:5px;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    .filters{
      flex-grow: 1;
      .filter-group{
        > div{
          flex-grow: 1;
        }
        .form-group{
          label{
            padding-bottom: calc(.375rem + 1px);
            margin-bottom: 0;
          }
        }
        label{
          color:#fff;
          font-weight:400;
        }
        input, select{
          border:none;
          border-radius:0;
          font-weight:600;
          color:#878787 !important;
        }

        .el-input__inner, .el-input__prefix, .el-icon-date, .el-icon-date:before, .el-input__inner::placeholder, input::placeholder, select::placeholder{
          color:#878787 !important;
          font-weight:600;
        }
        .el-date-editor.el-input, .el-date-editor.el-input__inner {
          width: 100%;
        }
        .el-input__inner{
          height: calc(2.25rem + 2px);
          padding-top: .375rem;
          padding-bottom: .375rem;
          line-height: 1.5;
        }
      }

    }
    .buttons-group{
      display:flex;
      flex-direction: column;
      text-align:center;
      #search-btn{
        background: none;
        padding:0;
        margin-bottom: 10px;
        border:none;
        i{
          font-size: 5rem;
          font-weight: 100;
        }
      }
      #search-btn:focus{
        box-shadow: none;
      }
      a{
        color:#fff;
        text-decoration: underline;
        font-weight: 400;
        cursor: pointer;
      }
    }
  }

  .events{
    h2{
      margin: 2rem 0;
    }
    .card-deck{
      display: flex;
      flex-direction: column;
      /*justify-content: space-between;*/
      .card{
        box-shadow: none;
        border-radius: 0;
        border: thin solid #878787;
        .card-header-img{
          height: 220px;
          display: flex;
          overflow: hidden;
          justify-content: center;
          align-items: center;
          background: #f5f5f5;
          .card-img.card-img-top{
            border-radius:0;
            flex-shrink:0;
            min-width:100%;
            min-height:100%
          }
          .card-img.card-img-top.card-img-missing{
            width: 50%;
            min-height: auto;
            min-width: auto;
          }
        }

        .card-body{
          padding: 15px 15px 60px 15px;
          h4.month{
            text-transform: uppercase;
            margin: 0;
          }
          h4.month{
            font-size: 1.2rem;
          }
          h4.date{
            font-size: 2.1rem;
          }
          h5.evt-title{
            font-weight: 700;
            color:$blue;
          }
          h6{
            font-weight:500;
          }
          .socials{
            position: absolute;
            bottom: 15px;
            right: 15px;
          }
        }
      }
    }
    .pagination{
      justify-content: center;
      margin: 15px 0 0 0;
    }
  }
  .card-extras{
    display:flex;
    flex-direction: row;
    flex-wrap: wrap;
    .card{
      width:100%;
      fieldset{
        margin: 0;
      }
    }
  }
}


.video{
  text-align:center;
  h2{
    color:$blue;
    margin: 4rem 0;
  }
  .videobg{
    background: url('video-bg.jpg')  left top no-repeat;
    background-size: cover;
    margin: 0px auto !important;
  }
  .videocontainer{
    /*display:flex;
    width:80%;
    height:100%;
    background-color: pink;
    justify-content: center;
    align-items: center; */

    background: url('video-bg.jpg') center center no-repeat;
    background-size: contain;
    /*padding: 15% 15% 21% 15%;  */

    position: relative;
    overflow: hidden;
    padding-top: 56.25%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    width: 100%;
    flex: 1;
    .fluid-width-video-wrapper{
      padding-top: 56%;
    }
  }
  #videoframe{
    /*background: url('video-bg.jpg') center center no-repeat;
    background-size: cover; */
   /* width: 80%;
    padding: 5rem;   */
    position: absolute;
    top: 9%;
    width: 65%;
    height: 70%;
    border: 0;
    flex-grow: 1;
    justify-content: center;
  }
}
@media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
  .video{
    .videocontainer{
      #videoframe{
        left: 17.5%;
      }
    }
  }
}
.features{
  background:#ececec;
  text-align:center;
  padding: 4rem 0;
  h2{
    color:$blue;
    margin-bottom: 4rem;
  }
  .key-features{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    > div {
      background:#fff;
      width: 100%;
      padding: 3rem;
      margin-bottom: 1%;
      h4{
        color:$blue;
        margin: 1rem 0 0 0;
      }
    }
    > div.cs{
      padding:0;
      a{
        display:block;
        padding: 3rem;
        h4{
          color:#ad1a59;
        }
      }
    }
  }
}

.clients{
  text-align:center;
  padding: 4rem 0;
  h2{
    color:$blue;
    margin: 0 0 4rem 0;
    text-transform: uppercase;
  }
  .client-logos{
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    > div{
      padding: 1rem;
    }
  }
}



.footer-banner{
  padding: 4rem 0;
  //background: $blue;
  background: #4a4a4a;
  font-size: 2rem;
  color:#fff;
  text-align: center;
  font-weight: 400;
  .call{
    font-weight: 700;
  }
}

.footer{
  padding: 4rem 0;
  .footer-groups{
    > div{
      font-weight: 400;
      margin-bottom: 2rem;
      h5{
        text-transform: uppercase;
        font-weight: 700;
        color: $blue;
        margin-bottom: 1rem;
      }
      ul{
        list-style:none;
        padding:0;
        margin:0;
        li{
          padding: 0;
          margin: 0;
          a{
            color:#878787;
          }
          a:hover, a:focus{
            color:$blue;
          }
        }
      }
      img{
        max-width: 90%;
        height: auto;
        margin-top: 5px;
      }
      .footer-icons{
        margin-bottom: 1rem;
        a{
          width: 50px;
          height: 50px;
          border-radius: 50%;
          border: 4px solid #ccc;
          text-align: center;
          display: inline-block;
          line-height: 2.5rem;
          margin-right: .3rem;
        }
        i{
          font-family: FontAwesome;
          font-style: normal;
          font-size: 1.5rem;
        }
        .fa-facebook-f{
          color: #3b5998;
        }
        .fa-twitter{
          color: #00aced;
        }
        .fa-linkedin-in{
          color: #0077b5;
        }
        a:hover, a:focus{
          border: 4px solid #878787;
        }
      }
    }
  }
  .copyright{
    text-align:center;
    font-weight: 600;
    font-size: .8rem;
    padding: 3rem 0 2rem 0;
  }
}

.case-studies{
  display:flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 40px 0;
   > a.tile{
      width: 100%;
     margin-bottom: 40px;
   }
}

#new_user{
   div.error{
      position: absolute;
      z-index: 500;
    }
}

#app{
  .venue-event{
    .col-venue, .col-event{
      margin: 15px 0;
    }
    .col-venue{
      order:2;
    }
    .col-event{
      order:1;
    }
    .card{
      margin: 0;
    }
  }
  table.datesEqual{
    tr{
      td{
        p{
          white-space: nowrap;
          display:inline-block;
        }
      }
    }
  }
  .table-tickets{
    display:inline-table;
    th{
      display:none;
    }
    td.tickets{
      border-top: 3px solid #dee2e6;
    }
    td.ticket-date-time {
      font-size: 90% !important;
      font-weight: 500;
    }
    td{
      font-size:.9rem;
      padding: .5rem;
      vertical-align: middle;
    }
    td[data-label] {
      position: relative;
      padding-left: 155px;
      min-height: 28px;
      display:block;
    }
    td[data-label]:before {
      content: attr(data-label);
      position: absolute;
      left: 5px;
      width: 148px;
      font-weight: bold;
    }
    td.hide-cell{
      display:none;
    }
    .ticket-select{
      min-height: 28px;
    }
    td.group-name{
      color:#000;
      font-weight: 600;
      background:#f5f5f5;
      min-height: 37px;
      span.gname{
        display:none;
      }
    }
    td.bsactualcoast{
      background:#fb9c40;
      color:#fff;
      font-weight: 600;
    }
  }
  .filter-btn{
    display:block;
  }

  .timer{
    display:flex;
    justify-content: center;
    margin: 1vh 0;
    span{
      min-width: 100px;
      text-align:center;
      color:#fff;
      font-weight: 600;
      padding: .5vh;
      border-radius: 3px;

      background: #67c23a;
      -webkit-animation-name: timer; /* Safari 4.0 - 8.0 */
      -webkit-animation-duration: 600s; /* Safari 4.0 - 8.0 */
      animation-name: timer;
      animation-duration: 600s;
    }
  }

  .attendees-select{
    width:-webkit-fill-available !important;
  }
  .cards-payment{
    display: flex;
    flex-wrap: wrap;
    .card{
      width:100%;
    }
    > .card{
      > .card-header{
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
    }
  }
  .summary{
    display: flex;
    flex-wrap: wrap;
    .card{
      width:100%;
}
    > .card{
      > .card-header{
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
    }
  }
}

.venue-search{
  strong{
    font-weight: 800;
  }
  img.assistant {
    max-width: 15%;
    min-width: 50px;
    margin: 0 20px 0 0;
    float: left;
  }
  img.cs-stop{
    height: 25px;
    margin-left: 5px;
  }
}

table.table-forecast{
  thead{
    tr.toprow{
      th.empty{
        border:none;
        background: transparent;
      }
      th{
        background: rgb(66, 74, 93);
        color: #fff;
        text-align:center;
        font-size:.8rem;
        vertical-align: middle;
      }
      th.nowrap{
        white-space: nowrap;
      }
    }
    tr.mainrow{
      th{
        font-weight:400;
        font-size: .7rem;
        background: rgba(66, 74, 93,.05);
        color: rgb(66, 74, 93);
      }
    }
  }
}
#reports, #summary-reports{
  margin-top: 0;
}
.reports#reports{
  ul.nav-tabs{
    li.nav-item{
      a.nav-link{
        color: rgb(66, 74, 93);
        font-weight: 600;
      }
      a.nav-link.active{
        color:$blue;
      }
    }
  }
  [aria-expanded="true"] i:before {
    content: "";
  }
  .el-icon-arrow-up:before {
    content: "\E605" !important;
  }
  .el-icon-date:before {
    content: "\E608" !important;
  }
  .el-icon-circle-close:before {
    content: "\E607" !important;
  }
}
.reports, .reports#summary-reports{
  div.group input, div.group select{
    width: 100%;
  }
}

@media screen and (min-width: 500px){
  .built-logo {
    width: 50%;
    #btybhg{
      /*img{
        max-width:inherit;
      } */
    }
  }
}
@media screen and (min-width:576px){
  .full-width-image-1{
    height: 268px;
  }
}
@media screen and (min-width:768px) {
  .mobile-nav {
    display: block;
  }
  .built-logo {
    width: 40%;
  }
  .slidestrap {
    padding: 30px;
    font-size: 1.2rem;
  }

  .slidestrap.charity {
    top: 20%;

    p {
      font-size: 1.1rem;
      line-height: 1.4rem;
    }

    img.cel {
      max-width: 100px;
      margin-top: -5px;
    }
  }
  .case-studies {
    > a.tile {
      width: 45%;
    }
  }
  #app {
    .venue-event {
      .col-venue {
        order: 1;

        .card {
          height: 100%;
        }
      }

      .col-event {
        order: 2;

        .card {
          height: 100%;
        }
      }
    }

    .card-extras {
      margin-left: -2%;
      margin-right: -2%;

      .card {
        width: 46%;
        margin: 2%;
      }
    }

  }
  .full-width-image-1{
    height: 362px;
  }
}
@media screen and (min-width: 769px){
  body{
    padding-top:0;
  }
  header{
    display:block;
    #navbarNav {
      display: block;
    }
  }
  .slidestrap{
    top:33%;
    left:10%;
    right:10%;
    font-size: 2rem;

}
  .evt-steps{
    .steps{
      display:flex;
      justify-content: space-around;
      font-weight: 600;
      align-items: center;
      i{
        font-size: 2rem;
        transform: rotate(0deg);
        margin: 0 2rem;
      }
    }
  }
  #splashscreen, #app{
    .filter {
      .filters{
        .filter-group {
          display: flex;
          > div {
            flex-grow: 1;
            margin-right: 5px;
          }
          > div:last-child {
            margin-right: 0;
          }
        }
      }
    }
    .events{
      .card-deck{
        flex-direction: row;
        padding: 0 15px;

        .card{
          width: 49%;
          margin-left: 0;
          margin-right: 2%;
          flex: inherit;
        }
        .card:nth-child(2n){
          margin-right: 0;
        }
      }
    }
  }
  .features{
    .key-features{
      > div {
        width: 49%;
        margin-bottom: 2%;
      }
    }
  }
  .footer{
    .footer-groups{
      display: flex;
      justify-content: space-between;
      > div{
        width: 24%;
        border-right:1px solid #878787;
        padding-right: 1%;
        margin-bottom: 0;
      }
    }
  }
}

@media screen and (min-width: 992px) {

  .slidestrap {
    left: 15%;
    right: 15%;
    font-size: 2.2rem;
  }
  .slidestrap.charity {
    top: 25%;

    p {
      font-size: 1.3rem;
      line-height: 1.6rem;
    }

    img.cel {
      max-width: 120px;
    }
  }
  #splashscreen, #app {
    .filter {
      flex-direction: row;

      .filters {
        margin-right: 2rem;
      }

      .filter-group:last-child {
        .form-group {
          margin-bottom: 0;
        }
      }
    }

    .buttons-group {
      padding-top: 31px;
    }

    .events {
      .card-deck {
        .card {
          width: 32.5%;
          margin-bottom: 1%;
          margin-right: 1.25%;
        }
        .card:nth-child(2n){
          margin-right: 1.25%;
        }
        .card:nth-child(3n){
          margin-right: 0;
        }
      }
    }
  }
  .features {
    .key-features {
      > div {
        width: 32.5%;
      }
    }
  }
  .built-logo {
    width: 35%;
  }

  .case-studies {
    > a.tile {
      width: 30%;
    }
  }
  #app {
    table.table-tickets {
      th {
        display: table-cell;
      }

      td {
        font-size: 1rem;
        padding: .75rem;
      }

      td.tickets {
        border-top: 1px solid #dee2e6;
      }

      td[data-label] {
        padding-left: 0.3rem;
        padding-left: 12px;
        display: table-cell;
      }

      td[data-label]:before {
        display: none;
      }

      td.hide-cell {
        display: table-cell;
      }

      .ticket-select {
        height: calc(2.25rem + 2px);
      }

      td.group-name {
        color: #000;
        font-weight: 600;
        background: #fff;

        span.gname {
          display: block;
        }
      }

    }

    .filter-btn {
      float: right;
    }

    .card-extras {
      margin-left: -1%;
      margin-right: -1%;

      .card {
        width: 31.33333%;
        margin: 1%;
      }
    }

  }
  .full-width-image-1{
    height: 488px;
  }
}

@media screen and (min-width: 1024px){
    .main-nav{
      display: flex;
      .call-menu {
        .callus {
          font-size: 1.6rem;
        }
      }
      .built-logo{
        width: 33%;
        .navbar-brand{
          #logo{
           /* width:auto;   */

            width: 100%;
            /*max-height: 100px; */
            max-width: 100%;
            height: inherit;
          }
        }
      }
    }
  .mobile-nav{
    display:none;
  }
}
@media screen and (min-width: 1200px){
  header{
    .main-nav{
      padding: 1rem 1rem;
      .call-menu {
        .callus {
          font-size: 2.4rem;
        }
      }
    }
  }
  .slidestrap{
    left:20%;
    right:20%;
    font-size: 2.5rem;
  }

  .slidestrap.charity{
    top: 25%;
    p{
      font-size: 1.6rem;
      line-height: 2rem;
    }
    img.cel{
      max-width: 150px;
    }
  }

  #app{
    .cards-payment{
      justify-content: space-between;
      > .card{
        width:48%;
      }
    }
    .summary{
      justify-content: space-between;
       > .card{
        width:48%;
      }
    }
  }
  .full-width-image-1{
    height: 583px;
  }
}

//override to remove double exclamation mark on the swal2 warning
.swal2-warning.swal2-animate-warning-icon .swal2-icon-text {
  //display:none;
}

// Manage Users

div.group{
  display:flex;
  justify-content: flex-start;
  margin-bottom: 1rem;
  input, select{
    width:19%;
    margin-right: 1.25%;
  }
  select:last-child{
    margin-right: 0;
  }
  > div{
    width:19%;
    margin-right: 1.25%;
  }
  > div:last-child{
    margin-right: 0;
  }
  .custom-select{
    height: 40px;
  }
  button{
    margin-right: 1.25%;
  }
  .group{
    margin-bottom: 0;
  }
}
div.group-left{
   > div.el-date-editor.el-input{
     width: 19%;
     input.el-input__inner{
       width: 100%;
     }
     .el-input__icon{
       height: auto;
     }
   }
}
div.group-filter{
  > div{
    width: auto;
  }
  > div.select{
    width:194px;
    margin-right:14px;
  }
}
#eventStatusFilter, #eventType{
  width:194px;
  margin-top: -2px;
}



.form-import-contacts{
  .el-date-editor.el-input, .el-date-editor.el-input__inner{
    width:100%;
    input.el-input__inner{
      width:100%;
    }
  }
}

#addinv, #collapseCsv, #collapseContacts{
  margin-bottom: 20px;
}

#collapseCsv, #collapseContacts{
  > .col-12{
    padding-left:0;
    padding-right:0;
    .card-deck{
      .card{
        margin-top: 0;
      }
    }
  }
}

table.tablemanageusers{
  tbody> tr > td{
    word-break: break-all;
  }
}

/* Safari 4.0 - 8.0 */
@-webkit-keyframes timer {
  0% {background-color: #67c23a;}
  50% {background-color: #ff9500;}
  100% {background-color: red;}
}

/* Standard syntax */
@keyframes timer {
  0% {background-color: #67c23a;}
  50% {background-color: #ff9500;}
  100% {background-color: red;}
}

.btn-group{
  .btn-yes, .btn-no{
    background: gray !important;
    border: none !important;
    color:#fff;
  }
  .btn-yes:hover, .btn-yes.active{
    background: #67c23a !important;
    color:#fff;
  }
  .btn-no:hover, .btn-no.active{
    background: red !important;
    color:#fff;
  }
}

.el-popover{
  word-break: break-word !important;
}

#autocomplete-results{
  position:absolute;
  border: thin solid #ced4da;
  border-radius: .25rem;
  list-style: none;
  padding: .375rem .75rem;
  width:100%;
  z-index:150;
  background:#fff;
  .autocomplete {
    position: relative;
  }


  .autocomplete-results {
    padding: 0;
    margin: 0;
    border: 1px solid #eeeeee;
    height: 120px;
    overflow: auto;
    width: 100%;
  }

  .autocomplete-result {
    list-style: none;
    text-align: left;
    padding: 4px 2px;
    cursor: pointer;
  }

  .autocomplete-result.is-active,
  .autocomplete-result:hover {
    background-color: #ff9500;
    color: white;
  }
}
