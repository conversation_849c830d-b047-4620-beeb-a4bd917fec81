@import "base_styles";

body {
  background-color: $white;
  font-family: 'Open Sans', sans-serif;
}

.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
  background-image: none !important;
  background-color: $hg_orange;
}

* {
  box-sizing: border-box;
}

input {
  border: 0;
  color: inherit;
  font: inherit;
  margin: 0;
  outline: 0;
  padding: 0;
  transition: background-color 0.3s;
}

.drop-box {
    background: #F8F8F8;
    border: 5px dashed #DDD;
    width: 280px;
    text-align: center;
    padding: 50px 10px;
    margin-left: 10px;
}

.drop-box-sponsor {
    background: #F8F8F8;
    border: 5px dashed #DDD;
    text-align: center;
    padding: 50px 10px;
    margin-left: 10px;
}

.up-buttons {
    float: right;
}

.drop-box.dragover {
    border: 5px dashed blue;
}

.drop-box.dragover-err {
    border: 5px dashed red;
}

%extend_1 {
  background-color: $seashell;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.form {
  input[type="password"] {
    width: 100%;
  }
  input[type="submit"] {
    width: 100%;
  }
  input[type="text"] {
    width: 100%;
  }
}

.form-login {
  color: $dark_grey;
  input[type="email"] {
    border-radius: 0.25rem;
    padding: 1rem;
    @extend %extend_1;
  }
  input[type="password"] {
    border-radius: 0.25rem;
    padding: 1rem;
    @extend %extend_1;
    &:focus {
      background-color: $seashell;
    }
    &:hover {
      background-color: $seashell;
    }
  }
  input[type="submit"] {
    border-radius: 0.25rem;
    padding: 1rem;
    background-color: $hg_orange;
    color: $stonewhite;
    font-weight: bold;
    text-transform: uppercase;
    &:focus {
      background-color: $hg_orange;
    }
    &:hover {
      background-color: $hg_orange;
    }
  }
  input[type="text"] {
    border-radius: 0.25rem;
    padding: 1rem;
    @extend %extend_1;
    &:focus {
      background-color: $seashell;
    }
    &:hover {
      background-color: $seashell;
    }
  }
  // label {
  //   border-radius: 0.25rem;
  //   padding: 1rem;
  //   background-color: $hg_orange;
  //   border-bottom-right-radius: 0;
  //   border-top-right-radius: 0;
  //   padding-left: 1.25rem;
  //   padding-right: 1.25rem;
  //   margin-bottom: 0;
  // }
}

.form_field {
  display: flex;
  margin-bottom: 1rem;
}

.box {
  width: 20px;
  height: 20px;
  border: thin solid #000;
  float: left;
  margin-right: 5px;
}

.box2 {
  width: 10px;
  height: 5px;
  float: left;
  margin-right: 15px;
}

/* Made by Jimmy Gillam */
$checked-color: #ff9500;
$unchecked-color: #000;
$checkbox-height: 20px;
$background-color: #eee;
$font-color: #efefef;
$duration: 0.4s;
/* Checkmark style starts */
@-moz-keyframes dothabottomcheck {
  0% {
    height: 0;
  }
  100% {
    height: $checkbox-height/2;
  }
}

@-webkit-keyframes dothabottomcheck {
  0% {
    height: 0;
  }
  100% {
    height: $checkbox-height/2;
  }
}

@keyframes dothabottomcheck {
  0% {
    height: 0;
  }
  100% {
    height: $checkbox-height/2;
  }
}

@keyframes dothatopcheck {
  0% {
    height: 0;
  }
  50% {
    height: 0;
  }
  100% {
    height: $checkbox-height * 1.2;
  }
}

@-webkit-keyframes dothatopcheck {
  0% {
    height: 0;
  }
  50% {
    height: 0;
  }
  100% {
    height: $checkbox-height * 1.2;
  }
}

@-moz-keyframes dothatopcheck {
  0% {
    height: 0;
  }
  50% {
    height: 0;
  }
  100% {
    height: $checkbox-height * 1.2;
  }
}

.checkbox {
  display: inline-block;
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
}
.checkbox label {
  cursor: pointer;
  padding-left: 0;
}
.checkbox input[type=checkbox] {
  opacity: 0;
  position: absolute;
  margin: 0;
  z-index: -1;
  width: 0;
  height: 0;
  overflow: hidden;
  left: 0;
  pointer-events: none;
}
.checkbox .checkbox-material {
  vertical-align: middle;
  position: relative;
  top: 3px;
}
.checkbox .checkbox-material:before {
  position: absolute;
  left: 8px;
  top: 2px;
  content: "";
  background-color: rgba(0, 0, 0, 0.5);
  height: 4px;
  width: 4px;
  border-radius: 100%;
  z-index: 1;
  opacity: 0;
  margin: 0;
}
.checkbox .checkbox-material .check {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid;
  border-radius: 2px;
  overflow: hidden;
  z-index: 1;
}
.checkbox .checkbox-material .check:before {
  position: absolute;
  content: "";
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  display: block;
  margin-top: -4px;
  margin-left: 6px;
  width: 0;
  height: 0;
  box-shadow: 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0 inset;
  -webkit-animation: checkbox-off 0.3s forwards ease-out;
          animation: checkbox-off 0.3s forwards ease-out;
}
.checkbox input[type=checkbox]:focus + .checkbox-material .check:after {
  opacity: 0.2;
}
.checkbox input[type=checkbox]:checked + .checkbox-material .check:before {
  box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
  -webkit-animation: checkbox-on 0.3s forwards ease-out;
          animation: checkbox-on 0.3s forwards ease-out;
}
.checkbox input[type=checkbox]:not(:checked) + .checkbox-material:before {
  -webkit-animation: rippleOff 700ms forwards ease-out;
          animation: rippleOff 700ms forwards ease-out;
}
.checkbox input[type=checkbox]:checked + .checkbox-material:before {
  -webkit-animation: rippleOn 700ms forwards ease-out;
          animation: rippleOn 700ms forwards ease-out;
}
.checkbox input[type=checkbox]:not(:checked) + .checkbox-material .check:after {
  -webkit-animation: rippleOff 700ms forwards ease-out;
          animation: rippleOff 700ms forwards ease-out;
}
.checkbox input[type=checkbox]:checked + .checkbox-material .check:after {
  -webkit-animation: rippleOn 700ms forwards ease-out;
          animation: rippleOn 700ms forwards ease-out;
}
.checkbox input[type=checkbox][disabled]:not(:checked) ~ .checkbox-material .check:before,
.checkbox input[type=checkbox][disabled] + .circle {
  opacity: 0.5;
}
.checkbox input[type=checkbox][disabled] + .checkbox-material .check:after {
  background-color: rgba(0, 0, 0, 0.84);
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}

.coloured .checkbox-material .check {
  color: #2FAEF8;
}
.coloured .checkbox-material:before {
  background-color: #2FAEF8;
}
.coloured input[type=checkbox]:checked + .checkbox-material .check {
  color: #2FAEF8;
}

@-webkit-keyframes checkbox-on {
  0% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 15px 2px 0 11px;
  }
  50% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px 2px 0 11px;
  }
  100% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
  }
}

@keyframes checkbox-on {
  0% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 15px 2px 0 11px;
  }
  50% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px 2px 0 11px;
  }
  100% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
  }
}
@-webkit-keyframes checkbox-off {
  0% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px, 0 0 0 0 inset;
  }
  25% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px, 0 0 0 0 inset;
  }
  50% {
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    margin-top: -4px;
    margin-left: 6px;
    width: 0px;
    height: 0px;
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 15px 2px 0 11px, 0 0 0 0 inset;
  }
  51% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    margin-top: -2px;
    margin-left: -2px;
    width: 20px;
    height: 20px;
    box-shadow: 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0px 0px 0 10px inset;
  }
  100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    margin-top: -2px;
    margin-left: -2px;
    width: 20px;
    height: 20px;
    box-shadow: 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0px 0px 0 0px inset;
  }
}
@keyframes checkbox-off {
  0% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px, 0 0 0 0 inset;
  }
  25% {
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px, 0 0 0 0 inset;
  }
  50% {
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    margin-top: -4px;
    margin-left: 6px;
    width: 0px;
    height: 0px;
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0px 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 15px 2px 0 11px, 0 0 0 0 inset;
  }
  51% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    margin-top: -2px;
    margin-left: -2px;
    width: 20px;
    height: 20px;
    box-shadow: 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0px 0px 0 10px inset;
  }
  100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    margin-top: -2px;
    margin-left: -2px;
    width: 20px;
    height: 20px;
    box-shadow: 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0, 0px 0px 0 0px inset;
  }
}
@-webkit-keyframes rippleOn {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(13, 13);
            transform: scale(13, 13);
  }
}
@keyframes rippleOn {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(13, 13);
            transform: scale(13, 13);
  }
}
@-webkit-keyframes rippleOff {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(13, 13);
            transform: scale(13, 13);
  }
}
@keyframes rippleOff {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(13, 13);
            transform: scale(13, 13);
  }
}

input.fancycheckbox {
  display: none;
}

.sponsor-slider-inner {
    vertical-align: middle;
    display: inline-block;
    float: none !important;
}
