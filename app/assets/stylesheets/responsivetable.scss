.rwd-table {
    margin: 1em 0;
    min-width: 300px;
    width: 100%;
    background: #fff;
    color: #000;
    border-radius: 0.4em;
    overflow: hidden;
}

.rwd-table th {
    text-align: left;
    display: none;
}

.rwd-table td {
    display: block;
    border-color: #46637f;
    background: #fff;
    color: #000;
    text-align: left;
    overflow: hidden;
}

.rwd-table td:first-child {
    padding-top: 0.5em;
}

.rwd-table td:last-child {
    padding-bottom: 0.5em;
}

.rwd-table td:before {
    content: attr(data-th) ": ";
    font-weight: bold;
    width: 9.5em;
    display: inline-block;
}

.rwd-table td,
.rwd-table th {
    margin: 0.5em 1em;
}

.rwd-table td:before,
.rwd-table th {
    color: #000;
}

.textalignment {
    text-align: left !important;
}
@media (max-width: 768px) {
    .outertable {
        border-radius: 20px;
        padding-bottom: 7px;
        margin-bottom: 15px;
        background-color: #ff9500;
        padding-top: 7px;
    }
}
@media (min-width: 768px) {
    .textalignment {
        text-align: right !important;
    }

    .rwd-table td,
    .rwd-table th {
        display: table-cell;
        padding: 0.25em 0.5em;
    }

    .rwd-table th {
        background-color: #fff;
        color: #000;
    }

    .rwd-table td {
        background-color: #fff;
        color: #000;
    }

    .rwd-table tr:first-child {
        border-bottom: 1px solid #333;
    }

    .rwd-table td:first-child,
    .rwd-table th:first-child {
        padding-left: 0;
        border-bottom-left-radius: 0.4em;
    }

    .rwd-table td:last-child,
    .rwd-table th:last-child {
        padding-right: 0;
        border-bottom-right-radius: 0.4em;
    }

    .rwd-table td:before {
        display: none;
    }

    .rwd-table td,
    .rwd-table th {
        padding: 1em !important;
    }

    .rwd-table td {
        background: #fff;
        color: #000;
        overflow: hidden;
    }
}
