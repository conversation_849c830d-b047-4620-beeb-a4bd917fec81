@import "base_styles";

.progress {
	width: 100px;
	height: 100px;
	background-color: $white !important;
	&.blue > .fill {
		stroke: rgb(41, 170, 227);
	}
	&.eventorange > .fill {
		stroke: #ff9500;
	}
	&.pink > .fill {
		stroke: rgb(157, 51, 143);
	}
}
.track {
	fill: rgba(0, 0, 0, 0);
	stroke-width: 5;
	stroke: #797979;
}
.fill {
	fill: rgba(0, 0, 0, 0);
	stroke-width: 5;
	stroke: $white;
	stroke-dasharray: 150.72259521484375;
	stroke-dashoffset: 150.72259521484375;
	transition: stroke-dashoffset 1s;
}
.display {
	font-family: $opensans;
	fill: $black;
	text-anchor: middle;
}
