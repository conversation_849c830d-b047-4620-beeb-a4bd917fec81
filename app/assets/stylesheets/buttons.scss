@import "base_styles";

.circle {
    background-color: $hg_orange;
    display: block;
    width: 160px;
    height: 160px;
    line-height: 1.5em;
    text-align: center;
    color: $white;
    text-transform: uppercase;
    border: 3px solid $white;
    border-radius: 80px;
    box-shadow: 0 0 0 5px $hg_orange;
    transition: all 0.5s ease-in-out;

    &:hover {
        background-color: $black;
        box-shadow: 0 0 0 5px $black;
        text-decoration: none;
        color: $hg_orange;
    }
}

.circleactive {
    background-color: $black;
    display: block;
    width: 160px;
    height: 160px;
    line-height: 1.5em;
    text-align: center;
    color: $white !important;
    text-transform: uppercase;
    border: 3px solid $white;
    border-radius: 80px;
    box-shadow: 0 0 0 5px $black;
    transition: all 0.5s ease-in-out;

    &:hover {
        background-color: $black;
        text-decoration: none;
        box-shadow: 0 0 0 5px $black;
    }
}

.btn-yes {
    color: #ffffff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #999;
    *background-color: #999;
    background-image: -moz-linear-gradient(top, #999, #999);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#999), to(#999));
    background-image: -webkit-linear-gradient(top, #999, #999);
    background-image: -o-linear-gradient(top, #999, #999);
    background-image: linear-gradient(to bottom, #999, #999);
    background-repeat: repeat-x;
    border-color: $hg_orange $hg_orange $hg_orange;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr= '#ff444444', endColorstr='#ff222222', GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}

.btn-yes:hover {
    // background-image:none !important;
    color: $hg_orange;
    border-color: $hg_orange;
    // background-color: transparent !important;
}

.btn-yes.active,
.btn-yes.disabled,
.btn-yes:active,
.btn-yes:focus,
.btn-yes[disabled] {
    color: #fff;
    border-color: $hg_orange;
    background-color: $hg_orange;
}

.btn-clear {
  color: #ff9500;
  background-color: #fff;
  border: 2px solid #ff9500 !important;
}

.btn-clear:hover {
    // background-image:none !important;
    color: #fff;
    border-color: $hg_orange;
    background-color: #ff9500 !important;
}

.btn-info.active.focus,
.btn-info.active:focus,
.btn-info.active:hover,
.btn-info:active.focus,
.btn-info:active:focus,
.btn-info:active:hover,
.open > .dropdown-toggle.btn-info.focus,
.open > .dropdown-toggle.btn-info:focus,
.open > .dropdown-toggle.btn-info:hover {
    color: $white;
    background-color: $hg_orange;
    border-color: $hg_orange;
}

.fabbuttoncontainer {
    bottom: 0;
    position: fixed;
    margin: 1em;
    right: 0;
}

.buttons {
    box-shadow: 0 5px 11px -2px rgba(0, 0, 0, 0.18), 0 4px 12px -7px rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    display: block;
    width: 56px;
    height: 56px;
    margin: 20px auto 0;
    position: relative;
    -webkit-transition: all 0.1s ease-out;
    transition: all 0.1s ease-out;
}

.buttons:active,
.buttons:focus,
.buttons:hover {
    box-shadow: 0 0 4px rgba(0,0,0,.14), 0 4px 8px rgba(0,0,0,.28);
}

.btn-light {
    color: #00B2F0;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.buttons:not(:last-child) {
    width: 40px;
    height: 40px;
    margin: 20px auto 0;
    opacity: 0;
    -webkit-transform: translateY(50px);
    -ms-transform: translateY(50px);
    transform: translateY(50px);
}

.fabbuttoncontainer:hover .buttons:not(:last-child) {
    opacity: 1;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    margin: 15px auto 0;
}
/* Unessential styling for sliding up buttons at differnt speeds */
.buttons:nth-last-child(1) {
    -webkit-transition-delay: 25ms;
    transition-delay: 25ms;
    background-color: #ff9500;
    color: white;
    padding-left: 18px;
    font-size: 24px;
    padding-top: 10px;
    background-size: contain;
}

.buttons:not(:last-child):nth-last-child(2) {
    -webkit-transition-delay: 50ms;
    transition-delay: 20ms;
    background-color: #ff9500;
    color: white;
    padding-left: 10px;
    font-size: 20px;
    padding-top: 5px;
    background-size: contain;
}

.buttons:not(:last-child):nth-last-child(3) {
    -webkit-transition-delay: 75ms;
    transition-delay: 40ms;
    background-color: #ff9500;
    color: white;
    padding-left: 10px;
    font-size: 20px;
    padding-top: 5px;
    background-size: contain;
}

.buttons:not(:last-child):nth-last-child(4) {
    -webkit-transition-delay: 100ms;
    transition-delay: 60ms;
    background-color: #ff9500;
    color: white;
    padding-left: 10px;
    font-size: 20px;
    padding-top: 5px;
    background-size: contain;
}
/* Show tooltip content on hover */
[tooltip]:before {
    bottom: 25%;
    font-family: arial;
    font-weight: 600;
    border-radius: 2px;
    background: #585858;
    color: #fff;
    content: attr(tooltip);
    font-size: 12px;
    visibility: hidden;
    opacity: 0;
    padding: 5px 7px;
    margin-right: 12px;
    position: absolute;
    right: 100%;
    white-space: nowrap;
    z-index: 1000;
}

[tooltip]:hover:after,
[tooltip]:hover:before {
    visibility: visible;
    opacity: 1;
    z-index: 1000;
}
/* Show tooltip content on hover */
[buttontooltip]:before {
    font-family: arial;
    font-weight: 600;
    border-radius: 2px;
    background: #585858;
    color: #fff;
    content: attr(buttontooltip);
    font-size: 12px;
    visibility: hidden;
    opacity: 0;
    padding: 5px 7px;
    margin-right: 12px;
    position: absolute;
    left: 300px;
    white-space: nowrap;
    z-index: 1000;
}

[buttontooltip]:hover:after,
[buttontooltip]:hover:before {
    visibility: visible;
    opacity: 1;
    z-index: 1000;
}
