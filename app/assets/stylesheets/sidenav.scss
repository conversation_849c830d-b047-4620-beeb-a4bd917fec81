
@-moz-keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(15px);
    transform: translateX(15px);
  }
  60% {
    -moz-transform: translateX(5px);
    transform: translateX(5px);
  }
}
@-webkit-keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -webkit-transform: translateX(15px);
    transform: translateX(15px);
  }
  60% {
    -webkit-transform: translateX(5px);
    transform: translateX(5px);
  }
}
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(15px);
    -ms-transform: translateX(15px);
    -webkit-transform: translateX(15px);
    transform: translateX(15px);
  }
  60% {
    -moz-transform: translateX(5px);
    -ms-transform: translateX(5px);
    -webkit-transform: translateX(5px);
    transform: translateX(5px);
  }
}

.bounce {
  -moz-animation: bounce 5s infinite;
  -webkit-animation: bounce 5s infinite;
  animation: bounce 5s infinite;
}

	.hamburger {
	  position: fixed;
	  z-index: 9999;
	  padding: 15px;
	}
	.hamburger:hover {
	  cursor: pointer;
	}

  #wrapper {
    width: 220px;
    overflow: hidden;
  }

  #scroller {
      width: 240px;
      height: 100vh;
      overflow: auto;
  }

	#sidenav {
	  background-color: #424a5d;
	  display: inline-block;
	  position: fixed;
	  height: 100%;
	  width: 240px;
	  left: -225px;
    top: 75px;
    bottom:0;
	  -webkit-transition: -webkit-transform 0.3s;
	  transition: -webkit-transform 0.3s;
	  transition: transform 0.3s;
	  transition: transform 0.3s, -webkit-transform 0.3s;
	}

	#sidenav .logo {
	  margin: 0vh 0;
	  text-align: center;
	}
	#sidenav .logo img {
	  border-radius: 50%;
	}
	#sidenav a {
	  display: block;
	  text-decoration: none;
	  color: white;
	  padding: 10px;
	}
	#sidenav a:hover {
	  background-color: #ff9500;
	}
	#sidenav a:visited {
	  color: white;
	}
	#sidenav a.active {
	  background-color: black;
	  color: white;
	}
	#sidenav footer {
	  width: 225px;
	  text-align: center;
	  position: absolute;
	  padding: 5px;
	  bottom: 0;
	  color: #959595;
	}
	#sidenav footer p {
	  font-size: 0.85em;
	}

	.sidenav-active .coveroverlay {
	  background-color: rgba(0, 0, 0, 0.3);
	  position: fixed;
	  top: 0;
	  bottom: 0;
	  left: 0;
	  right: 0;
	  height: 100%;
	  z-index: 5000;
	  -webkit-transition: background-color .3s, -webkit-transform 0.3s;
	  transition: background-color .3s, -webkit-transform 0.3s;
	  transition: background-color .3s, transform 0.3s;
	  transition: background-color .3s, transform 0.3s, -webkit-transform 0.3s;
	}

	#sidenav, #mainpage, .hamburger, .coveroverlay {
	  -webkit-transition: -webkit-transform 0.3s;
	  transition: -webkit-transform 0.3s;
	  transition: transform 0.3s;
	  transition: transform 0.3s, -webkit-transform 0.3s;
	}
	.sidenav-active nav, .sidenav-active #mainpage, .sidenav-active .hamburger, .sidenav-active .coveroverlay {
	  -webkit-transform: translateX(225px);
	          transform: translateX(225px);
	}

	// .noscroll {
	//   position: relative;
	//   // overflow: hidden;
	// }

	#mainpage {
	  box-shadow: -4px 0 8px rgba(100, 100, 100, 0.5);
	}
