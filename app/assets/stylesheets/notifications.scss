@import "base_styles";

// Place all the styles related to the notifications controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
.content-sidebar {
  width: 300px;
  background: #FFF;
  border-left: 1px solid #d6deeb;
  padding: 15px;
  // display: none;

  &__heading {
    background: #FF9500;
    color: white;
    padding: 16px 10px;
    text-align: center;
    margin-bottom: 0px !important;

    h3 {
      font-weight: normal;
      font-size: 16px;
    }
  }

  &--collapsed {
    display: none;
  }
}

.notification-list {
  list-style: none;
  font-size: 12px;
  position: relative;
  overflow-y: auto;
  height: 85%;

  &__item {
    border-bottom: 1px solid #eaeaea;
    padding: 20px 10px 10px 50px;
    min-height: 39px;
    position: relative;

    &:hover {
      background: #f2f2f2;
    }
  }

  &__time {
    font-size: 10px;
    margin-bottom: 5px;
    color: #666;
    text-transform: uppercase;
    font-style: italic;
  }

  &__message {
    margin-bottom: 10px;
    line-height: 18px;
  }

  &__icon {
    position: absolute;
    top: 7px;
    left: 10px;
    background: pink;

    .fa {
      vertical-align: middle;
    }
  }
}

.notification-icon {
  font-size: 12px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 50%;
  background: $link_color;
  color: #FFF;

  &--success {
    background: #9cc96b;
    color: #FFF;
  }
}
