.btn:hover {
  outline: 0 !important;
}
.btn:focus {
  outline: 0 !important;
}
.btn:active {
  outline: 0 !important;
}

.previewcard-container {
  -moz-perspective: 800px;
  -webkit-perspective: 800px;
  perspective: 800px;
  -moz-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  margin-bottom: 30px;
  width: 100%;
}

.previewcard-container-no-bottom {
  @extend .previewcard-container;
  margin-bottom: 0px;
}


.previewcard {
  -moz-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -moz-transition: -moz-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -o-transition: -o-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -webkit-transition: -webkit-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  background: none repeat scroll 0 0 #fff;
  color: #444444;
}
.previewcard .cover {
  // -moz-border-radius: 4px 4px 0 0;
  // -webkit-border-radius: 4px;
  // border-radius: 4px 4px 0 0;
  height: 6px;
  overflow: hidden;
  z-index: -2;
  background-color: #ff9500;
}

.cover-standalone {
  height: 6px;
  overflow: hidden;
  z-index: -2;
  background-color: #ff9500;
}

.previewcard .cover img {
  width: 100%;
}
.previewcard .branded {
  position: relative;
  background: #fff;
  display: block;
  height: 120px;
  margin: -55px auto 0;
  width: 120px;
  text-align: center;
}
.previewcard .content {
  background-color: transparent;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  padding: 10px 20px 20px;
  overflow: auto;
}
.previewcard .content .main {
  min-height: 140px;
  overflow: hidden;
}
.previewcard .use-for {
  font-size: 22px;
  text-align: center;
}
.previewcard h5 {
  margin: 5px 0;
  font-weight: 400;
  line-height: 20px;
}
.previewcard .footer {
  color: #999;
  padding: 10px 0 0;
  text-align: center;
}
.previewcard .footer .btn-simple {
  margin-top: -6px;
}
.previewcard .header {
  padding: 15px 20px;
  height: 90px;
}
.previewcard .back .content .main {
  height: 215px;
}

.front {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.14);
  background-color: #fff;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 2;
  text-align: left;
}
