.icon {
  height: 16px;
  width: 16px;
  display: inline-block;
}

.icon-gcal {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAADLElEQVR42n1TS0hUURj+zp2Ho02N18c0WXcmNSlJnAaECotJgyCNyrKiRSFRi7KC6EEtCkKCHvsWFQxIQYsWJtWigiTHQLGpZix1yh5TNmU2Nx93HO+zc+81qU0//Gdx/vN/5/v+ByHUyiurqp0uNhcadNNPgv8YodGJMZ7vj/Y+I/lc+aqtp28+UySJaKoKlbr+QNO0WdchNRU0ZmIrGgFjs2n3Lu1aTVhvZdC/52oHZBE2ImJbaQq3XllBHLkzACapEpeM+fJbhMeXmXdWG6KhA0Hi0gH2XusgmgRVU1CUGcIHyQ17jtPQos0cdm0KltE4xlzllKEFYOzoCzUFybyCRRWHzrfGZEU2ExRTwt9l0Knr0hRZNq7NOIMbLU0VhGVZrqe7O5GZmqLZBBMZFe9+iGBzLCgpsFG9mgE1npbwa1KCh7XNVBJYV1PDGQB9sb5EJpPB0IiEc20jCHiz0J+cxprSbOxby+Li3WGE+3kE/W4crnUZ+YyFQVVVlQkw0D+QEEURj/omwNNfdq/KQ8+HNC60DaPtWBlkSv1mbxrJXyKaqx06UdisVvhX+DlSWFhYTAHe6wBGi6heIaPgTi+PnvgYrh1YAlGU0No9gU+jUzi6NgsMw8BisSAQCHDE6/WWR6PRN7IkGwCvPtNHrR9pMRWc3e7FhuVzMU3BQ2Eeg18ncXJ9NuY4nUgkPqO+vo4jZWVlgcjzSEShVWYIY/aNTk3H2zRaHvC431wEMSPgaewrkqlprPQRDA7EIUkSTpw6zhG/31/d1dUV1n+/+FjA/BwVTSvnIJqUcPD2TzxsLoQwnkJ8MA4hncbr2BssKPKA4xZix84dHKmtrd3S3t7epgN0Dkk4c5fHUrcVCV7G5uV2HKlxIZn8hidPOvD92wh8Pg6Li30G0fpNVEJjY+P+UCh0nVD6dK/Ap1V8TClwOwmKcq2GpHBnF15EXqKktBgejxtsXh7EaZHOQZAjDQ0N+y9funJdEARj2tTZJQJtnwQ+xWP4yzBy6bK6XPPgcDhgt2dBpjWo27TRR2g77Pn5+RWqohrl+2ePjbElhv8xzdwuwjAWMjr6I/Yb/aiDYKs+1hYAAAAASUVORK5CYII=);
}

.icon-yahoo {
  background-image: url(data:image/gif;base64,R0lGODlhEAAQAOZOAP////39/ff39/X19fPz8/Hx8fPu8/Ls8u/r7+bU5uTU5OLR4uDR4NSy1NOv09Cs0M2pzcudy8iayMeZx7eGt7J3sqh2qLlaubdVt7VQtaFTobFKsa5ErpFNkaw/rJ9En5xAnKk4qZc/l5c7l6YzppM2k4M7g6Mto4o0ipAykI0zjYwwjKAnoIAygIwtjIYwhm80b4gpiJshm20wbYQkhGsua5YclngoeGkraXYmdoAfgGUoZZEVkXwafGMkY3UbdWEiYYsOi3gWeF4eXmoYanQRdFsbW1kYWVYVVlMRU1APUE4LTkoHSkgESAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAE4ALAAAAAAQABAAAAe5gE4wg4SFhU4zF4qLjIwzMxgfkpOUkhg1NRkoN5w3KCCgoRk4OxsRAKgAESMvOa4vGzs+HCUlDQEWKiUTAb0SHD4+HikpIgkGHSstCg7EHkBDIS7TGgcMJgsP0y4hQ0YkMeExFQIIEOIxJEZGJzTu7gMD7+4nR0gsOvk/RAQERD/5dLBAkkRGj4MUCigsQOFgDxlJlNgQQrGiRYo2lCzhUaSjx48deSxhEqSkyZMnmThpwrKlS5dOAgEAOw==);
}

.icon-ical {
  background-image: url(data:image/gif;base64,R0lGODlhEAAQAOYAADFr1lqM74yt79be52uU73ucvdbn/+fv9zlz3mul/9bW5+d7UsbW/5S192Oc/0qE73ut///Ovd7v/zlr3vfGtaXG/1KE597n9zl770JzzoSt787e92uU58bW92uc90J770p71nOl/2uc/1qM595zSmOU5zFj1jlz50p776W993Oc71KM72OU74St///GtVqU/0J752OU/1qM91J73ufn93Oc/2OM54SlxrW91rW9zt5zUoScvdbe74Slva29zq21zv///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAAAAAAALAAAAAAQABAAAAe4gAICHAwjBDYBFg8wHykIAAANBAYSNRAQCSIvMSsMGyAAAhWTHi0hCQ4OMgEdMxmRAholKgQsAQEPKCcYCBMmBTwDPgo8P0BAPsfGQD09Azw+wTjKxznHPTs+P9rc297aPTfUQNbIx9NAOwXHyUDL7cvNPzg+OfP1Ogs6OvPY7OM6KETQcU2cu2rHdERwseCYum7d9ukgAa7HPyA4eBTTmIPGAX/m3AUbVuzAhXDNdtxIuRJbyx2BAAA7);
}

.icon-outlook {
  background-image: url(data:image/gif;base64,R0lGODlhEAAQAOYAANd7AfnpuP/FAOLGbNqLO92UCvfJJ//9+P/mcdBqD+KkcenAPOy8IeiiDPvy3/7pm9J1DevBnP/dOdqDA/rv496ZM//RGf757fe+D9+TDuiuFu/CWee3guGjVNeAJv/XKfTfyNiIC96ZR+CeHdyPTtFrF//WGeSpYem7gvDOmP/ZMf7zyf3ICuqyGP/OEN6EANiBD////+OeEP/xrt+aCOWtONqIGtZzCPfo09FzDvnDFf/eRv731//bNOOkP9+bEdyRRNFwH/HLT9aCC+24G+7JlN6YVum4XNyMB//VIv/MAOWkEuusGf/MCduFBeacC/359t6EEODHdf/jfvrOKv/eRd6cIfHTk9R6DP///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEHAFkALAAAAAAQABAAAAfQgFlZUgsMREQtLRoaS0syj1kDQgEXMZaXmAVSQjwzCFUqKh9JSRYWLjFICwEzEhIGNT+yPjgrqEgMFwg9VCM0NDIFBRUUDzFORDE7KkzCVw4bSEMoB8ctMaEZGUcxpj9DIpYTGtgfISEpBy4uDVgElgDkHx8wMEXqLk8Q7zEASzGjYEDocMACCxs5jMCTEYOUjBw5OIA4kSBBhGoAGJbS4aGiRxJQVPT70c0UBiAlSgRRQGGKkn4ZSrpoQlOJzZv9CmDauRMAkp9OggIYSrRoIAA7);
}

@import url("//netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.css");

.titlepanelheader {
  background-color: #ff9500;
  color: White;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 20px;
  vertical-align: middle;
  width: 100%;
  float: left;
  line-height: 10px;
  font-size: 1.5em;
  font-weight: 700;
}

.main {
  position: relative;
  width: 100%;
  height: 96%;
  clear: both;
  background-color: white;
}

.main .nav {
  width: 200px;
  height: 100%;
  float: left;
  background-color: rgba(227, 234, 235, 0.8);
}

.main .nav .searchbox {
  width: 170px;
  height: 30px;
  line-height: 30px;
  margin: 15px;
  background-color: White;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}

.main .nav .searchbox i {
  display: inline-block;
  color: #bbb;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
}

.main .nav .searchbox input {
  display: inline-block;
  width: 120px;
  background-color: White;
  color: Black;
  font-family: "Lato";
  border: 0px;
}

.main .nav .menu {
  width: 100%;
  margin: 15px;
  color: #555;
}

.main .nav .menu .title {
  font-weight: 700;
  font-size: 1.0em;
  text-transform: uppercase;
}

.main .nav .menu ul {
  padding-left: 0px;
}

.main .nav .menu ul li {
  cursor: pointer;
  list-style: none;
  margin: 5px 0px;
  padding: 5px 0px;
  font-weight: 600;
  margin-right: 30px;
  padding-left: 10px;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  transition: 0.25s all;
}

.main .nav .menu ul li.active {
  color: #ff9500;
}

.main .nav .menu ul li i {
  font-size: 1.4em;
  margin-right: 10px;
}

.main .view {
  position: relative;
  height: 100%;
}

.main .view .viewHeader {
  width: 100%;
  height: 70px;
  line-height: 70px;
  padding: 20px 20px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #E0E0E0;
}

.main .view .viewHeader .title {
  float: left;
  font-size: 1.4em;
  font-weight: 400;
  padding: 0px;
  color: #AAA;
  margin-top: -18px;
}

.main .view .viewHeader .functions {
  float: right;
}

.main .view .viewHeader .functions .button {
  float: right;
  height: 30px;
  line-height: 30px;
  background-color: #AAA;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  margin: 0px 5px;
  padding: 0px 20px;
  cursor: pointer;
  color: White;
  font-weight: 700;
}

.main .view .viewHeader .functions .button.active {
  background-color: #ff9500;
}

.main .view .viewHeader .functions .button.makelive {
  background-color: #009Cff;
}

.main .view .viewHeader .functions .button.inverz {
  background-color: inherit;
  border: thin solid #AAA;
  width: 10px;
  color: #AAA;
  font-size: 1.3em;
  padding-left: 10px;
  padding-right: 16px;
}

.main .view .content {
  // position: absolute;
  left: 0px;
  top: 70px;
  right: 0px;
  bottom: 0px;
  padding: 10px;
}

.main .view .content .list .title {
  width: 100%;
  padding: 10px;
  text-transform: uppercase;
  font-weight: 700;
  color: #00B2F0;
}

.main .view .content .list ul {
  width: 100%;
  padding-left: 10px;
  color: #777;
}

.main .view .content .list ul li {
  width: 100%;
  // height: 100px;
  line-height: 50px;
  list-style: none;
  border-top: 1px solid #AAA;
}

.main .view .content .list ul li.checked {
  background-color: #F0F4F5;
}

.main .view .content .list ul li:last-child {
  border-bottom: 1px solid #AAA;
}

.main .view .content .list ul li i {
  float: left;
  width: 30px;
  // height: 50px;
  line-height: 50px;
  margin-left: 10px;
  font-size: 1.3em;
}

.main .view .content .list ul li span {
  float: left;
  font-weight: 600;
}

.main .view .content .list ul li .info {
  float: right;
  width: 345px
}

.main .view .content .list ul li .info span {
  font-weight: 300;
}

.main .view .content .list ul li .info .button {
  float: right;
  width: 40%;
  // height: 30px;
  line-height: 30px;
  background-color: #AAA;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  margin: 10px 10px;
  text-align: center;
  cursor: pointer;
  color: White;
  font-weight: 700;
}
.main .view .content .list .info .button {
  float: right;
  width: 100%;
  // height: 30px;
  line-height: 30px;
  background-color: #AAA;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  margin: 10px 10px;
  text-align: center;
  cursor: pointer;
  color: White;
  font-weight: 700;
}

.main .view .content .list .info .button.green {
  background-color: #85C157;
}
.main .view .content .list ul li .info .button.green {
  background-color: #85C157;
}
.main .view .content .list .info .button.blue {
  background-color: #5791c1;
}
.main .view .content .list ul li .info .button.blue {
  background-color: #5791c1;
}

.main .view .content .list .info .button.live {
  background-color: #c15757;
}

.main .view .content .list ul li .info .button.live {
  background-color: #c15757;
}

.main .view .content .list ul li .info .button.complete {
  background-color: #5784c1;
}

.main .view .content .list ul li .info .button.disabled {
  background-color: #6e6e6e;
}
