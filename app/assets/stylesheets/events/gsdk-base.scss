// .form-control::-moz-placeholder {
//   color: #DDDDDD;
//   opacity: 1;
// }
//
// .form-control:-moz-placeholder {
//   color: #DDDDDD;
//   opacity: 1;
// }
//
// .form-control::-webkit-input-placeholder {
//   color: #DDDDDD;
//   opacity: 1;
// }
//
// .form-control:-ms-input-placeholder {
//   color: #DDDDDD;
//   opacity: 1;
// }
//
// /*     General overwrite     */
// a {
//   color: #2CA8FF;
// }
//
// a:hover, a:focus {
//   color: #109CFF;
// }
//
// a:focus, a:active,
// button::-moz-focus-inner,
// input[type="reset"]::-moz-focus-inner,
// input[type="button"]::-moz-focus-inner,
// input[type="submit"]::-moz-focus-inner,
// select::-moz-focus-inner,
// input[type="file"] > input[type="button"]::-moz-focus-inner {
//   outline: 0;
// }
//
// p {
//   font-size: 16px;
//   line-height: 1.6180em;
// }
//
//
// h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, .h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small, h1 .small, h2 .small, h3 .small, h4 .small, h5 .small, h6 .small, .h1 .small, .h2 .small, .h3 .small, .h4 .small, .h5 .small, .h6 .small {
//   color: #999999;
//   font-weight: 300;
//   line-height: 1;
// }
//
// h1 small, h2 small, h3 small, h1 .small, h2 .small, h3 .small {
//   font-size: 60%;
// }
//
// h1 .subtitle {
//   display: block;
//   font-family: 'Grand Hotel', cursive;
//   line-height: 40px;
//   margin: 15px 0 30px;
//
// }
//
// /*           Animations              */
// .form-control, .input-group-addon {
//   -webkit-transition: all 300ms linear;
//   -moz-transition: all 300ms linear;
//   -o-transition: all 300ms linear;
//   -ms-transition: all 300ms linear;
//   transition: all 300ms linear;
// }
//
// .btn {
//   border-width: 1px;
//   font-weight: 400;
//   padding: 8px 16px;
// }
//
// .btn-round {
//   border-width: 1px;
//   border-radius: 30px !important;
//   opacity: 0.79;
//   padding: 9px 18px;
// }
//
// .btn-sm, .btn-xs {
//   border-radius: 3px;
//   font-size: 12px;
//   padding: 5px 10px;
// }
//
// .btn-xs {
//   padding: 1px 5px;
// }
//
// .btn-lg {
//   border-radius: 6px;
//   font-size: 18px;
//   font-weight: 400;
//   padding: 14px 30px;
// }
//
// .btn-wd {
//   min-width: 100px;
// }
//
// .btn-default {
//   color: #fff;
//   background-color: #777;
//   border-color: #999999
// }
//
// .btn-primary {
//   color: #3472F7;
//   border-color: #3472F7;
// }
//
// .btn-info {
//   color: #fff;
//   border-color: #868686;
//   background-color: $hg_orange;
// }
// .btn-conf {
//   color: #fff;
//   border-color: #B7355F;
//   background-color: #B7355F;
// }
// .btn-main {
//   color: #fff;
//   border-color: #fff;
//   background-color: rgba(255, 255, 255, 0);
// }
//
// .btn-info2 {
//   color: #fff;
//   border-color: #46b8da;
//   background-color: #5bc0de;
// }
//
// .btn-success {
//   color: white;
//   border-color: #099cff;
// }
//
// .btn-warning {
//   color: #000;
//   border-color: #868686;
//   }
//
// .btn-danger {
//   color: #ffffff;
//   border-color: #868686;
// }
//
//
// .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open .dropdown-toggle.btn-primary {
//   color: #1D62F0;
//   border-color: #1D62F0;
//   background-color: rgba(0, 0, 0, .0);
// }
//
// .btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .open .dropdown-toggle.btn-info {
//   color: #FF9500;
//   border-color: #FF9500;
//   background-color: rgba(0, 0, 0, .0);
// }
//
// .btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .open .dropdown-toggle.btn-success {
//   color: white;
//   border-color: #009cff;
//   background-color: #009cff;
// }
//
// .btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .open .dropdown-toggle.btn-warning {
//   color: #fff;
//   border-color: #868686;
//   background-color: #ff9500;
// }
//
// .btn-danger:hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active, .open .dropdown-toggle.btn-danger {
//   color: #EE2D20;
//   border-color: #EE2D20;
//   background-color: rgba(0, 0, 0, .0);
// }
//
// .btn-default:hover, .btn-default:focus, .btn-default:active, .btn-default.active, .open .dropdown-toggle.btn-default {
//   color: #fff;
//   border-color: #888888;
//   background-color:#777777;
// }
//
// .btn:active, .btn.active {
//   background-image: none;
//   color: white;
//   box-shadow: none;
//
// }
//
// .btn.disabled, .btn[disabled], fieldset[disabled] .btn {
//   opacity: 0.45;
// }
//
// .btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary.active[disabled], fieldset[disabled] .btn-primary.active {
//   background-color: rgba(0, 0, 0, 0);
//   border-color: #3472F7;
// }
//
// .btn-info.disabled, .btn-info[disabled], fieldset[disabled] .btn-info, .btn-info.disabled:hover, .btn-info[disabled]:hover, fieldset[disabled] .btn-info:hover, .btn-info.disabled:focus, .btn-info[disabled]:focus, fieldset[disabled] .btn-info:focus, .btn-info.disabled:active, .btn-info[disabled]:active, fieldset[disabled] .btn-info:active, .btn-info.disabled.active, .btn-info.active[disabled], fieldset[disabled] .btn-info.active {
//   background-color: #ff9500;
//   border-color: #ff9500;
// }
//
// .btn-success.disabled, .btn-success[disabled], fieldset[disabled] .btn-success, .btn-success.disabled:hover, .btn-success[disabled]:hover, fieldset[disabled] .btn-success:hover, .btn-success.disabled:focus, .btn-success[disabled]:focus, fieldset[disabled] .btn-success:focus, .btn-success.disabled:active, .btn-success[disabled]:active, fieldset[disabled] .btn-success:active, .btn-success.disabled.active, .btn-success.active[disabled], fieldset[disabled] .btn-success.active {
//   background-color: rgba(#5cb85c, 0.80);
//   border-color: #5cb85c;
// }
//
// .btn-danger.disabled, .btn-danger[disabled], fieldset[disabled] .btn-danger, .btn-danger.disabled:hover, .btn-danger[disabled]:hover, fieldset[disabled] .btn-danger:hover, .btn-danger.disabled:focus, .btn-danger[disabled]:focus, fieldset[disabled] .btn-danger:focus, .btn-danger.disabled:active, .btn-danger[disabled]:active, fieldset[disabled] .btn-danger:active, .btn-danger.disabled.active, .btn-danger.active[disabled], fieldset[disabled] .btn-danger.active {
//   background-color: rgba(0, 0, 0, 0);
//   border-color: #FF3B30;
// }
//
// .btn-warning.disabled, .btn-warning[disabled], fieldset[disabled] .btn-warning, .btn-warning.disabled:hover, .btn-warning[disabled]:hover, fieldset[disabled] .btn-warning:hover, .btn-warning.disabled:focus, .btn-warning[disabled]:focus, fieldset[disabled] .btn-warning:focus, .btn-warning.disabled:active, .btn-warning[disabled]:active, fieldset[disabled] .btn-warning:active, .btn-warning.disabled.active, .btn-warning.active[disabled], fieldset[disabled] .btn-warning.active {
//   background-color: rgba(0, 0, 0, 0);
//   border-color: #FF9500;
// }
//
// /*           Buttons fill .btn-fill           */
// .btn-fill {
//   color: #FFFFFF;
//   opacity: 1;
// }
//
// .btn-fill:hover, .btn-fill:active, .btn-fill:focus {
//   color: #FFFFFF;
// }
//
// .btn-primary.btn-fill {
//   background-color: #3472F7;
//   border-color: #3472F7;
// }
//
// .btn-info.btn-fill {
//   background-color: #FF9500;
//   border-color: #FF9500;
// }
//
// .btn-success.btn-fill {
//   background-color: #05AE0E;
//   border-color: #05AE0E;
// }
//
// .btn-warning.btn-fill {
//   background-color: #FF9500;
//   border-color: #FF9500;
// }
//
// .btn-danger.btn-fill {
//   background-color: #FF3B30;
//   border-color: #FF3B30;
// }
//
// .btn-primary.btn-fill:hover, .btn-primary.btn-fill:focus, .btn-primary.btn-fill:active, .btn-primary.btn-fill.active, .open .dropdown-toggle.btn-primary.btn-fill {
//   border-color: #1D62F0;
//   background-color: #1D62F0;
// }
//
// .btn-info.btn-fill:hover, .btn-info.btn-fill:focus, .btn-info.btn-fill:active, .btn-info.btn-fill.active, .open .dropdown-toggle.btn-info.btn-fill {
//   background-color: #FF9500;
//   border-color: #FF9500;
// }
//
// .btn-success.btn-fill:hover, .btn-success.btn-fill:focus, .btn-success.btn-fill:active, .btn-success.btn-fill.active, .open .dropdown-toggle.btn-fill.btn-success {
//   background-color: #049F0C;
//   border-color: #049F0C;
// }
//
// .btn-warning.btn-fill:hover, .btn-warning.btn-fill:focus, .btn-warning.btn-fill:active, .btn-warning.btn-fill.active, .open .dropdown-toggle.btn-fill.btn-warning {
//   background-color: #ED8D00;
//   border-color: #ED8D00;
// }
//
// .btn-danger.btn-fill:hover, .btn-danger.btn-fill:focus, .btn-danger.btn-fill:active, .btn-danger.btn-fill.active, .open .dropdown-toggle.btn-danger.btn-fill {
//   background-color: #EE2D20;
//   border-color: #EE2D20;
// }
//
// /*          End Buttons fill          */
//
// .btn-simple {
//   font-weight: 600;
//   border: 0;
//   padding: 10px 18px;
// }
//
// .btn-simple.btn-xs {
//   padding: 3px 5px;
// }
//
// .btn-simple.btn-sm {
//   padding: 7px 10px;
// }
//
// .btn-simple.btn-lg {
//   padding: 16px 60px;
// }
//
// .btn-round.btn-xs {
//   padding: 2px 5px;
// }
//
// .btn-round.btn-sm {
//   padding: 6px 10px;
// }
//
// .btn-round.btn-lg {
//   padding: 15px 30px;
// }
//
// /*             Inputs               */
// .form-control {
//   background-color: #FFFFFF;
//   border: 1px solid #E3E3E3;
//   border-radius: 4px;
//   box-shadow: none;
//   color: #444444;
//   height: 38px;
//   padding: 6px 16px;
// }
//
// .form-control:focus {
//   background-color: #FFFFFF;
//   border: 1px solid #9A9A9A;
//   box-shadow: none;
//   outline: 0 none;
// }
//
// .form-control + .form-control-feedback {
//   border-radius: 6px;
//   font-size: 14px;
//   padding: 0 12px 0 0;
//   position: absolute;
//   right: 25px;
//   top: 13px;
//   vertical-align: middle;
// }
//
// .input-lg {
//   height: 56px;
//   padding: 10px 16px;
// }
//
// .has-success .form-control, .has-error .form-control, .has-success .form-control:focus, .has-error .form-control:focus {
//   border-color: #E3E3E3;
//   box-shadow: none;
// }
//
// .has-success .form-control, .has-success .form-control-feedback {
//   color: #05AE0E;
// }
//
// .has-error .form-control, .has-error .form-control-feedback {
//   color: #FF3B30;
// }
//
// .has-success .form-control:focus {
//   border-color: #05AE0E;
// }
//
// .has-error .form-control:focus {
//   border-color: #FF3B30;
// }
//
// .input-group-addon {
//   background-color: #FFFFFF;
//   border: 1px solid #E3E3E3;
//   border-radius: 4px;
// }
//
// .form-control:focus + .input-group-addon, .form-control:focus ~ .input-group-addon {
//   background-color: #FFFFFF;
//   border-color: #9A9A9A;
// }
//
// .input-group .form-control:first-child, .input-group-addon:first-child, .input-group-btn:first-child > .btn, .input-group-btn:first-child > .dropdown-toggle, .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {
//   border-right: 0 none;
// }
//
// // .input-group .form-control:last-child, .input-group-addon:last-child, .input-group-btn:last-child > .btn, .input-group-btn:last-child > .dropdown-toggle, .input-group-btn:first-child > .btn:not(:first-child) {
// //   border-left: 0 none;
// // }
//
// .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
//   background-color: #EEEEEE;
//   color: #999999;
//   cursor: not-allowed;
// }
//
/*           Labels & Progress-bar              */
.label {
  padding: 0.2em 0.6em 0.2em;
  border: 1px solid #999999;
  border-radius: 3px;
  color: #999999;
  background-color: #FFFFFF;
  font-weight: 500;
  font-size: 11px;
  text-transform: uppercase;
}

.label-primary {
  border-color: #3472F7;
  color: #3472F7;
}

.label-info {
  border-color: #2CA8FF;
  color: #2CA8FF;
}

.label-success {
  padding: 0.2em 2.1em;
  border-color: #05AE0E;
  color: #05AE0E;
}

.label-warning {
  border-color: #FF9500;
  padding: 0.2em 0.8em;
  color: #FF9500;
}

.label-danger {
  border-color: #FF3B30;
  color: #FF3B30;
}

.label-unpaid {
  border-color: #FF3B30;
  background-color: #FF3B30;
  padding: 0.2em 1.5em;
  color: #fff;
}
.label-soldout {
  border-color: #FF3B30;
  background-color: #FF3B30;
  color: #fff;
}

.label-success:hover,
.label-success:focus,
.label-success:active,
.label-success.active,
.label-success.open,
.label-success.disabled,
.label-success[disabled] {
  background-color: #05AE0E;
  padding: 0.2em 2.1em;
  color: #fff;
}
.label-default:hover,
.label-default:focus,
.label-default:active,
.label-default.active,
.label-default.open,
.label-default.disabled,
.label-default[disabled] {
  background-color: #999;
  color: #fff;
}

.label-warning:hover,
.label-warning:focus,
.label-warning:active,
.label-warning.active,
.label-warning.disabled,
.label-warning[disabled] {
  background-color: #FF9500;
  color: #fff;
  padding: 0.2em 0.8em;
}

.label-danger:hover,
.label-danger:focus,
.label-danger:active,
.label-danger.active,
.label-danger.disabled,
.label-danger[disabled] {
  background-color: #FF3B30;
  color: #fff;
}
.label-unpaid:hover,
.label-unpaid:focus,
.label-unpaid:active,
.label-unpaid.active,
.label-unpaid.disabled,
.label-unpaid[disabled] {
  background-color: #FF3B30;
  padding: 0.2em 1.5em;
  color: #fff;
}

.label.label-fill {
  color: #FFFFFF;
}

.label-primary.label-fill, .progress-bar, .progress-bar-primary {
  background-color: #3472F7;
}

.label-info.label-fill, .progress-bar-info {
  background-color: #2CA8FF;
}

.label-success.label-fill, .progress-bar-success {
  background-color: #05AE0E;
}

.label-warning.label-fill, .progress-bar-warning {
  background-color: #FF9500;
}

.label-danger.label-fill, .progress-bar-danger {
  background-color: #FF3B30;
}

.label-default.label-fill {
  background-color: #999999;
}
//
// .progress {
//   background-color: #E5E5E5;
//   border-radius: 3px;
//   box-shadow: none;
//   height: 4px;
// }
//
// .progress-thin {
//   height: 2px;
// }
//
//
/* Navigation menu  */
// .nav-pills > li + li {
//   margin-left: 0;
// }
//
// .nav-pills > li > a {
//   border: solid #2CA8FF;
//   border-radius: 0;
//   color: #2CA8FF;
//   margin-left: -1px;
// }
//
// .nav > li > a:hover, .nav > li > a:focus {
//   background-color: #FF9500;
// }
//
//
// .nav-pills.ct-orange > li.active > a, .nav-pills.ct-orange > li.active > a:hover, .nav-pills.ct-orange > li.active > a:focus, .pagination.ct-orange > .active > a, .pagination.ct-orange > .active > span, .pagination.ct-orange > .active > a:hover, .pagination.ct-orange > .active > span:hover, .pagination.ct-orange > .active > a:focus, .pagination.ct-orange > .active > span:focus {
//   background-color: #FF9500;
//   color: white;
// }
//
// .nav-pills.ct-orange > li > a {
//   border: 1px solid #FF9500;
//   color: #FF9500;
// }
//
// .nav-pills.ct-orange > li > a:hover {
//   color: white;
// }
//
// .nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
//   background-color: #2CA8FF;
//   color: #FFFFFF;
// }
//
// .nav-pills > li:first-child > a {
//   border-radius: 4px 0 0 4px;
//   margin: 0;
// }
//
// .nav-pills > li:last-child > a {
//   border-radius: 0 4px 4px 0;
// }

//
// .pagination.no-border > li > a, .pagination.no-border > li > span {
//   border: 0;
// }
//
// .pagination > li > a, .pagination > li > span, .pagination > li:first-child > a, .pagination > li:first-child > span, .pagination > li:last-child > a, .pagination > li:last-child > span {
//   border-radius: 50%;
//   margin: 0 2px;
//   color: #777777;
// }
//
// .pagination > li.active > a, .pagination > li.active > span, .pagination > li.active > a:hover, .pagination > li.active > span:hover, .pagination > li.active > a:focus, .pagination > li.active > span:focus {
//   background-color: #2CA8FF;
//   border: 0;
//   color: #FFFFFF;
//   padding: 7px 13px;
// }
//
// .text-primary, .text-primary:hover {
//   color: #1D62F0;
// }
//
// .text-info, .text-info:hover {
//   color: #109CFF;
// }
//
// .text-success, .text-success:hover {
//   color: #0C9C14;
// }
//
// .text-warning, .text-warning:hover {
//   color: #ED8D00;
// }
//
// .text-danger, .text-danger:hover {
//   color: #EE2D20;
// }
//
// .modal-header {
//   border: 0 none;
// }
//
// .logo-container {
//   left: 50px;
//   position: absolute;
//   top: 20px;
//   z-index: 3;
// }
//
// .logo-container .logo {
//   overflow: hidden;
//   border-radius: 50%;
//   border: 1px solid #333333;
//   width: 60px;
//   float: left;
// }
//
// .logo-container .brand {
//   font-size: 18px;
//   color: #FFFFFF;
//   line-height: 20px;
//   float: left;
//   margin-left: 10px;
//   margin-top: 10px;
//   width: 60px
// }
//
// .card {
//   background-color: #FFFFFF;
//   padding: 0px 0 20px;
//   width: 100%;
// }
//
// .pick-class-label {
//   border-radius: 8px;
//   color: #ffffff;
//   cursor: pointer;
//   display: inline;
//   font-size: 75%;
//   font-weight: bold;
//   line-height: 1;
//   margin-right: 10px;
//   padding: 15px 23px;
//   text-align: center;
//   vertical-align: baseline;
//   white-space: nowrap;
// }
//
// /*      Here the code for the wizard           */
//
// label {
//   font-weight: 400;
// }
//
// .image-container {
//   height: 100%;
//   background-position: center center;
//   background-size: cover;
// }
//
.wizard-container {
  z-index: 3;
}

.wizard-card {
  min-height: 1180px;
  background-color: #FFFFFF;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15), 0 0 1px 1px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}

.wizard-container .wizard-header {
  padding-top: 65px;
}

.wizard-card .wizard-header h3 {
  font-weight: 200;
  text-align: center;
}

.wizard-card ul li a {
  border: 0 !important;
  text-transform: uppercase;
  background-color: #999999;
  color: #FFFFFF !important;
  font-size: 14px;
}


.wizard-card.ct-wizard-orange > ul > li.active a {
  background-color: #ff9500;
}


.wizard-card > ul > li > a:hover, .wizard-card > ul > li > a:focus {
  background-color: #999999;
  cursor: default;
}

.wizard-card .btn {
  text-transform: uppercase;
}

.wizard-card .nav-pills > li:first-child > a {
  border-radius: 0;
  margin: 0;
}

.wizard-card .nav-pills > li:last-child > a {
  border-radius: 0;
}

.wizard-card .info-text {
  text-align: center;
  font-weight: 300;
  margin: 10px 0 30px;
}

.wizard-card .choice {
  text-align: center;
  cursor: pointer;
  margin-top: 20px;
}

.wizard-card .choice .icon {
  text-align: center;
  vertical-align: middle;
  height: 116px;
  width: 116px;
  border-radius: 50%;
  background-color: #999999;
  color: #ff9500;
  margin: 0 auto 20px;
  border: 4px solid #CCCCCC;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}

.wizard-card .choice i {
  font-size: 30px;
  line-height: 111px;
}

.wizard-card .choice:hover .icon, .wizard-card .choice.active .icon {
  border-color: #2ca8ff;
}

.wizard-card.ct-wizard-blue .choice:hover .icon, .wizard-card.ct-wizard-blue .choice.active .icon {
  border-color: #3472f7;
}

.wizard-card.ct-wizard-green .choice:hover .icon, .wizard-card.ct-wizard-green .choice.active .icon {
  border-color: #05ae0e;
}

.wizard-card.ct-wizard-orange .choice:hover .icon, .wizard-card.ct-wizard-orange .choice.active .icon {
  border-color: #ff9500;
}

.wizard-card.ct-wizard-red .choice:hover .icon, .wizard-card.ct-wizard-red .choice.active .icon {
  border-color: #ff3b30;
}

.wizard-card .choice input[type="radio"] {
  position: absolute;
  left: -10000px;
  z-index: -1;
}

.wizard-card .btn-finish {
  display: none;
}

.wizard-card .description {
  color: #999999;
  font-size: 14px;
}

// .mycontent-left {
//   border-right: 1px dashed #3472f7;
// }
//
// .ticket-table {
//   text-align: left;
// }
