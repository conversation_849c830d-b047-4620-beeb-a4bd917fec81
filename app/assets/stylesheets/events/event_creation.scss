.events-masthead {
  height: 122px;
  background-image: image-url("Event-Stop-Logo.jpg");
  background-repeat: no-repeat;
  margin-top: 25px;
  margin-bottom: 20px;
}

/* ANIMATION STYLINGS
============================================================================= */
#signup-form {
  position: relative;
  min-height: 300px;
  overflow: hidden;
  padding: 30px;
}

/* basic styling for entering and leaving */
/* left and right added to ensure full width */
#form-views.ng-enter,
#form-views.ng-leave {
  position: absolute;
  left: 30px;
  right: 30px;
  transition: 0.5s all ease;
  -moz-transition: 0.5s all ease;
  -webkit-transition: 0.5s all ease;
}

/* enter animation */
#form-views.ng-enter {
  -webkit-animation: slideInRight 0.5s both ease;
  -moz-animation: slideInRight 0.5s both ease;
  animation: slideInRight 0.5s both ease;
}

/* leave animation */
#form-views.ng-leave {
  -webkit-animation: slideOutLeft 0.5s both ease;
  -moz-animation: slideOutLeft 0.5s both ease;
  animation: slideOutLeft 0.5s both ease;
}

/* ANIMATIONS
============================================================================= */
/* slide out to the left */
@keyframes slideOutLeft {
  to {
    transform: translateX(-200%);
  }
}

@-moz-keyframes slideOutLeft {
  to {
    -moz-transform: translateX(-200%);
  }
}

@-webkit-keyframes slideOutLeft {
  to {
    -webkit-transform: translateX(-200%);
  }
}

/* slide in from the right */
@keyframes slideInRight {
  from {
    transform: translateX(200%);
  }
  to {
    transform: translateX(0);
  }
}

@-moz-keyframes slideInRight {
  from {
    -moz-transform: translateX(200%);
  }
  to {
    -moz-transform: translateX(0);
  }
}

@-webkit-keyframes slideInRight {
  from {
    -webkit-transform: translateX(200%);
  }
  to {
    -webkit-transform: translateX(0);
  }
}
/* Have to set height explicity on ui-view
to prevent collapsing during animation*/
.ui-view-container {
  position: relative;
  height: 65px;
}

[ui-view].ng-enter, [ui-view].ng-leave {
  position: absolute;
  left: 0;
  right: 0;
  -webkit-transition:all .5s ease-in-out;
    -moz-transition:all .5s ease-in-out;
    -o-transition:all .5s ease-in-out;
    transition:all .5s ease-in-out;
}

[ui-view].ng-enter {
  opacity: 0;
  -webkit-transform: translateX(200%);
}

[ui-view].ng-enter-active {
  opacity: 1;
  -webkit-transform: translateX(0);
}

[ui-view].ng-leave {
  opacity: 1;
  -webkit-transform:translate3d(0, 0, 0);
  -moz-transform:translate3d(0, 0, 0);
  transform:translate3d(0, 0, 0);
}

[ui-view].ng-leave-active {
  opacity: 0;
  transform: translateX(-200%);
}
//Validation
.event-field.ng-dirty.ng-invalid {
  border-color: red;
}

span.invalid-message {
  font-weight: bold;
  color: red;
}

.set-initial-height {
  height: 20px;
}
