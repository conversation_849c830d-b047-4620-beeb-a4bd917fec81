@import 'navbar';
@import 'base_styles';
@import "buttons";
@import 'style';
@import 'select_box';
@import "events";
// @import "bootstrap-sprockets";
//@import "bootstrap/scss/bootstrap";

@import 'events/event_creation';
@import 'events/gsdk-base';
@import "font-awesome";
@import "splashscreen";
@import 'event_wizard';
@import 'dashboard';
// @import 'sweetalert2/dist/sweetalert2';
@import 'hg-admin';
@import 'time-picky';
@import 'cards';
@import 'carousel';
@import 'paymentcard';
@import 'responsivetable';
@import 'dialog';
@import 'notifications';
@import 'sidenav';
@import 'alertbar';
@import 'tile';
@import 'dropdown';
@import 'testimonials';
@import 'releases';

@import 'bootstrap_overrides';
@import 'meet-the-team';
@import 'knowledge_hub_articles';
*,
body {
    overflow-y: visible;
}

//Added by ESkelly to add shadow to card/////
.card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1)
}

////////////////////////////////////////////
#toast-container .toast {
    opacity: 1;
}

#collapse_title:after {
    content: none;
}

.uib-datepicker-popup {
    z-index: 9999999 !important;
}

.container-fluid {
    padding-left: 15px;
}

.panel-heading a.collapsed:after {
    content: "\e080";
}

.panel-heading-orange a:after {
    font-family: 'Glyphicons Halflings';
    content: "\e114";
    float: right;
    color: grey;
}

.glyphicon {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.glyphicon-chevron-left:before {
    content: "\f053";
}

.glyphicon-chevron-right:before {
    content: "\f054";
}

.panel-heading-orange a.collapsed:after {
    content: "\e080";
}

.panel-space-bottom {
    margin-bottom: 10px;
}

.feedback-error-class {
    color: red;
}

.feedback-valid-class {
    color: green;
}

.masthead {
    height: 200px;
    background-image: image-url("HG-Feedback-800x200px.png");
    background-repeat: no-repeat;
    margin-top: 10px;
    margin-bottom: 20px;
}

.nav-notices p {
    line-height: 10px !important;
}

.pdf-fixes tr {
    page-break-inside: avoid;
}

.bottom-spacer-10 {
    margin-bottom: 10px;
}

.small-gap-top {
    margin-top: 5px;
}

.small-gap-bottom {
    margin-bottom: 5px;
}

.buttonlike {
    cursor: pointer;
}

.form-group.required label:after {
    content: "*";
    color: #FF0000;
}

.refunded {
    text-decoration: line-through;
}

.gmnoprint img {
    max-width: none;
}

.no-gutter>[class*='col-'] {
    padding-left: 0 !important;
}

hr.fancy-line {
    border: 0;
}

hr.fancy-line:before {
    top: -0.5em;
    height: 1em;
}

hr.fancy-line:before {
    content: '';
    position: absolute;
    width: 100%;
}

hr.fancy-line,
hr.fancy-line:before {
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 75%);
}

.preview_step {
    padding: 25px;
    margin-top: 25px;
    position: relative;
    color: #fff;
    background-color: #999;
    border: 2px solid transparent;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
}

.preview_step::before {
    content: "\f1e5";
    font-family: "FontAwesome";
    color: black;
    background-color: white;
    position: absolute;
    top: -20px;
    left: -20px;
    height: 40px;
    width: 40px;
    text-align: center;
    border-radius: 100%;
    line-height: 40px;
    font-weight: bold;
    border: 4px solid #999;
    box-sizing: content-box;
    box-shadow: inset 2px 2px 6px rgba(0, 0, 0, 0.5);
    font-size: 100%;
}

.preview_step_active {
    padding: 25px;
    margin-top: 25px;
    position: relative;
    color: #fff;
    background-color: #ff9500;
    border: 2px solid transparent;
}

.preview_step_active::before {
    content: "\f1e5";
    font-family: "FontAwesome";
    color: black;
    background-color: white;
    position: absolute;
    top: -20px;
    left: -20px;
    height: 40px;
    width: 40px;
    text-align: center;
    border-radius: 100%;
    line-height: 40px;
    font-weight: bold;
    border: 4px solid #ff9500;
    box-sizing: content-box;
    box-shadow: inset 2px 2px 6px rgba(0, 0, 0, 0.5);
    font-size: 100%;
}

.vertical-steps .step {
    padding: 25px;
    margin-top: 25px;
    position: relative;
    color: #fff;
    border: 2px solid transparent;
}

.vertical-steps .sidestep {
    padding: 25px;
    margin-top: 25px;
    position: relative;
    color: #fff;
    border: 2px solid transparent;
}

.vertical-steps .step:first-child {
    margin-top: 30px;
}

.vertical-steps .step:last-child {
    margin-bottom: 30px;
}

.vertical-steps .step h1,
.vertical-steps .step h2,
.vertical-steps .step h3,
.vertical-steps .step h4,
.vertical-steps .step h5,
.vertical-steps .step h6,
.vertical-steps .step h7 {
    margin-top: 0;
}

.vertical-steps .step::before {
    content: attr(data-number);
    color: black;
    background-color: white;
    position: absolute;
    top: -20px;
    left: -20px;
    height: 40px;
    width: 40px;
    text-align: center;
    border-radius: 100%;
    line-height: 40px;
    font-weight: bold;
    border: 4px solid red;
    box-sizing: content-box;
    box-shadow: inset 2px 2px 6px rgba(0, 0, 0, 0.5);
    font-size: 100%;
}

// .vertical-steps .sidestep::before {
//     content: attr(icon-code);
//     font-family: "FontAwesome";
//     color: black;
//     background-color: white;
//     position: absolute;
//     top: -20px;
//     left: -20px;
//     height: 40px;
//     width: 40px;
//     text-align: center;
//     border-radius: 100%;
//     line-height: 40px;
//     font-weight: bold;
//     border: 4px solid red;
//     box-sizing: content-box;
//     box-shadow: inset 2px 2px 6px rgba(0, 0, 0, 0.5);
//     font-size: 100%;
// }
.nav-link {
    position: relative;
}

.nav-item .navfloat::before {
    content: attr(data-number);
    color: black;
    background-color: white;
    position: absolute;
    top: -10px;
    left: -10px;
    height: 20px;
    width: 20px;
    text-align: center;
    border-radius: 100%;
    line-height: 20px;
    font-weight: bold;
    border: 4px solid red;
    border-color: #999;
    box-sizing: content-box;
    box-shadow: inset 2px 2px 6px rgba(0, 0, 0, 0.5);
    font-size: 100%;
}

.nav-pills .nav-link.active {
    background: #ff9500;
}

.nav-pills .nav-link {
    border-radius: 0;
}

.navbarstepactive::before {
    color: white;
    border-color: #ff9500 !important;
    text-align: center;
    border-radius: 100%;
    font-weight: bold;
    font-size: 100%;
}

.vertical-steps .step::after {
    content: "";
    position: absolute;
    border: 20px solid transparent;
    bottom: -40px;
    right: 40px;
    height: 0;
    width: 0;
    border-top-color: red;
    margin-left: -20px;
    z-index: 1;
}

.vertical-steps .sidestep::after {
    content: "";
    position: absolute;
    border: 20px solid transparent;
    bottom: -40px;
    right: 40px;
    height: 0;
    width: 0;
    border-top-color: red;
    margin-left: -20px;
    z-index: 1;
}

.vertical-steps .sidestep:nth-child(1) {
    background-color: #999;
}

.vertical-steps .sidestep:nth-child(1)::before {
    border-color: #999;
}

.vertical-steps .sidestep:nth-child(1)::after {
    border-top-color: #999;
}

.vertical-steps .sidestep:nth-child(2) {
    background-color: #999;
}

.vertical-steps .sidestep:nth-child(2)::before {
    border-color: #999;
}

.vertical-steps .sidestep:nth-child(2)::after {
    border-top-color: #999;
}

.vertical-steps .sidestep:nth-child(3) {
    background-color: #999;
}

.vertical-steps .sidestep:nth-child(3)::before {
    border-color: #999;
}

.vertical-steps .sidestep:nth-child(3)::after {
    border-top-color: #999;
}

.vertical-steps .sidestep:nth-child(4) {
    background-color: #999;
}

.vertical-steps .sidestep:nth-child(4)::before {
    border-color: #999;
}

.vertical-steps .sidestep:nth-child(4)::after {
    border-top-color: #999;
}

.vertical-steps .stepactive {
    background-color: #ff9500 !important;
}

.vertical-steps .stepactive::before {
    border-color: #ff9500 !important;
}

.vertical-steps .stepactive::after {
    border-top-color: #ff9500 !important;
}

.vertical-steps .step:last-child::after {
    display: none;
}

@media only screen and (max-device-width: 1024px) and (orientation: portrait) {
    p {
        font-size: 12px !important
    }
    #stafftable {
        width: 500px !important
    }
    #pushtop {
        margin-top: 50px;
    }
}

#previewwatermark {
    font-family: Sans-Serif;
    font-size: 125px;
    font-weight: bolder;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-top: -50px;
    margin-left: -100px;
    opacity: 0.15;
    z-index: 999;
    color: #ff9500;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    pointer-events: none;
}

//For columns in bootstrap
.nopadding {
    padding: 0 !important;
    margin: 0 !important;
}

//.my-handle {
//  cursor: move;
//  cursor: -webkit-grabbing;
//}
