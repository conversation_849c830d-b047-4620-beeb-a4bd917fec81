/////* Dropdown Button */
////.dropbtn {
////  background-color: #4CAF50;
////  color: white;
////  padding: 16px;
////  font-size: 16px;
////  border: none;
////  cursor: pointer;
////}
////
/////* Dropdown button on hover & focus */
////.dropbtn:hover, .dropbtn:focus {
////  background-color: #3e8e41;
////}
////
/////* The container <div> - needed to position the dropdown content */
////.dropdown {
////  position: relative;
////  display: inline-block;
////}
//
///* Dropdown Content (Hidden by Default) */
//.mydropdown-content {
//  display: none;
//  position: absolute;
//  background-color: #d3d3d3;
//  min-width: 160px;
//  border-style: solid;
//  border-width: 2px 10px 4px 20px;
//  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
//  z-index: 100;
//}
//
///* Links inside the dropdown */
//.mydropdown-content a {
//  color: black;
//  padding: 12px 16px;
//  text-decoration: none;
//  display: block;
//}
//
///* Change color of dropdown links on hover */
//.mydropdown-content a:hover {background-color: #f1f1f1}
//
///* Show the dropdown menu (use JS to add this class to the .dropdown-content container when the user clicks on the dropdown button) */
//.show {display:block;}