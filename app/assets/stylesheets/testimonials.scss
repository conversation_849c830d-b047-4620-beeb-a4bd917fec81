$orange: #ff9500;

#testimonials{
  padding: 0;
  background: url('testi-bg.jpg') center center no-repeat;
  background-size: cover;
  .testi-group{
    .carousel-inner{
      display: flex;
    }
    .carousel-item{
      max-height: none;
      margin: 50px auto 80px 0;
      padding: 2rem;
      background: #fff;
      border-radius: 10px;
    }
    .testi{
      font-weight: 400;
      color:#000;
      width: 100%;
      position:relative;
      font-size: .9rem;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      height:400px !important;
      .who{
        font-weight: 700;
        color:$orange;
      }
    }
  }
}

@media screen and (min-width: 576px) {
  #testimonials {
    .testi-group {
      .testi {
        justify-content: center;
        height:100%;
      }
    }
  }
}

@media screen and (min-width: 992px) {
  #testimonials {
    .testi-group {
      .testi {
        font-size: 1rem;
      }
    }
  }
}
