// Place all the styles related to the KnowledgeHubArticles controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

$positive-color: #92e000;
$negative-color: #ff4400;

.space-between {
    display:flex;
    justify-content: space-between;
    align-items: stretch;
    
}

.display-on-parent-hover {
    visibility: hidden;
}

*:hover .display-on-parent-hover {
    visibility: visible;
}

.white-box {
    color: #fff;
    border-radius: 5px;
    padding:5px;
    margin:5px;
}

.form-has-quill-rt-editor {
    .quill-rt-editor {
        width:100%;
        min-width:100%;
        max-width:100%;
    }

}

.has-error {
    .help-block {
        color:red;
        padding-top:1px;
        padding-bottom:1px;
    }
    input {
        border-color:red;
    }
    .quill-rt-editor {
        border-color:red;
    }
    .ql-toolbar {
        border-color:red;
    }
}

.corner-ribbon {
    top: -10px;
    left: -10px;
    width: 150px;
    height: 150px;
    overflow:hidden;
    position: absolute;
    span {
        position: absolute;
        display: block;
        width: 225px;
        padding: 15px 0;
        background-color: #ff9500;
        box-shadow: 0 5px 10px rgba(0,0,0,.1);
        color: #fff;
        font-size:14px;
        text-shadow: 0 1px 1px rgba(0,0,0,.2);
        text-transform: uppercase;
        text-align: center;
        right: -25px;
        top: 30px;
        transform: rotate(-45deg);
    }
}

.corner-ribbon::before,
.corner-ribbon::after {
    border-top-color: transparent;
    border-left-color: transparent;
    position: absolute;
    z-index: -1;
    content: '';
    display: block;
    border: 5px solid #ff9500;
}
.corner-ribbon::before {
    top: 0;
    right: 0;

}
.corner-ribbon::after {
    bottom: 0;
    left: 0;
}

.corner-ribbon.positive {
    span {
        background-color: $positive-color;
    }

}
.corner-ribbon.positive::before,
.corner-ribbon.positive::after {
    border-color: $positive-color;
}
.corner-ribbon.negative {
    span {
        background-color: $negative-color;
    }

}
.corner-ribbon.negative::before,
.corner-ribbon.negative::after {
    border-color: $negative-color;
}

.ql-editor img {
    max-width:100%;
}