@import "base_styles";

%dropdown_menu_extend {
  color: $white;
  border-bottom: none !important;
  text-transform: uppercase;
}

/* BASIC THEME CONFIGURATION */
body {
  // color: #797979 !important;
  // font-family: 'Ruda', sans-serif !important;
  padding: 0 !important;
  margin: 0 !important;
}

a,
a:focus,
a:hover {
  text-decoration: none;
  outline: none;
}

::selection {
  background: $hg_orange;
  color: $white;
}

::-moz-selection {
  background: $hg_orange;
  color: $white;
}

/* Bootstrap Modifications */
// .modal-header {
//   background: #ff9500;
// }

// .modal-title {
//   color: $white;
// }

.btn-round {
  border-radius: 20px;
  -webkit-border-radius: 20px;
}


/*Helpers*/
.centered {
  text-align: center;
}

/*sidebar navigation*/
.sidebar {
  width: 210px;
  height: 100%;
  position: fixed;
  background: $color_sidebar;
  h5 {
    color: $white;
    font-weight: 700;
  }
  ul li {
    position: relative;
    list-style-type: none;
  }
  .sub-menu > .sub li {
    padding-left: 32px;
    &:last-child {
      padding-bottom: 10px;
    }
  }
  > ul > li {
    > ul.sub {
      display: none;
      > li > a {
        display: block;
      }
    }
    &.active > ul.sub {
      display: block;
    }
  }
}

ul.sidebar-menu {
  margin: -2px 0 0;
  padding: 0;
  margin-top: 30px;
  li ul.sub {
    margin: -2px 0 0;
    padding: 0;
    li {
      background: $color_sidebar;
      margin-bottom: 0;
      margin-left: 0;
      margin-right: 0;
      &:last-child {
        border-radius: 0 0 4px 4px;
      }
    }
  }
}

.nav-collapse.collapse {
  display: inline;
}

ul.sidebar-menu li ul.sub li a {
  font-size: 12px;
  padding: 6px 0;
  line-height: 35px;
  height: 35px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: #aeb2b7;
}

ul.sidebar-menu li ul.sub li a:hover {
  color: $white;
  background: transparent;
}

ul.sidebar-menu li ul.sub li.active a {
  color: $hg_orange;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
  display: block;
}

ul.sidebar-menu li {
  margin-bottom: 5px;
}

ul.sidebar-menu li.sub-menu {
  line-height: 15px;
}

ul.sidebar-menu li a span {
  display: inline-block;
}

ul.sidebar-menu li a {
  color: $white;
  text-decoration: none;
  display: block;
  padding: 15px 0 15px 10px;
  font-size: 14px;
  outline: none;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

ul.sidebar-menu li a.active,
ul.sidebar-menu li a:focus,
ul.sidebar-menu li a:hover {
  background: $hg_orange;
  color: $white;
  display: block;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

ul.sidebar-menu li a i {
  font-size: 15px;
  padding-right: 6px;
}

ul.sidebar-menu li a:focus i,
ul.sidebar-menu li a:hover i {
  color: $white;
}

ul.sidebar-menu li a.active i {
  color: $white;
}

.mail-info,
.mail-info:hover {
  margin: -3px 6px 0 0;
  font-size: 11px;
}

/* MAIN CONTENT CONFIGURATION */
.main-content {
  margin-left: 210px;
  margin-top: 80px;
  // position: absolute;
}

.main-content-shrink {
  margin-left: 0;
}

.header {
  min-height: 60px;
  padding: 0 15px;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1002;
}

.footer {
  min-height: 60px;
  padding: 0 15px;
}

.black-bg {
  background: #535c73;
  border-bottom: 1px solid black;
}

.wrapper {
  display: inline-block;
  margin-top: 20px;
  padding: 0 15px 15px;
  width: 100%;
}

a.logo {
  font-size: 20px;
  color: $white;
  float: left;
  margin-top: 15px;
  //text-transform: uppercase;
  b {
    font-weight: 900;
  }
  &:hover {
    text-decoration: none;
    outline: none;
  }
  &:focus {
    text-decoration: none;
    outline: none;
  }
  span {
    color: $hg_orange;
  }
}

/*notification*/

.notify-row {
  float: left;
  margin-top: 5px;
  margin-left: 20px;
  .notification span.label {
    display: inline-block;
    height: 15px;
    width: 20px;
    padding: 5px;
  }
}

.notify-row .badge {
  position: absolute;
  right: 5%;
  top: -10px;
  z-index: 100;
}

.dropdown-menu {
  &.extended {
    max-width: 300px !important;
    min-width: 160px !important;
    top: 42px;
    width: 235px !important;
    padding: 0;
    box-shadow: 0 2px 5px #000 !important;
    border: none !important;
    border-radius: 4px;
    li {
      p {
        background-color: $stonegrey;
        color: #666;
        margin: 0;
        padding: 10px;
        border-radius: 4px 4px 0 0;
        &.green {
          //background-color: $hg_orange;
          color: $white;
        }
        &.yellow {
          background-color: #fcb322;
          color: black;
        }
      }
      a {
        border-bottom: 1px solid #ebebeb !important;
        font-size: 12px;
        list-style: none;
        padding: 15px 10px !important;
        width: 100%;
        display: inline-block;
        &:hover {
          background-color: #f7f8f9 !important;
          color: #2e2e2e;
        }
      }
    }
    .progress {
      margin-bottom: 0 !important;
      height: 10px;
    }
  }
  &.tasks-bar .task-info {
    .desc {
      font-size: 13px;
      font-weight: normal;
    }
    .percent {
      display: inline-block;
      float: right;
      font-size: 13px;
      font-weight: 600;
      padding-left: 10px;
      margin-top: -4px;
    }
  }
  &.inbox li a {
    .subject {
      display: block;
      .from {
        font-size: 12px;
        font-weight: 600;
      }
      .time {
        font-size: 11px;
        font-style: italic;
        font-weight: bold;
        position: absolute;
        right: 5px;
      }
    }
    .message {
      display: block !important;
      font-size: 11px;
    }
    .photo img {
      border-radius: 2px 2px 2px 2px;
      float: left;
      height: 40px;
      margin-right: 4px;
      width: 40px;
    }
  }
}

@media screen and(-webkit-min-device-pixel-ratio: 0) {
  .dropdown-menu.extended {
    box-shadow: 0 2px 8px #fff !important;
    width: 200% !important;
  }
}

.dropdown-menu.extended.logout > li {
  float: left;
  text-align: center;
  width: 33.3%;
  &:last-child {
    float: left;
    text-align: center;
    width: 100%;
    background: #a9d96c;
    border-radius: 0 0 2px 2px;
    > a {
      @extend %dropdown_menu_extend;
      &:hover {
        @extend %dropdown_menu_extend;
        > i {
          color: $white;
        }
      }
    }
  }
  > a {
    color: #a4abbb;
    border-bottom: none !important;
    &:hover {
      background: none !important;
      i {
        color: #50c8ea;
      }
    }
    i {
      font-size: 17px;
    }
    > i {
      display: block;
    }
  }
}

.full-width .dropdown-menu.extended.logout > li > a:hover {
  background: none !important;
  color: #50c8ea !important;
}

.log-arrow-up {
  background: url("../img/arrow-up.png") no-repeat;
  width: 20px;
  height: 11px;
  position: absolute;
  right: 20px;
  top: -10px;
}

/*----*/
.notify-arrow {
  border-style: solid;
  border-width: 0 9px 9px;
  height: 0;
  margin-top: 0;
  opacity: 0;
  position: absolute;
  right: 7px;
  top: -18px;
  transition: all 0.25s ease 0s;
  width: 0;
  z-index: 10;
  margin-top: 10px;
  opacity: 1;
}

.notify-arrow-yellow {
  border-color: transparent transparent #FCB322;
  border-bottom-color: #FCB322 !important;
  border-top-color: #FCB322 !important;
}

.mtbox {
  margin-top: 80px;
  margin-bottom: 40px;
}

.box1 {
  padding: 15px;
  text-align: center;
  color: #989898;
  border-bottom: 1px solid #989898;
  span {
    font-size: 20px;
  }
  h3 {
    text-align: center;
  }
}

.box0 {
  &:hover {
    background: $white;
    box-shadow: 1px 3px 10px 1px rgba(0, 0, 0, 0.2);
    .box1 {
      border-bottom: 1px solid $white;
      cursor: pointer;
    }
    p {
      color: #ff865c;
    }
  }
  p {
    text-align: center;
    font-size: 12px;
    color: #f2f2f2;
  }
}

@media (max-width: 768px) {
  .header {
    position: absolute;
  }
  .sidebar {
    height: auto;
    overflow: hidden;
    position: absolute;
    width: 100%;
    z-index: 1001;
    margin: 0 !important;
    .btn-navbar {
      .arrow {
        position: absolute;
        right: 35px;
        width: 0;
        height: 0;
        top: 48px;
        border-bottom: 15px solid #282e36;
        border-left: 15px solid transparent;
        border-right: 15px solid transparent;
      }
      &.collapsed .arrow {
        display: none;
      }
    }
    > ul > li {
      margin: 0 10px 5px;
      > a {
        height: 35px;
        line-height: 35px;
        padding: 0 10px;
        text-align: left;
        width: 100%;
        > span {
          line-height: 35px;
        }
        i {
        }
        .arrow.open {
          margin-right: 10px;
          margin-top: 15px;
        }
        &:hover .arrow.open {
          margin-top: 15px;
        }
        &:focus .arrow.open {
          margin-top: 15px;
        }
      }
      &.active > a .arrow.open {
        margin-top: 15px;
      }
      > ul.sub > li {
        width: 100%;
        > a {
          background: transparent !important;
          &:hover {
          }
        }
      }
    }
    ul > li {
      > a {
        .arrow {
          margin-right: 10px;
          margin-top: 15px;
        }
        &:hover .arrow {
          margin-top: 15px;
        }
        &:focus .arrow {
          margin-top: 15px;
        }
      }
      &.active > a .arrow {
        margin-top: 15px;
      }
    }
  }
  .btn {
    margin-bottom: 5px;
  }

  .modal-footer .btn {
    margin-bottom: 0 !important;
  }
  ul.sidebar-menu li ul.sub li a {
    padding: 0;
  }
}
