@import 'base_styles';
@import "buttons";
@import 'style';
@import 'bootstrap_overrides';
@import "font-awesome";
@import 'cards';
@import 'tile';
@import 'dropdown';

[class*="fontawesome-"]:before {
  font-family: 'FontAwesome', sans-serif;
}

h1,
h2,
h3,
h4 {
  font-family: Helvetica, Arial, sans-serif;
  font-weight: 900;
}

h5,
h6 {
  font-family: 'Roboto', sans-serif;
}

/*body {
  font-family: 'Open Sans', sans-serif !important;
} */

input {
  border: 0;
  color: inherit;
  font: inherit;
  margin: 0;
  outline: 0;
  padding: 0;
  transition: background-color 0.3s;
}

.heading {
  margin: 20px 0;
}

.pad {
  padding: 0 10px;
  max-width: 25%; 
}

.header-image {
  display: block;
  width: 100%;
  text-align: center;
  background: no-repeat center center scroll;
  background-image: image-url("splashscreen-header.jpg");
  -webkit-background-size: cover;
  -moz-background-size: cover;
  background-size: cover;
  -o-background-size: cover;
  z-index: 99;
}

.header-textbox {
  background: rgba(0, 0, 0, 0.4);
}

.header-textbox-inner {
  background: rgba(0, 0, 0, 0);
}


body,
html {
  width: 100%;
  height: 100%;
}

body {
  margin: 0; // font-size:100%;
//   background: rgba(14, 24, 33, 0.95);
  font-family: 'Sentinel A', 'Sentinel B', Georgia, serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 100px;
  background: #7f8c8d;
  display: inline-block;
  text-align: center;
  cursor: pointer;
}

.buttonlike {
  cursor: pointer;
}

.form-group.required label:after {
  content: "*";
  color: #FF0000;
}

.no-gutter > [class*='col-'] {
  padding-left:0 !important;
}

