// Vue 3 compatible event bus implementation for dashboard
import mitt from 'mitt';

const emitter = mitt();

// Create a composable function to access the event bus throughout the application
export function useDashboardEventBus() {
  return {
    // Emit an event with optional data
    emit: (event, data) => emitter.emit(event, data),
    
    // Subscribe to an event
    on: (event, callback) => emitter.on(event, callback),
    
    // Unsubscribe from an event
    off: (event, callback) => emitter.off(event, callback)
  };
}

// Export the event bus instance for direct use if needed
export default {
  emit: emitter.emit,
  on: emitter.on,
  off: emitter.off
};