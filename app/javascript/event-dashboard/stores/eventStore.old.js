// Store for event-related state management using Pinia
import { defineStore } from 'pinia';

// Define the event store
export const useEventStore = defineStore('event', {
  // State equivalent to Vuex state
  state: () => ({
    event: {},
    eventBooking: null,
    selectedTickets: null,
    bookerRegResponses: [],
    bookerDetails: null,
    fees: null,
    readonly: false,
    bookingToken: null,
    importContactJobID: null,
    removeContactJobID: null,
    chargeable: true,
    unconfirmedCount: 0
  }),

  // Getters equivalent to Vuex getters
  getters: {
    getEvent: (state) => state.event,
    getEventBooking: (state) => state.eventBooking,
    getBookerDetails: (state) => state.bookerDetails,
    getFees: (state) => state.fees,
    getReadOnly: (state) => state.readonly,
    getBookingToken: (state) => state.bookingToken,
    getImportContactJobID: (state) => state.importContactJobID,
    getRemoveContactJobID: (state) => state.removeContactJobID,
    getChargeable: (state) => state.chargeable,
    getUnconfirmedCount: (state) => state.unconfirmedCount
  },

  // Actions equivalent to Vuex mutations (plus any async operations)
  actions: {
    // Refresh state when changing events
    refreshState(event_id) {
      if (this.event.id !== event_id) {
        this.importContactJobID = null;
        this.removeContactJobID = null;
      }
      this.eventBooking = null;
      this.selectedTickets = null;
      this.bookerRegResponses = [];
      this.fees = null;
      this.readonly = false;
      this.bookingToken = null;
    },

    // Set selected tickets
    setSelectedTickets(tickets) {
      this.selectedTickets = tickets;
    },

    // Set event
    setEvent(event) {
      this.event = event;
    },

    // Set event booking
    setEventBooking(eventBooking) {
      this.eventBooking = eventBooking;
    },

    // Set booker registration responses
    setBookerRegResponses(responses) {
      this.bookerRegResponses = responses;
    },

    // Set booker details
    setBookerDetails(bookerDetails) {
      bookerDetails.user_type = 'booker';
      this.bookerDetails = bookerDetails;
    },

    // Set fees
    setFees(fees) {
      this.fees = fees;
    },

    // Set readonly
    setReadOnly(readonly) {
      this.readonly = readonly;
    },

    // Set booking token
    setBookingToken(token) {
      this.bookingToken = token;
    },

    // Set import contact job ID
    setImportContactJobID(job_id) {
      this.importContactJobID = job_id;
    },

    // Set remove contact job ID
    setRemoveContactJobID(job_id) {
      this.removeContactJobID = job_id;
    },

    // Set chargeable
    setChargeable(hasPaidTickets) {
      this.chargeable = hasPaidTickets;
    },

    // Set unconfirmed count
    setUnconfirmedCount(count) {
      this.unconfirmedCount = count;
    }
  },

  // Add persistence similar to vuex-persistedstate
  persist: {
    key: 'event-store',
    storage: localStorage
  }
});
