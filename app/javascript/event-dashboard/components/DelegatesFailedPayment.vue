<!-- filepath: /Users/<USER>/Documents/Development/HGEvents/app/javascript/event-dashboard/components/DelegatesFailedPayment.vue -->
<template>
  <div class="col-12 q-mb-md">
    <q-card>
      <q-card-section class="hg-underline">
        <div class="text-h6">Bookings Failed Due To Payment Issue</div>
      </q-card-section>
      <q-card-section>
        <q-markup-table flat bordered>
          <thead>
            <tr>
              <th></th>
              <th>Booking ID</th>
              <th>Email Address</th>
              <th>Name</th>
              <th>Confirmed Date/Time</th>
              <th>Payment Type</th>
              <th>Failure Reason</th>
              <th>Book Again</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(booking, idx) in attendeesFailed" :key="idx">
              <tr>
                <td>
                  <q-btn
                    size="sm"
                    flat
                    round
                    :icon="booking.expanded ? 'remove' : 'add'"
                    @click="booking.expanded = !booking.expanded"
                  />
                </td>
                <td>{{ booking.id }}</td>
                <td>{{ booking.email }}</td>
                <td>{{ booking.name }}</td>
                <td>{{ formatDate(booking.booking_date) }}</td>
                <td>
                  {{
                    humanizeString(booking.payment_error.payment_method.type)
                  }}
                </td>
                <td>{{ booking.payment_error.message }}</td>
                <td>
                  <q-btn
                    color="primary"
                    label="Book Again"
                    size="sm"
                    no-caps
                    @click="bookAgain(booking)"
                  />
                </td>
              </tr>
              <tr v-if="booking.expanded">
                <td colspan="8">
                  <div class="q-pa-md bg-grey-1">
                    <strong>Booking Payment Error Message:</strong>
                    <pre>{{ booking.payment_error }}</pre>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </q-markup-table>

        <div class="q-mt-md text-caption">Total Records: {{ totalItems }}</div>

        <div class="q-mt-md">
          <q-pagination
            v-model="selectedPage"
            :max="Math.ceil(totalItems / 100)"
            :max-pages="5"
            :boundaries="1"
            @update:model-value="pageChanged"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import axios from "axios";

// Setup dependencies
const $q = useQuasar();
const eventStore = useEventStore();

// Component state
const event = computed(() => eventStore.getEvent);
const attendeesFailed = ref([]);
const totalItems = ref(0);
const selectedPage = ref(1);
const selectedAttendee = ref("");
const selectedTag = ref("");
const selectedEvent = ref("0");
const optInStatus = ref("0");
const eventStatus = ref("0");
const selectedDateFrom = ref(null);
const selectedDateTo = ref(null);

// Format dates with local formatting
const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

// Methods
const bookAgain = (booking) => {
  $q.dialog({
    title: "Are you sure?",
    message: "You will be redirected to the bookings page",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // Please see routes, invite goes to accept_invite not invite as expected
    window.open(`/invite/${booking.uuid}`, "_blank");
  });
};

const bookForAttendee = (booking) => {
  $q.dialog({
    title: `Do you want to book for: ${booking.name}?`,
    message: "You will be redirected to the bookings page",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    window.open(`/${booking.user_invite_url}`, "_blank");
  });
};

const pageChanged = (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  axios
    .get(`/registered_users/${event.value.id}/show_payments_failed`, {
      params: {
        page: selectedPage.value,
        attendeeFilter: selectedAttendee.value,
        eventFilter: selectedEvent.value,
        tagFilter: selectedTag.value,
        dateToFilter: selectedDateTo.value,
        dateFromFilter: selectedDateFrom.value,
      },
    })
    .then((response) => {
      // Add 'expanded' property to each booking
      attendeesFailed.value = response.data.event_bookings.map((booking) => ({
        ...booking,
        expanded: false,
      }));
      totalItems.value = response.data.total_count;
    })
    .catch((error) => {
      console.error("Error fetching failed payments:", error);
      $q.notify({
        message: "Failed to load payment failure data",
        color: "negative",
        icon: "error",
      });
    });
};

const humanizeString = (humanizeMe) => {
  if (!humanizeMe) return "";
  const string = humanizeMe.split("_").join(" ").toLowerCase();
  return string.charAt(0).toUpperCase() + string.slice(1);
};

const clearFilter = () => {
  selectedAttendee.value = "";
  selectedTag.value = "";
  selectedEvent.value = "0";
  optInStatus.value = "0";
  eventStatus.value = "0";
  selectedDateFrom.value = null;
  selectedDateTo.value = null;
  pageChanged();
};

// Fetch data on component mount
onMounted(() => {
  pageChanged();
});
</script>
