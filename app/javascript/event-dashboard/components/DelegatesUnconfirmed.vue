<!-- filepath: /Users/<USER>/Documents/Development/HGEvents/app/javascript/event-dashboard/components/DelegatesUnconfirmed.vue -->
<template>
  <div class="wrapper">
    <div class="row">
      <div class="col-12 q-mb-md">
        <q-card v-if="isClosed">
          <q-card-section class="hg-underline" :style="underlineOverride">
            <div class="text-h6">Invites can no longer be sent.</div>
          </q-card-section>
          <q-card-section>
            <p>
              The close date of this event has passed, therefore invitees will
              no longer be able to book tickets to attend.
            </p>
          </q-card-section>
        </q-card>

        <q-card>
          <q-card-section class="hg-underline">
            <div class="text-h6">Contacts Search</div>
          </q-card-section>
          <q-card-section>
            <events-filter
              :showPaymentFilter="false"
              :showInvitesSentFilter="true"
              @clear-filter="clearFilter"
              @search-contacts="filterOnServer"
            />

            <div class="row q-my-md">
              <div class="col-8">
                <q-btn
                  color="primary"
                  :icon-right="
                    newContactVisible ? 'expand_less' : 'expand_more'
                  "
                  :label="'Add New Contacts'"
                  :disable="importDisabled"
                  class="q-mr-sm"
                  @click="newContactVisible = !newContactVisible"
                />

                <q-btn
                  color="primary"
                  :icon-right="
                    importContactsVisible ? 'expand_less' : 'expand_more'
                  "
                  :label="'Import Org Contacts'"
                  :disable="importDisabled"
                  class="q-mr-sm"
                  @click="importContactsVisible = !importContactsVisible"
                />

                <q-btn
                  v-if="!deleting"
                  color="primary"
                  label="Remove All Contacts"
                  :disable="removeDisabled"
                  @click="deleteContacts"
                />

                <q-btn v-if="deleting" color="grey" disable class="q-ml-sm">
                  <q-spinner color="white" size="1em" class="q-mr-xs" />
                  Please Wait
                </q-btn>
              </div>
            </div>

            <div class="row q-mb-md">
              <div class="col-12">
                <q-slide-transition>
                  <div v-show="newContactVisible">
                    <csv-uploader :event="event" />
                  </div>
                </q-slide-transition>
              </div>
            </div>

            <div class="row q-mb-md">
              <div class="col-12">
                <q-slide-transition>
                  <div v-show="importContactsVisible">
                    <import-org-contacts :event="event" />
                  </div>
                </q-slide-transition>
              </div>
            </div>

            <q-markup-table flat bordered>
              <thead>
                <tr>
                  <th>Email Address</th>
                  <th>Name</th>
                  <th>Email Sent</th>
                  <th>Send Invites</th>
                  <th v-if="event.live">Book Event on Behalf of Invitee</th>
                  <th>Booking Timed Out</th>
                  <th>Decline Event</th>
                  <th>Remove Invitee</th>
                </tr>
              </thead>

              <tbody>
                <tr>
                  <td></td>
                  <td colspan="2">
                    <div class="text-right">
                      <strong>Select All Emails:</strong>
                    </div>
                  </td>
                  <td>
                    <q-checkbox
                      id="selectallmail"
                      v-model="selectedAll"
                      @update:model-value="selectAllEmails"
                    />
                  </td>
                  <td v-if="event.live"></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>

                <tr v-for="(user, idx) in unconfirmedUsers" :key="idx">
                  <td>{{ user.email }}</td>
                  <td>{{ user.forename }} {{ user.surname }}</td>
                  <td>
                    <strong>{{ user.invite_sent ? "Yes" : "No" }}</strong>
                  </td>
                  <td>
                    <div v-if="!user.opted_out">
                      <q-checkbox
                        :id="'mailto' + idx"
                        v-model="selectedEmails"
                        :val="user.email"
                        name="ticketSelection"
                      />
                    </div>
                    <q-tooltip v-if="user.opted_out">
                      This user has opted-out from receiving invites, this can
                      be managed in 'manage contacts'
                    </q-tooltip>
                    <q-icon
                      v-if="user.opted_out"
                      name="info_outline"
                      size="sm"
                      color="info"
                      class="cursor-pointer"
                    />
                  </td>
                  <td v-if="event.live">
                    <q-btn
                      color="primary"
                      label="Book for Attendee"
                      :disable="importDisabled || removeDisabled"
                      @click="bookForAttendee(user)"
                    />
                  </td>
                  <td>{{ user.booking_timed_out }}</td>
                  <td>
                    <q-btn
                      color="negative"
                      label="Decline for Attendee"
                      :disable="importDisabled || removeDisabled"
                      @click="declineEvent(user)"
                    />
                  </td>
                  <td>
                    <q-btn
                      color="negative"
                      icon="close"
                      round
                      flat
                      :disable="importDisabled || removeDisabled"
                      @click="removeInvite(user)"
                    />
                  </td>
                </tr>
              </tbody>
            </q-markup-table>

            <div v-if="totalItems && totalItems > 0" class="q-my-md">
              <q-btn
                v-if="event.complete && event.live"
                color="primary"
                label="Send Invites to Selected"
                :disable="importDisabled || removeDisabled"
                class="q-mr-sm"
                @click="sendEmailInvites"
              />

              <q-btn
                v-if="!event.complete || !event.live"
                color="primary"
                label="Send Invites to Selected"
                disable
                class="q-mr-sm"
              >
                <q-tooltip>Please Complete Your Event And Make Live!</q-tooltip>
              </q-btn>

              <q-btn
                v-if="event.complete && event.live"
                color="primary"
                label="Send Invites to All!"
                :disable="importDisabled || removeDisabled"
                @click="sendEmailInvitesAll"
              />

              <q-btn
                v-if="!event.complete || !event.live"
                color="primary"
                label="Send Invites to All"
                disable
              >
                <q-tooltip>Please Complete Your Event And Make Live!</q-tooltip>
              </q-btn>
            </div>

            <div class="q-mt-md text-caption">
              Total Records: {{ totalItems }}
            </div>

            <div class="row q-mt-md">
              <div class="col-6">
                <q-pagination
                  v-model="selectedPage"
                  :max="Math.ceil(totalItems / 100)"
                  :max-pages="5"
                  :boundaries="1"
                  @update:model-value="pageChanged"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { date, useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import axios from "axios";
import CsvUploader from "@/events/components/customise_emails/CsvUpload.vue";
import ImportOrgContacts from "./ImportOrgContacts.vue";
import EventBus from "../dashboard-event-bus.js";
import EventsFilter from "./EventsFilter.vue";

// Setup Quasar
const $q = useQuasar();
const eventStore = useEventStore();

// Reactive state
const event = computed(() => eventStore.getEvent);
const unconfirmedUsers = ref([]);
const selectedPage = ref(1);
const totalItems = ref(0);
const selectedEmails = ref([]);
const selectedAttendee = ref("");
const selectedTag = ref("");
const selectedEvent = ref("0");
const optInStatus = ref("0");
const eventStatus = ref("0");
const selectedDateFrom = ref(null);
const selectedDateTo = ref(null);
const attendeeFilter = ref("");
const paymentStatus = ref("");
const invitedStatus = ref("0");
const selectedAll = ref(false);
const polling = ref(false);
const deleting = ref(false);
const newContactVisible = ref(false);
const importContactsVisible = ref(false);
const importDisabled = ref(false);
const removeDisabled = ref(false);
const underlineOverride = ref(null);

// Computed properties
const queueId = computed({
  get: () => eventStore.getRemoveContactJobID,
  set: (value) => eventStore.setRemoveContactJobID(value),
});

const isClosed = computed(() => {
  const endDate = date.extractDate(event.value.datetimeto, "YYYY-MM-DD");
  const closeDate = date.extractDate(event.value.close_date, "YYYY-MM-DD");
  return (
    date.isBefore(endDate, new Date(), "day") ||
    date.isBefore(closeDate, new Date(), "day")
  );
});

// Methods
const pageChanged = (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  axios
    .get(`/registered_users/${event.value.id}.json`, {
      params: {
        page: selectedPage.value,
        attendeeFilter: selectedAttendee.value,
        optOutFilter: optInStatus.value,
        eventFilter: selectedEvent.value,
        tagFilter: selectedTag.value,
        eventStatusFilter: eventStatus.value,
        dateToFilter: selectedDateTo.value,
        dateFromFilter: selectedDateFrom.value,
        inviteFilter: invitedStatus.value,
      },
    })
    .then((response) => {
      unconfirmedUsers.value = response.data.unconfirmed_users;
      totalItems.value = response.data.total_count;
      eventStore.setUnconfirmedCount(response.data.unconfirmed_count);
      selectAllEmails();
    })
    .catch((error) => {
      console.error("Error fetching unconfirmed users:", error);
      $q.notify({
        message: "Failed to load unconfirmed users",
        color: "negative",
        icon: "error",
      });
    });
};

const filterOnServer = (e) => {
  selectedAttendee.value = e.selectedAttendee;
  selectedTag.value = e.selectedTag;
  selectedEvent.value = e.selectedEvent;
  optInStatus.value = e.optInStatus;
  eventStatus.value = e.eventStatus;
  selectedDateFrom.value = e.selectedDateFrom;
  selectedDateTo.value = e.selectedDateTo;
  invitedStatus.value = e.invitedStatus;
  pageChanged();
};

const clearFilter = () => {
  selectedAttendee.value = "";
  selectedTag.value = "";
  selectedEvent.value = "0";
  optInStatus.value = "0";
  eventStatus.value = "0";
  invitedStatus.value = "0";
  selectedDateFrom.value = null;
  selectedDateTo.value = null;
  pageChanged();
};

const selectAllEmails = () => {
  selectedEmails.value = [];

  if (selectedAll.value) {
    unconfirmedUsers.value.forEach((user) => {
      if (!user.has_booked_event) {
        selectedEmails.value.push(user.email);
      }
    });
  }
};

const toggleEmailSelection = (userEmail) => {
  const idx = selectedEmails.value.indexOf(userEmail);
  if (idx !== -1) {
    selectedAll.value = false;
    selectedEmails.value.splice(idx, 1);
  } else {
    selectedEmails.value.push(userEmail);
  }
};

const eventClosedNotification = () => {
  $q.dialog({
    title: "Invites Not Sent",
    message:
      "This event has already closed so attendees are no longer able to book.",
    color: "negative",
    icon: "error",
    ok: true,
  });
};

const sendEmailInvites = () => {
  if (isClosed.value) {
    eventClosedNotification();
    return false;
  }

  if (selectedEmails.value.length < 1) {
    $q.notify({
      message: "Please select emails to send to!",
      color: "warning",
      icon: "warning",
    });
    return false;
  }

  const emailData = {
    emails: selectedEmails.value,
  };

  $q.dialog({
    title: "Confirm if you wish to send emails to all attendees selected?",
    message: "Please Confirm!",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .put(`/event_invites_selected/${event.value.id}`, emailData)
      .then(() => {
        unconfirmedUsers.value.forEach((user) => {
          if (selectedEmails.value.includes(user.email)) {
            user.invite_sent = true;
          }
        });

        $q.notify({
          message: "Emails Sent!",
          color: "positive",
          icon: "check",
        });
      })
      .catch(() => {
        $q.notify({
          message: "Emails Not Sent",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const sendEmailInvitesAll = () => {
  if (isClosed.value) {
    eventClosedNotification();
    return false;
  }

  const emailData = {
    allSelected: true,
  };

  $q.dialog({
    title: "Confirm if you wish to send emails to Everyone in the list?",
    message: "These could take some time to send if numbers are large!",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .put(`/event_invites_selected/${event.value.id}`, emailData)
      .then(() => {
        unconfirmedUsers.value.forEach((user) => {
          user.invite_sent = true;
        });

        $q.notify({
          message: "Emails Sent!",
          color: "positive",
          icon: "check",
        });
      })
      .catch(() => {
        $q.notify({
          message: "Emails Not Sent",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const removeInvite = (email) => {
  $q.dialog({
    title: "Are you sure?",
    message: "This will delete this contact!",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .delete(`/registered_users/${email.id}.json`)
      .then(() => {
        $q.notify({
          message: "Removed Email Details!",
          color: "positive",
          icon: "check",
        });

        const index = unconfirmedUsers.value.findIndex(
          (u) => u.email === email.email
        );
        if (index !== -1) {
          unconfirmedUsers.value.splice(index, 1);
          totalItems.value--;
        }
      })
      .catch((error) => {
        $q.notify({
          message: `Email not removed: ${
            error.response?.data?.errors || "Unknown error"
          }`,
          color: "negative",
          icon: "error",
        });
      });
  });
};

const deleteContacts = () => {
  $q.dialog({
    title: "Are you sure?",
    message: "This will remove all the contacts!",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    deleting.value = true;
    newContactVisible.value = false;
    importContactsVisible.value = false;
    importDisabled.value = true;

    axios
      .delete(
        `/registered_users/remove_invites.json?event_id=${event.value.id}`
      )
      .then((response) => {
        queueId.value = response.data.queue;
        pollQueue();
      })
      .catch((error) => {
        $q.notify({
          message: `Contacts not removed: ${
            error.response?.data?.errors || "Unknown error"
          }`,
          color: "negative",
          icon: "error",
        });
        deleting.value = false;
        importDisabled.value = false;
      });
  });
};

const pollQueue = () => {
  polling.value = setInterval(() => {
    axios
      .get(`/registered_users/poll_remove_invites.json?job_id=${queueId.value}`)
      .then((response) => {
        const jobFinished = response.data.success;
        if (jobFinished) {
          stopPolling();
          $q.notify({
            message: "Removed All Contact Details!",
            color: "positive",
            icon: "check",
          });
          unconfirmedUsers.value = [];
          totalItems.value = 0;
          deleting.value = false;
          importDisabled.value = false;
          queueId.value = null;
        }
      })
      .catch(() => {
        $q.notify({
          message:
            "There was a problem removing all the unconfirmed contacts from this event",
          color: "negative",
          icon: "error",
        });
        stopPolling();
        deleting.value = false;
        importDisabled.value = false;
        queueId.value = null;
      });
  }, 10000);
};

const stopPolling = () => {
  clearInterval(polling.value);
};

const bookForAttendee = (user) => {
  $q.dialog({
    title: `Do you want to book for: ${user.forename} ${user.surname}?`,
    message: "You will be redirected to the bookings page",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    window.open(`/${user.user_invite_url}`, "_blank");
  });
};

const declineEvent = (user) => {
  $q.dialog({
    title: `Do you want to decline for: ${user.forename} ${user.surname}?`,
    message: "This can be undone if needed in the declined list",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const updatedUser = { ...user, declined: true };

    axios
      .put(`/registered_users/${user.id}/decline`, {
        declined: updatedUser.declined,
      })
      .then(() => {
        $q.notify({
          message: "Declined for User!",
          color: "positive",
          icon: "check",
        });

        const index = unconfirmedUsers.value.findIndex(
          (u) => u.email === user.email
        );
        if (index !== -1) {
          unconfirmedUsers.value.splice(index, 1);
          totalItems.value--;
        }
      })
      .catch((error) => {
        $q.notify({
          message: `Declined has failed! ${error.response?.data?.errors || ""}`,
          color: "negative",
          icon: "error",
        });
      });
  });
};

// Lifecycle hooks
onMounted(() => {
  pageChanged();

  // Event bus listeners
  EventBus.$on("usersUpdated", () => {
    attendeeFilter.value = "";
    pageChanged();
  });

  EventBus.$on("uploadingContacts", (status) => {
    removeDisabled.value = status === true;
  });

  // Check queue status
  if (queueId.value) {
    deleting.value = true;
    newContactVisible.value = false;
    importContactsVisible.value = false;
    importDisabled.value = true;
    setTimeout(() => pollQueue(), 5000);
  }
});

onBeforeUnmount(() => {
  stopPolling();
  EventBus.$off("usersUpdated");
  EventBus.$off("uploadingContacts");
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid var(--q-primary);
  padding-bottom: 8px;
}
</style>
