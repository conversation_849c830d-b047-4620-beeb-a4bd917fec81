<template>
  <q-dialog v-model="dialogVisible" persistent>
    <q-card class="q-pa-md" style="min-width: 350px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Register a Payment</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup @click="hideModal" />
      </q-card-section>

      <q-card-section>
        <q-form @submit="onSubmit" class="q-gutter-md">
          <q-input
            v-model="payment.ref"
            label="Payment Reference"
            :rules="[val => !!val || 'Please enter a reference']"
            outlined
            dense
          />

          <q-input
            v-model.number="payment.amount"
            label="Payment Amount"
            type="number"
            :rules="[
              val => !!val || 'Payment amount is required',
              val => val > 0 || 'Amount must be greater than 0',
              val => val <= amountDue || `Amount cannot exceed ${amountDue}`
            ]"
            outlined
            dense
          />

          <q-select
            v-model="payment.method"
            :options="paymentMethodOptions"
            label="Choose Payment Method"
            :rules="[val => !!val || 'Please select a payment method']"
            emit-value
            map-options
            outlined
            dense
          />

          <div class="row justify-between q-mt-md">
            <q-btn label="Cancel" color="grey" @click="hideModal" />
            <q-btn label="Save Payment" color="primary" type="submit" />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';

const props = defineProps({
  booking: {
    type: Object,
    required: true
  },
  event: {
    type: Object,
    required: true
  },
  advanced: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'payment-saved']);

const $q = useQuasar();

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const amountDue = ref(0);
const payment = ref({
  ref: null,
  user_id: props.booking.user_id,
  amount: 0,
  method: 'cash',
  event_booking_id: props.booking.booking_id,
  payment_type: "",
});

const paymentMethodOptions = computed(() => {
  const options = [
    { label: 'Pay At Event', value: 'cash' }
  ];
  
  // Uncomment and adapt these as needed based on your event object structure
  // if (props.event.payment_methods?.bacs_enabled) {
  //   options.push({ label: 'BACS Payment', value: 'bacs' });
  // }
  // if (props.event.payment_methods?.cheque_enabled) {
  //   options.push({ label: 'Pay by Cheque', value: 'cheque' });
  // }
  
  return options;
});

const canPay = computed(() => props.advanced);

onMounted(() => {
  // Calculate amount due - adapt this based on your actual data structure
  // This assumes there's a getAmountDue computed property in the parent
  // You might need to pass this value as a prop instead
  const totalDue = props.booking.amount_payable - props.booking.amount_paid;
  amountDue.value = parseFloat(totalDue).toFixed(2);
  payment.value.amount = amountDue.value;
  
  if (props.booking.payment_type) {
    payment.value.payment_type = props.booking.payment_type.toLowerCase();
  }
});

const checkPaymentAmountValid = () => {
  const toPay = parseFloat(props.booking.amount_payable);
  const paid = parseFloat(props.booking.amount_paid);
  const payAmount = parseFloat(payment.value.amount);
  const remaining = toPay - paid;

  if (remaining - payAmount < 0) {
    $q.notify({
      message: 'Amount paid is greater than remaining amount!',
      color: 'negative',
      icon: 'error'
    });
    return false;
  }
  
  return true;
};

const savePayment = async () => {
  if (!checkPaymentAmountValid()) return;
  
  try {
    const response = await fetch('/payments/register_payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(payment.value)
    });
    
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    
    const data = await response.json();
    
    if (data.status === 'failure') {
      $q.notify({
        message: 'Payment Not Saved!',
        color: 'negative',
        icon: 'error'
      });
      return;
    }
    
    // Update booking data with new payment info
    // This depends on how you want to handle state management
    // In Vue 2 this was modifying the props directly, which isn't a good practice
    // Instead, we'll emit an event with the updated data
    const updatedBookingData = {
      ...props.booking,
      payments: [...props.booking.payments, data.payment],
      amount_paid: data.booking.amount_paid,
      status: data.booking.status
    };
    
    emit('payment-saved', updatedBookingData);
    
    $q.notify({
      message: 'Payment Registered Successfully!',
      color: 'positive',
      icon: 'check_circle'
    });
    
    hideModal();
  } catch (error) {
    console.error('Error saving payment:', error);
    $q.notify({
      message: 'Payment Not Saved!',
      color: 'negative',
      icon: 'error'
    });
  }
};

const onSubmit = () => {
  savePayment();
};

const hideModal = () => {
  dialogVisible.value = false;
};
</script>
