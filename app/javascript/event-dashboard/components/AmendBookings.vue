<!-- filepath: /Users/<USER>/Documents/Development/HGEvents/app/javascript/event-dashboard/components/AmendBookings.vue -->
<template>
  <div class="col-12">
    <q-card>
      <q-card-section class="hg-underline">
        <div class="text-h6">Booking Details</div>
      </q-card-section>
      <q-card-section>
        <div class="text-subtitle2 text-grey">View a booking and amend the details if necessary</div>
      </q-card-section>

      <q-card-section>
        <booker-form v-if="event && eventBooking" :event="event" :regUserId="eventBooking.registered_user_id"></booker-form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';
import bookerForm from '@/bookings/main_booking/booker-form.vue'; // Assumes path is updated for Vue 3

// Props
const props = defineProps({
  bookingId: {
    type: [String, Number],
    required: true
  }
});

// Setup Quasar notification system
const $q = useQuasar();

// Reactive state
const eventBooking = ref(null);
const event = ref(null);

// Fetch booking data on component mount
onMounted(() => {
  axios
    .get(`/booking_details/event_booker/${props.bookingId}`)
    .then((response) => {
      eventBooking.value = response.data.event_booking;
      event.value = eventBooking.value.event;
    })
    .catch((error) => {
      $q.notify({
        message: 'Booking could not be retrieved',
        color: 'negative',
        icon: 'error'
      });
      console.error('Error fetching booking:', error);
    });
});
</script>
