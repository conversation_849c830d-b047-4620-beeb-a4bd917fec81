<template>
  <unified-attendee-table
    attendee-type="confirmed"
    title="Bookings Confirmed"
    :show-filters="true"
    :show-payment-filter="hasPaidTickets"
    :show-invites-sent-filter="false"
  />
</template>

<script setup>
import { computed } from 'vue';
import { useEventStore } from '@/stores/event';
import { storeToRefs } from 'pinia';
import UnifiedAttendeeTable from './UnifiedAttendeeTable.vue';

const eventStore = useEventStore();
const { event } = storeToRefs(eventStore);

const hasPaidTickets = computed(() => event.value?.has_paid_tickets);
</script>
