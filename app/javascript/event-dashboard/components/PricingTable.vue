<template>
  <div v-if="booking">
    <PaymentsDialog
      v-model="showTheModal"
      :booking="booking"
      :event="event"
      :advanced="advuser"
      @payment-saved="handlePaymentSaved"
    />

    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h5">Payment Summary</div>
        <q-markup-table>
          <thead>
            <tr>
              <th>Amount Payable</th>
              <th>Amount Paid</th>
              <th>Payment Type Expected</th>
              <th class="text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{{ formatCurrency(getAmountDue) }}</td>
              <td>{{ formatCurrency(booking.amount_paid || 0) }}</td>
              <td>{{ booking.payment_type || "BACS" }}</td>
              <td class="text-center">
                <div
                  v-if="
                    !booking.cancelled_at &&
                    !booking.free_booking &&
                    hasPaidTickets &&
                    booking.status !== 'refunded' &&
                    booking.status !== 'processing'
                  "
                  class="row justify-center"
                >
                  <q-btn-dropdown
                    color="primary"
                    label="Payment Options"
                    size="sm"
                    :style="{
                      'margin-top':
                        booking.payments.length > 0 ? '0px' : '20px',
                      'margin-right': '10px',
                    }"
                  >
                    <q-list>
                      <q-item
                        v-if="
                          booking.status !== 'paid' &&
                          canPay &&
                          booking.payment_type !== 'Card'
                        "
                        clickable
                        v-close-popup
                        @click="showModal"
                      >
                        <q-item-section>Add a Payment</q-item-section>
                      </q-item>
                      <q-item
                        v-if="
                          booking.status !== 'unpaid' && booking.amount_paid > 0
                        "
                        clickable
                        v-close-popup
                        @click="refundPayments(booking, true)"
                      >
                        <q-item-section>Refund and Cancel</q-item-section>
                      </q-item>
                    </q-list>
                  </q-btn-dropdown>
                </div>

                <q-badge
                  v-if="booking.cancelled_at"
                  color="negative"
                  class="q-pa-xs"
                >
                  CANCELLED BOOKING
                </q-badge>
              </td>
            </tr>
          </tbody>
        </q-markup-table>
      </q-card-section>
    </q-card>

    <q-card
      v-if="booking.payments && booking.payments.length > 0"
      class="q-mb-md"
    >
      <q-card-section>
        <div class="text-h5">Payments</div>
        <q-markup-table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Amount</th>
              <th>Event Fees</th>
              <th>Payment Date</th>
              <th>Payment Type</th>
              <th>Stripe Charge ID</th>
              <th>Refunded?</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(payment, idx) in booking.payments" :key="idx">
              <td>{{ payment.payment_id }}</td>
              <td :class="{ 'text-strike': payment.refunded }">
                {{ formatCurrency(payment.amount) }}
              </td>
              <td>
                <div v-if="payment.stripe_fees && payment.application_fees">
                  {{
                    formatCurrency(
                      +payment.application_fees + (+payment.stripe_fees || 0)
                    )
                  }}
                </div>
              </td>
              <td>{{ formatDateTime(payment.payment_date) }}</td>
              <td>{{ booking.payment_type }}</td>
              <td>{{ payment.stripe_charge_id }}</td>
              <td v-if="payment.refunded">Refunded</td>
              <td v-else>
                <q-btn
                  color="negative"
                  label="Refund Payment"
                  size="sm"
                  @click="refundSinglePayment(payment)"
                />
              </td>
              <td>
                <q-btn
                  :color="payment.notes ? 'positive' : 'grey'"
                  :icon="payment.notes ? 'note' : 'note_add'"
                  size="sm"
                  round
                >
                  <q-menu anchor="bottom middle" self="top middle">
                    <q-card style="width: 500px">
                      <q-card-section>
                        <q-input
                          v-model="payment.notes"
                          type="textarea"
                          rows="4"
                          label="Payment Notes"
                          outlined
                        />
                      </q-card-section>
                      <q-card-actions align="right">
                        <q-btn
                          color="primary"
                          label="Update"
                          @click="updateNotes(payment)"
                          v-close-popup
                        />
                      </q-card-actions>
                    </q-card>
                  </q-menu>
                </q-btn>
              </td>
            </tr>
          </tbody>
        </q-markup-table>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useEventStore } from "@/stores/event";
import { useQuasar, date } from "quasar";
import PaymentsDialog from "./PaymentsDialog.vue";

const props = defineProps({
  event: Object,
  booking: Object,
  advuser: Boolean,
});

const $q = useQuasar();
const eventStore = useEventStore();

const showTheModal = ref(false);

// Computed properties
const hasPaidTickets = computed(() => eventStore.getChargeable);

const getAmountDue = computed(() => {
  let vat = 0;

  if (props.booking.amount_payable.vat_amount && props.event.vat_exclusive) {
    vat = Number(props.booking.amount_payable.vat_amount);
  }

  let booking_cost = Number(props.booking.amount_payable.cost);

  if (!props.event.fees_included) {
    booking_cost = booking_cost + Number(props.booking.fees_payable || 0);
  }

  if (props.booking.amount_payable.cost === 0) {
    return props.booking.amount_paid;
  }

  return booking_cost + vat;
});

const canPay = computed(() => props.advuser);

// Methods
const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-GB", {
    style: "currency",
    currency: "GBP",
  }).format(value);
};

const formatDateTime = (dateString) => {
  if (!dateString) return "";
  return date.formatDate(dateString, "DD/MM/YYYY HH:mm");
};

const showModal = () => {
  showTheModal.value = true;
};

const handlePaymentSaved = (updatedBookingData) => {
  // Here we would emit an event to update the parent component with the new booking data
  // Or use a different state management approach as needed
  Object.assign(props.booking, updatedBookingData);
};

const refundPayments = (booking, cancel = false) => {
  const alertText = cancel
    ? "This will cancel the event and mark all payments as refunded"
    : "This will mark all payments as refunded";

  $q.dialog({
    title: "Are you sure you wish to refund all payments?",
    message: alertText,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const response = await fetch("/payments/refund_all", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          booking: props.booking,
          event_id: props.event.id,
          cancel: cancel,
        }),
      });

      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const data = await response.json();

      // Update booking data
      props.booking.status = data.status;
      props.booking.amount_paid = 0;
      props.booking.payments.forEach((payment) => {
        payment.refunded = true;
      });

      $q.notify({
        message: "Payments Refunded!",
        color: "positive",
        icon: "check_circle",
      });
    } catch (error) {
      console.error("Error refunding payments:", error);
      $q.notify({
        message: "Refunds Failed!",
        color: "negative",
        icon: "error",
      });
    }
  });
};

const refundSinglePayment = (payment) => {
  $q.dialog({
    title: "Are you sure you wish to refund this payment?",
    message:
      "This will mark this payment as refunded, but does not cancel the booking",
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const response = await fetch("/payments/refund", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          payment_id: payment.payment_id,
          event_id: props.event.id,
        }),
      });

      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const data = await response.json();

      // Update payment and booking data
      payment.refunded = true;
      props.booking.amount_paid = props.booking.amount_paid - payment.amount;
      props.booking.status = data.status;
      props.booking.payColor = "blue";

      $q.notify({
        message: "Payment refunded!",
        color: "positive",
        icon: "check_circle",
      });
    } catch (error) {
      console.error("Error refunding payment:", error);
      $q.notify({
        message: "Refund Failed!",
        color: "negative",
        icon: "error",
      });
    }
  });
};

const updateNotes = async (payment) => {
  try {
    const response = await fetch(`/payments/${payment.payment_id}/notes`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({
        notes: payment.notes,
      }),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    $q.notify({
      message: "Notes updated!",
      color: "positive",
      icon: "check_circle",
    });
  } catch (error) {
    console.error("Error updating notes:", error);
    $q.notify({
      message: "Update Notes Failed!",
      color: "negative",
      icon: "error",
    });
  }
};
</script>

<style scoped>
.text-strike {
  text-decoration: line-through;
}
</style>
