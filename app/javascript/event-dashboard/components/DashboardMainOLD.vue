<template>
  <div class="row">
    <download-poller
      v-if="showPoller"
      @close="showPoller = false"
      :dialogVisible="showPoller"
      :downloadUrl="downloadUrl"
    >
    </download-poller>

    <!--sidebar start-->
    <div class="col-md-3 col-sm-4 col-xs-12 bg-dashboard-sidebar">
      <!-- sidebar menu start-->
      <q-list class="sidebar-menu" padding id="nav-accordion">
        <div class="q-pb-md q-mb-md" style="border-bottom: 1px solid white">
          <h5 class="text-center text-white">
            <strong>{{ currentUser }}</strong>
          </h5>
        </div>

        <div class="q-mb-md text-center">
          <a :href="`/dashboard/${event.id}`" class="text-white">
            <h5 class="text-center">
              <strong>{{ event.title }}</strong>
            </h5>
          </a>
        </div>

        <q-item
          v-if="currentRoute !== 'dash-main'"
          clickable
          :href="`/dashboard/${event.id}`"
          style="background-color: coral"
        >
          <q-item-section avatar>
            <q-icon name="arrow_back" />
          </q-item-section>
          <q-item-section>Back To Event Dashboard</q-item-section>
        </q-item>

        <q-item clickable href="/dashboard">
          <q-item-section avatar>
            <q-icon name="list" />
          </q-item-section>
          <q-item-section>Show All Events</q-item-section>
        </q-item>

        <q-item
          clickable
          :id="'edit_event'"
          :href="`/event_details#/event/${event.id}`"
        >
          <q-item-section avatar>
            <q-icon name="edit" />
          </q-item-section>
          <q-item-section>Edit Event</q-item-section>
        </q-item>

        <q-expansion-item
          header-class="text-white"
          dense
          icon="people"
          label="View Bookings"
          default-opened
        >
          <q-item to="/attendees" clickable>
            <q-item-section avatar>
              <q-icon name="person_add" />
            </q-item-section>
            <q-item-section>Bookings Confirmed</q-item-section>
          </q-item>

          <q-item to="/attendees-payment-failed" clickable>
            <q-item-section avatar>
              <q-icon name="credit_card" />
            </q-item-section>
            <q-item-section>Bookings Payment Failed</q-item-section>
          </q-item>

          <q-item to="/bookings-unpaid" clickable>
            <q-item-section avatar>
              <q-icon name="credit_card" />
            </q-item-section>
            <q-item-section>Bookings Unpaid</q-item-section>
          </q-item>

          <q-item to="/attendees-unconfirmed" clickable>
            <q-item-section avatar>
              <q-icon name="person_outline" />
            </q-item-section>
            <q-item-section>
              Unconfirmed Invitees
              <q-badge v-if="unconfirmedCount > 0" color="primary" floating>
                {{ unconfirmedCount }}
              </q-badge>
            </q-item-section>
          </q-item>

          <q-item to="/attendees-cancelled" clickable>
            <q-item-section avatar>
              <q-icon name="person_off" />
            </q-item-section>
            <q-item-section>Cancelled Bookings</q-item-section>
          </q-item>

          <q-item to="/attendees-declined" clickable>
            <q-item-section avatar>
              <q-icon name="person_off" />
            </q-item-section>
            <q-item-section>Declined Bookings</q-item-section>
          </q-item>
        </q-expansion-item>

        <q-item to="/terms" clickable>
          <q-item-section avatar>
            <q-icon name="gavel" />
          </q-item-section>
          <q-item-section>Event Terms and Conditions</q-item-section>
        </q-item>

        <q-item
          v-if="hasPaidTickets"
          :to="{ name: 'payments', params: { status: null } }"
          clickable
        >
          <q-item-section avatar>
            <q-icon name="payments" />
          </q-item-section>
          <q-item-section>Payments and Refunds</q-item-section>
        </q-item>

        <q-item to="/preview" clickable>
          <q-item-section avatar>
            <q-icon name="visibility" />
          </q-item-section>
          <q-item-section>Preview Event</q-item-section>
        </q-item>

        <q-expansion-item
          header-class="text-white"
          icon="assessment"
          label="View Reports"
          dense
        >
          <q-item
            clickable
            @click="getReport(`/events/${event.id}/export_data.csv`)"
          >
            <q-item-section avatar>
              <q-icon name="settings" />
            </q-item-section>
            <q-item-section>Export Booker Data</q-item-section>
          </q-item>

          <q-item
            v-if="event.show_add_attendees"
            :href="`/events/${event.id}/export_bookings.csv`"
            clickable
          >
            <q-item-section avatar>
              <q-icon name="person_off" />
            </q-item-section>
            <q-item-section>Export Booking and Attendee Details</q-item-section>
          </q-item>

          <q-item
            v-if="event.show_add_attendees"
            :href="`/events/${event.id}/export_attendees.csv`"
            clickable
          >
            <q-item-section avatar>
              <q-icon name="person_off" />
            </q-item-section>
            <q-item-section>Export Attendee Summary</q-item-section>
          </q-item>

          <q-item
            :href="`/events/${event.id}/export_unbooked_users.csv`"
            clickable
          >
            <q-item-section avatar>
              <q-icon name="menu_book" />
            </q-item-section>
            <q-item-section>Export Unbooked User List</q-item-section>
          </q-item>

          <q-item
            v-if="hasPaidTickets"
            :href="`/events/${event.id}/export_payments.csv`"
            clickable
          >
            <q-item-section avatar>
              <q-icon name="credit_card" />
            </q-item-section>
            <q-item-section>Export Booking Payments</q-item-section>
          </q-item>
        </q-expansion-item>

        <q-item
          v-if="!event.is_public"
          @click="publicEvent(true)"
          clickable
          class="text-positive"
        >
          <q-item-section avatar>
            <q-icon name="people" color="positive" />
          </q-item-section>
          <q-item-section>Make Public</q-item-section>
        </q-item>

        <q-item
          v-if="event.is_public"
          @click="publicEvent(false)"
          clickable
          class="text-negative"
        >
          <q-item-section avatar>
            <q-icon name="person" color="negative" />
          </q-item-section>
          <q-item-section>Make Private</q-item-section>
        </q-item>

        <q-item
          v-if="hasPaidTickets"
          :href="`/events/${event.id}/edit#/payments/`"
          clickable
        >
          <q-item-section avatar>
            <q-icon name="credit_card" />
          </q-item-section>
          <q-item-section>
            {{
              advancedUser
                ? "Manage Payment Options"
                : "Manage Credit/Debit Card Payments"
            }}
          </q-item-section>
        </q-item>

        <q-item
          v-if="hasNoBookings"
          @click="deleteEvent()"
          clickable
          class="text-white"
        >
          <q-item-section avatar>
            <q-icon name="delete" />
          </q-item-section>
          <q-item-section>Delete Event</q-item-section>
        </q-item>
      </q-list>
      <!-- sidebar menu end-->
    </div>

    <div class="col-md-9 col-sm-8 col-xs-12">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useQuasar } from "quasar";
import axios from "axios";
import { useEventStore } from "@/stores/event";
import DownloadPoller from "./DownloadPoller.vue";

// Router setup and route definitions
const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const eventStore = useEventStore();

// Component data
const event = ref(window.eventData || {});
const advancedUser = ref(window.advancedUser || false);
const currentUser = ref(window.currentUser || "");
const showPoller = ref(false);
const downloadUrl = ref("");

// Computed properties
const hasNoBookings = computed(() => event.value.no_bookings);
const hasPaidTickets = computed(() => event.value.has_paid_tickets);
const unconfirmedCount = computed(() => event.value.unconfirmed_count || 0);
const currentRoute = computed(() => route.name);

// Methods
const deleteEvent = () => {
  $q.dialog({
    title: "Are you sure?",
    message: "This will delete the event!",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .delete(`/events/${event.value.id}.json`)
      .then(() => {
        window.location.replace("/dashboard");
      })
      .catch(() => {
        $q.notify({
          message: "The Event Has Not Been Deleted!",
          color: "negative",
          icon: "error",
        });
      });
  });
};

// Initialize store on component mount
onMounted(() => {
  if (event.value && Object.keys(event.value).length > 0) {
    // Set the event data in Pinia store
    eventStore.setEvent(event.value);

    // Set chargeable status based on event properties
    if (event.value.has_paid_tickets !== undefined) {
      eventStore.setChargeable(event.value.has_paid_tickets);
    }

    // Set unconfirmed count if available
    if (event.value.unconfirmed_count !== undefined) {
      eventStore.setUnconfirmedCount(event.value.unconfirmed_count);
    }
  }
});

const getReport = (url) => {
  axios
    .get(url)
    .then(() => {
      showPollerDialog(url);
    })
    .catch((err) => {
      console.error("Error fetching report:", err);
    });
};

const showPollerDialog = (url) => {
  downloadUrl.value = `${url}?load=true`;
  showPoller.value = true;
};

const publicEvent = (makePublic) => {
  const text = makePublic
    ? "You will allow anyone to attend this event!"
    : "You will have to send out the event invites manually!";
  const confirmation = makePublic
    ? "Yes, Make it Public"
    : "Yes, Make it Private";
  const type = makePublic ? "Public" : "Private";

  $q.dialog({
    title: "Are you sure?",
    message: text,
    cancel: true,
    persistent: true,
  })
    .onOk(() => {
      event.value.is_public = makePublic;

      axios
        .put(`/events/${event.value.id}/change_public_status.json`, event.value)
        .then(() => {
          $q.notify({
            message: `Your event is now ${type}`,
            color: "positive",
            icon: "check",
          });
        })
        .catch(() => {
          $q.notify({
            message: "Could not change event status",
            color: "negative",
            icon: "error",
          });
        });
    })
    .catch(() => {
      $q.notify({
        message: "Your event status hasn't changed",
        color: "warning",
        icon: "warning",
      });
    });
};
</script>

<style scoped>
.bg-dashboard-sidebar {
  background-color: #424a5d;
}

.text-white {
  color: white;
}

.text-center {
  text-align: center;
}

.sidebar-menu :deep(.q-expansion-item__content) {
  padding-left: 15px;
}

.sidebar-menu :deep(.q-item__section--avatar) {
  min-width: 30px;
}

.sidebar-menu :deep(.q-item) {
  min-height: 40px;
  color: white;
}

.sidebar-menu :deep(.router-link-active) {
  background-color: rgba(255, 255, 255, 0.2);
}

.sidebar-menu :deep(.q-expansion-item__toggle-icon) {
  color: white;
}
</style>
