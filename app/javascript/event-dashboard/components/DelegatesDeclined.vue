<!-- filepath: /Users/<USER>/Documents/Development/HGEvents/app/javascript/event-dashboard/components/DelegatesDeclined.vue -->
<template>
  <div class="wrapper">
    <div class="row q-mt-md">
      <div class="col-12 q-mb-md">
        <q-card>
          <q-card-section class="hg-underline">
            <div class="text-h6">Declined</div>
          </q-card-section>
          <q-card-section>
            <q-markup-table flat bordered>
              <thead>
                <tr>
                  <th></th>
                  <th>Email Address</th>
                  <th>Name</th>
                  <th>Undo Decline</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(user, idx) in declinedUsers" :key="idx">
                  <td>{{ idx + 1 }}</td>
                  <td>{{ user.email }}</td>
                  <td>{{ user.forename }} {{ user.surname }}</td>
                  <td>
                    <q-btn
                      @click="undoDecline(user)"
                      color="primary"
                      icon-left="undo"
                      label="Undo Decline"
                      no-caps
                    />
                  </td>
                </tr>
              </tbody>
            </q-markup-table>

            <div class="q-mt-md text-caption">
              Total Records: {{ totalItems }}
            </div>

            <div class="q-mt-md">
              <q-pagination
                v-model="selectedPage"
                :max="Math.ceil(totalItems / 100)"
                :max-pages="5"
                :boundaries="1"
                @update:model-value="pageChanged"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import axios from "axios";

// Setup dependencies
const $q = useQuasar();
const eventStore = useEventStore();

// Component state
const event = computed(() => eventStore.getEvent);
const declinedUsers = ref([]);
const selectedPage = ref(1);
const totalItems = ref(0);

// Methods
const pageChanged = (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  axios
    .get(
      `/registered_users/${event.value.id}/show_declined.json?page=${selectedPage.value}`
    )
    .then((response) => {
      declinedUsers.value = response.data.declined_users;
      totalItems.value = response.data.total_count;
      eventStore.setUnconfirmedCount(response.data.unconfirmed_count);
    })
    .catch((error) => {
      console.error("Error fetching declined users:", error);
      $q.notify({
        message: "Failed to load declined users",
        color: "negative",
        icon: "error",
      });
    });
};

const undoDecline = (user) => {
  $q.dialog({
    title: `Do you want to undo the decline for: ${user.forename} ${user.surname}?`,
    message: "This will move the user to the invitee list",
    color: "warning",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const updatedUser = { ...user, declined: false };

    axios
      .put(`/registered_users/${user.id}/decline`, {
        declined: updatedUser.declined,
      })
      .then(() => {
        pageChanged();
        $q.notify({
          message: "User no longer declined!",
          color: "positive",
          icon: "check",
        });
      })
      .catch((error) => {
        $q.notify({
          message: `Undo Decline has failed: ${
            error.response?.data?.errors || "Unknown error"
          }`,
          color: "negative",
          icon: "error",
        });
        console.error("Error undoing decline:", error);
      });
  });
};

// Fetch data on component mount
onMounted(() => {
  pageChanged();
});
</script>
