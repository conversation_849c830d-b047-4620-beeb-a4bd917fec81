<template>
  <div class="col-12 q-mb-md">
    <q-card>
      <q-card-section class="bg-grey-2">
        <div class="text-h6 hg-underline">Bookings Confirmed</div>
      </q-card-section>
      
      <q-card-section>
        <events-filter 
          :showPaymentFilter="hasPaidTickets" 
          :showInvitesSentFilter="false" 
          @clear-filter="clearFilters" 
          @search-contacts="applyFilters">
        </events-filter>

        <div class="overflow-auto">
          <q-table
            :rows="attendees"
            :columns="columns"
            row-key="id"
            :pagination="{rowsPerPage: itemsPerPage, page: currentPage}"
            @request="onPageChange"
            :loading="dashboardStore.isLoading"
            binary-state-sort
            :rows-per-page-options="[0]"
          >
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td auto-width>
                  <q-btn
                    size="sm"
                    round
                    flat
                    color="primary"
                    :icon="props.row.expanded ? 'remove' : 'add'"
                    @click="props.row.expanded = !props.row.expanded"
                  />
                </q-td>
                <q-td key="booking_id" :props="props">
                  {{ props.row.event_booking.id }}
                </q-td>
                <q-td key="email" :props="props">
                  {{ props.row.email }}
                </q-td>
                <q-td key="name" :props="props">
                  {{ props.row.forename }} {{ props.row.surname }}
                </q-td>
                <q-td key="booking_date" :props="props">
                  {{ formatDateAndTime(props.row.booking_date) }}
                </q-td>
                <q-td :props="props" v-if="hasPaidTickets && !props.row.event_booking.free_booking">
                  <q-badge :color="getStatusColor(props.row.event_booking.payment_status)" text-color="white">
                    {{ humanizeStatus(props.row.event_booking.payment_status) }}
                  </q-badge>
                </q-td>
                <q-td :props="props" v-else-if="hasPaidTickets">
                  <q-badge color="warning" text-color="white">Free Booking</q-badge>
                </q-td>
                <q-td key="invoice" :props="props" v-if="hasPaidTickets">
                  <q-btn
                    color="primary"
                    v-if="!props.row.event_booking.free_booking"
                    :loading="props.row.id === selectedAttendee.id"
                    @click="createInvoice(props.row)"
                    size="sm"
                    label="Create Invoice"
                  />
                </q-td>
                <q-td key="actions" :props="props">
                  <q-btn-dropdown color="primary" size="sm" label="Actions">
                    <q-list>
                      <q-item
                        v-if="hasPaidTickets && !props.row.event_booking.free_booking"
                        clickable
                        v-close-popup
                        @click="handleCommand('viewPayments', props.row)"
                      >
                        <q-item-section avatar>
                          <q-icon name="paid" />
                        </q-item-section>
                        <q-item-section>View Payments</q-item-section>
                      </q-item>
                      
                      <q-item clickable v-close-popup @click="handleCommand('resendEmail', props.row)">
                        <q-item-section avatar>
                          <q-icon name="email" />
                        </q-item-section>
                        <q-item-section>Re-send Email</q-item-section>
                      </q-item>
                      
                      <q-item clickable v-close-popup @click="handleCommand('cancelBooking', props.row)">
                        <q-item-section avatar>
                          <q-icon name="cancel" />
                        </q-item-section>
                        <q-item-section>Cancel Booking</q-item-section>
                      </q-item>
                    </q-list>
                  </q-btn-dropdown>
                </q-td>
              </q-tr>
              
              <q-tr v-if="props.row.expanded">
                <q-td colspan="8">
                  <div class="q-pa-md">
                    <strong>User invite url:</strong>
                    {{ rootUrl }}{{ props.row.user_invite_url }}
                  </div>
                </q-td>
              </q-tr>
            </template>

            <template v-slot:loading>
              <q-inner-loading showing color="primary">
                <q-spinner size="3em" />
                <div class="q-mt-sm">Loading attendees...</div>
              </q-inner-loading>
            </template>
          </q-table>
        </div>

        <div class="q-mt-md">
          <p>Total Records: {{ totalItems }}</p>

          <q-pagination
            v-model="currentPage"
            :max="Math.ceil(totalItems / itemsPerPage)"
            :max-pages="6"
            direction-links
            boundary-links
            @update:model-value="onPageChange"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useDashboardStore } from '@/stores/dashboard';
import axios from 'axios';
import EventsFilter from './EventsFilter.vue';
import { format } from 'date-fns';
import { storeToRefs } from 'pinia';

const $q = useQuasar();
const router = useRouter();
const dashboardStore = useDashboardStore();

// Destructure store properties with storeToRefs to maintain reactivity
const { 
  attendees, 
  pagination, 
  statistics,
  event
} = storeToRefs(dashboardStore);

// Initialize local state
const selectedAttendee = ref({});
const rootUrl = window.location.origin;

// Table columns definition
const columns = [
  { name: 'expander', label: '', field: 'expanded', sortable: false },
  { name: 'booking_id', align: 'left', label: 'Booking ID', field: row => row.event_booking.id, sortable: true },
  { name: 'email', align: 'left', label: 'Email Address', field: 'email', sortable: true },
  { name: 'name', align: 'left', label: 'Name', field: row => `${row.forename} ${row.surname}`, sortable: true },
  { name: 'booking_date', align: 'left', label: 'Confirmed Date/Time', field: 'booking_date', sortable: true },
  { 
    name: 'payment_status', 
    align: 'left', 
    label: 'Payment Status', 
    field: row => row.event_booking?.payment_status, 
    sortable: true,
    format: val => humanizeStatus(val)
  },
  { name: 'invoice', align: 'center', label: 'Invoice User', field: 'id', sortable: false },
  { name: 'actions', align: 'center', label: 'Action', sortable: false }
];

// Computed properties
const hasPaidTickets = computed(() => event.value.has_paid_tickets);
const currentPage = computed({
  get: () => pagination.value.currentPage,
  set: (value) => dashboardStore.setPage(value)
});
const totalItems = computed(() => pagination.value.totalItems);
const itemsPerPage = computed(() => pagination.value.itemsPerPage);

// Methods
const formatDateAndTime = (dateString) => {
  if (!dateString) return '';
  try {
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
  } catch (e) {
    return dateString;
  }
};

const getStatusColor = (status) => {
  if (status === 'paid' || status === 'refunded_paid_again' || status === 'complimentary') {
    return 'positive';
  } else if (status === 'part_paid' || status === 'refunded' || status === 'part_refunded') {
    return 'warning';
  } else {
    return 'negative';
  }
};

const onPageChange = (page) => {
  dashboardStore.setPage(typeof page === 'object' ? page.pagination.page : page);
  dashboardStore.fetchAttendees();
};

const humanizeStatus = (status) => {
  if (!status) return '';
  const string = status.split('_').join(' ').toLowerCase();
  return string.charAt(0).toUpperCase() + string.slice(1);
};

const applyFilters = (filters) => {
  dashboardStore.setSearchTerm(filters.searchTerm || '');
  dashboardStore.setTicketTypeFilter(filters.ticketType || 'all');
  dashboardStore.setDateRangeFilter(filters.dateRange);
  dashboardStore.fetchAttendees();
};

const clearFilters = () => {
  dashboardStore.resetFilters();
  dashboardStore.fetchAttendees();
};

const resendConfirmationEmail = (user) => {
  $q.dialog({
    title: 'Are you sure?',
    message: 'Resend Confirmation',
    cancel: true,
    persistent: true
  }).onOk(() => {
    axios.get(`/resend_confirmation/${user.id}.json`)
      .then(() => {
        $q.notify({
          message: 'The confirmation has been re-sent.',
          color: 'positive',
          icon: 'check'
        });
      })
      .catch(() => {
        $q.notify({
          message: 'The email has not been sent.',
          color: 'negative',
          icon: 'error'
        });
      });
  });
};

const cancelBooking = (user) => {
  $q.dialog({
    title: 'Are you sure?',
    message: 'This will cancel the booking!',
    cancel: true,
    persistent: true
  }).onOk(() => {
    axios.put(`/registered_users/${user.id}/cancel`)
      .then(response => {
        const updatedBooking = response.data;
        user.event_booking.cancelled_at = updatedBooking.cancelled_at;
        user.event_booking.cancelled_by = updatedBooking.cancelled_by;
        user.event_booking.payment_status = updatedBooking.payment_status;

        // Refresh attendees list
        dashboardStore.fetchAttendees();

        $q.notify({
          message: 'The booking has been cancelled',
          color: 'positive',
          icon: 'check'
        });
      })
      .catch(() => {
        $q.notify({
          message: 'The booking has not been cancelled!',
          color: 'negative',
          icon: 'error'
        });
      });
  });
};

const createInvoice = (user) => {
  selectedAttendee.value = user;
  const url = `/event_bookings/${user.event_booking.id}/invoice/`;
  
  axios.get(url, { responseType: 'blob' })
    .then(response => {
      if (response.data.receipt_url) {
        window.open(response.data.receipt_url, '_blank').focus();
      } else {
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();
        const formattedDate = `${dd}/${mm}/${yyyy}`;

        const filename = `${user.forename}_${user.surname}_${formattedDate}.pdf`;
        const data = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = data;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(data);
      }
      selectedAttendee.value = {};
    })
    .catch(error => {
      $q.notify({
        message: error.response?.data?.error_message || 'Failed to generate invoice',
        color: 'negative',
        icon: 'error',
      });
      selectedAttendee.value = {};
    });
};

const handleCommand = (command, user) => {
  switch(command) {
    case 'viewPayments':
      router.push({ name: 'payments', params: { status: null }, query: { user_id: user.id } });
      break;
    case 'resendEmail':
      resendConfirmationEmail(user);
      break;
    case 'cancelBooking':
      cancelBooking(user);
      break;
  }
};

// Initialize component
onMounted(() => {
  // Set the event data from window if available
  if (window.eventData) {
    dashboardStore.setEvent(window.eventData);
  }
  
  // Fetch attendees
  dashboardStore.fetchAttendees();
  
  // Load statistics
  dashboardStore.fetchStatistics();
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid var(--q-primary);
  font-weight: bold;
  padding-bottom: 10px;
}
</style>