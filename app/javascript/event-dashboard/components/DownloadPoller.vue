<template>
  <q-dialog
    v-model="showDialog"
    persistent
    :maximized="false"
    transition-show="scale"
    transition-hide="scale"
  >
    <q-card class="q-pa-md" style="min-width: 350px">
      <q-card-section>
        <div class="text-h6">Download Status</div>
        <q-separator class="q-my-md" />
        
        <div class="row justify-center q-py-md">
          <div class="text-center">
            <q-circular-progress
              :value="progress"
              size="100px"
              :thickness="0.15"
              color="primary"
              center-color="grey-1"
              track-color="grey-3"
              class="q-mb-md"
            >
              <div class="text-weight-bold text-primary">{{ progress }}%</div>
            </q-circular-progress>
            <div class="q-mt-sm text-body1">
              {{ message }}
            </div>
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          label="Close"
          color="primary"
          @click="closeDialog"
          :disable="progress < 100 && !hasError"
        />
        <q-btn
          v-if="progress === 100 && !hasError"
          flat
          label="Download"
          color="primary"
          @click="downloadFile"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';
import axios from 'axios';

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    required: true
  },
  downloadUrl: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['close']);
const $q = useQuasar();

// Component data
const progress = ref(0);
const message = ref('Generating file...');
const interval = ref(null);
const hasError = ref(false);

// Computed property for dialog visibility
const showDialog = computed({
  get: () => props.dialogVisible,
  set: (value) => {
    if (!value) emit('close');
  }
});

// Methods
const closeDialog = () => {
  emit('close');
};

const resetState = () => {
  progress.value = 0;
  message.value = 'Generating file...';
  hasError.value = false;
};

const downloadFile = () => {
  window.open(props.downloadUrl, '_blank');
  emit('close');
};

const checkProgress = async () => {
  try {
    const response = await axios.get(`${props.downloadUrl}&check_status=true`);
    const data = response.data;
    
    if (data.error) {
      message.value = data.error;
      hasError.value = true;
      clearInterval(interval.value);
      return;
    }
    
    progress.value = data.progress || 0;
    
    if (data.message) {
      message.value = data.message;
    }
    
    if (progress.value >= 100) {
      message.value = 'File is ready to download';
      clearInterval(interval.value);
    }
  } catch (error) {
    message.value = 'Error checking download status';
    hasError.value = true;
    clearInterval(interval.value);
    $q.notify({
      message: 'Error checking download status',
      color: 'negative',
      icon: 'error'
    });
  }
};

const startPolling = () => {
  resetState();
  // Initial check
  checkProgress();
  // Set up interval for subsequent checks
  interval.value = setInterval(checkProgress, 2000);
};

// Watchers
watch(() => props.dialogVisible, (newVal) => {
  if (newVal) {
    startPolling();
  } else {
    clearInterval(interval.value);
  }
});

watch(() => props.downloadUrl, (newVal, oldVal) => {
  if (newVal !== oldVal && props.dialogVisible) {
    startPolling();
  }
});

// Lifecycle hooks
onMounted(() => {
  if (props.dialogVisible) {
    startPolling();
  }
});

onBeforeUnmount(() => {
  clearInterval(interval.value);
});
</script>