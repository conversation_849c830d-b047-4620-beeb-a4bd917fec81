<template>
  <q-card-section>
    <div class="row q-col-gutter-md">
      <div class="col-12 col-md-3">
        <q-input
          v-model="attendeeFilter"
          label="Filter by Name / Email"
          dense
          outlined
        />
      </div>
      <div class="col-12 col-md-3">
        <q-select
          v-model="eventStatusFilter"
          :options="eventStatusOptions"
          label="Contact Status"
          option-value="value"
          option-label="text"
          emit-value
          map-options
          dense
          outlined
        />
      </div>
      <div class="col-12 col-md-3" v-if="props.showPaymentFilter">
        <q-select
          v-model="paymentStatus"
          :options="paymentOptions"
          label="Payment Status"
          option-value="value"
          option-label="text"
          emit-value
          map-options
          dense
          outlined
          clearable
        />
      </div>
      <div class="col-12 col-md-3" v-if="props.showInvitesSentFilter">
        <q-select
          v-model="inviteSentStatus"
          :options="invitedOptions"
          label="Invite Status"
          option-value="value"
          option-label="text"
          emit-value
          map-options
          dense
          outlined
        />
      </div>
      <div class="col-12 col-md-3">
        <q-btn
          icon="filter_list"
          label="Filter by Tags"
          color="primary"
          class="q-mr-sm"
        >
          <q-menu>
            <q-list style="min-width: 250px">
              <q-item>
                <q-item-section>
                  <q-select
                    v-model="tagsSelected"
                    multiple
                    :options="tagsList"
                    option-value="value"
                    option-label="label"
                    label="Select Tags"
                    emit-value
                    map-options
                    use-chips
                    stack-label
                    dense
                    outlined
                    @update:model-value="filterContacts"
                    style="width: 100%"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
      <div class="col-12 col-md-auto">
        <q-btn
          color="primary"
          label="Filter"
          @click="filterContacts"
          class="q-mr-sm"
        />
        <q-btn color="grey" label="Clear" @click="clearFilter" />
      </div>
    </div>
  </q-card-section>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useEventStore } from "@/stores/event"; // Using Pinia store

const props = defineProps({
  showPaymentFilter: Boolean,
  showInvitesSentFilter: Boolean,
});

const emit = defineEmits(["search-contacts", "clear-filter"]);

const eventStore = useEventStore();

const eventId = computed(() => eventStore.getEvent?.id); // Added optional chaining for safety

const attendeeFilter = ref("");
const tagsList = ref([]);
const tagsSelected = ref([]);

// Removed tagFilter, eventTitleFilter, eventOptions, optOutFilter, optOutOptions, dateFromFilter, dateToFilter as they were not used in the original template or filterContacts method
// If they are needed, they can be re-added.

const eventStatusFilter = ref(0);
const eventStatusOptions = ref([
  { text: "All Contacts", value: 0 },
  { text: "Attended Event", value: 1 },
  { text: "Booked on Event", value: 2 },
  { text: "Cancelled Booking", value: 3 },
]);

const inviteSentStatus = ref("0"); // Kept as string to match original data
const invitedOptions = ref([
  { text: "All Invites", value: "0" },
  { text: "Invite Sent", value: "1" },
  { text: "Invite Unsent", value: "2" },
]);

const paymentStatus = ref(null);
const paymentOptions = ref([
  { text: "Payment Status", value: null },
  { text: "Paid", value: "paid" },
  { text: "Part Paid", value: "part_paid" },
  { text: "Unpaid", value: "unpaid" },
  { text: "Refunded", value: "refunded" }, // Original had label: "refunded", changed to value
  { text: "Part Refunded", value: "part_refunded" },
  { text: "Refunded, Paid Again", value: "refunded_paid_again" },
  { text: "Free Booking", value: "free_booking" },
  { text: "Complimentary", value: "complimentary" },
  { text: "Payment Failed", value: "payment_failed" },
]);

onMounted(async () => {
  if (eventId.value) {
    try {
      const response = await fetch(
        `/org_user_lists/get_filter_options.json?eventId=${eventId.value}`
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      tagsList.value = data.tags.map((tag) => ({ value: tag, label: tag }));
    } catch (error) {
      console.error("Failed to fetch filter options:", error);
      // Handle error appropriately in UI if needed
    }
  } else {
    console.warn("Event ID is not available. Cannot fetch filter options.");
  }
});

const filterContacts = () => {
  const searchParams = {
    selectedAttendee: attendeeFilter.value,
    selectedTag: tagsSelected.value,
    // selectedEvent: eventTitleFilter.value, // Removed
    // optInStatus: optOutFilter.value, // Removed
    eventStatus: eventStatusFilter.value,
    // selectedDateFrom: dateFromFilter.value, // Removed
    // selectedDateTo: dateToFilter.value, // Removed
    paymentStatus: paymentStatus.value,
    invitedStatus: inviteSentStatus.value,
  };
  emit("search-contacts", searchParams);
};

const clearFilter = () => {
  attendeeFilter.value = "";
  // tagFilter.value = ""; // Removed
  // eventTitleFilter.value = "0"; // Removed
  // optOutFilter.value = "0"; // Removed
  eventStatusFilter.value = 0;
  // dateFromFilter.value = null; // Removed
  // dateToFilter.value = null; // Removed
  paymentStatus.value = null;
  inviteSentStatus.value = "0";
  tagsSelected.value = [];
  emit("clear-filter");
};
</script>

<style scoped>
/* Add any specific styling if needed */
.q-select,
.q-input {
  min-width: 180px; /* Adjust as needed */
}
</style>
