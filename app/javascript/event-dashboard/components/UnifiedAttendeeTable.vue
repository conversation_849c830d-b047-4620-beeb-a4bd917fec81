<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <q-card>
      <q-card-section class="bg-grey-2">
        <div class="text-h6 hg-underline">{{ title }}</div>
      </q-card-section>

      <q-card-section>
        <!-- Attendee Type Filter Buttons -->
        <div class="row q-mb-md">
          <div class="col-12">
            <div class="text-subtitle2 q-mb-sm">View:</div>
            <q-btn-toggle
              v-model="selectedAttendeeType"
              toggle-color="primary"
              :options="attendeeTypeOptions"
              @update:model-value="onAttendeeTypeChange"
              class="q-mb-md"
            />
          </div>
        </div>

        <!-- Filters -->
        <events-filter
          v-if="showFilters"
          :showPaymentFilter="showPaymentFilter"
          :showInvitesSentFilter="showInvitesSentFilter"
          @clear-filter="clearFilters"
          @search-contacts="applyFilters"
        >
        </events-filter>

        <!-- Table -->
        <div class="overflow-auto">
          <q-table
            :rows="tableData"
            :columns="dynamicColumns"
            row-key="id"
            :pagination="tablePagination"
            @request="onPageChange"
            :loading="isLoading"
            binary-state-sort
            :rows-per-page-options="[0]"
            flat
            bordered
          >
            <template v-slot:body="props">
              <q-tr :props="props">
                <!-- Expandable row button -->
                <q-td auto-width v-if="hasExpandableContent">
                  <q-btn
                    size="sm"
                    round
                    flat
                    color="primary"
                    :icon="props.row.expanded ? 'remove' : 'add'"
                    @click="toggleExpanded(props.row)"
                  />
                </q-td>

                <!-- Booking ID -->
                <q-td key="booking_id" :props="props">
                  {{ getBookingId(props.row) }}
                </q-td>

                <!-- Email -->
                <q-td key="email" :props="props">
                  {{ props.row.email }}
                </q-td>

                <!-- Name -->
                <q-td key="name" :props="props">
                  {{ getFullName(props.row) }}
                </q-td>

                <!-- Type (when showing all) -->
                <q-td
                  v-if="selectedAttendeeType === 'all'"
                  key="type"
                  :props="props"
                >
                  <q-badge
                    :color="getTypeColor(props.row._type)"
                    text-color="white"
                  >
                    {{ humanizeString(props.row._type) }}
                  </q-badge>
                </q-td>

                <!-- Date (varies by type) -->
                <q-td key="date" :props="props">
                  {{ formatDate(getDateField(props.row)) }}
                </q-td>

                <!-- Payment Status (conditional) -->
                <q-td
                  v-if="showPaymentStatus"
                  key="payment_status"
                  :props="props"
                >
                  <q-badge
                    v-if="!props.row.event_booking?.free_booking"
                    :color="
                      getStatusColor(props.row.event_booking?.payment_status)
                    "
                    text-color="white"
                  >
                    {{
                      humanizeStatus(props.row.event_booking?.payment_status)
                    }}
                  </q-badge>
                  <q-badge v-else color="warning" text-color="white">
                    Free Booking
                  </q-badge>
                </q-td>

                <!-- Type-specific columns -->
                <q-td
                  v-if="
                    selectedAttendeeType === 'failed' ||
                    (selectedAttendeeType === 'all' &&
                      props.row._type === 'failed')
                  "
                  key="payment_type"
                  :props="props"
                >
                  {{
                    humanizeString(
                      props.row.payment_error?.payment_method?.type
                    )
                  }}
                </q-td>

                <q-td
                  v-if="
                    selectedAttendeeType === 'failed' ||
                    (selectedAttendeeType === 'all' &&
                      props.row._type === 'failed')
                  "
                  key="failure_reason"
                  :props="props"
                >
                  {{ props.row.payment_error?.message }}
                </q-td>

                <q-td
                  v-if="
                    selectedAttendeeType === 'cancelled' ||
                    (selectedAttendeeType === 'all' &&
                      props.row._type === 'cancelled')
                  "
                  key="cancelled_by"
                  :props="props"
                >
                  {{ props.row.event_booking?.cancelled_by }}
                </q-td>

                <q-td
                  v-if="
                    (selectedAttendeeType === 'cancelled' ||
                      (selectedAttendeeType === 'all' &&
                        props.row._type === 'cancelled')) &&
                    showPaymentStatus
                  "
                  key="refunded"
                  :props="props"
                >
                  {{ isRefunded(props.row.event_booking?.payment_status) }}
                </q-td>

                <!-- Invoice column (for confirmed attendees) -->
                <q-td v-if="showInvoiceColumn" key="invoice" :props="props">
                  <q-btn
                    color="primary"
                    v-if="!props.row.event_booking?.free_booking"
                    :loading="props.row.id === selectedAttendee.id"
                    @click="createInvoice(props.row)"
                    size="sm"
                    label="Create Invoice"
                  />
                </q-td>

                <!-- Actions column -->
                <q-td key="actions" :props="props">
                  <q-btn-dropdown
                    v-if="
                      selectedAttendeeType === 'confirmed' ||
                      (selectedAttendeeType === 'all' &&
                        props.row._type === 'confirmed')
                    "
                    color="primary"
                    size="sm"
                    label="Actions"
                  >
                    <q-list>
                      <q-item
                        v-if="
                          showPaymentStatus &&
                          !props.row.event_booking?.free_booking
                        "
                        clickable
                        v-close-popup
                        @click="handleCommand('viewPayments', props.row)"
                      >
                        <q-item-section avatar>
                          <q-icon name="paid" />
                        </q-item-section>
                        <q-item-section>View Payments</q-item-section>
                      </q-item>

                      <q-item
                        clickable
                        v-close-popup
                        @click="handleCommand('resendEmail', props.row)"
                      >
                        <q-item-section avatar>
                          <q-icon name="email" />
                        </q-item-section>
                        <q-item-section>Re-send Email</q-item-section>
                      </q-item>

                      <q-item
                        clickable
                        v-close-popup
                        @click="handleCommand('cancelBooking', props.row)"
                      >
                        <q-item-section avatar>
                          <q-icon name="cancel" />
                        </q-item-section>
                        <q-item-section>Cancel Booking</q-item-section>
                      </q-item>
                    </q-list>
                  </q-btn-dropdown>

                  <!-- Failed payment specific actions -->
                  <q-btn
                    v-else-if="
                      selectedAttendeeType === 'failed' ||
                      (selectedAttendeeType === 'all' &&
                        props.row._type === 'failed')
                    "
                    color="primary"
                    label="Book Again"
                    size="sm"
                    no-caps
                    @click="bookAgain(props.row)"
                  />

                  <!-- Cancelled specific actions -->
                  <q-btn
                    v-else-if="
                      (selectedAttendeeType === 'cancelled' ||
                        (selectedAttendeeType === 'all' &&
                          props.row._type === 'cancelled')) &&
                      showPaymentStatus
                    "
                    color="primary"
                    size="sm"
                    :to="`/payments/${event.id}?user_id=${props.row.id}`"
                    label="Manage Refunds"
                    icon-right="fa fa-money"
                    no-caps
                  />
                </q-td>
              </q-tr>

              <!-- Expandable content row -->
              <q-tr v-if="props.row.expanded && hasExpandableContent">
                <q-td :colspan="dynamicColumns.length">
                  <div class="q-pa-md bg-grey-1">
                    <div
                      v-if="
                        selectedAttendeeType === 'failed' ||
                        (selectedAttendeeType === 'all' &&
                          props.row._type === 'failed')
                      "
                    >
                      <strong>Booking Payment Error Message:</strong>
                      <pre>{{ props.row.payment_error }}</pre>
                    </div>
                    <div v-else>
                      <strong>User invite url:</strong>
                      {{ rootUrl }}{{ props.row.user_invite_url }}
                    </div>
                  </div>
                </q-td>
              </q-tr>
            </template>

            <template v-slot:loading>
              <q-inner-loading showing color="primary">
                <q-spinner size="3em" />
                <div class="q-mt-sm">
                  Loading
                  {{
                    selectedAttendeeType === "all"
                      ? "all"
                      : selectedAttendeeType
                  }}
                  attendees...
                </div>
              </q-inner-loading>
            </template>
          </q-table>
        </div>

        <!-- Pagination -->
        <div class="q-mt-md">
          <p>Total Records: {{ totalItems }}</p>
          <q-pagination
            v-model="currentPage"
            :max="Math.ceil(totalItems / itemsPerPage)"
            :max-pages="6"
            direction-links
            boundary-links
            @update:model-value="onPageChange"
          />
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useRouter } from "vue-router";
import { useDashboardStore } from "@/stores/dashboard";
import { useEventStore } from "@/stores/event";
import axios from "axios";
import EventsFilter from "./EventsFilter.vue";
import { format } from "date-fns";
import { storeToRefs } from "pinia";

// Props
const props = defineProps({
  attendeeType: {
    type: String,
    required: true,
    validator: (value) =>
      [
        "confirmed",
        "unconfirmed",
        "cancelled",
        "declined",
        "failed",
        "unpaid",
      ].includes(value),
  },
  title: {
    type: String,
    default: "Attendees",
  },
  showFilters: {
    type: Boolean,
    default: true,
  },
  showPaymentFilter: {
    type: Boolean,
    default: true,
  },
  showInvitesSentFilter: {
    type: Boolean,
    default: false,
  },
});

// Setup dependencies
const $q = useQuasar();
const router = useRouter();
const dashboardStore = useDashboardStore();
const eventStore = useEventStore();

// Destructure store properties
const { event } = storeToRefs(eventStore);

// Local state
const selectedAttendee = ref({});
const selectedAttendeeType = ref(props.attendeeType);
const rootUrl = window.location.origin;

// Attendee type options for the toggle buttons
const attendeeTypeOptions = ref([
  { label: "All", value: "all" },
  { label: "Confirmed", value: "confirmed" },
  { label: "Unconfirmed", value: "unconfirmed" },
  { label: "Cancelled", value: "cancelled" },
  { label: "Declined", value: "declined" },
  { label: "Failed Payments", value: "failed" },
  { label: "Unpaid", value: "unpaid" },
]);

// Computed properties
const tableData = computed(() => {
  const currentType = selectedAttendeeType.value;

  if (currentType === "all") {
    // Combine all attendee types when "all" is selected
    return [
      ...dashboardStore.attendees.map((item) => ({
        ...item,
        _type: "confirmed",
      })),
      ...dashboardStore.unconfirmedAttendees.map((item) => ({
        ...item,
        _type: "unconfirmed",
      })),
      ...dashboardStore.cancelledAttendees.map((item) => ({
        ...item,
        _type: "cancelled",
      })),
      ...dashboardStore.declinedAttendees.map((item) => ({
        ...item,
        _type: "declined",
      })),
      ...dashboardStore.failedPaymentAttendees.map((item) => ({
        ...item,
        _type: "failed",
      })),
      ...dashboardStore.unpaidBookings.map((item) => ({
        ...item,
        _type: "unpaid",
      })),
    ];
  }

  switch (currentType) {
    case "confirmed":
      return dashboardStore.attendees;
    case "unconfirmed":
      return dashboardStore.unconfirmedAttendees;
    case "cancelled":
      return dashboardStore.cancelledAttendees;
    case "declined":
      return dashboardStore.declinedAttendees;
    case "failed":
      return dashboardStore.failedPaymentAttendees;
    case "unpaid":
      return dashboardStore.unpaidBookings;
    default:
      return [];
  }
});

const showPaymentStatus = computed(() => {
  const currentType = selectedAttendeeType.value;
  return (
    event.value?.has_paid_tickets &&
    (currentType === "all" || ["confirmed", "cancelled"].includes(currentType))
  );
});

const showInvoiceColumn = computed(() => {
  const currentType = selectedAttendeeType.value;
  return (
    event.value?.has_paid_tickets &&
    (currentType === "all" || currentType === "confirmed")
  );
});

const hasExpandableContent = computed(() => {
  const currentType = selectedAttendeeType.value;
  return currentType === "all" || ["confirmed", "failed"].includes(currentType);
});

const currentPage = computed({
  get: () => dashboardStore.pagination.currentPage,
  set: (value) => dashboardStore.setPage(value),
});

const totalItems = computed(() => dashboardStore.pagination.totalItems);
const itemsPerPage = computed(() => dashboardStore.pagination.itemsPerPage);
const isLoading = computed(() => dashboardStore.loading);

const tablePagination = computed(() => ({
  rowsPerPage: itemsPerPage.value,
  page: currentPage.value,
}));

// Dynamic columns based on attendee type
const dynamicColumns = computed(() => {
  const baseColumns = [
    {
      name: "booking_id",
      align: "left",
      label: "Booking ID",
      field: "id",
      sortable: true,
    },
    {
      name: "email",
      align: "left",
      label: "Email Address",
      field: "email",
      sortable: true,
    },
    {
      name: "name",
      align: "left",
      label: "Name",
      field: "name",
      sortable: true,
    },
  ];

  // Add expandable column if needed
  if (hasExpandableContent.value) {
    baseColumns.unshift({
      name: "expander",
      label: "",
      field: "expanded",
      sortable: false,
    });
  }

  // Add type column when showing all attendees
  if (selectedAttendeeType.value === "all") {
    baseColumns.push({
      name: "type",
      align: "left",
      label: "Type",
      field: "_type",
      sortable: true,
      format: (val) => humanizeString(val),
    });
  }

  // Add date column with appropriate label
  const dateLabel = getDateColumnLabel();
  baseColumns.push({
    name: "date",
    align: "left",
    label: dateLabel,
    field: "date",
    sortable: true,
  });

  // Add payment status column if needed
  if (showPaymentStatus.value) {
    baseColumns.push({
      name: "payment_status",
      align: "left",
      label: "Payment Status",
      field: "payment_status",
      sortable: true,
    });
  }

  // Add type-specific columns
  const currentType = selectedAttendeeType.value;

  if (currentType === "failed" || currentType === "all") {
    baseColumns.push(
      {
        name: "payment_type",
        align: "left",
        label: "Payment Type",
        field: "payment_type",
        sortable: false,
      },
      {
        name: "failure_reason",
        align: "left",
        label: "Failure Reason",
        field: "failure_reason",
        sortable: false,
      }
    );
  }

  if (currentType === "cancelled" || currentType === "all") {
    baseColumns.push({
      name: "cancelled_by",
      align: "left",
      label: "Cancelled By",
      field: "cancelled_by",
      sortable: false,
    });
    if (showPaymentStatus.value) {
      baseColumns.push({
        name: "refunded",
        align: "left",
        label: "Refunded",
        field: "refunded",
        sortable: false,
      });
    }
  }

  // Add invoice column if needed
  if (showInvoiceColumn.value) {
    baseColumns.push({
      name: "invoice",
      align: "center",
      label: "Invoice User",
      field: "id",
      sortable: false,
    });
  }

  // Add actions column
  baseColumns.push({
    name: "actions",
    align: "center",
    label: "Action",
    sortable: false,
  });

  return baseColumns;
});

// Methods
const getDateColumnLabel = () => {
  const currentType = selectedAttendeeType.value;

  if (currentType === "all") {
    return "Date";
  }

  switch (currentType) {
    case "confirmed":
      return "Confirmed Date/Time";
    case "cancelled":
      return "Cancelled At";
    case "failed":
      return "Confirmed Date/Time";
    default:
      return "Date";
  }
};

const getBookingId = (row) => {
  return row.event_booking?.id || row.id;
};

const getFullName = (row) => {
  return `${row.forename || ""} ${row.surname || ""}`.trim() || row.name;
};

const getDateField = (row) => {
  // If we're in "all" mode, use the row's _type to determine the date field
  if (selectedAttendeeType.value === "all" && row._type) {
    switch (row._type) {
      case "cancelled":
        return row.event_booking?.cancelled_at;
      case "failed":
        return row.booking_date;
      default:
        return row.booking_date;
    }
  }

  // Otherwise use the selected attendee type
  const currentType = selectedAttendeeType.value;
  switch (currentType) {
    case "cancelled":
      return row.event_booking?.cancelled_at;
    case "failed":
      return row.booking_date;
    default:
      return row.booking_date;
  }
};

const formatDate = (dateString) => {
  if (!dateString) return "";
  try {
    return format(new Date(dateString), "dd/MM/yyyy HH:mm");
  } catch (e) {
    return dateString;
  }
};

const getStatusColor = (status) => {
  if (
    status === "paid" ||
    status === "refunded_paid_again" ||
    status === "complimentary"
  ) {
    return "positive";
  } else if (
    status === "part_paid" ||
    status === "refunded" ||
    status === "part_refunded"
  ) {
    return "warning";
  } else {
    return "negative";
  }
};

const humanizeStatus = (status) => {
  if (!status) return "";
  const string = status.split("_").join(" ").toLowerCase();
  return string.charAt(0).toUpperCase() + string.slice(1);
};

const humanizeString = (str) => {
  if (!str) return "";
  const string = str.split("_").join(" ").toLowerCase();
  return string.charAt(0).toUpperCase() + string.slice(1);
};

const getTypeColor = (type) => {
  switch (type) {
    case "confirmed":
      return "positive";
    case "cancelled":
      return "negative";
    case "failed":
      return "negative";
    case "declined":
      return "warning";
    case "unconfirmed":
      return "info";
    case "unpaid":
      return "orange";
    default:
      return "grey";
  }
};

const isRefunded = (paymentStatus) => {
  return ["refunded", "part_refunded"].includes(paymentStatus) ? "Yes" : "No";
};

const toggleExpanded = (row) => {
  row.expanded = !row.expanded;
};

const onPageChange = (page) => {
  const pageNumber = typeof page === "object" ? page.pagination.page : page;
  dashboardStore.setPage(pageNumber);
  fetchData();
};

const fetchData = () => {
  const currentType = selectedAttendeeType.value;

  if (currentType === "all") {
    // Fetch all types when "all" is selected
    dashboardStore.fetchAttendees();
    dashboardStore.fetchUnconfirmedAttendees();
    dashboardStore.fetchCancelledAttendees();
    dashboardStore.fetchDeclinedAttendees();
    dashboardStore.fetchFailedPaymentAttendees();
    dashboardStore.fetchUnpaidBookings();
    return;
  }

  switch (currentType) {
    case "confirmed":
      dashboardStore.fetchAttendees();
      break;
    case "unconfirmed":
      dashboardStore.fetchUnconfirmedAttendees();
      break;
    case "cancelled":
      dashboardStore.fetchCancelledAttendees();
      break;
    case "declined":
      dashboardStore.fetchDeclinedAttendees();
      break;
    case "failed":
      dashboardStore.fetchFailedPaymentAttendees();
      break;
    case "unpaid":
      dashboardStore.fetchUnpaidBookings();
      break;
  }
};

const onAttendeeTypeChange = (newType) => {
  selectedAttendeeType.value = newType;
  dashboardStore.setPage(1); // Reset to first page when changing type
  fetchData();
};

// Action methods
const applyFilters = (filters) => {
  dashboardStore.setSearchTerm(filters.searchTerm || "");
  dashboardStore.setTicketTypeFilter(filters.ticketType || "all");
  dashboardStore.setDateRangeFilter(filters.dateRange);
  fetchData();
};

const clearFilters = () => {
  dashboardStore.resetFilters();
  fetchData();
};

const bookAgain = (booking) => {
  $q.dialog({
    title: "Are you sure?",
    message: "You will be redirected to the bookings page",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    window.open(`/invite/${booking.uuid}`, "_blank");
  });
};

const resendConfirmationEmail = (user) => {
  $q.dialog({
    title: "Are you sure?",
    message: "Resend Confirmation",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .get(`/resend_confirmation/${user.id}.json`)
      .then(() => {
        $q.notify({
          message: "The confirmation has been re-sent.",
          color: "positive",
          icon: "check",
        });
      })
      .catch(() => {
        $q.notify({
          message: "The email has not been sent.",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const cancelBooking = (user) => {
  $q.dialog({
    title: "Are you sure?",
    message: "This will cancel the booking!",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .put(`/registered_users/${user.id}/cancel`)
      .then((response) => {
        const updatedBooking = response.data;
        user.event_booking.cancelled_at = updatedBooking.cancelled_at;
        user.event_booking.cancelled_by = updatedBooking.cancelled_by;
        user.event_booking.payment_status = updatedBooking.payment_status;

        fetchData();

        $q.notify({
          message: "The booking has been cancelled",
          color: "positive",
          icon: "check",
        });
      })
      .catch(() => {
        $q.notify({
          message: "The booking has not been cancelled!",
          color: "negative",
          icon: "error",
        });
      });
  });
};

const createInvoice = (user) => {
  selectedAttendee.value = user;
  const url = `/event_bookings/${user.event_booking.id}/invoice/`;

  axios
    .get(url, { responseType: "blob" })
    .then((response) => {
      if (response.data.receipt_url) {
        window.open(response.data.receipt_url, "_blank").focus();
      } else {
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, "0");
        const mm = String(today.getMonth() + 1).padStart(2, "0");
        const yyyy = today.getFullYear();
        const formattedDate = `${dd}/${mm}/${yyyy}`;

        const filename = `${user.forename}_${user.surname}_${formattedDate}.pdf`;
        const data = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = data;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(data);
      }
      selectedAttendee.value = {};
    })
    .catch((error) => {
      $q.notify({
        message:
          error.response?.data?.error_message || "Failed to generate invoice",
        color: "negative",
        icon: "error",
      });
      selectedAttendee.value = {};
    });
};

const handleCommand = (command, user) => {
  switch (command) {
    case "viewPayments":
      router.push({
        name: "payments",
        params: { status: null },
        query: { user_id: user.id },
      });
      break;
    case "resendEmail":
      resendConfirmationEmail(user);
      break;
    case "cancelBooking":
      cancelBooking(user);
      break;
  }
};

// Initialize component
onMounted(() => {
  // Set the event data from window if available
  if (window.eventData) {
    dashboardStore.setEvent(window.eventData);
  }

  // Fetch data
  fetchData();

  // Load statistics if needed
  if (props.attendeeType === "confirmed") {
    dashboardStore.fetchStatistics();
  }
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid var(--q-primary);
  font-weight: bold;
  padding-bottom: 10px;
}
</style>
