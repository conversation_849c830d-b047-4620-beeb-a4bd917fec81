# UnifiedAttendeeTable Component

A single, reusable Vue component that combines all the functionality from the separate delegate components (`DelegatesAttending.vue`, `DelegatesFailedPayment.vue`, `DelegatesCancelled.vue`, etc.) into one unified table component.

## Features

- **Single Component**: Replaces multiple delegate components with one unified solution
- **Dynamic Columns**: Automatically adjusts columns based on attendee type
- **Conditional Features**: Shows/hides features based on event configuration and attendee type
- **Consistent Styling**: Uses the same Quasar styling patterns across all attendee types
- **Reusable Actions**: Common actions like pagination, filtering, and CRUD operations
- **Type Safety**: Props validation ensures correct usage

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `attendeeType` | String | Yes | - | Type of attendees to display. Valid values: `'confirmed'`, `'unconfirmed'`, `'cancelled'`, `'declined'`, `'failed'`, `'unpaid'` |
| `title` | String | No | `'Attendees'` | Title displayed in the card header |
| `showFilters` | Boolean | No | `true` | Whether to show the EventsFilter component |
| `showPaymentFilter` | Boolean | No | `true` | Whether to show payment-related filters |
| `showInvitesSentFilter` | Boolean | No | `false` | Whether to show invite status filters |

## Usage Examples

### Confirmed Attendees
```vue
<template>
  <unified-attendee-table
    attendee-type="confirmed"
    title="Bookings Confirmed"
    :show-filters="true"
    :show-payment-filter="hasPaidTickets"
    :show-invites-sent-filter="false"
  />
</template>
```

### Failed Payment Attendees
```vue
<template>
  <unified-attendee-table
    attendee-type="failed"
    title="Bookings Failed Due To Payment Issue"
    :show-filters="false"
    :show-payment-filter="false"
    :show-invites-sent-filter="false"
  />
</template>
```

### Cancelled Attendees
```vue
<template>
  <unified-attendee-table
    attendee-type="cancelled"
    title="Cancellations"
    :show-filters="false"
    :show-payment-filter="false"
    :show-invites-sent-filter="false"
  />
</template>
```

### Unconfirmed Attendees
```vue
<template>
  <unified-attendee-table
    attendee-type="unconfirmed"
    title="Contacts Search"
    :show-filters="true"
    :show-payment-filter="false"
    :show-invites-sent-filter="true"
  />
</template>
```

## Dynamic Features

### Columns
The component automatically adjusts columns based on the `attendeeType`:

- **All Types**: Booking ID, Email, Name, Date
- **Confirmed**: + Payment Status, Invoice, Actions dropdown
- **Failed**: + Payment Type, Failure Reason, Book Again button
- **Cancelled**: + Cancelled By, Refunded status, Manage Refunds button
- **Expandable**: Confirmed and Failed types have expandable rows

### Actions
Different attendee types have different available actions:

- **Confirmed**: View Payments, Re-send Email, Cancel Booking, Create Invoice
- **Failed**: Book Again
- **Cancelled**: Manage Refunds
- **Unconfirmed**: (handled by separate invite functionality)

### Data Sources
The component automatically fetches data from the appropriate dashboard store method:

- `confirmed` → `dashboardStore.fetchAttendees()`
- `unconfirmed` → `dashboardStore.fetchUnconfirmedAttendees()`
- `cancelled` → `dashboardStore.fetchCancelledAttendees()`
- `declined` → `dashboardStore.fetchDeclinedAttendees()`
- `failed` → `dashboardStore.fetchFailedPaymentAttendees()`
- `unpaid` → `dashboardStore.fetchUnpaidBookings()`

## Dependencies

- **Stores**: `useDashboardStore`, `useEventStore`
- **Components**: `EventsFilter.vue`
- **Libraries**: Quasar, Vue Router, axios, date-fns, Pinia

## Migration Guide

To migrate from individual delegate components:

1. **Replace component imports**:
   ```javascript
   // Old
   import DelegatesAttending from './DelegatesAttending.vue';
   import DelegatesFailedPayment from './DelegatesFailedPayment.vue';
   
   // New
   import UnifiedAttendeeTable from './UnifiedAttendeeTable.vue';
   ```

2. **Update component usage**:
   ```vue
   <!-- Old -->
   <delegates-attending />
   <delegates-failed-payment />
   
   <!-- New -->
   <unified-attendee-table attendee-type="confirmed" title="Bookings Confirmed" />
   <unified-attendee-table attendee-type="failed" title="Failed Payments" />
   ```

3. **Remove old component files** (optional):
   - `DelegatesAttending.vue`
   - `DelegatesFailedPayment.vue`
   - `DelegatesCancelled.vue`
   - `DelegatesDeclined.vue`
   - `DelegatesUnconfirmed.vue`

## Benefits

1. **Reduced Code Duplication**: Single component handles all attendee types
2. **Consistent UI/UX**: Same styling and behavior patterns across all tables
3. **Easier Maintenance**: Changes only need to be made in one place
4. **Better Performance**: Shared logic and optimized rendering
5. **Type Safety**: Props validation prevents incorrect usage
6. **Flexible Configuration**: Easy to customize for different use cases

## Store Integration

The component integrates with the dashboard store for:
- Data fetching and caching
- Pagination management
- Filter state management
- Loading states
- Error handling

All attendee data is managed through the centralized dashboard store, ensuring consistency across the application.
