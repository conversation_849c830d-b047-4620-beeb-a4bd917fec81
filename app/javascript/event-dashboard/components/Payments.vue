<!-- filepath: /Users/<USER>/Documents/Development/HGEvents/app/javascript/event-dashboard/components/Payments.vue -->
<template>
  <div class="row">
    <div class="col-12">
      <q-card class="previewcard-container">
        <q-card-section>
          <div class="text-h5 q-mb-md">View Payments For {{ event.name }}</div>

          <q-select
            v-model="selectedTab"
            :options="filterOptions"
            option-value="value"
            option-label="label"
            dense
            outlined
            emit-value
            map-options
            clearable
            label="Filter By Payment Status"
            @update:model-value="setStatus"
            class="q-mb-md"
          />

          <q-list bordered separator>
            <q-expansion-item
              v-for="(booking, idx) in event.bookings"
              :key="idx"
              :data-id="accordionName(idx)"
              expand-separator
              :label="`${booking.forename} ${booking.surname} (${booking.email})`"
              header-class="text-primary"
            >
              <template v-slot:header-right>
                <q-badge
                  v-if="booking.cancelled_at"
                  color="negative"
                  class="q-ml-sm"
                >
                  CANCELLED BOOKING
                </q-badge>
                <q-badge
                  v-if="!booking.free_booking"
                  :color="getStatusColor(booking.status)"
                  class="q-ml-sm"
                >
                  {{ humanizedPaymentStatus(booking.status) }}
                </q-badge>
                <q-badge
                  v-if="booking.free_booking"
                  color="warning"
                  class="q-ml-sm"
                >
                  Free Booking
                </q-badge>
              </template>

              <q-card>
                <q-card-section>
                  <pricing-table
                    :event="event"
                    :booking="booking"
                    :advuser="advancedUser"
                  />
                </q-card-section>
              </q-card>
            </q-expansion-item>
          </q-list>

          <div class="q-mt-md text-caption">
            Total Records: {{ totalItems }}
          </div>

          <div class="q-mt-md">
            <q-pagination
              v-model="selectedPage"
              :max="Math.ceil(totalItems / 20)"
              :max-pages="5"
              :boundaries="1"
              @update:model-value="pageChanged"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import { useRoute } from "vue-router";
import axios from "axios";
import PricingTable from "./PricingTable.vue";

// Setup dependencies
const $q = useQuasar();
const eventStore = useEventStore();
const route = useRoute();

// Component state
const event = computed(() => eventStore.getEvent);
const advancedUser = ref(window.advancedUser);
const totalItems = ref(0);
const selectedPage = ref(1);
const status = ref(null);
const selectedTab = ref(null);
const userID = ref(null);

// Filter options
const filterOptions = [
  { value: "paid", label: "Paid" },
  { value: "unpaid", label: "Unpaid" },
  { value: "refunded", label: "Refunded" },
  { value: "part_refunded", label: "Part Refunded" },
  { value: "refunded_paid_again", label: "Refunded, Paid Again" },
  { value: "free_booking", label: "Free Booking" },
  { value: "processing", label: "Processing" },
];

// Methods
const accordionName = (idx) => {
  return "accordion" + idx;
};

const setStatus = (newStatus) => {
  status.value = newStatus || null;
  pageChanged();
};

const pageChanged = (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  if (userID.value) {
    axios
      .get(`/delegate_payments_booking/${userID.value}`)
      .then((response) => {
        event.value = response.data.event;
        if (typeof event.value !== "undefined") {
          totalItems.value = event.value.total_event_bookings_count;
        }
      })
      .catch((error) => {
        console.error("Error fetching payment data:", error);
        $q.notify({
          message: "Failed to load payment data",
          color: "negative",
          icon: "error",
        });
      });
  } else {
    axios
      .get(`/delegate_payments/${event.value.id}/`, {
        params: {
          page: selectedPage.value,
          payment_filter: status.value,
        },
      })
      .then((response) => {
        event.value = response.data.event;
        if (typeof event.value !== "undefined") {
          totalItems.value = event.value.total_event_bookings_count;
        }
      })
      .catch((error) => {
        console.error("Error fetching payment data:", error);
        $q.notify({
          message: "Failed to load payment data",
          color: "negative",
          icon: "error",
        });
      });
  }
};

const canPay = (paymentMethods) => {
  return (
    advancedUser.value &&
    (paymentMethods["bacs_enabled"] || paymentMethods["cheque_enabled"])
  );
};

const humanizedPaymentStatus = (status) => {
  switch (status) {
    case "paid":
      return "Paid";
    case "part_paid":
      return "Part Paid";
    case "unpaid":
      return "Unpaid";
    case "refunded":
      return "Refunded";
    case "part_refunded":
      return "Partially Refunded";
    case "refunded_paid_again":
      return "Refunded, Paid Again";
    case "not_refunded":
      return "Not Refunded";
    case "payment_failed":
      return "Payment Failed";
    case "complimentary":
      return "Complimentary";
    case "processing":
      return "Processing";
    default:
      return status;
  }
};

const getStatusColor = (status) => {
  switch (status) {
    case "paid":
      return "positive";
    case "part_paid":
      return "warning";
    case "unpaid":
      return "grey-7";
    case "refunded":
      return "warning";
    case "part_refunded":
      return "warning";
    case "refunded_paid_again":
      return "positive";
    case "not_refunded":
      return "negative";
    case "payment_failed":
      return "negative";
    case "complimentary":
      return "positive";
    case "processing":
      return "warning";
    default:
      return "grey";
  }
};

// Initialize component
onMounted(() => {
  // Check for user_id in query params
  if (route.query.user_id) {
    userID.value = route.query.user_id;
  }

  // Check for status in route params
  if (route.params && route.params.status) {
    selectedTab.value = route.params.status;
    setStatus(route.params.status);
  } else {
    pageChanged();
  }
});

// Watch for route param changes
watch(
  () => route.params.status,
  (newStatus) => {
    if (newStatus !== undefined) {
      selectedTab.value = newStatus;
      setStatus(newStatus);
    }
  }
);
</script>
