<!-- filepath: /Users/<USER>/Documents/Development/HGEvents/app/javascript/event-dashboard/components/BookingsUnpaid.vue -->
<template>
  <div class="col-12 mb">
    <q-card>
      <q-card-section class="hg-underline">
        <div class="text-h6">Unpaid Bookings</div>
      </q-card-section>
      <q-card-section>
        <div class="q-my-md">
          <q-markup-table flat bordered>
            <thead>
              <tr>
                <th>Booking ID</th>
                <th>Email Address</th>
                <th>Name</th>
                <th>Created Date & Time</th>
                <th>Booking Date & Time</th>
                <th v-if="!event.is_public">Invite Sent</th>
                <th>Delete Booking</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(booking, idx) in unpaidBookings" :key="idx">
                <td>{{ booking.id }}</td>
                <td>{{ booking.email }}</td>
                <td>{{ booking.name }}</td>
                <td>{{ formatDate(booking.created_at) }}</td>
                <td>{{ formatDate(booking.booking_date) }}</td>
                <td v-if="!event.is_public">{{ booking.invite_sent }}</td>
                <td>
                  <q-btn
                    @click="deleteBooking(booking)"
                    color="negative"
                    icon="delete"
                    size="sm"
                    round
                    flat
                  />
                </td>
              </tr>
            </tbody>
          </q-markup-table>

          <div class="q-mt-md text-caption">
            Total Records: {{ totalItems }}
          </div>

          <div class="q-mt-md">
            <q-pagination
              v-model="selectedPage"
              :max="Math.ceil(totalItems / 100)"
              :max-pages="5"
              :boundaries="1"
              @update:model-value="pageChanged"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import axios from "axios";

// Setup dependencies
const $q = useQuasar();
const eventStore = useEventStore();

// Component state
const event = computed(() => eventStore.getEvent);
const unpaidBookings = ref([]);
const totalItems = ref(0);
const selectedPage = ref(1);

// Format dates with local formatting
const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

// Methods
const deleteBooking = (booking) => {
  $q.dialog({
    title: `Do you want to delete the booking for: ${booking.name}?`,
    message: "This will delete the booking permanently.",
    color: "negative",
    icon: "warning",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .delete(`/event_bookings/${booking.id}`)
      .then(() => {
        $q.notify({
          message: "Booking Deleted",
          color: "positive",
          icon: "check",
        });
        pageChanged();
      })
      .catch((error) => {
        $q.notify({
          message: "Booking Could Not Be Deleted",
          color: "negative",
          icon: "error",
        });
        console.error(error);
      });
  });
};

const pageChanged = (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  axios
    .get(`/unpaid_bookings/${event.value.id}?page=${selectedPage.value}`)
    .then((response) => {
      unpaidBookings.value = response.data.unpaid_bookings;
      totalItems.value = response.data.total_count;
    })
    .catch((error) => {
      console.error("Error fetching unpaid bookings:", error);
      $q.notify({
        message: "Failed to load unpaid bookings",
        color: "negative",
        icon: "error",
      });
    });
};

// Fetch data on component mount
onMounted(() => {
  pageChanged();
});
</script>
