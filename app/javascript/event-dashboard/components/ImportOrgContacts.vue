<template>
  <q-card class="q-mb-md">
    <q-card-section class="q-pb-xs">
      <div class="text-h6">Import contacts from organisation contact list</div>
    </q-card-section>
    <q-card-section>
      <form class="form form-import-contacts" @submit.prevent="importContacts">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-select
              v-model="eventTitle"
              :options="eventOptions"
              label="Select Event"
              option-value="value"
              option-label="text"
              emit-value
              map-options
              outlined
              dense
            />
          </div>
          <div class="col-12 col-md-4">
            <q-select
              v-model="eventStatus"
              :options="eventStatusOptions"
              label="Contact Status"
              option-value="value"
              option-label="text"
              emit-value
              map-options
              outlined
              dense
            />
          </div>
          <div class="col-12 col-md-4">
            <q-btn
              color="primary"
              label="Select Contact Tags"
              icon="filter_list"
            >
              <q-menu>
                <q-card style="width: 300px">
                  <q-card-section>
                    <div class="text-h6">Select Contact Tags</div>
                  </q-card-section>
                  <q-card-section>
                    <q-select
                      v-model="tagsSelected"
                      :options="tagsList"
                      option-value="value"
                      option-label="label"
                      multiple
                      label="Add Tags"
                      emit-value
                      map-options
                      use-chips
                      outlined
                      dense
                      style="width: 100%"
                    />
                  </q-card-section>
                </q-card>
              </q-menu>
            </q-btn>
          </div>
        </div>
        <div class="row q-col-gutter-md q-mt-md">
          <div class="col-12 col-md-4">
            <q-input
              v-model="dateFrom"
              outlined
              dense
              label="Events From"
              mask="date"
              placeholder="YYYY/MM/DD"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="dateFrom" minimal />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
          <div class="col-12 col-md-4">
            <q-input
              v-model="dateTo"
              outlined
              dense
              label="Events To"
              mask="date"
              placeholder="YYYY/MM/DD"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="dateTo" minimal />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
        </div>
        <div class="row q-mt-md">
          <div class="col">
            <q-btn
              :label="submitted ? 'Please Wait' : 'Import'"
              :loading="submitted"
              :color="submitted ? 'grey' : 'primary'"
              type="submit"
              :disable="submitted"
            />
          </div>
        </div>
      </form>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount, watch } from "vue";
import { debounce } from "lodash";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";

// Import the existing event bus
import { useDashboardEventBus } from "../dashboard-event-bus";
const eventBus = useDashboardEventBus();

const props = defineProps({
  event: Object,
});

const eventStore = useEventStore();
const $q = useQuasar();

// Reactive state
const eventId = computed(() => props.event?.id);
const eventTitle = ref(0);
const eventOptions = ref([{ text: "Select Event", value: 0 }]);
const tags = ref([]);
const tagsList = ref([]);
const tagsSelected = ref([]);
const eventStatus = ref(0);
const eventStatusOptions = ref([
  { text: "All Contacts", value: 0 },
  { text: "Attended Event", value: 1 },
  { text: "Booked on Event", value: 2 },
  { text: "Cancelled Booking", value: 3 },
]);
const dateFrom = ref(null);
const dateTo = ref(null);
const submitted = ref(false);
const polling = ref(null);

// Computed property for queueId
const queueId = computed({
  get: () => eventStore.getImportContactJobID,
  set: (value) => eventStore.setImportContactJobID(value),
});

// Fetch data on component creation
onMounted(async () => {
  if (eventId.value) {
    try {
      const response = await fetch(
        `/org_user_lists/get_filter_options.json?eventId=${eventId.value}`
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      eventOptions.value = [
        { text: "Select Event", value: 0 },
        ...data.event_filter.map((ev) => ({ text: ev.title, value: ev.id })),
      ];

      tags.value = data.tags;
      tagsList.value = data.tags.map((tag) => ({ value: tag, label: tag }));
    } catch (error) {
      console.error("Failed to fetch filter options:", error);
      // Handle error appropriately
    }
  }

  if (queueId.value) {
    submitted.value = true;
    eventBus.emit("uploadingContacts", true);
    setTimeout(() => {
      pollQueue();
    }, 5000);
  }
});

// Clean up on component destruction
onBeforeUnmount(() => {
  stopPolling();
});

// Methods
const clearFields = () => {
  eventTitle.value = 0;
  tagsSelected.value = [];
  eventStatus.value = 0;
  dateFrom.value = null;
  dateTo.value = null;
};

const importContacts = debounce(async () => {
  submitted.value = true;
  eventBus.emit("uploadingContacts", true);

  const searchAtts = {
    eventId: eventId.value,
    eventTitle: eventTitle.value,
    contactTag: tagsSelected.value,
    eventStatus: eventStatus.value,
    dateTo: dateTo.value,
    dateFrom: dateFrom.value,
  };

  try {
    const response = await fetch("/org_user_lists/import_contacts.json", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify(searchAtts),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    queueId.value = data.queue;
    pollQueue();
  } catch (error) {
    console.error("Failed to import contacts:", error);
    $q.notify({
      message: "There was a problem creating new invites",
      color: "negative",
      icon: "error",
    });
    submitted.value = false;
    eventBus.emit("uploadingContacts", false);
  }
}, 300);

const pollQueue = () => {
  polling.value = setInterval(async () => {
    try {
      const response = await fetch(
        `/org_user_lists/poll_imports.json?job_id=${queueId.value}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        stopPolling();
        loadNewContacts(data.no_added);
      }
    } catch (error) {
      console.error("Failed to poll imports:", error);
      $q.notify({
        message: "There was a problem creating new invites",
        color: "negative",
        icon: "error",
      });
      stopPolling();
      submitted.value = false;
      queueId.value = null;
      eventBus.emit("uploadingContacts", false);
    }
  }, 10000);
};

const loadNewContacts = (no_contacts_added) => {
  submitted.value = false;
  queueId.value = null;
  eventBus.emit("uploadingContacts", false);
  eventBus.emit("usersUpdated");
  clearFields();

  if (no_contacts_added > 0) {
    $q.notify({
      message: "Created invites for organisation contacts",
      color: "positive",
      icon: "check_circle",
    });
  } else {
    $q.notify({
      message: "No new contacts were found with these search criteria",
      color: "positive",
      icon: "check_circle",
    });
  }
};

const stopPolling = () => {
  if (polling.value) {
    clearInterval(polling.value);
    polling.value = null;
  }
};
</script>
