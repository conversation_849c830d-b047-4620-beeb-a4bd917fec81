<!-- filepath: /Users/<USER>/Documents/Development/HGEvents/app/javascript/event-dashboard/components/DelegatesCancelled.vue -->
<template>
  <div class="wrapper">
    <div class="row q-mt-md">
      <div class="col-12 q-mb-md">
        <q-card>
          <q-card-section class="hg-underline">
            <div class="text-h6">Cancellations</div>
          </q-card-section>
          <q-card-section>
            <q-markup-table flat bordered>
              <thead>
                <tr>
                  <th>Event Booking ID</th>
                  <th>Email Address</th>
                  <th>Name</th>
                  <th>Cancelled At</th>
                  <th>Cancelled By</th>
                  <th v-if="hasPaidTickets">Refunded</th>
                  <th v-if="hasPaidTickets">Manage Refunds</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(user, idx) in attendeesCancelled" :key="idx">
                  <td>{{ user.event_booking.id }}</td>
                  <td>{{ user.email }}</td>
                  <td>{{ user.forename }} {{ user.surname }}</td>
                  <td>{{ formatDateTime(user.event_booking.cancelled_at) }}</td>
                  <td>{{ user.event_booking.cancelled_by }}</td>
                  <td v-if="hasPaidTickets">
                    {{ isRefunded(user.event_booking.payment_status) }}
                  </td>
                  <td v-if="hasPaidTickets">
                    <q-btn
                      color="primary"
                      size="sm"
                      :to="`/payments/${event.id}?user_id=${user.id}`"
                      label="Manage Refunds"
                      icon-right="fa fa-money"
                      no-caps
                    />
                  </td>
                  <td>
                    <!-- Book Again functionality can be implemented if needed -->
                  </td>
                </tr>
              </tbody>
            </q-markup-table>

            <div class="q-mt-md text-caption">
              Total Records: {{ totalItems }}
            </div>

            <div class="q-mt-md">
              <q-pagination
                v-model="selectedPage"
                :max="Math.ceil(totalItems / 100)"
                :max-pages="5"
                :boundaries="1"
                @update:model-value="pageChanged"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import axios from "axios";

// Setup dependencies
const $q = useQuasar();
const eventStore = useEventStore();

// Component state
const event = computed(() => eventStore.getEvent);
const attendeesCancelled = ref([]);
const selectedPage = ref(1);
const totalItems = ref(0);
const rootUrl = ref(window.root_url || "");

// Computed properties
const hasPaidTickets = computed(() => eventStore.getChargeable);

// Format dates with local formatting
const formatDateTime = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

// Methods
const pageChanged = (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  axios
    .get(
      `/registered_users/${event.value.id}/show_cancelled.json?page=${selectedPage.value}`
    )
    .then((response) => {
      attendeesCancelled.value = response.data.cancelled_users;
      totalItems.value = response.data.total_count;
      eventStore.setUnconfirmedCount(response.data.unconfirmed_count);
    })
    .catch((error) => {
      console.error("Error fetching cancelled users:", error);
      $q.notify({
        message: "Failed to load cancelled users",
        color: "negative",
        icon: "error",
      });
    });
};

const bookAgain = () => {
  const customUrl = `event/${event.value.id}/${event.value.title
    .toLowerCase()
    .replace(/[\\!&|?$%@':;.<>/"=+\s]/g, "_")}`;

  $q.dialog({
    title: "Are you sure?",
    message: "You will be redirected to the bookings page",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    window.open(`/${customUrl}`, "_blank");
  });
};

const bookForAttendee = (user) => {
  $q.dialog({
    title: `Do you want to book for: ${user.forename} ${user.surname}?`,
    message: "You will be redirected to the bookings page",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    window.open(`/${user.user_invite_url}`, "_blank");
  });
};

const isRefunded = (status) => {
  if (status === "refunded") {
    return "Yes";
  } else if (status === "part_refunded") {
    return "Part";
  } else {
    return "No";
  }
};

// Initialize component
onMounted(() => {
  pageChanged();
});
</script>
