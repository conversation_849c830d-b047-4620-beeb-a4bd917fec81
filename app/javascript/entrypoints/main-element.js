// Main module entry point
import { createApp } from 'vue';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'font-awesome/css/font-awesome.min.css';
import { createPinia } from 'pinia';

// Import the main component
import MainSummary from '@/main/MainSummary.vue';

// Import the router
import router from '@/main/router';

// Create the Vue application
const app = createApp(MainSummary);

// Create and use Pinia for state management
const pinia = createPinia();
app.use(pinia);

// Use the router
app.use(router);

// Mount the application when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const mainElement = document.getElementById('main-app');
  if (mainElement) {
    app.mount(mainElement);
  }
});

// Export the app instance for potential external use
export default app;