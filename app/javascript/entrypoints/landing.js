import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import { router } from "@/landing/routes/index.js";
import axios from "axios";

import { Quasar } from "quasar";
import "quasar/src/css/index.sass";

// Import icon libraries
import "@quasar/extras/material-icons/material-icons.css";
import "@quasar/extras/fontawesome-v6/fontawesome-v6.css";

// Import necessary components
import MainLayout from "@/layouts/MainLayout.vue";

const token = document
  .querySelector('meta[name="csrf-token"]')
  ?.getAttribute("content");
if (token) {
  axios.defaults.headers.common["X-CSRF-Token"] = token;
  axios.defaults.headers.common["Accept"] = "application/json";
}

import { Notify, Dialog, Loading, QEditor } from "quasar";

// Create app with MainLayout as root component
const app = createApp(MainLayout);

app.use(Quasar, {
  plugins: {
    Notify,
    Dialog,
    Loading,
  },
  config: {
    notify: {
      position: "top-right",
      timeout: 2500,
    },
  },
});

// Use plugins with persistence
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
app.use(pinia);
app.use(router);

// Mount app
app.mount("#landing-app");
