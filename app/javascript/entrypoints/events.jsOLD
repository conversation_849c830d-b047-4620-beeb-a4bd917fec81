import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { router } from '@/events/router/index.js'
// import EventsMain from '@/events/components/EventsMain.vue'
import axios from 'axios'
// import _ from 'lodash'

import EventWizard from '@/events/views/EventWizard.vue'

import { Quasar } from 'quasar'
// import quasarIconSet from 'quasar/icon-set/fontawesome-v6'

// import mdiIconSet from 'quasar/icon-set/mdi-v7.js'

// import '@quasar/extras/fontawesome-v6/fontawesome-v6.css'
import 'quasar/src/css/index.sass'

// Import VueDatePicker styles globally
import '@vuepic/vue-datepicker/dist/main.css'

import VueDatePicker from '@vuepic/vue-datepicker';

const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
if (token) {
    axios.defaults.headers.common['X-CSRF-Token'] = token
    axios.defaults.headers.common['Accept'] = 'application/json'
}

import {
  Notify,
  Dialog,
  Loading,
  QEditor
} from 'quasar'

const app = createApp(EventWizard)

app.component('VueDatePicker', VueDatePicker);

app.use(Quasar, {
  plugins: {
    Notify,
    Dialog,
    Loading
  },
  config: {
    notify: {
      position: 'top-right',
      timeout: 2500
    }
  },
  // iconSet: quasarIconSet
})

// Use plugins
app.use(createPinia())
app.use(router)

// Mount app
app.mount('#eventsnew')