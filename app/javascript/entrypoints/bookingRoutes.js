// Router configuration for bookings
import Start from '../bookings/startpage.vue'
import UserDetails from '../bookings/user_details/user-details.vue'
import Payment from '../bookings/payments/payment.vue'
import Summary from '../bookings/summary/summary.vue'

export const routes = [
  {
    path: '/',
    component: Start,
    name: 'home'
  },
  {
    path: '/user-details',
    name: 'userdetails',
    component: UserDetails
  },
  {
    path: '/payment',
    name: 'payment',
    component: Payment
  },
  {
    path: '/summary',
    name: 'summary',
    component: Summary
  },
  {
    path: '/summaryfree',
    name: 'summaryfree',
    component: Summary
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

export const scrollBehavior = (to, from, savedPosition) => {
  return {
    x: 0,
    y: 0
  }
}
