import { createApp } from 'vue';
import { Quasar, QUploader, QInput, QBtn, QList, QItem, QItemSection, QItemLabel, QTooltip, QSpinner } from 'quasar';
import axios from 'axios';

// Import Quasar language pack
import quasarLang from 'quasar/lang/en-GB';

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css';
import '@quasar/extras/fontawesome-v6/fontawesome-v6.css';

// Import Quasar css
import 'quasar/src/css/index.sass';

// Get CSRF token
let token = document.getElementsByName("csrf-token")[0].getAttribute("content");
axios.defaults.headers.common["X-CSRF-Token"] = token;
axios.defaults.headers.common["Accept"] = "application/json";

window.imageFileName = null;

// Create Vue app for knowledge hub
const khubApp = createApp({
  data() {
    return {
      date: (() => {
        let el = document.getElementById('khub-date-picker');
        if (el) {
          const { initialValue } = el.dataset;
          return initialValue;
        }
        return null;
      })(),
      imageFileName: window.imageFileName || null,
      headers: {
        'X-CSRF-Token': token
      }
    };
  },
  methods: {
    uploadedImage(info) {
      console.log('Upload success:', info);
      if (info.xhr) {
        try {
          const response = JSON.parse(info.xhr.response);
          if (response.filename) {
            this.imageFileName = response.filename;
            window.imageFileName = response.filename;
          }
        } catch (e) {
          console.error('Error parsing upload response:', e);
        }
      }
    },
    deleteImage() {
      this.imageFileName = null;
      window.imageFileName = null;
      // Call backend to delete image if needed
      // You might want to implement this endpoint
    },
    checkFileType(file) {
      const isValidType = /\.(jpg|jpeg|png|gif)$/i.test(file.name);
      const isValidSize = file.size <= 2097152; // 2MB
      
      if (!isValidType) {
        this.$q.notify({
          color: 'negative',
          message: 'Please select a valid image file (JPG, JPEG, PNG, GIF)',
          icon: 'warning'
        });
        return false;
      }
      
      if (!isValidSize) {
        this.$q.notify({
          color: 'negative',
          message: 'File size must be less than 2MB',
          icon: 'warning'
        });
        return false;
      }
      
      return true;
    }
  },
  mounted() {
    // Watch for date changes and update hidden input
    this.$watch('date', (newDate) => {
      const hiddenInput = document.querySelector('input[name="knowledge_hub_article[date]"]');
      if (hiddenInput) {
        hiddenInput.value = newDate;
      }
    });
  }
});

// Configure Quasar
khubApp.use(Quasar, {
  lang: quasarLang,
  components: {
    QUploader,
    QInput,
    QBtn,
    QList,
    QItem,
    QItemSection,
    QItemLabel,
    QTooltip,
    QSpinner
  }
});

// Mount the app
document.addEventListener('DOMContentLoaded', () => {
  const khubElement = document.getElementById('khub-date-app');
  if (khubElement) {
    khubApp.mount(khubElement);
  }
});

export default khubApp;
