import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createRouter, createWebHashHistory } from 'vue-router';
import { Quasar, Dialog, Notify, Loading } from 'quasar';
import axios from 'axios';

// Add debug logging
console.log('🚀 Bookings.js starting to load...');
console.log('📊 Window event booking data:', window.eventBookingData);
console.log('🆔 Event ID:', window.eventID);

// Import Quasar language pack
import quasarLang from 'quasar/lang/en-GB';

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css';
import '@quasar/extras/fontawesome-v6/fontawesome-v6.css';

// Import Quasar css
import 'quasar/src/css/index.sass';

// Import routes configuration
import { routes, scrollBehavior } from './bookingRoutes.js';

import BookingLayout from '../layouts/BookingLayout.vue';

import piniaPluginPersistedstate from "pinia-plugin-persistedstate";

// Create and configure Pinia with persistence
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

// Set up CSRF token for axios requests
let token = document.getElementsByName('csrf-token')[0].getAttribute('content');

axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] = 'no-cache,no-store,must-revalidate,max-age=-1,private';


// Create Vue Router
const router = createRouter({
  history: createWebHashHistory(),
  routes: routes,
  scrollBehavior: scrollBehavior
});


const app = createApp(BookingLayout);

// Use Pinia
app.use(pinia);

// Use Vue Router
app.use(router);

// Configure Quasar
app.use(Quasar, {
  plugins: {
    Dialog,
    Notify,
    Loading
  },
  lang: quasarLang,
  config: {
  }
});

app.mount("#booking-app");