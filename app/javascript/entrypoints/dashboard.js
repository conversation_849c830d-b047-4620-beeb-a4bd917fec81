import { createApp } from 'vue';
import { createRouter, createWebHashHistory } from 'vue-router';
import { Quasar, Dialog, Notify, Loading } from 'quasar';
import axios from 'axios';

// Import Quasar language pack
import quasarLang from 'quasar/lang/en-GB';

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css';
import '@quasar/extras/fontawesome-v6/fontawesome-v6.css';

// Import Quasar css
import 'quasar/src/css/index.sass';

// Import the main dashboard component
import DashboardMain from '@/event-dashboard/components/DashboardMain.vue';

// Import Pinia store
import pinia from '@/event-dashboard/stores';

import router from "@/event-dashboard/routes";

// Set up CSRF token for axios requests
let token = document.getElementsByName('csrf-token')[0].getAttribute('content');
axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] = 'no-cache,no-store,must-revalidate,max-age=-1,private';

// Create the Vue application
const app = createApp(DashboardMain);

// Use Pinia store
app.use(pinia);

app.use(router);

// Use Quasar
app.use(Quasar, {
  plugins: {
    Dialog,
    Notify,
    Loading
  },
  lang: quasarLang,
  config: {
    brand: {
      primary: '#006572',
      secondary: '#b0bec5',
      accent: '#8c9eff',
      dark: '#1a237e',
      positive: '#21BA45',
      negative: '#C10015',
      info: '#31CCEC',
      warning: '#F2C037'
    },
    notify: {
      position: 'top-right',
      timeout: 2500
    }
  }
});

// Use router
app.use(router);

// Mount the application when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const dashboardElement = document.getElementById('dashboard-app');
  if (dashboardElement) {
    app.mount(dashboardElement);
  }
});

// Export the app instance for potential external use
export default app;