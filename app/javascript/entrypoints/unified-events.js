import { createApp } from "vue";
import { Quasar, Dialog, Notify, Loading } from "quasar";
import quasarLang from "quasar/lang/en-US";

// Import icon libraries - these need to be imported before Quasar CSS
import "@quasar/extras/material-icons/material-icons.css";
import "@quasar/extras/fontawesome-v6/fontawesome-v6.css";

// Import fonts
import "@/styles/fonts.css";

// Import Quasar css
import "quasar/src/css/index.sass";

import EventLayout from "@/layouts/eventLayout.vue";

// Import Pinia store
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";

// Create and configure Pinia with persistence
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

// Add a callback to check for event ID in the URL hash when app initializes
const checkForEventIdInUrl = () => {
  const hash = window.location.hash;
  const ticketsRegex = /\/tickets\/(\d+)/;
  const match = hash.match(ticketsRegex);

  if (match && match[1]) {
    const eventId = match[1];
    console.log(
      `Found event ID ${eventId} in URL hash, setting to localStorage`
    );
    localStorage.setItem("hg-current-event-id", eventId);
    localStorage.setItem("hg-force-ticket-reload", "true");
  }
};

// Check for event ID in URL when app initializes
checkForEventIdInUrl();

// Import unified router
import { router } from "@/shared/router";

// Import axios for API requests
import axios from "axios";

// Import debug utilities for development
import "@/events/utils/debug-utils.js";

// Set up CSRF token for axios requests
const token = document
  .getElementsByName("csrf-token")[0]
  ?.getAttribute("content");
if (token) {
  axios.defaults.headers.common["X-CSRF-Token"] = token;
  axios.defaults.headers.common["Accept"] = "application/json";
  axios.defaults.headers.common["Cache-Control"] =
    "no-cache,no-store,must-revalidate,max-age=-1,private";
}

// Create the Vue application
const app = createApp(EventLayout);

// Use Pinia store
app.use(pinia);

// Use router
app.use(router);

// Use Quasar
app.use(Quasar, {
  plugins: {
    Dialog,
    Notify,
    Loading,
  },
  lang: quasarLang,
  config: {
    notify: {
      position: "top-right",
      timeout: 2500,
    },
  },
});

// Mount the app
app.mount("#app");
