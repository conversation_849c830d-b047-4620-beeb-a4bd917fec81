/* Custom application styles */
body {
  margin: 0;
  padding: 0;
  font-family: '<PERSON><PERSON>', sans-serif;
  background-color: #f5f5f5;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

.app-container {
  min-height: 100vh;
}

.main-header {
  background-color: #006572;
  color: white;
}

.event-selector {
  border-bottom: 1px solid #ddd;
  background-color: #fafafa;
}


/* Content positioning to avoid header overlay */
.q-page-container {
  padding-top: 60px !important; /* Important to override any inline styles */
  position: relative;
  z-index: 1;
}

.stepper-fixed {
  position: relative;
  width: calc(100% - 70px); /* Account for mini drawer width by default */
  margin-left: 70px;
}

/* For desktop - adjust based on drawer state */
@media (min-width: 992px) {
  .q-drawer--standard + .q-page-container .stepper-fixed {
    width: calc(100% - 280px);
    margin-left: 280px;
  }
}

/* For mobile - full width stepper */
@media (max-width: 991px) {
  .stepper-fixed {
    width: 100%;
    margin-left: 0;
  }
}

/* Drawer styles */
.full-height {
  height: 100vh !important;
}

.q-drawer {
  transition: width 0.3s ease, min-width 0.3s ease;
}

.drawer-item {
  transition: background-color 0.3s ease;
}

.drawer-item:hover {
  background-color: rgba(0, 101, 114, 0.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .event-selector .row {
    flex-direction: column;
  }
  
  .event-selector .col-auto {
    margin: 5px 0;
    width: 100%;
  }
  
  .event-selector .q-btn {
    width: 100%;
  }
  
}

/* Transition animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
