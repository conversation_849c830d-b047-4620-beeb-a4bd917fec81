// Main module entry point
import { createApp } from "vue";
import { <PERSON>uasar, Dialog, Notify, Loading } from "quasar";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import axios from "axios";

// Import Quasar language pack
import quasarLang from "quasar/lang/en-GB";

// Import icon libraries
import "@quasar/extras/material-icons/material-icons.css";
import "@quasar/extras/fontawesome-v6/fontawesome-v6.css";

// Import fonts
import "@/styles/fonts.css";

// Import Quasar css
import "quasar/src/css/index.sass";

// Import VueDatePicker styles globally
import "@vuepic/vue-datepicker/dist/main.css";

import VueDatePicker from "@vuepic/vue-datepicker";

import dashboardLayout from "@/layouts/dashboardLayout.vue";

// Import the router from main folder
import router from "@/main/router";

const token = document
  .querySelector('meta[name="csrf-token"]')
  ?.getAttribute("content");
if (token) {
  axios.defaults.headers.common["X-CSRF-Token"] = token;
  axios.defaults.headers.common["Accept"] = "application/json";
}

// Create the Vue application
//const app = createApp(Navigation);
const app = createApp(dashboardLayout);

app.component("VueDatePicker", VueDatePicker);

// Create and use Pinia for state management with persistence
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
app.use(pinia);

// Use the router
app.use(router);

// Configure Quasar
app.use(Quasar, {
  plugins: {
    Dialog,
    Notify,
    Loading,
  },
  lang: quasarLang,
  config: {
    notify: {
      position: "top-right",
      timeout: 2500,
    },
  },
});

// Mount the application when the DOM is ready
document.addEventListener("DOMContentLoaded", () => {
  const mainElement = document.getElementById("main-app");
  if (mainElement) {
    app.mount(mainElement);
  }
});

// Export the app instance for potential external use
export default app;
