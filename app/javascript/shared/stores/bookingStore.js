import { defineStore } from 'pinia'

export const useBookingStore = defineStore('booking', {
  state: () => ({
    event: {},
    eventBooking: null,
    selectedTickets: [], // Initialize as empty array instead of null
    bookerRegResponses: [],
    bookerDetails: null,
    fees: null,
    readonly: false,
    expired: false,
    editByOrg: false,
    bookingToken: null,
    assignedChildTickets: [],
    discCodes: [],
    refPath: '',
    extWebsite: null,
    payStart: null,
    chargeable: true,
    timeRemaining: 30,
    paymentMethod: null,
    paymentInfo: {},
    inProgress: false,
    timerDisplay: null
  }),

  getters: {
    getSelectedTickets: (state) => state.selectedTickets,
    getEvent: (state) => state.event,
    getEventId: (state) => state.event.id,
    getEventBooking: (state) => state.eventBooking,
    getEventBookingId: (state) => state.eventBooking?.id,
    getBookerDetails: (state) => state.bookerDetails,
    getFees: (state) => state.fees,
    getReadOnly: (state) => state.readonly,
    getExpired: (state) => state.expired,
    getBookingToken: (state) => state.bookingToken,
    getEditByOrg: (state) => state.editByOrg,
    getDiscCodes: (state) => state.discCodes,
    getRefPath: (state) => state.refPath,
    getExtWebsite: (state) => state.extWebsite,
    getPayStart: (state) => state.payStart,
    getChargeable: (state) => state.chargeable,
    getTimeRemaining: (state) => state.timeRemaining,
    getTimerDisplay: (state) => state.timerDisplay,
    getPaymentMethod: (state) => state.paymentMethod,
    getPaymentInfo: (state) => state.paymentInfo,
    getInProgress: (state) => state.inProgress,

    getChildTicketAssignments: (state) => (ticket_id) => {
      return state.assignedChildTickets.find(
        assignedTicket => assignedTicket.package_id === ticket_id
      )
    },

    getChildTicketTotalBooked: (state) => (ticket_id) => {
      const ticketsAssigned = state.assignedChildTickets.filter(
        assignedTicket => assignedTicket.package_id === ticket_id
      )
      let total = 0

      if (ticketsAssigned) {
        ticketsAssigned.forEach(
          assigned => (total += assigned.quantity_assigned)
        )
      }

      return total
    },

    getTotalTicketsRemaingForOthers: (state) => (ticket_id, attendee_idx) => {
      const ticketsAssigned = state.assignedChildTickets.filter(
        assignedTicket => {
          return (
            assignedTicket.package_id === ticket_id &&
            assignedTicket.attendee_idx != attendee_idx
          )
        }
      )

      let total = 0

      if (ticketsAssigned) {
        ticketsAssigned.forEach(
          assigned => (total += assigned.quantity_assigned)
        )
      }

      return total
    },

    // Check if there are valid ticket selections
    hasValidTicketSelection: (state) => {
      return Array.isArray(state.selectedTickets) &&
             state.selectedTickets.length > 0 &&
             state.selectedTickets.some(ticket =>
               ticket &&
               typeof ticket.quantity_tickets === 'number' &&
               ticket.quantity_tickets > 0
             )
    }
  },

  actions: {
    resetBooking() {
      this.event = {}
      this.eventBooking = null
      this.selectedTickets = [] // Reset to empty array instead of null
      this.bookerRegResponses = []
      this.bookerDetails = null
      this.fees = null
      this.readonly = false
      this.expired = false
      this.editByOrg = false
      this.bookingToken = null
      this.assignedChildTickets = []
      this.discCodes = []
      this.refPath = ''
      this.extWebsite = null
      this.chargeable = true
      this.paymentMethod = null
      this.paymentInfo = {}
      this.inProgress = false
      this.timeRemaining = 600
      this.timerDisplay = null
    },

    setSelectedTickets(tickets) {
      console.log('🏪 BookingStore: Setting selected tickets:', tickets);
      this.selectedTickets = tickets;
      console.log('🏪 BookingStore: hasValidTicketSelection after update:', this.hasValidTicketSelection);
    },

    setEvent(event) {
      this.event = event
    },

    setEventBooking(eventBooking) {
      this.eventBooking = eventBooking
    },

    setEventBookingPaymentType(paymentType) {
      if (this.eventBooking) {
        this.eventBooking.payment_type = paymentType
      }
    },

    setEventBookingDiscount(discount) {
      if (this.eventBooking) {
        this.eventBooking.discount_percentage = discount.discount_percentage
        this.eventBooking.discount_code_id = discount.discount_code_id
      }
    },

    setPackageBookingDiscount(discount) {
      if (this.eventBooking?.package_bookings) {
        const packageBooking = this.eventBooking.package_bookings.find(pb => pb.id == discount.id)
        if (packageBooking) {
          packageBooking.discount_amount = discount.discount_amount
          packageBooking.discount_code_id = discount.discount_code_id
          packageBooking.discount_type = discount.discount_type
        }
      }
    },

    setBookerRegResponses(responses) {
      this.bookerRegResponses = responses
    },

    setBookerDetails(bookerDetails) {
      bookerDetails.user_type = 'booker'
      this.bookerDetails = bookerDetails
    },

    setFees(fees) {
      this.fees = fees
    },

    setReadOnly(readonly) {
      this.readonly = readonly
    },

    setExpired(expired) {
      this.expired = expired
    },

    setEditByOrg(editor) {
      this.editByOrg = editor
    },

    setBookingToken(token) {
      this.bookingToken = token
    },

    setDiscCode(code) {
      this.discCodes.push(code)
    },

    setRefPath(path) {
      this.refPath = path
    },

    setExtWebsite(url) {
      this.extWebsite = url
    },

    setChargeable(chargeable) {
      this.chargeable = chargeable
    },

    setAssignedChildTicket(assignedTicket) {
      const existingAssignment = this.assignedChildTickets.find(
        assticket =>
          assticket.package_id == assignedTicket.package_id &&
          assticket.attendee_idx == assignedTicket.attendee_idx
      )

      if (existingAssignment) {
        existingAssignment.quantity_assigned = assignedTicket.quantity_assigned
      } else {
        this.assignedChildTickets.push(assignedTicket)
      }
    },

    removeAssignedChildTicket(assignedTicket) {
      const idx = this.assignedChildTickets.indexOf(assignedTicket)
      if (idx !== -1) {
        this.assignedChildTickets.splice(idx, 1)
      }
    },

    setPayStart(start) {
      this.payStart = start
    },

    setTimeRemaining(totalNoSecs) {
      this.timeRemaining = totalNoSecs
    },

    setTimerDisplay(formattedTime) {
      this.timerDisplay = formattedTime
    },

    setPaymentMethod(payMethod) {
      this.paymentMethod = payMethod
    },

    setPaymentInfo(paymentInfo) {
      this.paymentInfo = paymentInfo
    },

    setInProgress(prog_state) {
      this.inProgress = prog_state
    },

    // Submit booking action
    async submitBooking() {
      try {
        this.setInProgress(true)

        console.log('📋 Preparing booking submission...')
        console.log('📋 Event:', this.event)
        console.log('📋 Selected tickets:', this.selectedTickets)
        console.log('📋 Booker details:', this.bookerDetails)

        // Format data according to Rails controller expectations
        const bookingData = this.formatBookingData()

        console.log('📋 Formatted booking data:', bookingData)

        // Make API call to submit booking
        const response = await fetch('/event_bookings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
          },
          body: JSON.stringify(bookingData)
        })

        console.log('📋 Response status:', response.status)

        if (!response.ok) {
          const errorData = await response.json()
          console.error('📋 Booking submission failed:', errorData)
          throw new Error(errorData.errors ? errorData.errors.join(', ') : `Booking submission failed: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('📋 Booking submitted successfully:', result)

        return result
      } catch (error) {
        console.error('📋 Booking submission error:', error)
        throw error
      } finally {
        this.setInProgress(false)
      }
    },

    // Format booking data for Rails controller
    formatBookingData() {
      console.log('📋 Formatting booking data for Rails controller...')

      // Format registered user data (booker details)
      const registeredUserData = {
        event_id: this.event.id,
        forename: this.bookerDetails?.forename || '',
        surname: this.bookerDetails?.surname || '',
        email: this.bookerDetails?.email || '',
        title: this.bookerDetails?.title || '',
        company: this.bookerDetails?.company || '',
        phone: this.bookerDetails?.phone || '',
        // Add other fields as needed
        registered_user_responses_attributes: this.formatRegistrationResponses()
      }

      // Format package bookings (selected tickets)
      const packageBookingsData = this.selectedTickets.map(ticket => ({
        package_id: ticket.id,
        quantity_tickets: ticket.quantity_tickets || 0,
        // Add other package booking fields as needed
        registered_users_attributes: [] // For attendee details if needed
      }))

      // Main booking data structure expected by Rails controller
      const formattedData = {
        event_booking: {
          event_id: this.event.id,
          payment_type: null, // Will be set later for payment
          free_booking: this.isFreeBooking(),
          booking_token: this.bookingToken,
          registered_user_attributes: registeredUserData,
          package_bookings_attributes: packageBookingsData
        }
      }

      console.log('📋 Formatted data structure:', formattedData)
      return formattedData
    },

    // Format registration responses for custom fields
    formatRegistrationResponses() {
      // TODO: Implement custom field responses formatting
      // This would handle any custom registration fields from the event
      return []
    },

    // Check if this is a free booking
    isFreeBooking() {
      if (!this.selectedTickets || this.selectedTickets.length === 0) {
        return true
      }

      // Check if all selected tickets are free (cost_b = 0)
      return this.selectedTickets.every(ticket =>
        !ticket.cost_b || ticket.cost_b === 0
      )
    },

    // Update booking action
    async updateBooking() {
      if (!this.getEventBookingId) {
        throw new Error('No booking ID found to update.')
      }

      try {
        this.setInProgress(true)

        console.log('📋 Preparing booking update...')
        console.log('📋 Event booking ID:', this.getEventBookingId)

        // Format data according to Rails controller expectations
        const bookingData = this.formatBookingData()

        console.log('📋 Formatted update data:', bookingData)

        // Make API call to update booking using the correct Rails endpoint
        const response = await fetch(`/event_bookings/${this.getEventBookingId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
          },
          body: JSON.stringify(bookingData)
        })

        console.log('📋 Update response status:', response.status)

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          console.error('📋 Booking update failed:', errorData)
          throw new Error(errorData.errors ? errorData.errors.join(', ') : `Booking update failed: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('📋 Booking updated successfully:', result)

        // Optionally update local state with the result
        this.setEventBooking(result.event_booking)

        return result
      } catch (error) {
        console.error('📋 Booking update error:', error)
        throw error
      } finally {
        this.setInProgress(false)
      }
    }
  },

  persist: true
})
