import { defineStore } from 'pinia'
import axios from 'axios'
import { readonly } from 'vue'

export const useEventStore = defineStore('event', {
  state: () => ({
    // Dashboard-related state
    unconfirmedCount: 0,
    attendeesCount: 0,
    declinedCount: 0,
    cancelledCount: 0,
    failedPaymentCount: 0,
    eventBooking: null,
    selectedTickets: null,
    bookerRegResponses: [],
    bookerDetails: null,
    fees: null,
    bookingToken: null,
    importContactJobID: null,
    removeContactJobID: null,
    
    // Event-related state
    event: null,
    eventTypes: [],
    
    // Shared state
    isLoading: false,
    error: null,
    readOnly: false,
    chargeable: false,
    
    // Application state
    advancedUser: false,
    currentRoute: null,
    
    // Stepper navigation state
    visitedPages: []
  }),
  
  getters: {
    // Dashboard getters
    getUnconfirmedCount: (state) => state.unconfirmedCount,
    getAttendeesCount: (state) => state.attendeesCount,
    getDeclinedCount: (state) => state.declinedCount,
    getCancelledCount: (state) => state.cancelledCount,
    getFailedPaymentCount: (state) => state.failedPaymentCount,
    getEventBooking: (state) => state.eventBooking,
    getBookerDetails: (state) => state.bookerDetails,
    getFees: (state) => state.fees,
    getBookingToken: (state) => state.bookingToken,
    getImportContactJobID: (state) => state.importContactJobID,
    getRemoveContactJobID: (state) => state.removeContactJobID,
    
    // Event getters
    getEvent: (state) => state.event,
    getEventId: (state) => state.event?.id,
    getEventTypes: (state) => state.eventTypes,
    getReadOnly: (state) => state.readOnly,
    getChargeable: (state) => state.chargeable,
    getVisitedPages: (state) => state.visitedPages || [],
    
    // Auto-loading getter that ensures event is loaded
    getEventWithAutoLoad: (state) => {
      return (eventId) => {
        // If no event ID provided, return current event or null
        if (!eventId) {
          return state.event
        }
        
        // If event is already loaded and matches requested ID, return it
        if (state.event && state.event.id === Number(eventId)) {
          return state.event
        }
        
        // Event not loaded or different ID - trigger loading
        // Note: This should be used with a computed property that watches for changes
        return null
      }
    },
    
    // Safe getter that always returns an object with expected properties
    getSafeEvent: (state) => {
      if (!state.event) {
        return {
          id: null,
          title: '',
          tickets: [],
          ticket_groups: [],
          event_address: {
            id: null,
            address1: '',
            address2: '',
            city: '',
            county: '',
            postcode: '',
            country_code: ''
          }
        }
      }
      
      // Ensure event has basic required properties
      const safeEvent = { ...state.event }
      
      // Ensure tickets array exists
      if (!Array.isArray(safeEvent.tickets)) {
        safeEvent.tickets = []
      }
      
      // Ensure ticket_groups array exists
      if (!Array.isArray(safeEvent.ticket_groups)) {
        safeEvent.ticket_groups = []
      }
      
      // Ensure event_address is an object even if null
      if (!safeEvent.event_address) {
        safeEvent.event_address = {
          id: null,
          address1: '',
          address2: '',
          city: '',
          county: '',
          postcode: '',
          country_code: ''
        }
      }
      
      return safeEvent
    },
    
    // Application getters
    isAdvancedUser: (state) => state.advancedUser
  },
  
  actions: {
    // Dashboard actions
    setUnconfirmedCount(count) {
      this.unconfirmedCount = count
    },
    setAttendeesCount(count) {
      this.attendeesCount = count
    },
    setDeclinedCount(count) {
      this.declinedCount = count
    },
    setCancelledCount(count) {
      this.cancelledCount = count
    },
    setFailedPaymentCount(count) {
      this.failedPaymentCount = count
    },
    setEventBooking(eventBooking) {
      this.eventBooking = eventBooking
    },
    setBookerRegResponses(responses) {
      this.bookerRegResponses = responses
    },
    setBookerDetails(bookerDetails) {
      if (bookerDetails) {
        bookerDetails.user_type = 'booker'
      }
      this.bookerDetails = bookerDetails
    },
    setFees(fees) {
      this.fees = fees
    },
    setBookingToken(token) {
      this.bookingToken = token
    },
    setImportContactJobID(job_id) {
      this.importContactJobID = job_id
    },
    setRemoveContactJobID(job_id) {
      this.removeContactJobID = job_id
    },
    setSelectedTickets(tickets) {
      this.selectedTickets = tickets
    },
    
    // Shared state actions
    setReadOnly(val) {
      this.readOnly = val
    },
    setChargeable(val) {
      this.chargeable = val
    },
    
    // Event actions
    setEvent(event) {
      console.log('Setting event in store:', event)
      
      // Validation to ensure we have a proper event object
      if (!event || typeof event !== 'object') {
        console.error('Invalid event object passed to store:', event)
        return
      }
      
      // Ensure event has an ID
      if (!event.id) {
        console.warn('Event without ID was passed to store:', event)
      }
      
      // Store the event with proper reactivity - avoid deep copy which breaks reactivity
      // Only copy if absolutely necessary, otherwise maintain references for reactivity
      if (typeof event === 'object' && event !== null) {
        this.event = { ...event }
        
        // Ensure arrays are properly reactive
        if (Array.isArray(event.registration_fields)) {
          this.event.registration_fields = [...event.registration_fields]
        }
        if (Array.isArray(event.tickets)) {
          this.event.tickets = [...event.tickets]  
        }
        if (Array.isArray(event.ticket_groups)) {
          this.event.ticket_groups = [...event.ticket_groups]
        }
      } else {
        this.event = event
      }
      
      console.log('Event after store update:', this.event)
    },
    setEventTypes(types) {
      this.eventTypes = types
    },
    
    // Load event from API
    async loadEvent(eventId) {
      if (!eventId) {
        console.error('No event ID provided to loadEvent')
        return false
      }
      
      // Prevent loading the same event multiple times in quick succession
      const currentEventId = this.getEventId
      if (currentEventId === Number(eventId) && !this.isLoading) {
        console.log(`Event ${eventId} is already loaded, skipping duplicate load`)
        return true
      }
      
      this.isLoading = true
      this.error = null
      
      try {
        // Use cache-busting to ensure fresh data
        const cacheBuster = new Date().getTime()
        const response = await axios.get(`/event_details/${eventId}?_=${cacheBuster}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
        
        if (response.data.event) {
          // Use the setter to properly update state
          this.setEvent(response.data.event)
          
          if (response.data.event.event_types) {
            this.setEventTypes(response.data.event.event_types)
          }
          
          console.log(`Loaded event with ID: ${eventId}`, response.data.event)
          
          // Store the current event ID in localStorage for persistence
          if (localStorage.getItem('hg-current-event-id') !== eventId.toString()) {
            localStorage.setItem('hg-current-event-id', eventId.toString())
          }
          
          // Record when we last loaded the data
          localStorage.setItem('hg-event-last-loaded', Date.now().toString())
          
          // Automatically load tickets for immediate menu availability
          // This ensures hasEventAndTickets computed property works immediately
          console.log(`Auto-loading tickets for event ${eventId} to enable menu items`)
          await this.loadTickets(eventId)
          
          return true
        } else {
          this.error = 'Event data not found in response'
          return false
        }
      } catch (error) {
        console.error(`Error loading event with ID: ${eventId}`, error)
        this.error = error.response?.data?.error || 'Failed to load event'
        return false
      } finally {
        this.isLoading = false
      }
    },
    
    // Ensure event is loaded - automatically loads if not present
    async ensureEventLoaded(eventId) {
      if (!eventId) {
        console.error('No event ID provided to ensureEventLoaded')
        return false
      }
      
      const numericEventId = Number(eventId)
      
      // Check if event is already loaded and matches
      if (this.event && this.event.id === numericEventId) {
        // Event is loaded but check if tickets are also loaded for menu availability
        const hasTicketsWithIds = this.event.tickets && this.event.tickets.some(ticket => ticket && ticket.id)
        const hasGroupTicketsWithIds = this.event.ticket_groups && 
          this.event.ticket_groups.some(group => group.packages && group.packages.some(ticket => ticket && ticket.id))
        
        if (!hasTicketsWithIds && !hasGroupTicketsWithIds) {
          console.log(`Event ${eventId} loaded but tickets missing, loading tickets for menu availability`)
          await this.loadTickets(eventId)
        } else {
          console.log(`Event ${eventId} already loaded with tickets, no need to reload`)
        }
        return true
      }
      
      // Event not loaded or different - load it (which will also load tickets)
      console.log(`Auto-loading event ${eventId} as it's not currently loaded`)
      return await this.loadEvent(eventId)
    },
    
    // Direct action to load tickets only
    async loadTickets(eventId) {
      if (!eventId) {
        console.error('No event ID provided to loadTickets')
        return false
      }
      
      this.isLoading = true
      this.error = null
      
      try {
        console.log(`Loading tickets for event ID: ${eventId} from store action`)
        
        // Add cache-busting to prevent browser/CDN caching
        const cacheBuster = new Date().getTime()
        
        // Use the namespaced route with cache-busting headers
        const response = await axios.get(`/event_details/${eventId}/tickets?_=${cacheBuster}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
        
        if (response.data.tickets) {
          // If the event doesn't exist in the store, load it first
          if (!this.event || this.event.id != eventId) {
            await this.loadEvent(eventId)
          }
          
          // Ensure we have an event object
          if (this.event) {
            // Update tickets in the event
            this.event.tickets = response.data.tickets
            
            // Update ticket groups if available
            if (response.data.ticket_groups) {
              this.event.ticket_groups = response.data.ticket_groups
            } else {
              this.event.ticket_groups = []
            }
            
            // Update last loaded timestamp
            localStorage.setItem('hg-tickets-last-loaded', Date.now().toString())
            console.log(`Loaded ${this.event.tickets.length} tickets for event ID: ${eventId}`)
            return true
          } else {
            this.error = 'No event object available to store tickets'
            return false
          }
        } else {
          this.error = 'Tickets data not found in response'
          return false
        }
      } catch (error) {
        console.error(`Error loading tickets for event ID: ${eventId}`, error)
        this.error = error.response?.data?.error || 'Failed to load tickets'
        return false
      } finally {
        this.isLoading = false
      }
    },
    
    // Reset event state - THE ONLY centralized method to clear all event data
    // All components should use this method instead of directly manipulating localStorage/sessionStorage
    clearEvent() {
      console.log('Clearing event data in eventStore - centralized method')
      
      // Reset event to null
      this.event = null
      
      // Reset associated event data
      this.eventTypes = []
      this.visitedPages = []
      
      // Reset event-related flags
      this.readOnly = false
      this.chargeable = false
      
      // Reset dashboard counts related to the event
      this.unconfirmedCount = 0
      this.attendeesCount = 0
      this.declinedCount = 0
      this.cancelledCount = 0
      this.failedPaymentCount = 0
      
      // Reset tickets data
      this.tickets = []
      this.ticketsLoaded = false
      this.selectedTickets = []
      
      // Reset form fields that might have persisted
      this.bookerDetails = null
      this.eventBooking = null
      this.bookerRegResponses = []
      this.fees = null
      this.bookingToken = null
      
      // Clear all localStorage items related to events (centralized)
      localStorage.removeItem('hg-event-store')
      localStorage.removeItem('hg-current-event-id')
      localStorage.removeItem('hg-event-last-loaded')
      localStorage.removeItem('hg-tickets-last-loaded')
      localStorage.removeItem('hg-force-ticket-reload')
      
      // Also clear from sessionStorage
      sessionStorage.removeItem('hg-current-event-id-backup')
      
      console.log('Event store has been completely cleared for new event creation')
    },
    
    // Refresh state when changing events
    refreshState(event_id) {
      if (this.event?.id !== event_id) {
        this.importContactJobID = null
        this.removeContactJobID = null
      }
      this.eventBooking = null
      this.selectedTickets = null
      this.bookerRegResponses = []
      this.fees = null
      this.bookingToken = null
    },
    
    // Application actions
    setAdvancedUser(value) {
      this.advancedUser = value
    },
    setCurrentRoute(route) {
      this.currentRoute = route
    },
    
    // Stepper navigation actions
    addVisitedPage(pageName) {
      if (!this.visitedPages.includes(pageName)) {
        this.visitedPages.push(pageName)
      }
    }
  },
  
  // Configure persistence with specific options for better control
  persist: {
    key: 'hg-event-store',
    storage: localStorage,
    paths: ['event', 'eventTypes', 'advancedUser', 'visitedPages', 'chargeable', 'readOnly'],
    beforeRestore: (context) => {
      console.log('Restoring event store from localStorage');
      
      // Check if we're on the create-event route - if so, prevent restoration of event data
      const pathname = window.location.pathname;
      const hash = window.location.hash;
      
      // Enhanced detection for create-event related routes
      const isCreateEventRoute = 
        pathname.includes('/events/new') || 
        pathname.includes('/create-event') || 
        hash.includes('/create-event') ||
        hash.includes('#/create') ||
        window.location.href.includes('create-event');
      
      if (isCreateEventRoute) {
        console.log('On create-event route, preventing event restoration');
        
        // Use the centralized clearEvent method via the store instance
        if (context.store.clearEvent) {
          // If the store is already initialized, use its method
          context.store.clearEvent();
        } else {
          // Otherwise, just return a clean state
          return {
            ...context.store.$state,
            event: null,
            eventTypes: [],
            visitedPages: [],
            unconfirmedCount: 0,
            attendeesCount: 0,
            declinedCount: 0,
            cancelledCount: 0,
            failedPaymentCount: 0,
            eventBooking: null,
            selectedTickets: null,
            bookerRegResponses: [],
            bookerDetails: null,
            fees: null,
            bookingToken: null
          };
        }
      }
    },
    afterRestore: (context) => {
      console.log('Event store restored successfully');
      // Set a flag that we've restored from localStorage
      context.store.restoredFromStorage = true;
      
      // Validate restored event data to ensure we have tickets properly loaded
      if (context.store.event) {
        console.log(`Restored event #${context.store.event.id} with ${context.store.event.tickets?.length || 0} tickets`);
        
        // Ensure we have proper ticket arrays
        if (!Array.isArray(context.store.event.tickets)) {
          context.store.event.tickets = [];
          console.warn('Tickets array was missing, initialized empty array');
        } else if (context.store.event.tickets.length === 0) {
          console.warn('Restored event has empty tickets array - will auto-load tickets on next access');
        } else if (!context.store.event.tickets.some(t => t && t.id)) {
          console.warn('Restored tickets appear to be invalid (no IDs found) - will auto-load tickets on next access');
          // Mark for reload by setting a flag
          localStorage.setItem('hg-force-ticket-reload', 'true');
        } else {
          console.log('Restored event has valid tickets with IDs - menu items should be available');
        }
        
        if (!Array.isArray(context.store.event.ticket_groups)) {
          context.store.event.ticket_groups = [];
          console.warn('Ticket groups array was missing, initialized empty array');
        }
        
        // Set the event ID in localStorage for redundancy
        if (context.store.event.id) {
          localStorage.setItem('hg-current-event-id', context.store.event.id);
          localStorage.setItem('hg-event-last-loaded', Date.now().toString());
          // Also backup to sessionStorage as another redundancy layer
          sessionStorage.setItem('hg-current-event-id-backup', context.store.event.id);
        }
      }
    }
  }
})
