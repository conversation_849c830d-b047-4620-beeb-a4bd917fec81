<template>
  <q-header elevated>
    <q-toolbar style="height: 70px">
      <q-btn flat round dense icon="menu" @click="toggleDrawer" />
      <img
        src="../../assets/IndigoSparkCircularLogoTransparentInverse.png"
        alt="Indigo Spark Logo"
        height="70px"
        width="70px"
        fetchpriority="high"
      />
      <q-separator vertical inset class="q-mr-md" />
      <q-toolbar-title class="q-mr-md text-h6">{{
        eventTitle || "Event Manager"
      }}</q-toolbar-title>
      <NotifyLink mini />
      <q-btn flat round dense icon="logout" @click="logout" class="q-ml-sm" />
    </q-toolbar>
  </q-header>
</template>
<script setup>
import { computed } from "vue";
import { useQuasar } from "quasar";
import NotifyLink from "@/common/notifications-link.vue";
import { useAuthStore } from "@/stores/auth";
import { useMainStore } from "@/stores/main";
import { useEventStore } from "@/stores/event";

const $q = useQuasar();
const authStore = useAuthStore();
const mainStore = useMainStore();
const eventStore = useEventStore();

const eventTitle = computed(() => {
  return eventStore.event && eventStore.event.title
    ? eventStore.event.title
    : null;
});

const toggleDrawer = () => {
  mainStore.setLeftDrawerOpen(mainStore.leftDrawerOpen);
};

const logout = () => {
  // todo: add confirmation dialog before logout
  $q.dialog({
    title: "Confirm Logout",
    message: "Are you sure you want to log out?",
    cancel: true,
    persistent: true,
    ok: {
      push: true,
      label: "Logout",
      color: "negative",
    },
  }).onOk(() => {
    performLogout();
  });
};

const performLogout = async () => {
  try {
    await authStore.logout();
    window.location.href = "/#/login";
  } catch (err) {
    $q.notify({
      message: "There was an error logging out",
      color: "negative",
      icon: "error",
    });
  }
};
</script>
