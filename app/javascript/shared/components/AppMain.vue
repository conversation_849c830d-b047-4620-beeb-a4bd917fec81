<template>
  <q-layout view="hhh lpr fff">
    <!-- Left drawer for navigation - with mini-mode support -->
    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      :width="280"
      :mini="miniState"
      :mini-width="70"
      :breakpoint="991"
      class="bg-white shadow-1"
      @mouseover="hoverDrawer(true)"
      @mouseleave="hoverDrawer(false)"
    >
      <q-scroll-area class="fit">
        <q-list padding class="text-grey-8">
          <div class="drawer-header q-pa-md" :class="{ 'q-px-sm': miniState }">
            <div v-if="!miniState">
              <!-- Show event logo if available, otherwise show default app logo -->
              <q-img
                v-if="hasActiveEvent && eventLogo"
                :src="eventLogo"
                spinner-color="primary"
                width="160px"
                class="q-mb-md"
              />
              <q-img
                v-else-if="!hasActiveEvent"
                src="/images/logo.png"
                spinner-color="primary"
                width="160px"
                class="q-mb-md"
              />
              <q-btn
                color="primary"
                class="full-width q-mt-sm"
                label="Create New Event"
                icon="add"
                @click="navigateTo('create-event')"
              />
            </div>
            <div v-else class="text-center">
              <q-btn
                round
                color="primary"
                icon="add"
                size="md"
                @click="navigateTo('create-event')"
              />
            </div>
          </div>

          <q-separator class="q-my-md" />

          <q-expansion-item
            expand-separator
            icon="dashboard"
            :label="miniState ? null : 'Event Management'"
            :header-class="miniState ? 'justify-center' : ''"
            :default-opened="!miniState"
          >
            <q-item
              clickable
              v-ripple
              @click="navigateTo('dash-main')"
              class="drawer-item"
              :active="currentTab === 'dashboard'"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section v-if="!miniState">Dashboard</q-item-section>
            </q-item>

            <!-- Event related navigation items -->
            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateToEvent('edit-event')"
              class="drawer-item"
              :active="currentTab === 'event-details'"
            >
              <q-item-section avatar>
                <q-icon name="event" />
              </q-item-section>
              <q-item-section v-if="!miniState">Event Details</q-item-section>
            </q-item>

            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateToEvent('ticket-creation')"
              class="drawer-item"
              :active="currentTab === 'tickets'"
            >
              <q-item-section avatar>
                <q-icon name="confirmation_number" />
              </q-item-section>
              <q-item-section v-if="!miniState">Tickets</q-item-section>
            </q-item>

            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateToEvent('reg-questions')"
              class="drawer-item"
              :active="currentTab === 'reg-questions'"
            >
              <q-item-section avatar>
                <q-icon name="list_alt" />
              </q-item-section>
              <q-item-section v-if="!miniState"
                >Registration Questions</q-item-section
              >
            </q-item>

            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateToEvent('customise-emails')"
              class="drawer-item"
              :active="currentTab === 'customise-emails'"
            >
              <q-item-section avatar>
                <q-icon name="email" />
              </q-item-section>
              <q-item-section v-if="!miniState"
                >Customise Emails</q-item-section
              >
            </q-item>
          </q-expansion-item>

          <q-expansion-item
            expand-separator
            icon="people"
            :label="miniState ? null : 'Attendees'"
            :header-class="miniState ? 'justify-center' : ''"
          >
            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateTo('attendees')"
              class="drawer-item"
              :active="currentTab === 'attendees'"
            >
              <q-item-section avatar>
                <q-icon name="people" />
              </q-item-section>
              <q-item-section v-if="!miniState"
                >Confirmed Attendees</q-item-section
              >
            </q-item>

            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateTo('attendees-unconfirmed')"
              class="drawer-item"
              :active="currentTab === 'attendees-unconfirmed'"
            >
              <q-item-section avatar>
                <q-icon name="people_outline" />
              </q-item-section>
              <q-item-section v-if="!miniState"
                >Unconfirmed Attendees</q-item-section
              >
            </q-item>

            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateTo('attendees-cancelled')"
              class="drawer-item"
              :active="currentTab === 'attendees-cancelled'"
            >
              <q-item-section avatar>
                <q-icon name="cancel" />
              </q-item-section>
              <q-item-section v-if="!miniState"
                >Cancelled Bookings</q-item-section
              >
            </q-item>
          </q-expansion-item>

          <q-expansion-item
            expand-separator
            icon="description"
            :label="miniState ? null : 'Reports'"
            :header-class="miniState ? 'justify-center' : ''"
          >
            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateTo('payments', { status: 'all' })"
              class="drawer-item"
              :active="currentTab === 'payments'"
            >
              <q-item-section avatar>
                <q-icon name="payments" />
              </q-item-section>
              <q-item-section v-if="!miniState">Payments Report</q-item-section>
            </q-item>

            <q-item
              clickable
              v-ripple
              :disable="!hasActiveEvent"
              @click="navigateTo('bookings-unpaid')"
              class="drawer-item"
              :active="currentTab === 'bookings-unpaid'"
            >
              <q-item-section avatar>
                <q-icon name="receipt_long" />
              </q-item-section>
              <q-item-section v-if="!miniState">Unpaid Bookings</q-item-section>
            </q-item>
          </q-expansion-item>

          <q-separator class="q-my-md" />

          <q-item-label header v-if="!miniState">Account</q-item-label>

          <q-item clickable v-ripple @click="logout" class="drawer-item">
            <q-item-section avatar>
              <q-icon name="logout" />
            </q-item-section>
            <q-item-section v-if="!miniState">Logout</q-item-section>
          </q-item>

          <!-- Mini-mode toggle button at the bottom -->
          <q-item
            clickable
            v-ripple
            @click="toggleMiniState"
            class="drawer-item q-mt-lg"
          >
            <q-item-section avatar>
              <q-icon :name="miniState ? 'chevron_right' : 'chevron_left'" />
            </q-item-section>
            <q-item-section v-if="!miniState">Collapse Menu</q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- App Header -->
    <q-header elevated class="bg-primary text-white" style="z-index: 1000">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="leftDrawerOpen = !leftDrawerOpen"
        />

        <q-toolbar-title> HG Events Manager </q-toolbar-title>

        <q-btn-dropdown flat label="Actions">
          <q-list>
            <q-item clickable v-close-popup @click="navigateTo('dash-main')">
              <q-item-section>
                <q-item-label>Dashboard</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-close-popup @click="navigateTo('create-event')">
              <q-item-section>
                <q-item-label>Create New Event</q-item-label>
              </q-item-section>
            </q-item>

            <q-separator />

            <q-item clickable v-close-popup @click="logout">
              <q-item-section>
                <q-item-label>Logout</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </q-toolbar>
    </q-header>

    <!-- Event selection bar (when in dashboard context) -->
    <div
      v-if="showEventSelector"
      class="event-selector q-pa-md bg-grey-2"
      style="position: relative; z-index: 99; margin-top: 50px"
    >
      <div class="row items-center">
        <div class="col-auto q-mr-md">
          <q-icon name="event" size="24px" />
          <span class="q-ml-sm text-weight-bold">Current Event:</span>
        </div>

        <div class="col">
          <q-select
            v-model="selectedEvent"
            :options="eventOptions"
            option-value="id"
            option-label="title"
            dense
            outlined
            emit-value
            map-options
            label="Select Event"
            @update:model-value="onEventSelected"
          />
        </div>

        <div class="col-auto q-ml-md">
          <q-btn
            color="primary"
            label="Create New Event"
            icon="add"
            @click="navigateTo('create-event')"
          />
        </div>
      </div>
    </div>

    <!-- Main content area -->
    <q-page-container
      :class="{
        'has-stepper':
          hasActiveEvent && !['create-event', 'dash-main'].includes(route.name),
      }"
    >
      <div
        v-if="!hasActiveEvent && route.name === 'dash-main'"
        class="q-pa-xl text-center"
      >
        <q-icon name="event_note" size="4rem" color="primary" />
        <div class="text-h4 q-mt-md">Welcome to HGEvents</div>
        <div class="text-h6 q-mt-sm q-mb-lg">
          Please select an event from the dropdown above or create a new event
          to get started
        </div>
        <q-btn
          color="primary"
          icon="add"
          label="Create New Event"
          size="lg"
          @click="navigateTo('create-event')"
        />
      </div>
      <router-view v-else v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";
import axios from "axios";
// import { prepareForTicketNavigation } from '../router/ticketRoutesHandler'

// Setup router and store
const router = useRouter();
const route = useRoute();
const eventStore = useEventStore();
const $q = useQuasar();

// Local state
const currentTab = ref("dashboard");
const selectedEvent = ref(null);
const eventOptions = ref([]);
const isLoadingEvents = ref(false);
const leftDrawerOpen = ref(true); // Start with drawer open
const miniState = ref(false); // State for mini drawer mode

// Methods for mini drawer
const toggleMiniState = () => {
  miniState.value = !miniState.value;
  // Store preference in localStorage
  localStorage.setItem("drawerMiniState", miniState.value);
  // Adjust stepper position after drawer state changes
  adjustStepperPosition();
};

// Adjust stepper position based on drawer state
const adjustStepperPosition = () => {
  // Wait for DOM update
  setTimeout(() => {
    const stepper = document.querySelector(".stepper-fixed");
    if (stepper) {
      const isMobile = window.innerWidth <= 991;

      if (isMobile) {
        // On mobile, stepper takes full width
        stepper.style.width = "100%";
        stepper.style.marginLeft = "0";
      } else if (leftDrawerOpen.value) {
        if (miniState.value) {
          // Mini drawer is open
          stepper.style.width = "calc(100% - 70px)";
          stepper.style.marginLeft = "70px";
        } else {
          // Full drawer is open
          stepper.style.width = "calc(100% - 280px)";
          stepper.style.marginLeft = "280px";
        }
      } else {
        // Drawer is closed
        stepper.style.width = "100%";
        stepper.style.marginLeft = "0";
      }

      // Ensure stepper has correct z-index and positioning
      stepper.style.zIndex = "100";
      stepper.style.position = "fixed";
      stepper.style.top = "50px"; // Position below header
    }

    // Adjust page container class based on stepper presence
    const pageContainer = document.querySelector(".q-page-container");
    if (
      pageContainer &&
      hasActiveEvent.value &&
      !["create-event", "dash-main"].includes(route.name)
    ) {
      pageContainer.classList.add("has-stepper");
    } else if (pageContainer) {
      pageContainer.classList.remove("has-stepper");
      // Ensure there's adequate padding at the top
      pageContainer.style.paddingTop = "60px";
    }
  }, 50);
};

const hoverDrawer = (isHover) => {
  // Only expand on hover if we're in mini mode and drawer is open
  if (miniState.value && isHover && leftDrawerOpen.value) {
    miniState.value = false;
    adjustStepperPosition();
  } else if (!isHover && leftDrawerOpen.value) {
    // Return to mini state when mouse leaves if drawer should be in mini mode
    const savedMiniState = localStorage.getItem("drawerMiniState") === "true";
    if (savedMiniState) {
      miniState.value = true;
      adjustStepperPosition();
    }
  }
};

// Computed properties
const hasActiveEvent = computed(() => {
  return !!eventStore.getEventId;
});

const eventLogo = computed(() => {
  // Return the event logo path if it exists
  if (eventStore.getEvent && eventStore.getEvent.logo_path) {
    return eventStore.getEvent.logo_path;
  }

  // If there's no event or no logo_path, return null
  return null;
});

const showEventSelector = computed(() => {
  // Show event selector when in dashboard context but not when in event creation/editing
  const creationRoutes = ["create-event", "edit-event"];
  return !creationRoutes.includes(route.name);
});

// Navigation methods
const navigateTo = (routeName, params = {}) => {
  // Clear event data when navigating to create-event
  if (routeName === "create-event") {
    console.log("Navigating to create new event, clearing existing event data");

    // Use the centralized clearEvent method from the store
    // This handles clearing all localStorage and sessionStorage items
    eventStore.clearEvent();

    // Clear selectedEvent to ensure dropdown shows no selection
    selectedEvent.value = null;

    router.push({ name: routeName, params });
  } else if (routeName === "dash-main" && hasActiveEvent.value) {
    // Pass the event ID when navigating to dashboard
    router.push({
      name: routeName,
      params: { eventId: eventStore.getEventId, ...params },
    });
  } else if (routeName === "payments" && params && hasActiveEvent.value) {
    // Handle payments report with status parameter
    router.push({
      name: routeName,
      params: {
        status: params.status || "all",
        eventId: eventStore.getEventId,
      },
    });
  } else if (hasActiveEvent.value && ["bookings-unpaid"].includes(routeName)) {
    // Pass event ID to bookings reports
    router.push({
      name: routeName,
      params: { eventId: eventStore.getEventId, ...params },
    });
  } else {
    router.push({ name: routeName, params });
  }
};

// Import ticket navigation handler
import { prepareForTicketNavigation } from "../router/ticketRoutesHandler";

const navigateToEvent = (routeName) => {
  if (hasActiveEvent.value) {
    // Get the event ID once to prevent reactivity issues
    const currentEventId = eventStore.getEventId;

    // FIX: Prepare for ticket navigation if we're going to the tickets page
    // This is the key fix for router navigation issues
    if (routeName === "ticket-creation") {
      console.log(
        `Navigating to tickets for event ${currentEventId} via sidebar link`
      );
      prepareForTicketNavigation(currentEventId);

      // Force a short delay to ensure flags are set before navigation
      setTimeout(() => {
        router.push({ name: routeName, params: { eventId: currentEventId } });
      }, 10);
      return;
    } else if (routeName === "edit-event") {
      router.push({ name: routeName, params: { id: currentEventId } });
    } else if (routeName === "reg-questions") {
      // Use non-reactive eventId to prevent circular updates
      router.push({ name: routeName, params: { eventId: currentEventId } });
    } else if (routeName === "customise-emails") {
      router.push({ name: routeName, params: { eventId: currentEventId } });
    } else {
      router.push({ name: routeName });
    }
  } else {
    // If no event is selected, show message
    $q.notify({
      type: "warning",
      message: "Please select an event first",
      position: "top",
      timeout: 2000,
    });
  }
};

// Add after navigateToEvent
const goToStep = (step) => {
  if (hasActiveEvent.value) {
    currentTab.value = step;

    // Map step to route
    let routeName = "dash-main";
    let params = {};

    if (step === "event-details") {
      routeName = "edit-event";
    } else if (step === "tickets") {
      routeName = "ticket-creation";
    } else if (step === "reg-questions") {
      routeName = "reg-questions";
    } else if (step === "customise-emails") {
      routeName = "customise-emails";
    } else if (step === "attendees") {
      routeName = "attendees";
    } else if (step === "reports") {
      routeName = "payments";
      params = { status: "all" };
    }

    // Navigate to the appropriate route with params if needed
    if (step === "reports") {
      navigateTo(routeName, params);
    } else {
      navigateToEvent(routeName);
    }
  } else {
    // If no event is selected, show message
    $q.notify({
      type: "warning",
      message: "Please select an event first",
      position: "top",
      timeout: 2000,
    });
  }
};

// Load user's events for dropdown
const loadEvents = async () => {
  isLoadingEvents.value = true;
  try {
    const response = await axios.get("/events/user_events");
    if (response.data.events) {
      eventOptions.value = response.data.events.map((event) => ({
        id: event.id,
        title: event.title || `Event #${event.id}`,
      }));

      // Set selected event if one is stored
      if (eventStore.getEventId) {
        selectedEvent.value = eventStore.getEventId;
      }
    }
  } catch (error) {
    console.error("Failed to load events", error);
    $q.notify({
      type: "negative",
      message: "Failed to load your events",
      position: "top",
      timeout: 3000,
    });
  } finally {
    isLoadingEvents.value = false;
  }
};

// Handle event selection
const onEventSelected = async (eventId) => {
  if (eventId) {
    const success = await eventStore.loadEvent(eventId);
    if (success) {
      $q.notify({
        type: "positive",
        message: `Event "${eventStore.getEvent.title}" selected`,
        position: "top",
        timeout: 2000,
      });

      // If we're on the dashboard, update the route to include the event ID
      if (route.name === "dash-main") {
        router.push({ name: "dash-main", params: { eventId } });
      }
    } else {
      $q.notify({
        type: "negative",
        message: "Failed to load event details",
        position: "top",
        timeout: 3000,
      });
    }
  }
};

// Logout method
const logout = () => {
  // Clear local storage but preserve drawer state
  const drawerMiniState = localStorage.getItem("drawerMiniState");
  localStorage.clear();
  if (drawerMiniState) {
    localStorage.setItem("drawerMiniState", drawerMiniState);
  }

  // Submit logout form
  const form = document.createElement("form");
  form.method = "POST";
  form.action = "/users/sign_out";

  // Add authenticity token
  const token = document
    .querySelector('meta[name="csrf-token"]')
    ?.getAttribute("content");
  if (token) {
    const csrfInput = document.createElement("input");
    csrfInput.type = "hidden";
    csrfInput.name = "authenticity_token";
    csrfInput.value = token;
    form.appendChild(csrfInput);
  }

  document.body.appendChild(form);
  form.submit();
};

// Watch for drawer open/close to adjust stepper position
watch(
  () => leftDrawerOpen.value,
  (newVal) => {
    adjustStepperPosition();
  }
);

// Watch for route changes to adjust stepper position
watch(
  () => route.name,
  (newRouteName, oldRouteName) => {
    // Wait for DOM to update before adjusting
    setTimeout(() => {
      adjustStepperPosition();
    }, 100);

    // If navigating to create-event route, ensure event data is cleared
    if (newRouteName === "create-event") {
      console.log(
        "Route changed to create-event, ensuring event data is cleared"
      );

      // Use the centralized clearEvent method from the store
      // This handles clearing all localStorage and sessionStorage items
      eventStore.clearEvent();

      // Clear selectedEvent
      selectedEvent.value = null;
    }
  }
);

// Watch for mini-state changes
watch(
  () => miniState.value,
  () => {
    adjustStepperPosition();
  }
);

// Update tab based on current route
watch(
  () => route.name,
  (newRouteName) => {
    if (newRouteName === "dash-main") {
      currentTab.value = "dashboard";
    } else if (
      newRouteName === "create-event" ||
      newRouteName === "edit-event"
    ) {
      currentTab.value = "event-details";
      if (hasActiveEvent.value) eventStore.addVisitedPage("event-details");
    } else if (newRouteName === "ticket-creation") {
      currentTab.value = "tickets";
      if (hasActiveEvent.value) eventStore.addVisitedPage("tickets");
    } else if (newRouteName === "reg-questions") {
      currentTab.value = "reg-questions";
      if (hasActiveEvent.value) eventStore.addVisitedPage("reg-questions");
    } else if (
      newRouteName === "customise-emails" ||
      newRouteName?.includes("customise-")
    ) {
      currentTab.value = "customise-emails";
      if (hasActiveEvent.value) eventStore.addVisitedPage("customise-emails");
    } else if (
      newRouteName === "attendees" ||
      newRouteName?.includes("attendees-")
    ) {
      currentTab.value = "attendees";
      if (hasActiveEvent.value) eventStore.addVisitedPage("attendees");
    } else if (
      newRouteName === "payments" ||
      newRouteName === "bookings-unpaid"
    ) {
      currentTab.value = "reports";
      if (hasActiveEvent.value) eventStore.addVisitedPage("reports");
    }
  }
);

// Initialize component
onMounted(async () => {
  // Check if there's a stored mini state preference in localStorage
  const savedMiniState = localStorage.getItem("drawerMiniState");
  if (savedMiniState !== null) {
    miniState.value = savedMiniState === "true";
  }

  // Load user's events
  await loadEvents();

  // Check if there's an event ID in the route params
  if (route.params.eventId && route.params.eventId !== eventStore.getEventId) {
    // Load this specific event
    await eventStore.loadEvent(route.params.eventId);
    selectedEvent.value = route.params.eventId;
  }

  // If we're on the create-event route, ensure event data is cleared
  if (route.name === "create-event") {
    console.log("On create-event route during mount, clearing event data");

    // Use the centralized clearEvent method from the store
    // This handles clearing all localStorage and sessionStorage items
    eventStore.clearEvent();

    // Clear selected event
    selectedEvent.value = null;
  }

  // Update tab based on initial route
  if (route.name) {
    if (route.name === "dash-main") {
      currentTab.value = "dashboard";
    } else if (route.name === "create-event" || route.name === "edit-event") {
      currentTab.value = "event-details";
    } else if (route.name === "ticket-creation") {
      currentTab.value = "tickets";
    } else if (route.name === "reg-questions") {
      currentTab.value = "reg-questions";
    } else if (
      route.name === "customise-emails" ||
      route.name?.includes("customise-")
    ) {
      currentTab.value = "customise-emails";
    } else if (
      route.name === "attendees" ||
      route.name?.includes("attendees-")
    ) {
      currentTab.value = "attendees";
    } else if (route.name === "payments" || route.name === "bookings-unpaid") {
      currentTab.value = "reports";
    }
  }

  // Initialize stepper position
  adjustStepperPosition();

  // Add listener for window resize to adjust stepper position
  window.addEventListener("resize", adjustStepperPosition);

  // Add listener for browser back/forward navigation
  window.addEventListener("popstate", () => {
    // Check if we're on the create-event route after navigation
    const pathname = window.location.pathname;
    const hash = window.location.hash;

    // Enhanced detection for create-event routes
    const isCreateEventRoute =
      pathname.includes("/events/new") ||
      pathname.includes("/create-event") ||
      hash.includes("/create-event") ||
      hash.includes("#/create") ||
      window.location.href.includes("create-event");

    if (isCreateEventRoute) {
      console.log(
        "Navigated to create-event via browser history, clearing event data"
      );

      // Use centralized clearEvent method
      eventStore.clearEvent();

      // Clear selectedEvent in the UI
      selectedEvent.value = null;
    }
  });
});
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.event-selector {
  border-bottom: 1px solid #ddd;
}

.drawer-header {
  border-bottom: 1px solid #ddd;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.drawer-item {
  transition: background-color 0.3s ease;
}

.drawer-item:hover {
  background-color: rgba(0, 101, 114, 0.1);
}

/* Stepper styles with fixed position adjustment */
.q-stepper {
  border-bottom: 1px solid #ddd;
  position: relative;
  z-index: 100; /* Higher z-index to appear above other elements */
}

.stepper-fixed {
  transition: margin-left 0.3s ease;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
  margin-top: 50px; /* Add space between header and stepper */
}

/* Ensure pages have proper spacing with stepper */
.q-page-container {
  padding-top: 60px !important;
  position: relative;
}

/* Add additional padding when stepper is present */
.has-stepper {
  padding-top: 110px !important; /* Account for header + stepper height */
}

/* Full height drawer */
.q-drawer {
  height: 100% !important;
  position: fixed; /* Ensure drawer stays fixed */
}

/* Padding for page container to avoid content being hidden under header */
.q-page-container {
  padding-top: 60px; /* Increased padding to ensure content isn't hidden */
}

/* Fix stepper to ensure it's not hidden behind side menu */
.q-stepper {
  border-bottom: 1px solid #ddd;
  position: relative;
  z-index: 1;
  margin-left: 70px; /* Accounts for mini drawer width */
}

/* Adjusts stepper margin when drawer is not in mini state */
@media (min-width: 992px) {
  .q-drawer:not(.q-mini-drawer) + .q-page-container .q-stepper {
    margin-left: 280px; /* Full drawer width */
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .event-selector .row {
    flex-direction: column;
  }

  .event-selector .col-auto {
    margin: 5px 0;
    width: 100%;
  }

  .event-selector .q-btn {
    width: 100%;
  }

  .q-stepper-header {
    flex-wrap: wrap;
  }

  .q-stepper-header .q-stepper-tab {
    padding: 8px;
  }

  .q-stepper-header .q-stepper-tab .q-stepper-title {
    font-size: 0.8rem;
  }
}

/* Smooth transition for mini drawer */
.q-drawer {
  transition: width 0.3s ease, min-width 0.3s ease;
}
</style>
