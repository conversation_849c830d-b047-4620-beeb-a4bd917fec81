/**
 * Ticket Routes Handler
 *
 * This file provides utilities to ensure proper data loading when navigating
 * to ticket-related routes, especially using router links.
 */

import { useEventStore } from "@/stores/event";

/**
 * Prepare for ticket route navigation
 * Call this function before navigating to ticket routes via router.push
 *
 * @param {string|number} eventId - The ID of the event to navigate to
 */
export const prepareForTicketNavigation = (eventId) => {
  if (!eventId) return;

  console.log(
    `[TicketRoutesHandler] Preparing for ticket navigation to event ${eventId}`
  );

  // Set flags to ensure fresh data is loaded
  localStorage.setItem("hg-current-event-id", eventId.toString());
  localStorage.setItem("hg-force-ticket-reload", "true");

  // Store the request timestamp to help debug navigation issues
  localStorage.setItem("hg-ticket-navigation-time", Date.now().toString());
};

/**
 * Navigation guard to handle ticket route transitions
 * Add this in your router's beforeEach hooks
 */
export const ticketNavigationGuard = (to, from, next) => {
  // Only apply to transitions to the ticket-creation route
  if (to.name === "ticket-creation" && to.params.eventId) {
    prepareForTicketNavigation(to.params.eventId);

    console.log(
      `[TicketRoutesHandler] Navigation guard: Routing to tickets for event ${to.params.eventId}`
    );

    // If navigating from one event's tickets to another event's tickets,
    // clear the store to prevent state issues
    if (
      from.name === "ticket-creation" &&
      from.params.eventId !== to.params.eventId
    ) {
      console.log(
        "[TicketRoutesHandler] Changing between different event tickets, clearing store data"
      );
      const eventStore = useEventStore();

      // Set a flag to indicate we're changing events
      localStorage.setItem("hg-changing-events", "true");
    }
  }

  // Always proceed with navigation
  next();
};

/**
 * Update AppMain.vue to call this before router navigation
 *
 * Example:
 * import { prepareForTicketNavigation } from '../router/ticketRoutesHandler'
 *
 * // Then in your navigation method:
 * const navigateToEvent = (routeName) => {
 *   if (routeName === 'ticket-creation') {
 *     prepareForTicketNavigation(event.id)
 *   }
 *   router.push({ name: routeName, params: { eventId: event.id } })
 * }
 */
