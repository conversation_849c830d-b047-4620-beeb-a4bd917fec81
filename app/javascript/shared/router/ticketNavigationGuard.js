// Navigation guard middleware to ensure ticket ID is preserved
// when navigating directly to ticket routes
import { useEventStore } from "@/stores/event";
import { createPinia } from "pinia";

// Use manual method to clear localStorage items without depending on Pinia store
const clearLocalStorageItems = () => {
  console.log("Manually clearing localStorage items for create-event");
  localStorage.removeItem("hg-event-store");
  localStorage.removeItem("hg-current-event-id");
  localStorage.removeItem("hg-event-last-loaded");
  localStorage.removeItem("hg-tickets-last-loaded");
  localStorage.removeItem("hg-force-ticket-reload");
  sessionStorage.removeItem("hg-current-event-id-backup");
};

export default function ticketNavigationGuard(router) {
  // We'll get the eventStore inside the navigation guard function
  // so it's retrieved at the right time when Pinia is available

  router.beforeEach(async (to, from, next) => {
    // Handle the create-event route - clear all event-related data
    if (to.name === "create-event") {
      console.log(
        "Navigation guard: Routing to create-event, clearing event data"
      );

      try {
        // Try to get the event store - might fail if Pinia isn't initialized
        const eventStore = useEventStore();
        // Use the centralized clearEvent method from the store
        eventStore.clearEvent();
      } catch (error) {
        console.warn(
          "Failed to access event store in navigation guard, falling back to manual clearing:",
          error
        );
        // Manually clear localStorage as fallback
        clearLocalStorageItems();
      }
    }

    // Check if the route contains a ticket ID in the path
    else if (to.name === "ticket-creation" && to.params.eventId) {
      console.log("Ticket route detected, eventId:", to.params.eventId);
      const newEventId = to.params.eventId;

      try {
        // Try to get the store - might fail if Pinia isn't initialized
        const eventStore = useEventStore();
        const currentEventId = eventStore.getEventId;

        // Only take action if we're changing events or don't have an event loaded
        if (newEventId !== currentEventId || !currentEventId) {
          console.log(
            `Event ID change detected: ${currentEventId} -> ${newEventId}`
          );

          // Store the event ID in localStorage for persistence
          localStorage.setItem("hg-current-event-id", newEventId);

          // Also store in sessionStorage as a backup
          sessionStorage.setItem("hg-current-event-id-backup", newEventId);

          // Mark tickets for reload
          localStorage.setItem("hg-force-ticket-reload", "true");
        }
      } catch (error) {
        console.warn(
          "Failed to access event store in navigation guard:",
          error
        );

        // Store the event ID in localStorage as fallback
        localStorage.setItem("hg-current-event-id", newEventId);
        sessionStorage.setItem("hg-current-event-id-backup", newEventId);
        localStorage.setItem("hg-force-ticket-reload", "true");
      }
    }

    // Also check URL hash for ticket ID
    const hash = window.location.hash;
    const ticketsRegex = /\/tickets\/(\d+)/;
    const match = hash.match(ticketsRegex);

    if (match && match[1] && to.name !== "ticket-creation") {
      console.log("Detected event ID in URL hash:", match[1]);
      const eventId = match[1];

      // Store the event ID in localStorage without accessing the store
      localStorage.setItem("hg-current-event-id", eventId);
      sessionStorage.setItem("hg-current-event-id-backup", eventId);
      localStorage.setItem("hg-force-ticket-reload", "true");
    }

    next();
  });
}
