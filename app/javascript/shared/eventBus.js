import mitt from 'mitt';

const eventBus = mitt();

// Add debugging in development
const isDevelopment = process.env.NODE_ENV === 'development';
if (isDevelopment) {
  const originalEmit = eventBus.emit;
  const originalOn = eventBus.on;
  const originalOff = eventBus.off;

  eventBus.emit = function(type, ...args) {
    console.log(`🚌 EventBus EMIT: ${type}`, args);
    return originalEmit.call(this, type, ...args);
  };

  eventBus.on = function(type, handler) {
    console.log(`🚌 EventBus ON: ${type}`, handler.name || 'anonymous');
    return originalOn.call(this, type, handler);
  };

  eventBus.off = function(type, handler) {
    console.log(`🚌 EventBus OFF: ${type}`, handler?.name || 'anonymous');
    return originalOff.call(this, type, handler);
  };
}

export default eventBus;