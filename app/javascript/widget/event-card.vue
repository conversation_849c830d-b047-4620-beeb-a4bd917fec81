<template>
    <q-card class="my-card cursor-pointer" @click="goToEvent">
        <q-img
            :src="imgSrc"
            @error="handleImgNotFound"
            :class="{ 'card-img-missing': imgMissClass }"
            :alt="event.title"
            basic
            height="200px"
        />
        
        <q-card-section>
            <div class="row no-wrap">
                <div class="col-3 flex flex-center column">
                    <div class="text-subtitle1 text-weight-medium text-center text-secondary month">
                        {{ event.shortdate[0].split("_")[1] }}
                    </div>
                    <div class="text-h5 text-weight-medium text-center text-secondary date">
                        {{ event.shortdate[0].split("_")[0] }}
                    </div>
                </div>
                
                <div class="col-9">
                    <div class="text-h6 evt-title" :style="customColor">
                        {{ event.title }}
                    </div>
                    <div class="text-subtitle2">{{ event.datetimefrom }}</div>
                    <div v-if="event.datetimefrom != event.datetimeto" class="text-subtitle2">
                        {{ event.datetimeto }}
                    </div>
                    <div class="text-subtitle2">{{ event.location }}</div>
                    <div class="text-subtitle2">{{ event.lowest_price }}</div>
                </div>
            </div>
            
            <div class="socials q-mt-sm">
                <socialMediaBtn :eventTitle="event.title" :bookingUrl="event.booking_url"></socialMediaBtn>
            </div>
        </q-card-section>
    </q-card>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import socialMediaBtn from "./social-media-share.vue";

// Props
const props = defineProps({
    event: {
        type: Object,
        required: true
    },
    colour: {
        type: String,
        default: null
    }
});

// Reactive state
const imgSrc = ref("https://via.placeholder.com/400x200?text=Your+Event+Image+Here");

// Methods
const goToEvent = () => {
    // Needs to use parent, otherwise link won't work
    parent.location = props.event.booking_url;
};

const handleImgNotFound = () => {
    imgSrc.value = "https://via.placeholder.com/400x200?text=Your+Event+Image+Here";
};

// Computed properties
const imgMissClass = computed(() => {
    return imgSrc.value === "https://via.placeholder.com/400x200?text=Your+Event+Image+Here";
});

const customColor = computed(() => {
    if (props.colour) {
        return { color: props.colour };
    } else if (props.event.custom_text_colour) {
        return { color: props.event.custom_text_colour };
    } else {
        return { color: '#ff9500' };
    }
});

// Lifecycle hooks
onMounted(() => {
    if (props.event.image_url) {
        imgSrc.value = `${props.event.image_url}?${Math.random()}`;
    }
});

// Watch for changes
watch(() => props.event, (newVal) => {
    imgSrc.value = newVal.image_url ? `${newVal.image_url}?${Math.random()}` : "/assets/placeholder.png";
}, { deep: true });
</script>

<style scoped>
/* Quasar specific styles */
.my-card {
    width: 100%;
    max-width: 100%;
    transition: transform 0.3s;
}

.my-card:hover {
    transform: translateY(-5px);
}

.card-img-missing {
    object-fit: contain;
}

.evt-title {
    line-height: 1.2;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}
</style>
