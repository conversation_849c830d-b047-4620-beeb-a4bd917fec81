<template>
    <social-sharing :url="bookingUrl" :title="'I am going to ' + eventTitle + ' event. Come to see it here:'" :type="'popup'" inline-template>
        <div>
            <div class="text-right">
                <q-btn round color="grey-7" size="sm" class="q-ml-xs" @click.stop>
                    <network network="email">
                        <q-icon name="email" size="sm" />
                    </network>
                </q-btn>
                <q-btn round color="blue-8" size="sm" class="q-ml-xs" @click.stop>
                    <network network="facebook">
                        <q-icon name="fab fa-facebook-f" size="sm" />
                    </network>
                </q-btn>
                <q-btn round color="light-blue-5" size="sm" class="q-ml-xs" @click.stop>
                    <network network="twitter">
                        <q-icon name="fab fa-twitter" size="sm" />
                    </network>
                </q-btn>
            </div>
        </div>
    </social-sharing>
</template>

<script setup>
// Component name
defineOptions({
    name: "socialMediaBtn"
});

// Props
defineProps({
    bookingUrl: {
        type: String,
        required: true
    },
    eventTitle: {
        type: String,
        required: true
    }
});
</script>

<style scoped>
/* Quasar buttons already have appropriate styling */
</style>
