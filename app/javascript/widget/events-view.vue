<template>
  <div>
    <events-filter :colour="colour" @filterEvents="filterEvents"></events-filter>

    <q-container class="q-pa-md">
      <div class="events">
        <div class="text-h4 q-mb-md">Forthcoming {{ orgname }} events</div>

        <div>
          <event-deck :events="pageEvents" :colour="colour"></event-deck>
          <div v-if="events.length === 0" class="text-h5 text-center q-pa-md">No Events found.</div>
        </div>

        <div class="q-pa-md flex justify-center">
          <q-pagination
            v-if="events.length > 0"
            v-model="currentPage"
            :max="Math.ceil(events_count / 6)"
            :max-pages="6"
            boundary-links
            direction-links
            @update:model-value="pageChange"
          />
        </div>
      </div>
    </q-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';
import eventDeck from "./events-card-deck.vue";
import eventsFilter from "./events-filter.vue";

// Initialize Quasar
const $q = useQuasar();

// Props
const props = defineProps({
  orgid: {
    type: [String, Number],
    required: true
  },
  orgname: {
    type: String,
    required: true
  },
  colour: {
    type: String,
    default: null
  }
});

// Reactive state
const events = ref([]);
const eventsOld = ref([]);
const currentPage = ref(1);
const events_count = ref(0);
const filter = ref({
  title: "",
  date_on: null,
  location: null,
  tag: null,
  type: null,
  cost: 'all'
});
const apiToken = ref(null);
const legacyUrl = ref(null);

// Computed properties
const pageEvents = computed(() => {
  const start = (currentPage.value - 1) * 6;
  const end = (currentPage.value * 6);
  
  if (currentPage.value * 6 <= events.value.length) {
    return events.value.slice(start, end);
  } else {
    return events.value.slice(start);
  }
});

// Methods
const filterEvents = (newFilter) => {
  filter.value = newFilter;
  getOrgEvents();
};

const pageChange = (page) => {
  currentPage.value = page;
  getOrgEvents(page);
};

const getOrgEvents = async (page = 1) => {
  $q.loading.show({
    message: 'Loading events...'
  });

  if (page) {
    currentPage.value = page;
  }

  try {
    const response = await axios.get(`/organisation/${props.orgid}/event_details`, {
      params: {
        title: filter.value.title,
        date_on: filter.value.date_on,
        location: filter.value.location,
        tag: filter.value.tag,
        type: filter.value.type,
        cost: filter.value.cost
      }
    });
    
    events.value = response.data.events;
    events_count.value = response.data.events_count;
    
    // Do it this way to avoid a chase issue meaning no old events
    await getOldOrgEvents();
    $q.loading.hide();
  } catch (error) {
    $q.loading.hide();
    $q.notify({
      type: 'negative',
      message: 'Failed to load events',
      caption: error.message
    });
    console.error("Error fetching events:", error);
  }
};

const getOldOrgEvents = async () => {
  try {
    const config = {
      headers: {'Authorization': `Bearer ${apiToken.value}`},
      params: {
        title: filter.value.title,
        date_on: filter.value.date_on,
        location: filter.value.location,
        tag: filter.value.tag,
        type: filter.value.type,
        cost: filter.value.cost
      }
    };
    
    const response = await axios.get(`${legacyUrl.value}/api/v1/widget_events/public_events`, config);
    eventsOld.value = response.data.events || [];
    events.value = [...eventsOld.value, ...events.value];
  } catch (error) {
    $q.notify({
      type: 'warning',
      message: 'Could not load legacy events',
      caption: error.message
    });
    console.error("Error fetching old org events:", error);
  }
};

// Lifecycle hooks
onMounted(() => {
  legacyUrl.value = document.querySelector('[name=legacy-eventstop-url]')?.content;
  const params = new URLSearchParams(window.location.search);
  apiToken.value = params.get('token');
  getOrgEvents();
});
</script>

<style scoped>
/* Quasar specific styles */
.q-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.q-pagination {
  --q-primary: v-bind('colour || "#ff9500"');
}
</style>