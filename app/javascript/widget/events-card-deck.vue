<template>
    <div class="row q-col-gutter-md">
        <div 
            v-for="(event, idx) in events"
            :key="idx"
            class="col-xs-12 col-sm-6 col-md-4 col-lg-3"
        >
            <event-card :event="event" :colour="colour"></event-card>
        </div>
    </div>
</template>

<script setup>
import eventCard from "./event-card.vue";

// Props
defineProps({
    events: {
        type: Array,
        required: true
    },
    colour: {
        type: String,
        default: null
    }
});
</script>