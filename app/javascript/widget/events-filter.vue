<template>
    <div class="q-pa-md">
        <q-card :style="{ backgroundColor: props.colour || '#ff9500' }" class="filter-card">
            <q-card-section>
                <div class="row q-col-gutter-md">
                    <div class="col-12 col-md-6">
                        <div class="row q-col-gutter-md">
                            <div class="col-12">
                                <q-input filled dark v-model="title" label="I'm looking for" placeholder="Event Name">
                                    <template v-slot:prepend>
                                        <q-icon name="search" />
                                    </template>
                                </q-input>
                            </div>
                            <div class="col-12">
                                <q-input filled dark v-model="tag" label="By tag" placeholder="Event Tag">
                                    <template v-slot:prepend>
                                        <q-icon name="local_offer" />
                                    </template>
                                </q-input>
                            </div>
                            <div class="col-12">
                                <q-input filled dark v-model="location" label="In" placeholder="Enter location e.g. Boston, UK">
                                    <template v-slot:prepend>
                                        <q-icon name="location_on" />
                                    </template>
                                </q-input>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-md-6">
                        <div class="row q-col-gutter-md">
                            <div class="col-12">
                                <q-input filled dark v-model="dateOn" label="On" placeholder="Select Event Date">
                                    <template v-slot:prepend>
                                        <q-icon name="event" />
                                    </template>
                                    <template v-slot:append>
                                        <q-icon name="event" class="cursor-pointer">
                                            <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                                                <q-date v-model="dateOn" mask="YYYY-MM-DD" />
                                            </q-popup-proxy>
                                        </q-icon>
                                    </template>
                                </q-input>
                            </div>
                            <div class="col-12">
                                <q-select 
                                    filled 
                                    dark 
                                    v-model="eventType" 
                                    :options="eventTypeOptions" 
                                    label="Event type" 
                                    option-value="value" 
                                    option-label="text"
                                    emit-value 
                                    map-options
                                >
                                    <template v-slot:prepend>
                                        <q-icon name="category" />
                                    </template>
                                </q-select>
                            </div>
                            <div class="col-12">
                                <q-select 
                                    filled 
                                    dark 
                                    v-model="cost" 
                                    :options="costOptions" 
                                    label="By Price" 
                                    option-value="value" 
                                    option-label="text"
                                    emit-value 
                                    map-options
                                >
                                    <template v-slot:prepend>
                                        <q-icon name="attach_money" />
                                    </template>
                                </q-select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row justify-end q-mt-md">
                    <q-btn color="white" text-color="primary" icon="search" label="Search" @click="filterEvents" class="q-mr-sm" />
                    <q-btn color="white" text-color="primary" flat label="Clear filters" @click="clearFilters" />
                </div>
            </q-card-section>
        </q-card>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';

const $q = useQuasar();

// Props
const props = defineProps({
    colour: {
        type: String,
        default: null
    }
});

// Emits
const emit = defineEmits(['filterEvents']);

// Reactive state
const placeHolderText = ref("Filter text");
const title = ref("");
const dateOn = ref(null);
const location = ref(null);
const tag = ref(null);
const eventType = ref(null);
const eventTypeOptions = ref([]);
const cost = ref('all');
const costOptions = ref([
    { text: 'All', value: 'all' },
    { text: 'Free', value: 'free' },
    { text: 'Paid', value: 'paid' }
]);

// Methods
const filterEvents = () => {
    emit("filterEvents", { 
        title: title.value, 
        date_on: dateOn.value, 
        location: location.value, 
        tag: tag.value, 
        type: eventType.value, 
        cost: cost.value 
    });
};

const clearFilters = () => {
    placeHolderText.value = "Filter text";
    title.value = "";
    dateOn.value = null;
    location.value = null;
    tag.value = null;
    eventType.value = null;
    cost.value = 'all';
    filterEvents();
};

const getEventTypeOptions = async () => {
    try {
        const response = await axios.get("/splash_screen/event_type_selector_options.json");
        const data = response.data.map(item => ({
            text: item.text,
            value: item.value
        }));
        eventTypeOptions.value = [{ text: 'All', value: null }, ...data];
    } catch (error) {
        $q.notify({
            type: 'negative',
            message: 'Failed to load event types',
            caption: error.message
        });
        console.error("Error fetching event type options:", error);
    }
};

// Lifecycle hooks
onMounted(() => {
    getEventTypeOptions();
});
</script>

<style scoped>
.filter-card {
    border-radius: 8px;
}

/* Add white label text for dark background */
:deep(.q-field__label) {
    color: white !important;
}

:deep(.q-field__native) {
    color: white !important;
}

:deep(.q-field__input) {
    color: white !important;
}
</style>