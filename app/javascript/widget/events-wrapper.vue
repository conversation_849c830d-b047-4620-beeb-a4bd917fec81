<template>
    <div ref="el2">
        <q-container class="q-pa-md" id="splashscreen">
            <q-card flat bordered>
                <q-card-section>
                    <div class="text-h5 text-center">SEARCH OUR EVENTS</div>
                </q-card-section>
                <q-card-section>
                    <events-view :orgid="orgid" :org-name="orgname" :colour="colour"></events-view>
                </q-card-section>
            </q-card>
        </q-container>
    </div>
</template>

<script setup>
import { useElementSize } from '@vueuse/core'
import EventsView from './events-view.vue'
import { defineProps, ref, watch } from 'vue'

const url = new URL(location.href).origin

const el2 = ref(null)

const { height, width } = useElementSize(el2)

const props = defineProps({
    orgid: String,
    orgname: String,
    colour: String
})


// TODO needs to be the current url for *
watch(height, async (newHeight) => {
    console.log('height changed')
    parent.postMessage(
            {
                height: newHeight,
            },
            '*'
        );
})

</script>

<style scoped>
/* Quasar specific styles */
.q-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}
</style>