import { defineStore } from "pinia";
import axios from "axios";

export const useMainStore = defineStore("main", {
  state: () => ({
    orgContacts: [],
    summaryReport: [],
    paymentInfo: {},
    stripeAccount: {
      test_mode: true,
      publishable_key: "",
      secret_key: "",
    },
    website: window.org_website || "",
    orgId: window.org_id || "",
    advancedUser: window.advancedUser || false,
    loading: false,
    events: [],
    archivedEvents: [],
    brandColors: {
      primaryColor: "#b39ddb",
      secondaryColor: "#6c757d",
      accentColor: "#28a745",
    },
    globalTerms: "",
    charitySettings: {
      enable_charity_fundraising: false,
      charity_name: "",
      charity_registration_number: "",
      suggested_donation_amount: 0,
      charity_description: "",
    },
    filters: {
      filter: "All",
      tags: [],
      searchTerm: "",
    },
    leftDrawerOpen: true,
  }),

  getters: {
    getOrgContacts: (state) => state.orgContacts,
    getSummaryReport: (state) => state.summaryReport,
    getPaymentInfo: (state) => state.paymentInfo,
    getWebsite: (state) => state.website,
    getOrgId: (state) => state.orgId,
    isAdvancedUser: (state) => state.advancedUser,
    isLoading: (state) => state.loading,
    getEvents: (state) => state.events,
    getArchivedEvents: (state) => state.archivedEvents,
    getBrandColors: (state) => state.brandColors,
    getGlobalTerms: (state) => state.globalTerms,
    getCharitySettings: (state) => state.charitySettings,
    getLeftDrawerOpen: (state) => state.leftDrawerOpen,
  },

  actions: {
    async fetchOrgContacts() {
      this.loading = true;
      try {
        const response = await axios.get("/org_contacts.json");
        this.orgContacts = response.data;
        return response.data;
      } catch (error) {
        console.error("Error fetching org contacts:", error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    async fetchSummaryReport(filters = {}) {
      this.loading = true;
      try {
        const response = await axios.get("/summary_report.json", {
          params: filters,
        });
        this.summaryReport = response.data;
        return response.data;
      } catch (error) {
        console.error("Error fetching summary report:", error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    async fetchStripeAccount() {
      this.loading = true;
      try {
        const response = await axios.get("/stripe_account");
        this.stripeAccount = response.data;
        return response.data;
      } catch (error) {
        console.error("Error fetching payment info:", error);
        return {};
      } finally {
        this.loading = false;
      }
    },
    async saveStripeAccount() {
      this.loading = true;
      try {
        // if paymentinfo has no id, it means it's a new payment info
        if (!this.stripeAccount.id) {
          const response = await axios.post(
            "/stripe_account",
            this.stripeAccount
          );
        } else {
          const response = await axios.put(
            "/stripe_account",
            this.stripeAccount
          );
        }
      } catch (error) {
        console.error("Error saving payment info:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async checkStripeAccount() {
      try {
        const response = await axios.get("/stripe_account/check_keys");
        return response.data?.message === "success" ? true : false;
      } catch (err) {
        return false;
      }
    },

    initiateStripeConnect(eventId = null) {
      try {
        let url = "/connect/oauth";
        if (eventId) {
          url += `?state=${eventId}`;
        }
        console.log("Attempting to redirect to:", url);
        // Since the backend redirects to Stripe, we need to navigate directly
        // Make this synchronous to ensure proper redirect
        window.location.href = url;
      } catch (error) {
        console.error("Error initiating Stripe Connect:", error);
        throw error;
      }
    },

    async deauthorizeStripeConnect() {
      try {
        const response = await axios.get("/connect/deauthorize");
        await this.fetchStripeAccount(); // Refresh the account data
        return response.data;
      } catch (error) {
        console.error("Error deauthorizing Stripe Connect:", error);
        throw error;
      }
    },

    async checkStripeConnectStatus() {
      try {
        const response = await axios.get("/stripe_account");
        const account = response.data;
        return {
          isConnected: !!(account?.stripe_user_id && account?.publishable_key),
          account: account,
        };
      } catch (error) {
        console.error("Error checking Stripe Connect status:", error);
        return { isConnected: false, account: null };
      }
    },

    async fetchPaymentInfo() {
      this.loading = true;
      try {
        const response = await axios.get("/payment_info");
        this.paymentInfo = response.data;
        return response.data.payment_info;
      } catch (error) {
        console.error("Error fetching payment info:", error);
        return {};
      } finally {
        this.loading = false;
      }
    },

    async savePaymentInfo() {
      this.loading = true;
      try {
        let response = null;
        // if paymentinfo has no id, it means it's a new payment info
        if (!this.paymentInfo.id) {
          response = await axios.post("/payment_info", {
            payment_info: this.paymentInfo,
          });
        } else {
          response = await axios.put("/payment_info", {
            payment_info: this.paymentInfo,
          });
        }
        this.paymentInfo = response.data.payment_info;
        return response.data.payment_info;
      } catch (error) {
        console.error("Error saving payment info:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchEvents() {
      this.loading = true;
      try {
        const response = await axios.get("/events.json", {
          params: {
            status: "live",
            search: this.filters.searchTerm,
            date_range: this.filters.dateRange,
          },
        });
        this.events = response.data;
        return response.data;
      } catch (error) {
        console.error("Error fetching events:", error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    async fetchArchivedEvents() {
      this.loading = true;
      try {
        const response = await axios.get("/events.json", {
          params: {
            status: "archived",
            search: this.filters.searchTerm,
            date_range: this.filters.dateRange,
          },
        });
        this.archivedEvents = response.data;
        return response.data;
      } catch (error) {
        console.error("Error fetching archived events:", error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    async fetchBrandColors() {
      this.loading = true;
      try {
        const response = await axios.get("/brand_colour");
        this.brandColors.primaryColor = response.data.hex_code;
        return response.data;
      } catch (error) {
        console.error("Error fetching brand colors:", error);
        return {};
      } finally {
        this.loading = false;
      }
    },

    async saveBrandColors() {
      this.loading = true;
      try {
        const response = await axios.put("/brand_colour", {
          hex_code: this.getBrandColors.primary,
        });
        return response.data;
      } catch (error) {
        console.error("Error saving brand colors:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchGlobalTerms() {
      this.loading = true;
      try {
        const response = await axios.get("/legal_terms");
        this.globalTerms = response.data.terms;
        return response.data.terms;
      } catch (error) {
        console.error("Error fetching global terms:", error);
        return "";
      } finally {
        this.loading = false;
      }
    },

    async saveGlobalTerms() {
      this.loading = true;
      try {
        const response = await axios.post("/event_terms", {
          my_terms: { terms: this.globalTerms },
        });
        return response.data;
      } catch (error) {
        console.error("Error saving global terms:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchCharitySettings() {
      this.loading = true;
      try {
        const response = await axios.get("/charity_settings");
        this.charitySettings = response.data;
        return response.data;
      } catch (error) {
        console.error("Error fetching charity settings:", error);
        return {};
      } finally {
        this.loading = false;
      }
    },

    async saveCharitySettings(settings) {
      this.loading = true;
      try {
        const response = await axios.post("/charity_settings", {
          charity: settings,
        });
        this.charitySettings = response.data;
        return response.data;
      } catch (error) {
        console.error("Error saving charity settings:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    setOrgContacts(contacts) {
      this.orgContacts = contacts;
    },

    addOrgContact(contact) {
      this.orgContacts.push(contact);
    },

    removeContact(contactId) {
      const index = this.orgContacts.findIndex(
        (contact) => contact.id === contactId
      );
      if (index !== -1) {
        this.orgContacts.splice(index, 1);
      }
    },

    addContactTag({ id, tag }) {
      const contact = this.orgContacts.find((contact) => contact.id === id);
      if (contact) {
        contact.tags ? contact.tags.push(tag) : (contact.tags = [tag]);
      }
    },
    removeContactTag({ id, tag }) {
      const contact = this.orgContacts.find((contact) => contact.id === id);
      if (contact) {
        contact.tags = contact.tags.filter((t) => t !== tag);
      }
    },

    setSummaryReport(report) {
      this.summaryReport = report;
    },

    setFilter(filter) {
      this.filters.filter = filter;
    },

    setTags(tags) {
      this.filters.tags = tags;
    },

    setSearchTerm(searchTerm) {
      this.filters.searchTerm = searchTerm;
    },
    setLeftDrawerOpen(open) {
      this.leftDrawerOpen = !open;
    },
  },
});
