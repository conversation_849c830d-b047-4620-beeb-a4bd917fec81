import { defineStore } from 'pinia';

export const useUsersStore = defineStore('users', {
  state: () => ({
    event: {},
    selectedTickets: null,
    paymentInfo: null,
    advancedUser: null,
    chargeable: true,
    hasBookings: false,
    imageBucket: null,
  }),
  actions: {
    setEvent(event) {
      this.event = event;
    },
    setSelectedTickets(tickets) {
      this.selectedTickets = tickets;
    },
    setPaymentInfo(paymentInfo) {
      this.paymentInfo = paymentInfo;
    },
    setAdvancedUser(advanced) {
      this.advancedUser = advanced;
    },
    updatePromoCode(discountDetails) {
      this.event.one_off_discount_code = discountDetails.discount_code;
      this.event.one_off_discount_percentage = discountDetails.discount_percentage;
    },
    setBookerQuestionsMandatory(required) {
      this.event.booker_questions_mandatory = required;
    },
    setAttendeeQuestionsMandatory(required) {
      this.event.attendee_questions_mandatory = required;
    },
    setChargeable(hasPaidTickets) {
      this.chargeable = hasPaidTickets;
    },
    setHasBookings(hasBookings) {
      this.hasBookings = hasBookings;
    },
    setImageBucket(bucket) {
      this.imageBucket = bucket;
    },
  },
  getters: {
    getEvent: (state) => state.event,
    getPaymentInfo: (state) => state.paymentInfo,
    getAdvancedUser: (state) => {
      if (state.advancedUser == null) {
        state.advancedUser = window.advancedUser;
      }
      return state.advancedUser;
    },
    getBookerQuestionsMandatory: (state) => state.event.booker_questions_mandatory,
    getAttendeeQuestionsMandatory: (state) => state.event.attendee_questions_mandatory,
    getChargeable: (state) => state.chargeable,
    getHasBookings: (state) => state.hasBookings,
    getImageBucket: (state) => state.imageBucket,
  },
});