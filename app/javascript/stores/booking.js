import { defineStore } from "pinia";
import axios from "axios";
import dayjs from 'dayjs';

export const useBookingStore = defineStore("booking", {
  // State
  state: () => ({
    eventBooking: {},
    selectedTickets: [],
    error: null,
    isLoading: false,
    // Merged from shared/stores/bookingStore.js
    event: {},
    bookerDetails: null,
    bookingToken: null,
    chargeable: true,
    inProgress: false,
    editByOrg: false,
  }),

  // Getters
  getters: {
    getEventBooking: (state) => state.eventBooking,
    getSelectedTickets: (state) => state.selectedTickets,
    // Merged from shared/stores/bookingStore.js
    getEvent: (state) => state.event,
    getEventId: (state) => state.event.id,
    getBookerDetails: (state) => state.bookerDetails,
    getBookingToken: (state) => state.bookingToken,
    getChargeable: (state) => state.chargeable,
    getInProgress: (state) => state.inProgress,
    getEditByOrg: (state) => state.editByOrg,
  },

  // Actions
  actions: {
    setEventBooking(booking) {
      this.eventBooking = booking;
    },

    setSelectedTickets(tickets) {
      this.selectedTickets = tickets;
    },

    updateBookingStatus(status) {
      if (this.eventBooking) {
        this.eventBooking.payment_status = status;
      }
    },

    cancelBooking() {
      if (this.eventBooking) {
        this.eventBooking.booking_count = 0;
        this.eventBooking.cancelled_at = Date.now();
      }
    },

    async fetchBookings(eventId) {
      if (!eventId) {
        console.error("No event ID provided to loadEvent");
        return false;
      }
      // Note: this.getEventId is now available
      if (this.getEventId === Number(eventId) && !this.isLoading) {
        console.log(
          `Event ${eventId} is already loaded, skipping duplicate load`
        );
        return true;
      }
      this.isLoading = true;
      this.error = null;
      try {
        const response = await axios.get(
          `/dashboard/${eventId}/confirmed_booking_data`
        );

        if (response.data.booking_data) {
          this.setEventBooking(response.data.booking_data);
          return true;
        } else {
          this.error = "Event data not found in response";
          return false;
        }
      } catch (error) {
        console.error(`Error loading event with ID: ${eventId}`, error);
        this.error = error.response?.data?.error || "Failed to load event";
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // Setters for merged state
    setEvent(event) {
      this.event = event;
    },
    setBookerDetails(details) {
      this.bookerDetails = details;
    },
    setBookingToken(token) {
      this.bookingToken = token;
    },
    setChargeable(chargeable) {
      this.chargeable = chargeable;
    },
    setInProgress(inProgress) {
      this.inProgress = inProgress;
    },
    setEditByOrg(editByOrg) {
      this.editByOrg = editByOrg;
    },

    // --- Merged from useBookingMixin ---

    _failedBooking(error, $q) {
      console.error('🎫 Booking failed:', error);
      this.setInProgress(false);
      let errorMessage = 'Event Booking Not Saved';
      if (error.response?.data?.errors) {
        errorMessage = error.response.data.errors[0];
      } else if (error.message) {
        errorMessage = error.message;
      }
      $q.notify({
        type: 'negative',
        message: errorMessage,
        caption: 'Problem making booking'
      });
    },

    async _successfulBooking(response, $q, router) {
      console.log('🎫 Successful booking response:', response);
      const eventBookingData = response.data;
      eventBookingData.cancelled_at = null;
      eventBookingData.cancelled_by = null;
      $q.notify({
        type: 'positive',
        message: 'Event Booking Saved',
        caption: 'Your booking has been successfully created!'
      });
      this.setEventBooking(eventBookingData);
      if (
        this.chargeable &&
        eventBookingData.free_booking === false &&
        eventBookingData.payment_type !== 'bacs' &&
        eventBookingData.payment_type !== 'cheque'
      ) {
        router.push({ name: 'payment' });
      } else {
        console.log('🎫 Routing to summary (free booking)');
        router.push('/summaryfree');
      }
    },

    async updateBooking({ mode, router, $q }) {
      console.log('Updating booking with mode:', mode);
      try {
        const eventBooking = this.eventBooking;
        const event = this.event;
        if (!eventBooking || !eventBooking.uuid) {
          throw new Error('No event booking found');
        }
        const updatedEventBooking = {
          ...eventBooking,
          booking_amendment: dayjs().format(),
          registered_user_attributes: this.bookerDetails,
          package_bookings_attributes: eventBooking.package_bookings
        };
        const tickets = this.selectedTickets;
        const packageBookings = updatedEventBooking.package_bookings_attributes;
        if (tickets && packageBookings) {
          tickets.forEach((ticket) => {
            packageBookings.forEach(booking => {
              booking.registered_users_attributes = booking.registered_users;
              if (booking.package_id === ticket.id) {
                booking.sub_options_selected = ticket.optionsSelected;
              }
            });
          });
        }
        const booking = {
          event_booking: updatedEventBooking,
          booking_token: this.bookingToken
        };
        const response = await axios.put(
          `/event_bookings/${eventBooking.uuid}.json`,
          booking
        );
        $q.notify({
          type: 'positive',
          message: 'Details Updated!',
          caption: 'You have updated the booking details!'
        });
        const editByOrg = mode === 'edit';
        if (editByOrg) {
          window.location = `/dashboard/${event.id}#/attendees`;
        } else {
          router.push({ name: 'summary' });
        }
        return response.data;
      } catch (error) {
        console.error('Update booking failed:', error);
        this._failedBooking(error, $q);
        throw error;
      }
    },

    async completeBooking({
      router,
      $q,
      isEuCard = null,
      paymentType = null,
      stripeToken = null,
      freeFromTicket = false
    }) {
      console.log('🎫 Starting completeBooking...');
      try {
        const tickets = this.selectedTickets;
        const event = this.event;
        const eventBooking = this.eventBooking;
        if (!tickets || tickets.length === 0) {
          throw new Error('No tickets selected');
        }
        if (!event || !event.id) {
          throw new Error('No event found');
        }
        const eventBookingData = {
          registered_user_attributes: {
            ...this.bookerDetails,
            event_id: event.id
          },
          event_id: event.id,
          free_booking: freeFromTicket,
          package_bookings_attributes: [],
          payment_type: paymentType
        };
        tickets.forEach((ticket) => {
          const registeredUsers = [];
          if (ticket.attendees) {
            ticket.attendees.forEach((attendee) => {
              if (attendee.registered_user_attributes) {
                attendee.registered_user_attributes.user_type = 'attendee';
                attendee.registered_user_attributes.event_id = event.id;
                registeredUsers.push(attendee.registered_user_attributes);
              }
            });
          }
          let discountType = 0;
          if (ticket.discount_code_id && ticket.discount_type !== 'percentage') {
            discountType = 1;
          }
          const packageBooking = {
            package_id: ticket.id,
            quantity_tickets: ticket.quantity_tickets,
            registered_users_attributes: registeredUsers,
            sub_options_selected: ticket.optionsSelected || [],
            discount_code_id: ticket.discount_code_id,
            discount_amount: ticket.discount_amount,
            discount_type: ticket.discount_type
          };
          eventBookingData.package_bookings_attributes.push(packageBooking);
        });
        const booking = {
          event_booking: eventBookingData,
          completing: true,
          booking_token: this.bookingToken
        };
        console.log('🎫 Submitting booking data:', booking);
        let response;
        if (eventBooking && eventBooking.uuid) {
          response = await axios.put(
            `/event_bookings/${eventBooking.uuid}.json`,
            booking
          );
        } else {
          response = await axios.post('/event_bookings/', booking);
        }
        await this._successfulBooking(response, $q, router);
        return response.data;
      } catch (error) {
        console.error('🎫 Complete booking failed:', error);
        this._failedBooking(error, $q);
        throw error;
      }
    },
  },
});
