// src/stores/auth.js
import { defineStore } from "pinia";
import authService from "@/services/auth";

export const useAuthStore = defineStore("auth", {
  state: () => ({
    user: null,
    authenticated: false,
  }),

  actions: {
    async login(email, password, rememberMe = false) {
      try {
        const response = await authService.login(email, password, rememberMe);
        console.log("Login response:", response);
        if (response.success) {
          this.user = response.user;
          this.authenticated = true;
          console.log(
            "Auth store updated - authenticated:",
            this.authenticated
          );
        }
        return response;
      } catch (error) {
        throw error;
      }
    },

    async logout() {
      console.log("in logout");
      try {
        await authService.logout();
        this.user = null;
        this.authenticated = false;
      } catch (error) {
        // Even if logout fails on server, clear local state
        this.user = null;
        this.authenticated = false;
      }
    },

    async register(email, password, passwordConfirmation, organisationName) {
      try {
        const response = await authService.register(
          email,
          password,
          passwordConfirmation,
          organisationName
        );
        if (response.success) {
          this.user = response.user;
          this.authenticated = true;
        }
        return response;
      } catch (error) {
        throw error;
      }
    },

    async requestPasswordReset(email) {
      this.passwordResetLoading = true;
      try {
        const response = await authService.requestPasswordReset(email);
        return response;
      } catch (error) {
        throw error;
      } finally {
        this.passwordResetLoading = false;
      }
    },

    async validateResetToken(token) {
      try {
        const response = await authService.validateResetToken(token);
        return response;
      } catch (error) {
        throw error;
      }
    },

    async resetPassword(token, password, passwordConfirmation) {
      try {
        const response = await authService.resetPassword(
          token,
          password,
          passwordConfirmation
        );
        if (response.success && response.user) {
          this.user = response.user;
          this.authenticated = true;
        }
        return response;
      } catch (error) {
        throw error;
      }
    },

    async confirmEmail(token) {
      try {
        const response = await authService.confirmEmail(token);
        if (response.success && response.user) {
          this.user = response.user;
          this.authenticated = true;
        }
        return response;
      } catch (error) {
        throw error;
      }
    },

    async resendConfirmation(email) {
      try {
        const response = await authService.resendConfirmation(email);
        return response;
      } catch (error) {
        throw error;
      }
    },
  },

  // Add persistence to share auth state between different Vue entry points
  persist: {
    key: "eventstop-auth",
    storage: localStorage,
    paths: ["user", "authenticated"],
  },
});
