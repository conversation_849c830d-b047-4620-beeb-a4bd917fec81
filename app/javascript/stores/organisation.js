// src/stores/organisation.js
import { defineStore } from 'pinia'
import organisationService from '@/services/organisation'

export const useOrganisationStore = defineStore('organisation', {
  state: () => ({
    organisations: [],
    currentOrganisation: null,
    loading: false
  }),
  
  getters: {
    ownedOrganisations: (state) => {
      return state.organisations.filter(org => org.owner_id === state.currentUserId)
    },
    
    memberOrganisations: (state) => {
      return state.organisations.filter(org => org.owner_id !== state.currentUserId)
    }
  },
  
  actions: {
    async fetchOrganisations() {
      this.loading = true
      try {
        const response = await organisationService.getOrganisations()
        this.organisations = response.organisations || []
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async fetchOrganisation(id) {
      this.loading = true
      try {
        const response = await organisationService.getOrganisation(id)
        this.currentOrganisation = response.organisation
        return response.organisation
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async createOrganisation(name) {
      try {
        const response = await organisationService.createOrganisation(name)
        if (response.success) {
          this.organisations.push(response.organisation)
        }
        return response
      } catch (error) {
        throw error
      }
    },
    
    async updateOrganisation(id, name) {
      try {
        const response = await organisationService.updateOrganisation(id, name)
        if (response.success) {
          const index = this.organisations.findIndex(org => org.id === id)
          if (index !== -1) {
            this.organisations[index] = response.organisation
          }
          if (this.currentOrganisation && this.currentOrganisation.id === id) {
            this.currentOrganisation = response.organisation
          }
        }
        return response
      } catch (error) {
        throw error
      }
    },
    
    async deleteOrganisation(id) {
      try {
        const response = await organisationService.deleteOrganisation(id)
        if (response.success) {
          this.organisations = this.organisations.filter(org => org.id !== id)
          if (this.currentOrganisation && this.currentOrganisation.id === id) {
            this.currentOrganisation = null
          }
        }
        return response
      } catch (error) {
        throw error
      }
    }
  }
})