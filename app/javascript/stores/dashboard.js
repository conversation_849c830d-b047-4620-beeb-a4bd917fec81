import { defineStore } from 'pinia';
import axios from 'axios';

export const useDashboardStore = defineStore('dashboard', {
  state: () => ({
    event: {},
    attendees: [],
    unconfirmedAttendees: [],
    cancelledAttendees: [],
    declinedAttendees: [],
    failedPaymentAttendees: [],
    unpaidBookings: [],
    statistics: {
      totalAttendees: 0,
      totalConfirmed: 0,
      totalCancelled: 0,
      totalRevenue: 0,
      totalTicketsSold: 0
    },
    loading: false,
    filters: {
      dateRange: null,
      searchTerm: '',
      ticketType: 'all'
    },
    pagination: {
      currentPage: 1,
      totalItems: 0,
      itemsPerPage: 20
    },
    exportStatus: null,
    exportUrl: null
  }),
  
  actions: {
    setEvent(event) {
      this.event = event;
    },
    
    async fetchAttendees() {
      if (!this.event.id) return;
      
      this.loading = true;
      try {
        const response = await axios.get(`/dashboard/${this.event.id}/attendees`, {
          params: {
            page: this.pagination.currentPage,
            per_page: this.pagination.itemsPerPage,
            search: this.filters.searchTerm,
            ticket_type: this.filters.ticketType,
            date_range: this.filters.dateRange
          }
        });
        
        this.attendees = response.data.attendees;
        this.pagination.totalItems = response.data.total_count;
        this.statistics.totalAttendees = response.data.total_attendees;
        this.statistics.totalConfirmed = response.data.total_confirmed;
      } catch (error) {
        console.error('Error fetching attendees:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async fetchUnconfirmedAttendees() {
      if (!this.event.id) return;
      
      this.loading = true;
      try {
        const response = await axios.get(`/dashboard/${this.event.id}/attendees-unconfirmed`, {
          params: {
            page: this.pagination.currentPage,
            per_page: this.pagination.itemsPerPage,
            search: this.filters.searchTerm
          }
        });
        
        this.unconfirmedAttendees = response.data.attendees;
        this.pagination.totalItems = response.data.total_count;
      } catch (error) {
        console.error('Error fetching unconfirmed attendees:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async fetchCancelledAttendees() {
      if (!this.event.id) return;
      
      this.loading = true;
      try {
        const response = await axios.get(`/dashboard/${this.event.id}/attendees-cancelled`, {
          params: {
            page: this.pagination.currentPage,
            per_page: this.pagination.itemsPerPage,
            search: this.filters.searchTerm
          }
        });
        
        this.cancelledAttendees = response.data.attendees;
        this.pagination.totalItems = response.data.total_count;
        this.statistics.totalCancelled = response.data.total_cancelled;
      } catch (error) {
        console.error('Error fetching cancelled attendees:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async fetchDeclinedAttendees() {
      if (!this.event.id) return;
      
      this.loading = true;
      try {
        const response = await axios.get(`/dashboard/${this.event.id}/attendees-declined`, {
          params: {
            page: this.pagination.currentPage,
            per_page: this.pagination.itemsPerPage,
            search: this.filters.searchTerm
          }
        });
        
        this.declinedAttendees = response.data.attendees;
        this.pagination.totalItems = response.data.total_count;
      } catch (error) {
        console.error('Error fetching declined attendees:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async fetchFailedPaymentAttendees() {
      if (!this.event.id) return;
      
      this.loading = true;
      try {
        const response = await axios.get(`/dashboard/${this.event.id}/attendees-payment-failed`, {
          params: {
            page: this.pagination.currentPage,
            per_page: this.pagination.itemsPerPage,
            search: this.filters.searchTerm
          }
        });
        
        this.failedPaymentAttendees = response.data.attendees;
        this.pagination.totalItems = response.data.total_count;
      } catch (error) {
        console.error('Error fetching failed payment attendees:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async fetchUnpaidBookings() {
      if (!this.event.id) return;
      
      this.loading = true;
      try {
        const response = await axios.get(`/dashboard/${this.event.id}/bookings-unpaid`, {
          params: {
            page: this.pagination.currentPage,
            per_page: this.pagination.itemsPerPage,
            search: this.filters.searchTerm
          }
        });
        
        this.unpaidBookings = response.data.bookings;
        this.pagination.totalItems = response.data.total_count;
      } catch (error) {
        console.error('Error fetching unpaid bookings:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async fetchStatistics() {
      if (!this.event.id) return;
      
      try {
        const response = await axios.get(`/dashboard/${this.event.id}/statistics`);
        this.statistics = response.data;
      } catch (error) {
        console.error('Error fetching statistics:', error);
      }
    },
    
    async exportAttendees(format = 'csv') {
      if (!this.event.id) return;
      
      this.exportStatus = 'processing';
      try {
        const response = await axios.post(`/dashboard/${this.event.id}/export`, {
          format,
          filters: this.filters
        });
        
        this.exportStatus = 'completed';
        this.exportUrl = response.data.export_url;
        return response.data.export_url;
      } catch (error) {
        this.exportStatus = 'failed';
        console.error('Error exporting attendees:', error);
        return null;
      }
    },
    
    setPage(page) {
      this.pagination.currentPage = page;
    },
    
    setItemsPerPage(itemsPerPage) {
      this.pagination.itemsPerPage = itemsPerPage;
      this.pagination.currentPage = 1; // Reset to first page when changing items per page
    },
    
    setSearchTerm(term) {
      this.filters.searchTerm = term;
      this.pagination.currentPage = 1; // Reset to first page when searching
    },
    
    setTicketTypeFilter(ticketType) {
      this.filters.ticketType = ticketType;
      this.pagination.currentPage = 1; // Reset to first page when filtering
    },
    
    setDateRangeFilter(dateRange) {
      this.filters.dateRange = dateRange;
      this.pagination.currentPage = 1; // Reset to first page when filtering
    },
    
    resetFilters() {
      this.filters = {
        dateRange: null,
        searchTerm: '',
        ticketType: 'all'
      };
      this.pagination.currentPage = 1;
    }
  },
  
  getters: {
    getEvent: (state) => state.event,
    getAttendees: (state) => state.attendees,
    getUnconfirmedAttendees: (state) => state.unconfirmedAttendees,
    getCancelledAttendees: (state) => state.cancelledAttendees,
    getDeclinedAttendees: (state) => state.declinedAttendees,
    getFailedPaymentAttendees: (state) => state.failedPaymentAttendees,
    getUnpaidBookings: (state) => state.unpaidBookings,
    getStatistics: (state) => state.statistics,
    isLoading: (state) => state.loading,
    getCurrentPage: (state) => state.pagination.currentPage,
    getTotalItems: (state) => state.pagination.totalItems,
    getItemsPerPage: (state) => state.pagination.itemsPerPage,
    getTotalPages: (state) => Math.ceil(state.pagination.totalItems / state.pagination.itemsPerPage),
    getExportStatus: (state) => state.exportStatus,
    getExportUrl: (state) => state.exportUrl
  }
});
