<template>
  <q-card
    bordered
    flat
    v-ripple
    @click="onClick"
    :class="[
      'cool-button cursor-pointer q-hoverable transition-all',
      `cool-button--${variant}`,
      { 'cool-button--disabled': disabled }
    ]"
  >
    <span class="q-focus-helper"></span>
    <q-card-section horizontal class="items-center no-padding">
      <q-card-section class="cool-button__icon-section">
        <q-icon
          :name="icon"
          :size="iconSize"
          :color="disabled ? 'grey-5' : color"
          class="cool-button__icon"
        />
      </q-card-section>
      <q-card-section class="cool-button__content">
        <div class="cool-button__label">{{ label }}</div>
        <div v-if="description" class="cool-button__description">{{ description }}</div>
      </q-card-section>
      <q-card-section class="cool-button__arrow-section">
        <q-icon
          name="keyboard_arrow_right"
          size="md"
          :color="disabled ? 'grey-5' : 'grey-7'"
          class="cool-button__arrow"
        />
      </q-card-section>
    </q-card-section>
  </q-card>
</template>
<script setup>
defineProps({
  icon: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: "primary",
  },
  label: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: "",
  },
  variant: {
    type: String,
    default: "default", // default, primary, secondary, danger
    validator: (value) => ["default", "primary", "secondary", "danger"].includes(value),
  },
  iconSize: {
    type: String,
    default: "md",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  onClick: {
    type: Function,
    default: () => {},
  },
});
</script>

<style lang="scss" scoped>
.cool-button {
  min-height: 5rem;
  width: 100%;
  max-width: 18rem;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--q-primary);

    &::before {
      left: 100%;
    }

    .cool-button__icon {
      transform: scale(1.1);
    }

    .cool-button__arrow {
      transform: translateX(4px);
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &--primary {
    background: linear-gradient(135deg, var(--q-primary) 0%, #1565c0 100%);
    color: white;
    border-color: var(--q-primary);

    .cool-button__label,
    .cool-button__description {
      color: white;
    }

    .cool-button__icon,
    .cool-button__arrow {
      color: white !important;
    }

    &:hover {
      background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    }
  }

  &--secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border-color: #6c757d;

    .cool-button__label,
    .cool-button__description {
      color: white;
    }

    .cool-button__icon,
    .cool-button__arrow {
      color: white !important;
    }
  }

  &--danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-color: #dc3545;

    .cool-button__label,
    .cool-button__description {
      color: white;
    }

    .cool-button__icon,
    .cool-button__arrow {
      color: white !important;
    }

    &:hover {
      background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    }
  }

  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}

.cool-button__icon-section {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 4rem;
}

.cool-button__icon {
  transition: transform 0.3s ease;
}

.cool-button__content {
  flex: 1;
  padding: 1rem 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.cool-button__label {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.cool-button__description {
  font-size: 0.875rem;
  opacity: 0.8;
  line-height: 1.3;
}

.cool-button__arrow-section {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cool-button__arrow {
  transition: transform 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .cool-button {
    min-height: 4rem;
    max-width: none;
  }

  .cool-button__icon-section {
    min-width: 3rem;
    padding: 0.75rem;
  }

  .cool-button__label {
    font-size: 0.9rem;
  }

  .cool-button__description {
    font-size: 0.8rem;
  }
}
</style>
