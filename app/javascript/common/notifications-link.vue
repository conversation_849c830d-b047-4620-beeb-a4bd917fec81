<template>
  <q-btn
    flat
    :round="props.mini ? true : false"
    dense
    :label="props.mini ? '' : 'Show Notifications'"
    icon="notifications"
  >
    <q-badge v-if="notifyCount !== 0" color="negative" floating>{{
      notifyCount
    }}</q-badge>

    <q-menu
      anchor="bottom right"
      self="top right"
      :offset="[0, 10]"
      style="width: 400px"
      @before-show="getNotifications"
    >
      <q-card>
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Notifications</div>
          <q-space />
          <q-btn
            flat
            dense
            label="Clear All Notifications"
            @click="completeAllNotifications"
          />
        </q-card-section>

        <q-card-section class="q-pa-none">
          <q-list separator>
            <q-item
              v-for="notification in notifications"
              :key="notification.id"
              clickable
              @click="
                !notification.actioned && completeNotification(notification)
              "
              class="notification-list__item"
            >
              <q-item-section avatar>
                <q-avatar
                  :color="notification.actioned ? 'positive' : 'grey-4'"
                  text-color="white"
                  size="sm"
                >
                  <q-icon
                    :name="notification.actioned ? 'check' : 'add'"
                    size="sm"
                  />
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label
                  v-if="notification.who"
                  class="text-primary text-weight-bold"
                  style="font-size: 12px"
                >
                  {{ notification.who }}
                </q-item-label>

                <q-item-label
                  v-if="notification.who"
                  class="text-primary text-weight-bold"
                  style="font-size: 12px"
                >
                  {{ notification.details }}
                </q-item-label>

                <q-item-label
                  v-if="!notification.who"
                  class="text-primary text-weight-bold"
                  style="font-size: 12px"
                >
                  {{ notification.attendee_name }} signed up to:
                </q-item-label>

                <q-item-label
                  v-if="!notification.who"
                  class="text-weight-bold"
                  style="font-size: 12px"
                >
                  {{ notification.event_name }}
                </q-item-label>

                <q-item-label caption style="font-size: 12px">
                  At: {{ formatDate(notification.created_at) }}
                </q-item-label>

                <q-item-label v-if="!notification.who">
                  <q-btn
                    flat
                    dense
                    color="primary"
                    :href="
                      '/dashboard/' + notification.event_id + '#/payments/null'
                    "
                    label="View Payments"
                    no-caps
                  />
                </q-item-label>
              </q-item-section>
            </q-item>

            <q-item v-if="notifications.length === 0">
              <q-item-section>
                <q-item-label class="text-weight-bold text-center">
                  No new notifications
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>
    </q-menu>
  </q-btn>
</template>

<script setup>
import { ref, onMounted } from "vue";
import axios from "axios";
import dayjs from "dayjs";

const props = defineProps({
  mini: {
    type: Boolean,
    default: false,
  },
});

const notifyCount = ref(0);
const notifications = ref([]);
const notificationArray = ref([]);

// Format dates
const formatDate = (date) => {
  return dayjs(date).format("MMMM Do YYYY, h:mm:ss a");
};

const getNotificationCount = async () => {
  try {
    const response = await axios.get("/notifications/notification_count");
    notifyCount.value = response.data.count;
  } catch (error) {
    console.error("Error fetching notification count:", error);
  }
};

const completeAllNotifications = async () => {
  notificationArray.value = [];
  notifications.value.forEach((notification) => {
    notificationArray.value.push(notification.id);
    notification.actioned = true;
  });

  try {
    await axios.put("/notifications/update_all_notifications", {
      notification: notificationArray.value,
    });
    notifyCount.value = 0;
  } catch (error) {
    console.error("Error updating all notifications:", error);
  }
};

const completeNotification = async (notification) => {
  notification.actioned = true;
  notifyCount.value = notifyCount.value - 1;

  try {
    await axios.put(`/notifications/${notification.id}`);
  } catch (error) {
    console.error(`Error updating notification ${notification.id}:`, error);
  }
};

const getNotifications = async () => {
  try {
    const response = await axios.get("/notifications");
    notifications.value = [];

    if (response.data.length === 0) {
      return;
    }

    response.data.forEach((notification) => {
      notifications.value.push({
        id: notification.id,
        event_id: notification.event_id,
        actioned: notification.viewed,
        attendee_name: notification.attendee_name,
        event_name: notification.event_name,
        who: notification.email,
        details: notification.details,
        created_at: notification.created_at,
      });
    });
  } catch (error) {
    console.error("Error fetching notifications:", error);
  }
};

onMounted(() => {
  getNotificationCount();
});
</script>
