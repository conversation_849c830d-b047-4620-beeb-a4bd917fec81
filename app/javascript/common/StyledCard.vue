<template>
  <q-card
    :style="`border-left: 4px solid ${props.color}; ${heightStyle}`"
    :class="{ 'full-height-card': props.fullHeight }"
  >
    <q-item>
      <q-item-section avatar>
        <q-icon
          :name="props.icon"
          size="sm"
          :style="`color: ${props.color} !important`"
        />
      </q-item-section>
      <q-item-section
        class="text-weight-bold"
        :style="`color: ${props.color} !important`"
      >
        {{ props.title }}
      </q-item-section>
    </q-item>
    <q-separator />
    <q-card-section>
      <slot />
    </q-card-section>
  </q-card>
</template>

<script setup>
import { defineProps, computed } from "vue";

const props = defineProps({
  color: {
    type: String,
    default: "#1976d2",
  },
  title: {
    type: String,
    default: "",
  },
  icon: {
    type: String,
    default: "info", // Default icon
  },
  fullHeight: {
    type: Boolean,
    default: false, // Default to fit content
  },
});

const heightStyle = computed(() => {
  return props.fullHeight ? "height: 100%" : "height: auto";
});
</script>

<style scoped>
.full-height-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.full-height-card .q-card__section:last-child {
  flex: 1;
}
</style>
