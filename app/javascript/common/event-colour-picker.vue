<template>
  <div>
    <q-btn @click="dialogVisible = true" class="float-right q-mt-n16" size="sm" flat>
      <q-icon name="fa fa-paint-brush" class="q-mr-xs" /> Modify Event Colours
    </q-btn>

    <q-dialog v-model="dialogVisible" @hide="resetEmailColours">
      <q-card style="width: 500px; max-width: 80vw;">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Customise your booking colours</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div class="q-mb-md">
            Click to select a header colour:
            <q-color v-model="event.phcolour" :palette="imagePalette" class="full-width" />
          </div>

          <div class="q-mb-md">
            Click to select a button colour:
            <q-color v-model="event.buttoncolour" :palette="imagePalette" class="full-width" />
          </div>

          <div class="q-mb-md">
            Click to select a text colour:
            <q-color v-model="event.textcolour" :palette="imagePalette" class="full-width" />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Confirm" color="primary" @click="saveEmailColours" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
// import Vibrant from "node-vibrant";
import { useQuasar } from 'quasar';

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
});

const $q = useQuasar();
const dialogVisible = ref(false);
const imageBucket = ref(window.imageBucket);
const imagePalette = ref(["#000000", "#FF9500"]);

const addToPalette = (palette) => {
  if (palette.DarkMuted) {
    imagePalette.value.push(palette.DarkMuted.getHex());
  }
  if (palette.DarkVibrant) {
    imagePalette.value.push(palette.DarkVibrant.getHex());
  }
  if (palette.LightMuted) {
    imagePalette.value.push(palette.LightMuted.getHex());
  }
  if (palette.LightVibrant) {
    imagePalette.value.push(palette.LightVibrant.getHex());
  }
  if (palette.Muted) {
    imagePalette.value.push(palette.Muted.getHex());
  }
  if (palette.Vibrant) {
    imagePalette.value.push(palette.Vibrant.getHex());
  }
};

const resetEmailColours = () => {
  props.event.phcolour = null;
  props.event.textcolour = null;
  props.event.buttoncolour = null;
  dialogVisible.value = false;
};

const saveEmailColours = async () => {
  try {
    await axios.post("/colour_changes", {
      phcolour: props.event.phcolour,
      textcolour: props.event.textcolour,
      buttoncolour: props.event.buttoncolour,
      eid: props.event.id
    });
    
    $q.notify({
      color: 'positive',
      message: 'Your colours have been updated',
      icon: 'check_circle'
    });
    
    dialogVisible.value = false;
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'Failed to update colours',
      icon: 'error'
    });
  }
};

onMounted(() => {
  if (props.event.image1) {
    const url = `https://s3-eu-west-1.amazonaws.com/${imageBucket.value}/${props.event.id}/${props.event.image1}?cachebust=${Math.random()}`;
    
    Vibrant.from(url).getPalette((err, palette) => {
      if (palette) {
        addToPalette(palette);
      }
    });
  }
});
</script>
