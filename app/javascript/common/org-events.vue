<template>
  <div class="textcolours">
    <div v-if="orgEvents.length > 0" class="col-md-12" align="center">
      <h3 class="text-h5 q-mb-md q-font-medium" :style="{color: event.textcolour}">
        Check out the other events we have
        <div class="filter-btn q-mt-sm">
          <q-btn
            :href="`/organisation/${event.organisation_id}/events`"
            target="_blank"
            no-caps
            :style="{'background-color': event.buttoncolour, 'border-color': event.buttoncolour}"
            color="primary"
            label="All Our Events"
          />
          
          <q-btn
            color="primary"
            icon="fa fa-filter"
            no-caps
            class="q-ml-md"
            :style="{'background-color': event.buttoncolour, 'border-color': event.buttoncolour}"
            label="Click to filter by tags"
          >
            <q-menu anchor="bottom middle" self="top middle">
              <q-card style="min-width: 250px">
                <q-card-section>
                  <div class="text-h6">Filter By Event Tags</div>
                </q-card-section>
                
                <q-card-section class="q-pt-none">
                  <q-select
                    v-model="tagsSelected"
                    :options="tagsList"
                    multiple
                    emit-value
                    map-options
                    option-label="label"
                    option-value="value"
                    label="Select Tags"
                    @update:model-value="getEvents"
                    style="min-width: 220px"
                  />
                </q-card-section>
              </q-card>
            </q-menu>
          </q-btn>
        </div>
      </h3>

      <q-separator 
        class="q-mx-auto" 
        style="width: 80%; height: 2px;" 
        :color="event.phcolour ? undefined : 'amber'"
        :style="{ 'background-color': event.phcolour }"
      />
    </div>
    
    <div v-if="orgEvents.length > 0" class="events q-mt-md">
      <event-deck :events="orgEvents"></event-deck>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import axios from 'axios';
import eventDeck from "@/splash/events-card-deck.vue";

const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  isPreview: {
    type: Boolean,
    required: false,
    default: false
  }
});

const imageBucket = ref(window.imageBucket);
const orgEvents = ref([]);
const tagsSelected = ref([]);
const tagsList = ref([]);
const loading = ref(false);
const tags = ref([]);

// Computed property to sort events by start date
const orderEventsByStartDate = computed(() => {
  if (orgEvents.value.length === 0) return orgEvents.value;
  return [...orgEvents.value].sort(
    (a, b) => new Date(a.datetimefrom) - new Date(b.datetimefrom)
  );
});

// Watch for changes in the event prop
watch(() => props.event, (newVal) => {
  if (props.isPreview) {
    showEventPreview();
  }
}, { deep: true });

// Fetch events based on selected tags
const getEvents = async () => {
  try {
    const response = await axios.get(`/get_all_events/${props.event.organisation_id}`, {
      params: {
        event_id: props.event.id,
        tags: tagsSelected.value
      }
    });
    
    orgEvents.value = response.data.events;
    setOrgEventUrls();
  } catch (error) {
    console.error("Error fetching events:", error);
  }
};

// Show event preview
const showEventPreview = async () => {
  try {
    const response = await axios.get(`/preview_event_card/${props.event.id}.json`);
    orgEvents.value = [];
    orgEvents.value.push(response.data);
    setOrgEventUrls();
  } catch (error) {
    console.error("Error fetching preview event:", error);
  }
};

// Set event URLs
const setOrgEventUrls = () => {
  orgEvents.value.forEach((event) => {
    if (event.image2) {
      event.imageFileURL = `https://s3-eu-west-1.amazonaws.com/${imageBucket.value}/${event.id}/${event.image2}?${Math.random()}`;
    }
    if (event.custom_url) {
      event.eventurl = `/${event.custom_url}`;
    } else {
      event.eventurl = `/event/${event.id}/${event.title.toLowerCase().replace(/[\\!&|?$%@':;.<>/"=+\s]/g, '_')}`;
    }
  });
};

// Initialize component
onMounted(async () => {
  if (!props.event.phcolour) {
    props.event.phcolour = "rgba(255, 149, 0, 0.86)";
  }

  if (props.isPreview) {
    showEventPreview();
  } else {
    try {
      const response = await axios.get(`/get_all_org_tags/${props.event.organisation_id}`, {
        params: {
          event_id: props.event.id
        }
      });
      
      response.data.tags.forEach(tag => {
        tags.value.push(tag);
        tagsList.value.push({ value: tag, label: tag });
      });
      
      getEvents();
    } catch (error) {
      console.error("Error fetching tags:", error);
    }
  }
});
</script>

<style scoped>
.textcolours {
  color: black !important;
}

.filter-btn {
  margin-top: 10px;
}
</style>
