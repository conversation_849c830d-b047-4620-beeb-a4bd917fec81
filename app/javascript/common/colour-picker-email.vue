<template>
  <q-card class="q-pa-md">
    <q-card-section>
      Click to select a header colour.
      <swatches style="margin-left: -10px" v-model="event.ehcolour" :colors="imagePalette" inline></swatches>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, onMounted, defineProps, watch } from 'vue';
import Vibrant from "node-vibrant";
import Swatches from "vue-swatches";

// Import the styles too, globally
import "vue-swatches/dist/vue-swatches.min.css";

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
});

const imageBucket = ref(window.imageBucket);
const imagePalette = ref([]);

const addToPalette = (palette) => {
  if (palette.DarkMuted) {
    imagePalette.value.push(palette.DarkMuted.getHex());
  }
  if (palette.DarkVibrant) {
    imagePalette.value.push(palette.DarkVibrant.getHex());
  }
  if (palette.LightMuted) {
    imagePalette.value.push(palette.LightMuted.getHex());
  }
  if (palette.LightVibrant) {
    imagePalette.value.push(palette.LightVibrant.getHex());
  }
  if (palette.Muted) {
    imagePalette.value.push(palette.Muted.getHex());
  }
  if (palette.Vibrant) {
    imagePalette.value.push(palette.Vibrant.getHex());
  }
};

const loadColorPalette = () => {
  props.event.ehcolour = "#ff9500";
  
  if (props.event.image1) {
    const url = `https://s3-eu-west-1.amazonaws.com/${imageBucket.value}/${props.event.id}/${props.event.image1}?cachebust=${Math.random()}`;
    
    Vibrant.from(url).getPalette((err, palette) => {
      if (palette) {
        addToPalette(palette);
      }
    });
  }
};

// Watch for changes to event.image1
watch(() => props.event.image1, (newVal) => {
  if (newVal) {
    loadColorPalette();
  }
}, { immediate: false });

onMounted(() => {
  loadColorPalette();
});
</script>

<style>
/* Add any custom styles here */
</style>
