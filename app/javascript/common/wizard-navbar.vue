<template>
  <q-stepper 
    v-model="step" 
    header-nav
    ref="stepper"
    active-color="primary"
    done-color="positive"
    class="wizard-stepper"
    flat
  >
    <q-step
      :name="1"
      title="Details"
      icon="info"
      :done="step > 1"
      :header-nav="step > 1"
    />
    
    <q-step
      :name="2"
      title="Tickets"
      icon="confirmation_number"
      :done="step > 2"
      class="current-step"
    />

    <q-step
      :name="3"
      title="Attendee Questions"
      icon="people"
      :done="step > 3"
      :header-nav="step > 3"
    />
  </q-stepper>
</template>

<script setup>
import { ref } from 'vue';

// Set the current step to 2 as per the original template
const step = ref(2);
</script>

<style scoped>
.wizard-stepper {
  border-radius: 6px;
  border: 1px solid #d4d4d4;
  background-color: #f9f9f9;
}

.q-stepper__tab--active {
  color: #FF9500 !important;
  background: #d9edf7 !important;
}

.current-step .q-stepper__title {
  color: #FF9500 !important;
  font-weight: bold;
}
</style>
