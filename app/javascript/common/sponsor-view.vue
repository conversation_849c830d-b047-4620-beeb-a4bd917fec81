<template>
  <div v-if="event.add_sponsors && event.sponsor_view_delegate && event.sponsors.length > 0" class="q-mt-md">
    <q-card>
      <q-card-section class="bg-grey-2" :style="underlineOverride">
        <div class="text-h6">{{event.sponsor_title || " Sponsors "}}</div>
      </q-card-section>
      
      <q-card-section>
        <q-carousel
          v-model="slide"
          transition-prev="slide-right"
          transition-next="slide-left"
          swipeable
          animated
          control-color="primary"
          navigation
          padding
          arrows
          height="220px"
          class="bg-white"
          :autoplay="3500"
        >
          <q-carousel-slide v-for="(sponsor, idx) in event.sponsors" :name="idx" :key="idx" class="column no-wrap flex-center">
            <q-img
              :src="'https://s3-eu-west-1.amazonaws.com/' + imageBucket + '/sponsors/' + event.id + '/' + sponsor.sponsor_file"
              :alt="sponsor.sponsor_file"
              fit="contain"
              style="max-height: 125px; max-width: 100%"
            />
          </q-carousel-slide>
        </q-carousel>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
});

const imageBucket = ref(window.imageBucket);
const slide = ref(0);

const underlineOverride = computed(() => {
  return {
    borderBottom: "4px solid " + props.event.phcolour
  };
});
</script>

<style scoped>
/* Add any custom styles here */
</style>
