<template>
  <div class="row q-col-gutter-md">
    <div class="col-12 col-md-auto">
      <q-select
        v-model="selectedRange"
        :options="dateRanges"
        option-value="val"
        option-label="name"
        label="Filter By Date"
        dense
        outlined
        @update:model-value="setDefaults"
      />
    </div>

    <div v-if="selectedRange == 0" class="col-12 col-md-auto">
      <q-select
        v-if="quarters.length"
        v-model="selectedQuarter"
        :options="quarters"
        option-value="value"
        option-label="label"
        label="Please select a quarter"
        dense
        outlined
        @update:model-value="updateQuarter"
      />
    </div>

    <template v-if="selectedRange == 1">
      <div class="col-12 col-md-auto">
        <label for="startMonth">Start Month</label>
        <VueDatePicker
          v-model="datefrom"
          month-picker
          model-type="format"
          format="YYYY-MM"
          input-class-name="date-picker"
          :min-date="twoYearsAgo"
          @update:model-value="updateMonth"
        />
      </div>

      <div class="col-auto q-my-auto">to</div>

      <div class="col-12 col-md-auto">
        <label for="endMonth">End Month</label>
        <VueDatePicker
          v-model="dateto"
          month-picker
          model-type="format"
          format="YYYY-MM"
          input-class-name="date-picker"
          :min-date="twoYearsAgo"
          @update:model-value="updateMonth"
        />
      </div>
    </template>

    <template v-if="selectedRange == 2">
      <div class="col-12 col-md-auto">
        <label for="startDate">Start Date</label>
        <VueDatePicker
          v-model="datefrom"
          model-type="format"
          format="YYYY-MM-DD"
          input-class-name="date-picker"
          :min-date="twoYearsAgo"
          @update:model-value="updateDates"
        />
      </div>

      <div class="col-auto q-my-auto">to</div>

      <div class="col-12 col-md-auto">
        <label for="endDate">End Date</label>
        <VueDatePicker
          v-model="dateto"
          model-type="format"
          format="YYYY-MM-DD"
          input-class-name="date-picker"
          :min-date="twoYearsAgo"
          @update:model-value="updateDates"
        />
      </div>
    </template>
  </div>
</template>

<script>
import { ref, defineProps, defineEmits } from 'vue'
import VueDatePicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'

export default {
  components: {
    VueDatePicker
  },
}
</script>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue';
import dayjs from "dayjs";
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import { useEventBus } from "../events/event-bus";

// Add dayjs plugins
dayjs.extend(quarterOfYear);

const props = defineProps({
  showDefault: {
    type: Boolean,
    required: false,
    default: true,
  }
});

const eventBus = useEventBus();
const dateRanges = ref([
  { val: 0, name: "Quarterly" },
  { val: 1, name: "Monthly" },
  { val: 2, name: "Custom Dates" }
]);

const selectedRange = ref(0);
const quarters = ref([]);
const selectedQuarter = ref(4 - dayjs().quarter());
const datefrom = ref(dayjs().startOf("quarter").format('YYYY-MM-DD'));
const dateto = ref(dayjs().endOf("quarter").format('YYYY-MM-DD'));

// Validation tracking
const touchedFields = reactive({})
const formSubmitted = ref(false)

// Mark field as touched
const markFieldAsTouched = (fieldName) => {
  touchedFields[fieldName] = true
}

// Check if field should show error
const shouldShowError = (fieldName) => {
  return formSubmitted.value || touchedFields[fieldName]
}

// Computed properties for formatted dates
const datefromFormatted = computed({
  get: () => datefrom.value ? dayjs(datefrom.value).format('DD/MM/YYYY') : '',
  set: (val) => { datefrom.value = val ? dayjs(val, 'DD/MM/YYYY').format('YYYY-MM-DD') : null }
});

const datetoFormatted = computed({
  get: () => dateto.value ? dayjs(dateto.value).format('DD/MM/YYYY') : '',
  set: (val) => { dateto.value = val ? dayjs(val, 'DD/MM/YYYY').format('YYYY-MM-DD') : null }
});

// Function to limit selectable dates
const dateLimits = (date) => {
  const twoYearsAgo = dayjs().subtract(2, "years").format('YYYY-MM-DD');
  return date >= twoYearsAgo;
};

const twoYearsAgo = dayjs().subtract(2, "years").format('YYYY-MM-DD');

// Function to set default dates based on selected range
const setDefaults = (val) => {
  if (val == 0) {
    updateQuarter(selectedQuarter.value);
  } else if (val == 1) {
    datefrom.value = dayjs().startOf("month").format('YYYY-MM-DD');
    dateto.value = dayjs().endOf("month").format('YYYY-MM-DD');
    updateMonth();
  } else if (val == 2) {
    datefrom.value = dayjs().startOf("month").format('YYYY-MM-DD');
    dateto.value = dayjs().endOf("month").format('YYYY-MM-DD');
    updateDates();
  }
};

// Function to update dates based on selected quarter
const updateQuarter = (val) => {
  const quarter = dayjs().subtract(val, "Q");
  datefrom.value = quarter.startOf("quarter").format('YYYY-MM-DD');
  dateto.value = quarter.endOf("quarter").format('YYYY-MM-DD');
  emitUpdateEvent();
};

// Function to update dates
const updateDates = () => {
  emitUpdateEvent();
};

// Function to update month
const updateMonth = () => {
  // Ensure we set the date to beginning/end of month
  if (datefrom.value) {
    datefrom.value = dayjs(datefrom.value).startOf('month').format('YYYY-MM-DD');
  }
  if (dateto.value) {
    dateto.value = dayjs(dateto.value).endOf('month').format('YYYY-MM-DD');
  }
  emitUpdateEvent();
};

// Function to emit date change event
const emitUpdateEvent = () => {
  const fromDate = datefrom.value ? dayjs(datefrom.value).toDate() : null;
  const toDate = dateto.value ? dayjs(dateto.value).toDate() : null;
  eventBus.emit("datesChanged", fromDate, toDate);
};

// Generate quarters
const generateQuarters = () => {
  return [12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0].map(i => {
    const quarter = dayjs().subtract(i, "Q");
    
    return {
      value: i,
      label:
        quarter.startOf("quarter").format("[Q]Q - MMM YY to ") +
        quarter.endOf("quarter").format("MMM YY")
    };
  });
};

// Listen for reset events
onMounted(() => {
  eventBus.on("resetDateFilter", () => {
    if (props.showDefault) {
      selectedRange.value = 0;
      selectedQuarter.value = 4 - dayjs().quarter();
      setDefaults(selectedRange.value);
    } else {
      datefrom.value = null;
      dateto.value = null;
      selectedRange.value = 2;
    }
  });

  if (props.showDefault === false) {
    datefrom.value = null;
    dateto.value = null;
    selectedRange.value = 2;
  }

  quarters.value = generateQuarters();
  setDefaults(selectedRange.value);
});
</script>