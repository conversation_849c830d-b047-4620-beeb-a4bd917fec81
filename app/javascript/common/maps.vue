<template>
  <q-card class="q-pa-none">
    <div :id="name" class="map-container" style="height: 300px; width: 100%">
      <div v-if="!mapLoaded" class="absolute-center">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-sm">Loading map...</div>
      </div>
    </div>
  </q-card>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";

const props = defineProps({
  postcode: {
    type: String,
    required: true,
  },
  lat: {
    type: Number,
    required: false,
  },
  lng: {
    type: Number,
    required: false,
  },
  name: {
    type: String,
    required: false,
    default: "map",
  },
});

const coordinates = ref({
  lat: props.lat || null,
  lng: props.lng || null,
});
const map = ref(null);
const marker = ref(null);
const googleMapScript = document.createElement("script");
const mapLoaded = ref(false);

// Function to initialize Google Map
const initGoogleMap = () => {
  if (coordinates.value.lat && coordinates.value.lng) {
    initMap();
  } else if (props.postcode) {
    geocodePostcode();
  }
};

// Function to geocode postcode using Google's geocoder
const geocodePostcode = () => {
  if (!window.google || !window.google.maps || !window.google.maps.Geocoder) {
    console.error("Google Maps API not fully loaded");
    return;
  }

  const geocoder = new google.maps.Geocoder();

  geocoder.geocode(
    {
      componentRestrictions: {
        country: "GB",
      },
      address: props.postcode,
    },
    function (results, status) {
      if (status === google.maps.GeocoderStatus.OK) {
        const loc = results[0].geometry.location;

        coordinates.value = {
          lat: loc.lat(),
          lng: loc.lng(),
        };

        if (map.value) {
          map.value.panTo(coordinates.value);
          if (marker.value) {
            marker.value.position = coordinates.value;
          }
          map.value.setZoom(16);
        } else {
          initMap();
        }
      } else {
        console.error("Geocoding failed:", status);
      }
    }
  );
};

// Function to initialize the map with coordinates
const initMap = () => {
  if (!window.google || !window.google.maps) {
    console.error("Google Maps API not fully loaded");
    return;
  }

  //TODO: CREATE A MAP ID TO USE ADVANCED MARKERS
  const mapId = null;

  const mapOptions = {
    zoom: 15,
    center: coordinates.value,
    mapTypeControl: true,
    mapTypeControlOptions: {
      style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
      position: google.maps.ControlPosition.TOP_RIGHT,
    },
    mapTypeId: google.maps.MapTypeId.ROADMAP,
    // mapId: "DEMO_MAP_ID", // Required for Advanced Markers
  };

  map.value = new google.maps.Map(
    document.getElementById(props.name),
    mapOptions
  );

  // Add advanced marker (requires marker library to be loaded)
  if (
    mapId &&
    window.google.maps.marker &&
    window.google.maps.marker.AdvancedMarkerElement
  ) {
    marker.value = new google.maps.marker.AdvancedMarkerElement({
      position: coordinates.value,
      map: map.value,
    });
  } else {
    // Fallback to regular marker if advanced markers not available
    marker.value = new google.maps.Marker({
      position: coordinates.value,
      map: map.value,
    });
  }

  mapLoaded.value = true;
};

onMounted(() => {
  // Check if Google Maps API is loaded
  if (window.google && window.google.maps) {
    initGoogleMap();
  } else {
    // Load Google Maps API
    googleMapScript.setAttribute(
      "src",
      "https://maps.googleapis.com/maps/api/js?key=" +
        window.google_maps_api_key +
        "&libraries=marker&loading=async"
    );
    document.head.appendChild(googleMapScript);

    googleMapScript.addEventListener("load", () => {
      if (window.google && window.google.maps && window.google.maps.Geocoder) {
        initGoogleMap();
      } else {
        console.error("Google Maps failed to load.");
      }
    });
  }
});

watch(
  () => props.postcode,
  (newVal, oldVal) => {
    if (newVal && newVal !== oldVal) {
      geocodePostcode();
    }
  }
);
</script>

<style scoped>
.map-container {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}
</style>
