<template>
  <div class="q-mb-md">
    <q-input
      v-model="search"
      :label="label"
      @input="onChange"
      @keydown.down.prevent="onArrowDown"
      @keydown.up.prevent="onArrowUp"
      @keydown.enter.prevent="onEnter"
      outlined
      dense
      class="autocomplete-input"
      :hide-bottom-space="!isOpen"
    >
      <template v-if="isLoading" v-slot:append>
        <q-spinner color="primary" size="1.5em" />
      </template>
    </q-input>
    
    <q-menu
      v-model="isOpen"
      fit
      anchor="bottom start"
      self="top start"
      :offset="[0, 4]"
      style="width: 100%"
      :target="() => document.querySelector('.autocomplete-input')"
    >
      <q-list dense style="min-width: 100%">
        <q-item
          v-for="(result, i) in results"
          :key="i"
          clickable
          @click="setResult(result)"
          :active="i === arrowCounter"
          active-class="bg-primary text-white"
        >
          <q-item-section>
            {{ result }}
          </q-item-section>
        </q-item>
        
        <q-item v-if="isLoading">
          <q-item-section>
            <div class="text-center">
              <q-spinner color="primary" size="1.5em" />
              <div class="q-mt-sm">Loading results...</div>
            </div>
          </q-item-section>
        </q-item>
        
        <q-item v-if="results.length === 0 && !isLoading && search.length > 0">
          <q-item-section>
            <div class="text-center text-grey">No results found</div>
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { useEventBus } from '../events/event-bus';

const emit = defineEmits(['typeahead', 'autoSelected', 'clearAutocomplete']);

const props = defineProps({
  items: {
    type: Array,
    required: false,
    default: () => [],
  },
  isAsync: {
    type: Boolean,
    required: false,
    default: false,
  },
  inLine: {
    type: Boolean,
    required: false,
    default: true
  },
  label: {
    type: String,
    required: false,
    default: ""
  }
});

const isOpen = ref(false);
const results = ref([]);
const search = ref('');
const isLoading = ref(false);
const arrowCounter = ref(-1);
const eventBus = useEventBus();

const onChange = () => {
  // Let's emit to the parent that a change was made
  eventBus.emit('typeahead', search.value);
  arrowCounter.value = -1;

  // Is the data given by an outside ajax request?
  if (props.isAsync) {
    isLoading.value = true;
  } else {
    // Let's filter our array
    filterResults();
    isOpen.value = Boolean(search.value);
  }
};

const filterResults = () => {
  // first uncapitalize all the things
  results.value = props.items.filter((item) => {
    return item.toLowerCase().indexOf(search.value.toLowerCase()) > -1;
  });
};

const setResult = (result) => {
  eventBus.emit('autoSelected', result);
  search.value = result;
  isOpen.value = false;
};

const onArrowDown = (evt) => {
  if (results.value.length > 0 && arrowCounter.value < results.value.length - 1) {
    arrowCounter.value = arrowCounter.value + 1;
  }
};

const onArrowUp = () => {
  if (arrowCounter.value > 0) {
    arrowCounter.value = arrowCounter.value - 1;
  }
};

const onEnter = () => {
  if (arrowCounter.value >= 0 && results.value[arrowCounter.value]) {
    eventBus.emit('autoSelected', results.value[arrowCounter.value]);
    search.value = results.value[arrowCounter.value];
    isOpen.value = false;
    arrowCounter.value = -1;
  }
};

const handleClickOutside = (evt) => {
  // Check if click target is outside of the autocomplete component
  const el = document.querySelector('.autocomplete-input');
  if (el && !el.contains(evt.target)) {
    isOpen.value = false;
    arrowCounter.value = -1;
  }
};

// Watch for changes in the items prop
watch(() => props.items, (newVal, oldVal) => {
  // actually compare them
  if (newVal.length !== oldVal.length) {
    results.value = newVal;
    isLoading.value = false;
    isOpen.value = newVal.length > 0 && search.value.length > 0;
  }
}, { deep: true });

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  
  // Listen for clearAutocomplete event
  eventBus.on("clearAutocomplete", () => {
    search.value = '';
    isOpen.value = false;
  });
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.autocomplete-input {
  width: 100%;
}
</style>
