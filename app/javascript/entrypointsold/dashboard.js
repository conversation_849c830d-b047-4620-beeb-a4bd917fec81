import Vue from 'vue/dist/vue.esm';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';

import BootstrapVue from 'bootstrap-vue';

import 'bootstrap-vue/dist/bootstrap-vue.css';

import axios from 'axios';
import VeeValidate from 'vee-validate';
import VueRouter from 'vue-router';
import Vue2Filters from 'vue2-filters';

import VueClipboard from 'vue-clipboard2';

import '@/element_custom_styles/index.css';

import dayjs from 'dayjs';

import MainDash from '../dashboard/main.vue';
import Element from 'element-ui';
import locale from 'element-ui/lib/locale/lang/en';

//TODO may switch to built in alerts from element
import VueSweetAlert from 'vue-sweetalert2';
import VueQuillEditor from 'vue-quill-editor';

let token = document.getElementsByName('csrf-token')[0].getAttribute('content');

axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] =
    'no-cache,no-store,must-revalidate,max-age=-1,private';

var quillToolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'], // toggled buttons
    [
        {
            header: 1
        },
        {
            header: 2
        }
    ], // custom button values
    [
        {
            list: 'ordered'
        },
        {
            list: 'bullet'
        }
    ],
    [
        {
            indent: '-1'
        },
        {
            indent: '+1'
        }
    ], // outdent/indent
    [
        {
            direction: 'rtl'
        }
    ], // text direction

    [
        {
            size: ['small', false, 'large', 'huge']
        }
    ], // custom dropdown
    [
        {
            header: [1, 2, 3, 4, 5, 6, false]
        }
    ],

    [
        {
            color: []
        },
        {
            background: []
        }
    ], // dropdown with defaults from theme
    [
        {
            font: []
        }
    ],
    [
        {
            align: []
        }
    ]
];

var quillOptions = {
    modules: {
        toolbar: quillToolbarOptions
    }
};

Vue.prototype.$http = axios;

Vue.use(VueSweetAlert);

Vue.use(VueQuillEditor, quillOptions);

Vue.use(Vue2Filters);

Vue.use(VueClipboard);

Vue.use(Vuex);
Vue.use(Element, {
    locale
});
Vue.use(BootstrapVue);
Vue.use(VeeValidate);
Vue.use(VueRouter);
// Vue.use(VueToastr);

Vue.filter('formatDate', function(value) {
    if (value) {
        return dayjs(value).format('DD/MM/YYYY');
    }
});

Vue.filter('formatTime', function(value) {
    if (value) {
        return dayjs(value).format('HH:mm');
    }
});

Vue.filter('formatDateAndTime', function(value) {
    if (value) {
        return (
            dayjs(value).format('DD/MM/YYYY') +
            ' at ' +
            dayjs(value).format('HH:mm')
        );
    }
});

import IdleVue from 'idle-vue';

const eventsHub = new Vue();

const veuxPlugins = [createPersistedState()];

Vue.use(IdleVue, {
    eventEmitter: eventsHub,
    idleTime: 900000
});

const store = new Vuex.Store({
    state: {
        event: {},
        eventBooking: null,
        selectedTickets: null,
        bookerRegResponses: [],
        bookerDetails: null,
        fees: null,
        readonly: false,
        bookingToken: null,
        importContactJobID: null,
        removeContactJobID: null,
        chargeable: true,
        unconfirmedCount: 0
    },
    actions: {},
    plugins: veuxPlugins,
    mutations: {
        refreshState(state, event_id) {
            if (state.event.id != event_id) {
                state.importContactJobID = null;
                state.removeContactJobID = null;
            }
            state.eventBooking = null;
            state.selectedTickets = null;
            state.bookerRegResponses = [];
            state.fees = null;
            state.readonly = false;
            state.bookingToken = null;
        },
        setSelectedTickets(state, tickets) {
            state.selectedTickets = tickets;
        },

        setEvent(state, event) {
            state.event = event;
        },

        setEventBooking(state, eventBooking) {
            state.eventBooking = eventBooking;
        },

        setBookerRegResponses(state, responses) {
            state.bookerRegResponses = responses;
        },

        setBookerDetails(state, bookerDetails) {
            bookerDetails.user_type = 'booker';
            state.bookerDetails = bookerDetails;
        },

        setFees(state, fees) {
            state.fees = fees;
        },

        setReadOnly(state, readonly) {
            state.readonly = readonly;
        },

        setBookingToken(state, token) {
            state.bookingToken = token;
        },

        setImportContactJobID(state, job_id) {
            state.importContactJobID = job_id;
        },

        setRemoveContactJobID(state, job_id) {
            state.removeContactJobID = job_id;
        },

        setChargeable(state, hasPaidTickets) {
            state.chargeable = hasPaidTickets;
        },

        setUnconfirmedCount(state, count) {
            state.unconfirmedCount = count;
        }
    },
    getters: {
        getEvent: state => state.event,
        getEventBooking: state => state.eventBooking,
        getBookerDetails: state => state.bookerDetails,
        getFees: state => state.fees,
        getReadOnly: state => state.readonly,
        getBookingToken: state => state.bookingToken,
        getImportContactJobID: state => state.importContactJobID,
        getRemoveContactJobID: state => state.removeContactJobID,
        getChargeable: state => state.chargeable,
        getUnconfirmedCount: state => state.unconfirmedCount
    },
    modules: {}
});

new Vue({
    store: store,

    components: {
        mainDash: MainDash
    },

    data: function() {
        return {
            event: window.event_json,
            hasPaidTickets: window.chargeable == true
        };
    },

    onIdle() {
        this.$swal({
            title: 'You Have Timed Out',
            text: 'Please login again!',
            type: 'warning',
            showCancelButton: false,
            confirmButtonColor: '#FF9500'
        }).then(result => {
            window.location.href = '/logout';
        });
    },

    created() {
        this.$store.commit('refreshState', this.event.id);
        this.$store.commit('setEvent', this.event);
        this.$store.commit('setChargeable', this.hasPaidTickets);
    },

    template: `
    <div id="app">
		<main-dash></main-dash>
    </div>
  `
}).$mount('#dashboard');
