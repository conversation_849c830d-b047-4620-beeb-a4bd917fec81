import Vue from "vue/dist/vue.esm";
import Vuex from "vuex";

import BootstrapVue from "bootstrap-vue";

import "bootstrap-vue/dist/bootstrap-vue.css";

import axios from "axios";
import VeeValidate from "vee-validate";
import VueRouter from "vue-router";
import Vue2Filters from "vue2-filters";

import lodash from "lodash";

Vue.use(lodash)

// import VueClipboard from "vue-clipboard2";

import "@/element_custom_styles/index.css";

import dayjs from "dayjs";

import mainAdmin from "../admin/main-admin.vue";

import Element from "element-ui";
import locale from "element-ui/lib/locale/lang/en";

import VueSweetAlert from 'vue-sweetalert2'

Vue.use(VueSweetAlert)

let token = document.getElementsByName("csrf-token")[0].getAttribute("content");

axios.defaults.headers.common["X-CSRF-Token"] = token;
axios.defaults.headers.common["Accept"] = "application/json";
axios.defaults.headers.common["Cache-Control"] =
  "no-cache,no-store,must-revalidate,max-age=-1,private";

Vue.prototype.$http = axios;

Vue.use(Vue2Filters);

Vue.use(Vuex);

Vue.use(Element, {
  locale
});

Vue.use(BootstrapVue);
const veeConfig = {
  fieldsBagName: "fieldss"
};
Vue.use(VeeValidate, veeConfig);
Vue.use(VueRouter);

Vue.filter("formatDate", function (value) {
  if (value) {
    return dayjs(value).format("DD/MM/YYYY");
  }
});

Vue.filter("formatTime", function (value) {
  if (value) {
    return dayjs(value).format("HH:mm");
  }
});

Vue.filter("formatDateAndTime", function (value) {
  if (value) {
    return (
      dayjs(value).format("DD/MM/YYYY") +
      " at " +
      dayjs(value).format("HH:mm")
    );
  }
});

import IdleVue from "idle-vue";

const eventsHub = new Vue();

Vue.use(IdleVue, {
  eventEmitter: eventsHub,
  idleTime: 900000
});

new Vue({
  // router: router,
  // store: store,

  components: {
    mainAdmin: mainAdmin
  },

  data: function () {
    return {
      event: window.event_json
    };
  },

  onIdle() {
    this.$swal({
      title: "You Have Timed Out",
      text: "Please login again!",
      type: "warning",
      showCancelButton: false,
      confirmButtonColor: "#FF9500"
    }).then(result => {
      window.location.href = "/logout";
    });
  },

  template: `
    <div id="app">
		<main-admin></main-admin>
    </div>
  `
}).$mount("#hg-admin");
