import "es6-object-assign/auto";
import 'url-search-params-polyfill';
import Vee<PERSON>alidate from "vee-validate";
import axios from "axios";
import ElementUI from "element-ui";
import locale from "element-ui/lib/locale/lang/en";
import Vue from "vue/dist/vue.esm";
import { update } from "lodash";
let token = document.getElementsByName("csrf-token")[0].getAttribute("content");
axios.defaults.headers.common["X-CSRF-Token"] = token;
axios.defaults.headers.common["Accept"] = "application/json";

Vue.prototype.$http = axios;

Vue.use(ElementUI, {
    locale
});

// get initial value:

window.imageFileName = null;

window.khubVueComponents = new Vue({
    el:'#khub-date-app',
    data:{
        date:(()=>{
            let el = document.getElementById('khub-date-picker');
            if(el) {
                const {initialValue} = el.dataset;
                return initialValue;
            } else {
                return null;
            }
        })(),
        headers: {"X-CSRF-Token":token},
        articleId:(()=>{
            let el = document.getElementById('khub-image-chooser');
            if(el) {
                const {articleId} = el.dataset;
                return articleId;
            } else {
                return null;
            }

        })(),
        imageFileName:(()=>{
            let el = document.getElementById('khub-image-chooser');
            if(el) {
                const {imageFileName} = el.dataset;
                return imageFileName;
            } else {
                return null;
            }

        })(),
        permittedFileTypes: ["image/png", "image/gif", "image/jpeg"],
    },
    methods: {
        deleteImage:function(a) {
            axios.post('/knowledge_hub_image_upload/remove_file',{
                articleId:this.articleId,
                filename:this.imageFileName
            }).then( ({data}) => {
                if(data.status == 200) {
                    this.imageFileName=null;
                }
            } );
        },
        uploadedImage:function(response) {
            if(response.status == 200) {
                this.imageFileName = response.filename;
            }
        },
        checkFileType:function(file) {
            if(this.permittedFileTypes.includes(file.type)){
              return true;
            } else {
              this.$notify.error({
                title: "Unsupported file type",
                message: "Only PNG, GIF, JPG and JPEG files allowed "
              });
              return false;
            }
        },
    }
});

jQuery(function($){
    window.updateFileList = (files) => {
        $('#uploaded-file-list').empty();
        if(Object.keys(files).length) {
            $('#no-articles').addClass("d-none");
        } else {
            $('#no-articles').removeClass("d-none");
        }
        Object.keys(files).forEach( key => {
            var count = 0;
            $('[data-delete-name]').each(function(){
                if($(this).attr("data-delete-name") == encodeURI(files[key])) {
                    count++;
                }
            })
            if(count<1) {
                var $container = $('<div class="text-center my-1 p-1">');
                $container.append('<img class="my-1" style="width:64px;" src="' + files[key] + '"/>');
                $container.append('<a target="_blank" href="' + files[key] + '"><small class="d-block">' + files[key] + '</small></a>');
                var $btn = $('<button class="btn btn-clear btn-sm" type="button" data-url="' + files[key] + '" data-name="' + key + '"><small>Delete</small></button>');
                $container.append($btn);
                $('#uploaded-file-list').append($container);
                $('button.btn[data-url]').on("click", function(e) {
                    e.preventDefault();
                    var url=encodeURI($(this).attr("data-url"));
                    var name=$(this).attr("data-name");
                    $('.quill-rt-editor img').each(function(){
                        if($(this).attr("src") == url) {
                            $(this).remove();
                        }
                    });
                    $(this).closest("form").append('<input type="hidden" name="knowledge_hub_article[delete_file][]" value="' + name +'" data-delete-name="'+ url +'"/>');
                    $(this).closest("div").empty().append('<div class="alert alert-secondary">This image will be deleted when you save your changes</div>');
                });
            }
        });
    };
    $(document).ready(function(){
        $('.form-has-quill-rt-editor').each(function() {
            $(this).find('.quill-rt-editor').each(function(){
                const axios = require('axios');
                var $quill = $(this);
                var quill = new Quill($quill.get(0), {
                    modules: {
                        toolbar: [
                            ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
                            ['blockquote', 'code-block', { 'header': 1 }, { 'header': 2 }, { 'script': 'sub'}, { 'script': 'super' }],
                            [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1'}, { 'indent': '+1' }],
                            [
                                { 'font': [] }, 
                                { 'size': ['small', false, 'large', 'huge'] },
                                { 'color': [] }, 
                                { 'background': [] }, 
                                { 'align': [] },
                                { 'header': [1, 2, 3, 4, 5, 6, false] },

                            ],
                            [ 'link', 'image'],
                            [{ 'direction': 'rtl' }],                         // text direction
                        ]
                    },
                    theme: "snow",
                    imageHandler: imgHandler
                });
                
                var imgHandler = (a,b,c) => {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');

                    input.onchange = () => {
                        const file = input.files[0];

                        // Ensure only images are uploaded
                        if (/^image\//.test(file.type)) {
                            //uploadImage(textEditor, file);
                            var formData = new FormData();
                            formData.append("file", input.files[0]);
                            formData.append("article_id",$('#khub-image-chooser').attr("data-article-id"));
                            axios.post('/knowledge_hub_articles/upload_article_image', formData, {
                                headers: {
                                    'Content-Type': 'multipart/form-data'
                                }
                            }).then( ({data}) => {
                                const range = quill.getSelection();
                                quill.insertEmbed(range.index,'image', encodeURI(data.filename))
                                $('[data-delete-name]').each(function(){
                                    if($(this).attr("data-delete-name") == encodeURI(data.filename)) {
                                        $(this).remove();
                                    }
                                })
                                updateFileList(data.files);
                            });
                        } else {
                            alert('Only images allowed');
                        }
                    };
                    input.click();

                };
                quill.getModule("toolbar").addHandler("image", imgHandler);

                

                $(this).closest('form').on("submit",function(e) {
                    var newContent=quill.root.innerHTML;
                    var currentContent=$(this).find('#'+$quill.attr('data-id')).val();
                    if(newContent!=currentContent) {
                        currentContent=$(this).find('#'+$quill.attr('data-id')).val(
                            newContent
                        );
                        $(this).submit();
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    } else {
                        return true;
                    }
                });
            });            
        });
    });
});