import Vue from "vue/dist/vue.esm";
import Vuex from "vuex";

import BootstrapVue from "bootstrap-vue";

import "bootstrap-vue/dist/bootstrap-vue.css";

import axios from "axios";
import VeeValidate from "vee-validate";
import VueRouter from "vue-router";
import Vue2Filters from "vue2-filters";

import '@/element_custom_styles/index.css'

import dayjs from "dayjs";

import Element from "element-ui";
import locale from "element-ui/lib/locale/lang/en";

// import mainOrgDash from './../orgdash/main-org-dash'

let token = document.getElementsByName("csrf-token")[0].getAttribute("content");

axios.defaults.headers.common["X-CSRF-Token"] = token;
axios.defaults.headers.common["Accept"] = "application/json";
axios.defaults.headers.common["Cache-Control"] =
    "no-cache,no-store,must-revalidate,max-age=-1,private";

Vue.prototype.$http = axios;

Vue.use(Vue2Filters);

Vue.use(Vuex);
Vue.use(Element, {
    locale
});
Vue.use(BootstrapVue);
Vue.use(VeeValidate);
Vue.use(VueRouter);

Vue.filter("formatDate", function (value) {
    if (value) {
        return dayjs(value).format("DD/MM/YYYY");
    }
});

Vue.filter("formatTime", function (value) {
    if (value) {
        return dayjs(value).format("HH:mm");
    }
});

Vue.filter("formatDateAndTime", function (value) {
    if (value) {
        return dayjs(value).format("DD/MM/YYYY") + " at " + dayjs(value).format("HH:mm");
    }
});

import IdleVue from 'idle-vue'

const eventsHub = new Vue()

// Vue.use(IdleVue, {
//     eventEmitter: eventsHub,
//     idleTime: 1500000
// })

new Vue({
    // router: router,
    // store: store,

    components: {
        mainOrgDash: mainOrgDash
    },

    data: function () {
        return {
            event: window.event_json,
        };
    },

    created() {
        var self = this
    },

    computed: {

    },

    template: `
    <div id="app">
		<register-user></register-user>
    </div>
  `
}).$mount("#loginreg");