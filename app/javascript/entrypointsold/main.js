import Vue from 'vue/dist/vue.esm';
import Vuex from 'vuex';

import BootstrapVue from 'bootstrap-vue';

import 'bootstrap-vue/dist/bootstrap-vue.css';

import axios from 'axios';
import VeeValidate from 'vee-validate';
import VueRouter from 'vue-router';
import Vue2Filters from 'vue2-filters';

import VueClipboard from 'vue-clipboard2';
import VueQuillEditor from 'vue-quill-editor';

import '@/element_custom_styles/index.css';

import dayjs from 'dayjs';

import MainSummary from '../main/main-summary.vue';

import Element from 'element-ui';
import locale from 'element-ui/lib/locale/lang/en';

import VueSweetAlert from 'vue-sweetalert2';

Vue.use(VueSweetAlert);

import lodash from 'lodash';

let token = document.getElementsByName('csrf-token')[0].getAttribute('content');

axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] =
    'no-cache,no-store,must-revalidate,max-age=-1,private';

Vue.prototype.$http = axios;

Vue.use(Vue2Filters);

Vue.use(VueClipboard);

var quillToolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'], // toggled buttons
    [
        {
            header: 1
        },
        {
            header: 2
        }
    ], // custom button values
    [
        {
            list: 'ordered'
        },
        {
            list: 'bullet'
        }
    ],
    [
        {
            indent: '-1'
        },
        {
            indent: '+1'
        }
    ], // outdent/indent
    [
        {
            direction: 'rtl'
        }
    ], // text direction

    [
        {
            size: ['small', false, 'large', 'huge']
        }
    ], // custom dropdown
    [
        {
            header: [1, 2, 3, 4, 5, 6, false]
        }
    ],

    [
        {
            color: []
        },
        {
            background: []
        }
    ], // dropdown with defaults from theme
    [
        {
            font: []
        }
    ],
    [
        {
            align: []
        }
    ]
];

var quillOptions = {
    modules: {
        toolbar: quillToolbarOptions
    }
};

Vue.use(VueQuillEditor, quillOptions);

Vue.use(Vuex);
Vue.use(Element, {
    locale
});
Vue.use(BootstrapVue);
const veeConfig = {
    fieldsBagName: 'fieldss'
};
Vue.use(VeeValidate, veeConfig);
Vue.use(VueRouter);
// Vue.use(VueToastr);

Vue.filter('formatDate', function(value) {
    if (value) {
        return dayjs(value).format('DD/MM/YYYY');
    }
});

Vue.filter('formatTime', function(value) {
    if (value) {
        return dayjs(value).format('HH:mm');
    }
});

Vue.filter('formatDateAndTime', function(value) {
    if (value) {
        return (
            dayjs(value).format('DD/MM/YYYY') +
            ' at ' +
            dayjs(value).format('HH:mm')
        );
    }
});

const store = new Vuex.Store({
    state: {
        orgContacts: [],
        summaryReport: []
    },
    mutations: {
        setOrgContacts(state, contacts) {
            state.orgContacts = [];
            state.orgContacts = contacts;
        },
        setSummaryReport(state, report) {
            state.summaryReport = [];
            state.summaryReport = report;
        },
        addOrgContact(state, contact) {
            state.orgContacts.push(contact);
        },
        removeContact(state, contactId) {
            var index = state.orgContacts.findIndex(x => x.id === contactId);
            state.orgContacts.splice(index, 1);
        },
        addContactTag(state, value) {
            var index = state.orgContacts.findIndex(x => x.id === value.id);
            state.orgContacts[index].tags.push(value.tag);
        },
        removeContactTag(state, value) {
            var contact_index = state.orgContacts.findIndex(
                x => x.id === value.id
            );
            var tag_index = state.orgContacts[contact_index].tags.findIndex(
                x => x === value.tag
            );
            state.orgContacts[contact_index].tags.splice(tag_index, 1);
        }
    },
    getters: {
        getOrgContacts: state => state.orgContacts,
        getSummaryReport: state => state.summaryReport
    }
});

import IdleVue from 'idle-vue';

const eventsHub = new Vue();

Vue.use(IdleVue, {
    eventEmitter: eventsHub,
    idleTime: 900000
});

new Vue({
    store: store,

    components: {
        mainSummary: MainSummary
    },

    data: function() {
        return {
            events: window.events_json
        };
    },

    onIdle() {
        this.$swal({
            title: 'You Have Timed Out',
            text: 'Please login again!',
            type: 'warning',
            showCancelButton: false,
            confirmButtonColor: '#FF9500'
        }).then(result => {
            if (result.value) {
                window.location.href = '/logout';
            }
        });
    },

    created() {
        var self = this;
    },

    template: `
    <div id="app">
		<main-summary></main-summary>
    </div>
  `
}).$mount('#orgdashboard');
