import 'es6-object-assign/auto';
import 'url-search-params-polyfill';

import Vue from 'vue/dist/vue.esm';
import Vuex from 'vuex';

import BootstrapVue from 'bootstrap-vue';

import 'bootstrap-vue/dist/bootstrap-vue.css';

import axios from 'axios';
import VeeValidate from 'vee-validate';
import VueRouter from 'vue-router';
import Vue2Filters from 'vue2-filters';
import SocialSharing from 'vue-social-sharing';

import '@/element_custom_styles/index.css';

import dayjs from 'dayjs';

import Start from '../book/startpage.vue';
import UserDetails from '../book/user_details/user-details.vue';
import Payment from '../book/payments/payment.vue';
import Summary from '../book/summary/summary.vue';
import Element from 'element-ui';
import locale from 'element-ui/lib/locale/lang/en';
import createPersistedState from 'vuex-persistedstate';

import VueSweetAlert from 'vue-sweetalert2';
Vue.use(VueSweetAlert);

import lodash from 'lodash';

let token = document.getElementsByName('csrf-token')[0].getAttribute('content');

axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] =
    'no-cache,no-store,must-revalidate,max-age=-1,private';

Vue.prototype.$http = axios;

Vue.use(Vue2Filters);

Vue.use(Vuex);

Vue.use(Element, {
    locale
});
Vue.use(BootstrapVue);
Vue.use(VeeValidate);
Vue.use(VueRouter);
Vue.use(SocialSharing);
// Vue.use(VueToastr);

Vue.filter('formatDate', function(value) {
    if (value) {
        return dayjs(value).format('DD/MM/YYYY');
    }
});

Vue.filter('formatTime', function(value) {
    if (value) {
        return dayjs(value).format('HH:mm');
    }
});

import IdleVue from 'idle-vue';

const eventsHub = new Vue();

try {
    const veuxPlugins = [createPersistedState()];
} catch (e) {
    $('#no-storage')
        .removeClass('d-none')
        .addClass('d-block');
}

const veuxPlugins = [createPersistedState()];

Vue.use(IdleVue, {
    eventEmitter: eventsHub,
    idleTime: 900000
});

const store = new Vuex.Store({
    state: {
        event: {},
        eventBooking: null,
        selectedTickets: null,
        bookerRegResponses: [],
        bookerDetails: null,
        fees: null,
        readonly: false,
        expired: false,
        editByOrg: false,
        bookingToken: null,
        assignedChildTickets: [],
        discCodes: [],
        refPath: '',
        extWebsite: null,
        payStart: null,
        chargeable: true,
        timeRemaining: 30,
        paymentMethod: null,
        paymentInfo: {},
        inProgress: false,
        timerDisplay: null
    },
    actions: {},
    plugins: veuxPlugins,
    mutations: {
        resetBooking(state) {
            state.event = {};
            state.eventBooking = null;
            state.selectedTickets = null;
            state.bookerRegResponses = [];
            state.bookerDetails = null;
            state.fees = null;
            state.readonly = false;
            state.expired = false;
            state.editByOrg = false;
            state.bookingToken = null;
            state.assignedChildTickets = [];
            state.discCodes = [];
            state.refPath = '';
            state.extWebsite = null;
            state.chargeable = true;
            state.paymentMethod = null;
            state.paymentInfo = {};
            (state.inProgress = false),
                (state.timeRemaining = 600),
                (state.timerDisplay = null);
        },

        setSelectedTickets(state, tickets) {
            state.selectedTickets = tickets;
        },

        setEvent(state, event) {
            state.event = event;
        },

        setEventBooking(state, eventBooking) {
            state.eventBooking = eventBooking;
        },

        setEventBookingPaymentType(state, paymentType) {
            state.eventBooking.payment_type = paymentType;
        },

        setEventBookingDiscount(state, discount) {
            state.eventBooking.discount_percentage = discount.discount_percentage;
            state.eventBooking.discount_code_id = discount.discount_code_id;
        },

        setPackageBookingDiscount(state, discount) {
            let packageBooking = state.eventBooking.package_bookings.find( pb => pb.id == discount.id);
            packageBooking.discount_amount = discount.discount_amount;
            packageBooking.discount_code_id = discount.discount_code_id;
            packageBooking.discount_type = discount.discount_type;

            // Force a refresh, vue2 nonsense
            state.eventBooking.package_bookings.push({})
            state.eventBooking.package_bookings.pop();
        },

        setBookerRegResponses(state, responses) {
            state.bookerRegResponses = responses;
        },

        setBookerDetails(state, bookerDetails) {
            bookerDetails.user_type = 'booker';
            state.bookerDetails = bookerDetails;
        },

        setFees(state, fees) {
            state.fees = fees;
        },

        setReadOnly(state, readonly) {
            state.readonly = readonly;
        },

        setExpired(state, expired) {
            state.expired = expired;
        },

        setEditByOrg(state, editor) {
            state.editor = editor;
        },

        setBookingToken(state, token) {
            state.bookingToken = token;
        },

        setDiscCode(state, code) {
            state.discCodes.push(code);
        },

        setRefPath(state, path) {
            state.refPath = path;
        },

        setExtWebsite(state, url) {
            state.extWebsite = url;
        },

        setChargeable(state, chargeable) {
            state.chargeable = chargeable;
        },

        setAssignedChildTicket(state, assignedTicket) {
            var existingAssignment = state.assignedChildTickets.find(
                assticket =>
                    assticket.package_id == assignedTicket.package_id &&
                    assticket.attendee_idx == assignedTicket.attendee_idx
            );

            if (existingAssignment) {
                existingAssignment.quantity_assigned =
                    assignedTicket.quantity_assigned;
            } else {
                state.assignedChildTickets.push(assignedTicket);
            }
        },

        removeAssignedChildTicket(state, assignedTicket) {
            var idx = state.assignedChildTickets.indexOf(assignedTicket);
            state.assignedChildTickets.splice(idx, 1);
        },
        setPayStart(state, start) {
            state.payStart = start;
        },
        setTimeRemaining(state, totalNoSecs) {
            state.timeRemaining = totalNoSecs;
        },
        setTimerDisplay(state, formattedTime) {
            state.timerDisplay = formattedTime;
        },
        setPaymentMethod(state, payMethod) {
            state.paymentMethod = payMethod;
        },
        setPaymentInfo(state, paymentInfo) {
            state.paymentInfo = paymentInfo;
        },
        setInProgress(state, prog_state) {
            state.inProgress = prog_state;
        }
    },
    getters: {
        getSelectedTickets: state => state.selectedTickets,
        getEvent: state => state.event,
        getEventId: state => state.event.id,
        getEventBooking: state => state.eventBooking,
        getEventBookingId: state => state.eventBooking.id,
        getBookerDetails: state => state.bookerDetails,
        getFees: state => state.fees,
        getReadOnly: state => state.readonly,
        getExpired: state => state.expired,
        getBookingToken: state => state.bookingToken,
        getEditByOrg: state => state.editor,
        getDiscCodes: state => state.discCodes,
        getRefPath: state => state.refPath,
        getExtWebsite: state => state.extWebsite,
        getPayStart: state => state.payStart,
        getChargeable: state => state.chargeable,
        getTimeRemaining: state => state.timeRemaining,
        getTimerDisplay: state => state.timerDisplay,
        getPaymentMethod: state => state.paymentMethod,
        getPaymentInfo: state => state.paymentInfo,
        getInProgress: state => state.inProgress,

        getChildTicketAssignments: state => ticket_id => {
            return state.assignedChildTickets.find(
                assignedTicket => assignedTicket.package_id === ticket_id
            );
        },

        getChildTicketTotalBooked: state => ticket_id => {
            var ticketsAssigned = state.assignedChildTickets.filter(
                assignedTicket => assignedTicket.package_id === ticket_id
            );
            var total = 0;

            if (ticketsAssigned) {
                ticketsAssigned.forEach(
                    assigned => (total += assigned.quantity_assigned)
                );
            }

            return total;
        },

        getTotalTicketsRemaingForOthers: state => (ticket_id, attendee_idx) => {
            var ticketsAssigned = state.assignedChildTickets.filter(
                assignedTicket => {
                    return (
                        assignedTicket.package_id === ticket_id &&
                        assignedTicket.attendee_idx != attendee_idx
                    );
                }
            );

            var total = 0;

            if (ticketsAssigned) {
                ticketsAssigned.forEach(
                    assigned => (total += assigned.quantity_assigned)
                );
            }

            return total;
        }
    },
    modules: {}
});

// export default store

//Examples
// store.commit('setSelectedTickets', tickets);

const router = new VueRouter({
    routes: [
        {
            path: '/',
            component: Start,
            name: 'home'
        },
        {
            path: '/user-details',
            name: 'userdetails',
            component: UserDetails
        },
        {
            path: '/payment',
            name: 'payment',
            component: Payment
        },
        {
            path: '/summary',
            name: 'summary',
            component: Summary
        },
        {
            path: '/summaryfree',
            name: 'summaryfree',
            component: Summary
        },
        {
            path: '*',
            redirect: '/'
        }
    ],
    scrollBehavior(to, from, savedPosition) {
        return {
            x: 0,
            y: 0
        };
    }
});

new Vue({
    router: router,
    store: store,

    data: function() {
        return {
            eventBooking: window.myEventBooking,
            event: window.myEventBooking.event,
            readonly: false,
            declined: false
        };
    },

    onIdle() {
        var path = this.$store.getters.getRefPath;
        this.$swal({
            title: 'Your Session Has Timed Out',
            text: 'Please start again!',
            type: 'warning',
            showCancelButton: false,
            confirmButtonColor: '#FF9500'
        }).then(result => {
            window.location.href = path;
        });
    },

    created() {
        this.setUpBooking();
    },

    methods: {
        setUpBooking() {
            console.log(this.eventBooking.id);
            var self = this;
            var currentEvent = this.$store.getters.getEvent;
            var currentBooking = this.$store.getters.getEventBooking;

            if (
                this.$route.name == 'home' &&
                (currentEvent == undefined ||
                    this.event.id != this.$store.getters.getEventId ||
                    currentBooking == undefined ||
                    this.eventBooking.id == undefined ||
                    this.eventBooking.id !=
                        this.$store.getters.getEventBookingId ||
                    this.eventBooking.booking_count == 0)
            ) {
                console.log('resetting.....');
                this.$store.commit('resetBooking');
                this.$store.commit('setEvent', this.event);
                this.$store.commit('setEventBooking', this.eventBooking);
                this.$store.commit('setEditByOrg', window.editbyorg);
                this.$store.commit('setRefPath', window.refPath);
                this.$store.commit(
                    'setChargeable',
                    window.chargeable == 'true'
                );
                this.$store.commit('setPayStart', null);
            }

            if (
                this.eventBooking &&
                this.eventBooking.booking_count > 0 &&
                !this.eventBooking.cancelled_at
            ) {
                this.readonly = true;
                this.$store.commit('setReadOnly', true);
            }

            if (this.eventBooking && this.eventBooking.cancelled_at) {
                var header = `Hi ${window.booker}`;
                var msg = `Your booking for the ${this.event.title} has been cancelled. If you have any questions about this please contact the event organiser: ${this.event.organiser} (${this.event.organiser_email}) `;
                this.$swal({
                    title: header,
                    text: msg,
                    type: 'warning'
                });
            }

            if (
                this.eventBooking &&
                this.eventBooking.declined &&
                this.eventBooking.booking_count == 0
            ) {
                this.declined = true;
                var messageTitle =
                    'Please Confirm If You Wish To Decline this Event!';
                var messageHtml =
                    '<p>Please type <strong>your email</strong> into the box below to continue</pr>';

                if (this.eventBooking.opted_out) {
                    var messageTitle =
                        'Please Confirm If You Wish To Decline this Event AND Opt Out of Further Communications!';
                    var messageHtml =
                        '<p>You will no longer receive event invites or any other emails concerning events from us</p><p>Please type <strong>your email</strong> into the box below to continue</pr>';
                }

                self.$swal({
                    title: messageTitle,
                    html: messageHtml,
                    input: 'text',
                    allowEnterKey: false,
                    showCancelButton: true,
                    confirmButtonText: 'Continue',
                    showLoaderOnConfirm: true,
                    preConfirm: function(text) {
                        return new Promise(function(resolve) {
                            setTimeout(function() {
                                var userEmail =
                                    self.eventBooking.user_email || '';
                                if (
                                    !text ||
                                    text.toLowerCase() !=
                                        userEmail.toLowerCase()
                                ) {
                                    self.$swal.showValidationError(
                                        'Please enter your email if you wish to continue!'
                                    );
                                    resolve();
                                } else {
                                    resolve();
                                }
                            }, 1000);
                        });
                    },
                    allowOutsideClick: false
                }).then(function(result) {
                    if (result.value) {
                        self.$http
                            .put(
                                '/registered_users/' +
                                    self.eventBooking.registered_user_id +
                                    '/decline',
                                {
                                    declined: true,
                                    opted_out: self.eventBooking.opted_out
                                }
                            )
                            .then(function() {
                                if (self.eventBooking.opted_out) {
                                    self.$swal(
                                        'Declined & Opted Out!',
                                        'You have declined this event, and opted out of further communications regarding this organisers events, the organiser has been notified!',
                                        'success'
                                    );
                                } else {
                                    self.$swal(
                                        'Declined!',
                                        'You have declined this event, the organiser has been notified!',
                                        'success'
                                    );
                                }
                            });
                    }
                });
            }

            if (
                this.eventBooking &&
                this.eventBooking.declined &&
                this.eventBooking.booking_count > 0
            ) {
                this.declined = true;
                self.$swal({
                    title: 'Cannot decline event',
                    text:
                        'You are already booked on this event, do you wish to cancel your booking?',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#DD6B55',
                    allowEnterKey: false,
                    confirmButtonText: 'Cancel Booking',
                    cancelButtonText: 'Review Booking',
                    closeOnConfirm: false
                }).then(function(result) {
                    if (result.value) {
                        var that = self;
                        self.$swal({
                            title: 'Are you sure?',
                            text: 'This will permanently cancel this booking',
                            allowEnterKey: false,
                            showCancelButton: true,
                            confirmButtonColor: '#ff9500',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Yes, remove it!'
                        }).then(function(result) {
                            if (result.value) {
                                self.$http
                                    .put(
                                        `/event_bookings/${that.eventBooking.uuid}/cancel`
                                    )
                                    .then(
                                        function() {
                                            that.eventBooking.booking_count = 0;
                                            that.eventBooking.cancelled_at = Date.now();
                                            that.$store.commit(
                                                'setEventBooking',
                                                that.eventBooking
                                            );
                                            that.$notify.success({
                                                message:
                                                    'The booking has been cancelled',
                                                title: 'Success'
                                            });
                                            that.$router.push('summary');
                                        },
                                        function() {
                                            that.$notify.error({
                                                message:
                                                    'The booking has not been cancelled',
                                                title: 'Error'
                                            });
                                        }
                                    );
                            }
                        });
                    }
                });
            }
        }
    },

    computed: {
        timer() {
            return this.$store.getters.getTimerDisplay;
        },

        topLineOverride() {
            return {
                borderTop: '4px solid ' + this.event.phcolour
            };
        },

        active() {
            var routeName = this.$route['name'];

            if (routeName == 'home') {
                return 0;
            } else if (routeName == 'userdetails') {
                return 1;
            } else if (routeName == 'payment') {
                return 2;
            } else if (routeName == 'summary') {
                return 3;
            }
        },

        showAttendees() {
            if (this.event.show_add_attendees) {
                return true;
            } else {
                return false;
            }
        },

        chargeable() {
            return this.$store.getters.getChargeable;
        }
    },

    template: `
      <div class="container" id="app">
      <div class="row">
        <div class="col">
          <div class="card hg-topline" style="padding-top: 20px; padding-bottom: 10px; " :style="topLineOverride">
            <el-steps :active="active" finish-status="success" align-center>
              <el-step title="Choose Tickets"></el-step>
              <el-step v-if="showAttendees" title="Add Attendees"></el-step>
              <el-step v-if="chargeable && !readonly" title="Payments"></el-step>
              <el-step v-if="!readonly || declined" title="Summary"></el-step>
            </el-steps>
            <div v-if="timer" class="timer">
                    <span>
                    {{ timer }}
                    </span>
            </div>
          </div>
        </div>
      </div>
      <router-view class="view"></router-view>
      </div>
    `
}).$mount('#booking');
