import Vue from "vue/dist/vue.esm";

import BootstrapVue from "bootstrap-vue";

import "bootstrap-vue/dist/bootstrap-vue.css";

import EventsWrapper from "../widget/events-wrapper.vue";

import axios from "axios";

import SocialSharing from "vue-social-sharing";

import "@/element_custom_styles/index.css";

import Element from 'element-ui'
import locale from 'element-ui/lib/locale/lang/en'

Vue.prototype.$http = axios;

Vue.use(BootstrapVue);
Vue.use(SocialSharing);
Vue.use(Element, { locale })

new Vue({
  components: {
    EventsWrapper
  }
}).$mount("#widgetview");