<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";

// Import images
import indigoSparkLogo from "@/assets/IndigoSparkLogo.jpg";
import indigoSparkCircularLogo from "@/assets/IndigoSparkCircularLogo.jpg";
import facebookIcon from "@/assets/facebook.png";
import linkedinIcon from "@/assets/linkedin.png";
import xIcon from "@/assets/x.png";
import linkedIcon from "@/assets/Linked.png";

// Reactive state
const leftDrawerOpen = ref(false);
const router = useRouter();
const authStore = useAuthStore();

// Computed property to get authentication state from store
const isLoggedIn = computed(() => {
  return authStore.authenticated;
});

const goHome = () => {
  router.push("/");
};

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const navigateToRegister = () => {
  router.push("/register");
  leftDrawerOpen.value = false; // Close drawer on mobile
};

const navigateToLogin = () => {
  router.push("/login");
  leftDrawerOpen.value = false; // Close drawer on mobile
};

const goToApp = () => {
  leftDrawerOpen.value = false; // Close drawer on mobile
  window.location.href = "/dashboard";
};

const signOut = async () => {
  try {
    await authStore.logout();
    router.push("/login");
  } catch (error) {
    console.error("Logout error:", error);
  }
  leftDrawerOpen.value = false; // Close drawer on mobile
};
</script>

<template>
  <q-layout container view="hhh lpR fff">
    <q-header class="bg-white">
      <q-toolbar>
        <div class="lt-md">
          <q-btn
            flat
            round
            dense
            icon="menu"
            color="primary"
            @click="toggleLeftDrawer"
          />
        </div>
        <q-toolbar-title class="q-mr-md" @click="goHome">
          <img
            class="q-ml-md logo"
            :src="indigoSparkLogo"
            alt="Indigo Spark Logo"
        /></q-toolbar-title>
        <div class="gt-sm">
          <template v-if="!isLoggedIn">
            <q-btn
              flat
              unelevated
              class="q-mr-sm"
              color="primary"
              label="Register"
              @click="navigateToRegister"
            />
            <q-btn
              flat
              unelevated
              class="q-mr-sm"
              color="primary"
              label="Login"
              @click="navigateToLogin"
            />
          </template>
          <template v-else>
            <q-btn
              flat
              unelevated
              class="q-mr-sm"
              color="primary"
              label="Go To App"
              @click="goToApp"
            />
            <q-btn
              flat
              unelevated
              class="q-mr-sm"
              color="primary"
              label="Logout"
              @click="signOut"
            />
          </template>
          <q-btn
            unelevated
            class="q-mr-sm"
            color="primary"
            label="REQUEST DEMO"
          />
          <q-btn
            unelevated
            class="q-mr-sm"
            color="primary"
            label="CONTACT US"
          />
        </div>
      </q-toolbar>
    </q-header>
    <q-drawer
      side="left"
      bordered
      v-model="leftDrawerOpen"
      :width="260"
      :breakpoint="700"
      class="bg-primary text-white"
    >
      <q-scroll-area class="fit">
        <q-list padding class="menu-list">
          <template v-if="!isLoggedIn">
            <q-item
              class="bg-primary"
              clickable
              v-ripple
              @click="navigateToRegister"
            >
              <q-item-section avatar>
                <q-icon name="app_registration" />
              </q-item-section>

              <q-item-section> Register </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateToLogin">
              <q-item-section avatar>
                <q-icon name="login" />
              </q-item-section>

              <q-item-section> Login </q-item-section>
            </q-item>
          </template>
          <template v-else>
            <q-item clickable v-ripple @click="goToApp">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>

              <q-item-section> Go To App </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="signOut">
              <q-item-section avatar>
                <q-icon name="logout" />
              </q-item-section>

              <q-item-section> Logout </q-item-section>
            </q-item>
          </template>

          <q-item clickable v-ripple>
            <q-item-section avatar>
              <q-icon name="send" />
            </q-item-section>

            <q-item-section> Request Demo </q-item-section>
          </q-item>

          <q-item clickable v-ripple>
            <q-item-section avatar>
              <q-icon name="phone" />
            </q-item-section>

            <q-item-section> Contact Us </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>
    <q-page-container>
      <router-view></router-view>
    </q-page-container>
    <q-footer class="bg-white text-black height-hint-50">
      <div class="row">
        <q-space />
        <div class="col-12 col-md justify-around">
          <q-img
            class="q-ml-xl q-mt-lg"
            :src="indigoSparkCircularLogo"
            style="height: 150px; width: 150px"
          />
        </div>
        <div class="col-12 col-md justify-around">
          <div
            class="q-list text-h6 text-weight-medium text q-mt-sm q-mb-sm q-ml-lg"
          >
            GO TO
          </div>
          <div class="text-h7 text-weight-regular q-ml-lg">
            How does it work?
          </div>
          <div class="text-h7 text-weight-regular q-ml-lg">
            Indigo Events App
          </div>
          <div class="text-h7 text-weight-regular q-ml-lg">
            Indigo Feedback App
          </div>
          <div class="text-h7 text-weight-regular q-ml-lg">
            Indigo Custom Apps
          </div>
          <div class="text-h7 text-weight-regular q-ml-lg">Free Trial</div>
        </div>
        <div class="col-12 col-md justify-around">
          <div
            class="q-list text-h6 text-weight-medium q-mt-sm q-mb-sm q-ml-lg"
          >
            CONTACT US
          </div>
          <div class="text-h7 text-weight-regular q-ml-lg">
            <EMAIL>
          </div>
          <div class="text-h7 text-weight-regular q-ml-lg">0344 822 3227</div>
          <div class="text-h7 text-weight-regular q-ml-lg">About Us</div>
          <div class="text-h7 text-weight-regular q-ml-lg">Contact</div>
        </div>
        <div class="col-12 col-md justify-around">
          <div
            class="q-list text-h6 text-weight-medium q-mt-sm q-mb-sm q-ml-xl"
          >
            FOLLOW US
          </div>
          <div class="q-mb-lg q-ml-xl">
            <q-img :src="facebookIcon" style="height: 40px; max-width: 40px" />
            <q-img :src="linkedinIcon" style="height: 40px; max-width: 40px" />
            <q-img :src="xIcon" style="height: 40px; max-width: 40px" />
          </div>
          <div class="q-list text-h7 text-weight-medium q-ml-xl">
            READ OUR LATEST
          </div>
          <div class="q-list text-h7 text-weight-medium q-mb-md q-ml-xl">
            NEWS ON
          </div>
          <q-img
            class="q-ml-xl"
            :src="linkedIcon"
            alt="linkedicon"
            style="height: 30px; max-width: 100px"
          />
        </div>
        <q-space />
      </div>
      <div class="row">
        <q-space />
        <div class="col-12 col-md justify-around">
          <q-toolbar>
            <q-space />
            <q-tabs>
              <q-route-tab to="/page1" label="Policies" />
              <hr
                class="q-separator q-separator--vertical q-separator--vertical-inset q-separator--black q-mx-xs"
                aria-orientation="vertical"
              />
              <q-route-tab to="/page1" label="Website Usage" />
              <hr
                class="q-separator q-separator--vertical q-separator--vertical-inset q-separator--black q-mx-xs"
                aria-orientation="vertical"
              />
              <q-route-tab to="/page1" label="Privacy Policy" />
              <hr
                class="q-separator q-separator--vertical q-separator--vertical-inset q-separator--black q-mx-xs"
                aria-orientation="vertical"
              />
              <q-route-tab to="/page1" label="Cookie Policy" />
              <hr
                class="q-separator q-separator--vertical q-separator--vertical-inset q-separator--black q-mx-xs"
                aria-orientation="vertical"
              />
              <q-route-tab to="/page1" label="T&Cs" />
              <hr
                class="q-separator q-separator--vertical q-separator--vertical-inset q-separator--black q-mx-xs"
                aria-orientation="vertical"
              />
              <q-route-tab to="/page1" label="FAQs" />
            </q-tabs>
            <q-space />
          </q-toolbar>
        </div>
        <q-space />
      </div>

      <div class="row">
        <q-space />
        <div class="col-12 col-md justify-around center">
          &copy; Indigo Spark 2025 Registered Number: 345274. VAT Number:
          259425697245.
        </div>
      </div>
      <q-space />
    </q-footer>
  </q-layout>
</template>

<style lang="sass" scoped>
.menu-list .q-item
  border-radius: 0 32px 32px 0

.center
  text-align: center

.secondarybgcolor
  background-color: #f0efef

.logo
  width: 250px
  height: 120px
  cursor: pointer
</style>
