<template>
  <q-layout view="hHh lpR fFf">
    <AppBar :toggleDrawer="toggleDrawer" />
    <q-drawer
      showIfAbove
      :mini="miniState"
      v-model="leftDrawerOpen"
      bordered
      style="background-color: #f0efef"
      side="left"
      :width="260"
      :breakpoint="800"
    >
      <q-scroll-area class="fit" v-bind:style="!miniState ? sparkLogo : null">
        <q-list class="q-pb-sm q-pt-md">
          <q-item>
            <q-item-section avatar class="q-pr-sm" style="min-width: 30px">
              <q-icon
                name="person"
                size="sm"
                :color="miniState ? 'primary' : 'white'"
                class="q-ma-none q-pa-none"
            /></q-item-section>
            <q-item-section
              class="text-bold line-clamp-2"
              :class="miniState ? 'text-primary' : 'text-white'"
            >
              {{ currentUser }} (#{{ orgId }})
            </q-item-section>
          </q-item>
        </q-list>
        <q-list padding>
          <q-item class="justify-center">
            <q-btn
              :round="miniState"
              color="primary"
              icon="add"
              :size="miniState ? 'sm' : 'md'"
              :label="miniState ? '' : 'Create Event'"
              @click="siblingLink('/events/unified#/event/new')"
            />
          </q-item>
          <q-item to="/" active-class="text-secondary" v-ripple>
            <q-tooltip anchor="center right" self="center left"
              >My Events</q-tooltip
            >
            <q-item-section avatar><q-icon name="view_list" /></q-item-section>
            <q-item-section>My Events</q-item-section>
          </q-item>
          <q-separator />
          <template v-for="(menuItem, index) in menuList" :key="index">
            <q-item
              clickable
              :to="menuItem.route"
              @click="navigate(menuItem)"
              active-class="text-secondary"
              v-ripple
            >
              <q-tooltip
                v-if="miniState"
                anchor="center right"
                self="center left"
                >{{ menuItem.label }}</q-tooltip
              >
              <q-item-section avatar>
                <q-icon :name="menuItem.icon" />
              </q-item-section>
              <q-item-section>
                {{ menuItem.label }}
              </q-item-section>
              <q-item-section v-if="menuItem.external" avatar>
                <q-icon name="launch" />
              </q-item-section>
            </q-item>
            <q-separator :key="'sep' + index" v-if="menuItem.separator" />
          </template>
        </q-list>
      </q-scroll-area>
      <div class="absolute" style="top: 15px; right: -12px">
        <q-btn
          v-if="$q.screen.width > 800"
          dense
          round
          unelevated
          color="accent"
          size="sm"
          :icon="miniState ? 'chevron_right' : 'chevron_left'"
          @click="miniState = !miniState"
        />
      </div>
    </q-drawer>
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>
<script setup>
import { ref, computed } from "vue";
import { useQuasar } from "quasar";
import { useRouter } from "vue-router";

import { useMainStore } from "@/stores/main.js";
import { useAuthStore } from "@/stores/auth";
import AppBar from "@/shared/components/AppBar.vue";
import { storeToRefs } from "pinia";
//import sparkImage from "@/assets/IndigoSparkCircularLogoTransparent.png";
import sparkImage from "@/assets/DrawerBackground.png";

const $q = useQuasar();
const router = useRouter();
const miniState = ref(false);
const authStore = useAuthStore();
const store = useMainStore();

const { leftDrawerOpen } = storeToRefs(store);

// Use auth store for current user instead of main store
const currentUser = computed(() => {
  try {
    return authStore.user?.name || authStore.user?.email || "";
  } catch (error) {
    console.error("Auth store not initialized:", error);
    return "";
  }
});

//todo: add a background?
//v-bind:style="sparkLogo"
const sparkLogo = computed(() => ({
  background: `url(${sparkImage}) no-repeat top`,
  backgroundSize: "100%",
}));

const website = computed(() => {
  try {
    return store.getWebsite;
  } catch (error) {
    console.error("Store not initialized:", error);
    return "";
  }
});

const orgId = computed(() => {
  try {
    return store.getOrgId;
  } catch (error) {
    console.error("Store not initialized:", error);
    return "";
  }
});

function toggleMini(e) {
  miniState.value = !miniState.value;

  // notice we have registered an event with capture flag;
  // we need to stop further propagation as this click is
  // intended for switching drawer to "normal" mode only
  e.stopPropagation();
}

function toggleDrawer() {
  store.setLeftDrawerOpen(store.leftDrawerOpen);
}

const navigate = (menuItem) => {
  if (menuItem.external) {
    externalLink(menuItem.route);
  } else if (menuItem.sibling) {
    siblingLink(menuItem.route);
  } else {
    router.push(menuItem.route);
  }
};

const siblingLink = (url) => {
  window.location.href = url;
};

const externalLink = (url) => {
  console.log("going out");
  $q.dialog({
    title: "Warning",
    message: "This will redirect you away from eventstop. Continue?",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    window.open(url, "_blank");
  });
};

const logout = async () => {
  await authStore.logout();
  window.location.href = "/login";
};

const menuList = [
  /*{
    icon: "dashboard",
    label: "Home",
    route: "events",
  },*/
  {
    icon: "credit_card",
    label: "Manage Payments",
    route: "payment-admin",
  },
  {
    icon: "fab fa-cc-stripe",
    label: "Your Stripe Account",
    route: "https://dashboard.stripe.com/dashboard",
    external: true,
  },
  {
    icon: "description",
    label: "Summary Report",
    route: "summary-report",
  },
  {
    icon: "gavel",
    label: "Global Terms",
    route: "global-terms",
  },
  {
    icon: "favorite",
    label: "Charity Settings",
    route: { name: "charity-settings" },
  },
  {
    icon: "people",
    label: "Manage Contacts",
    route: { name: "manage-users" },
  },
  {
    icon: "palette",
    label: "Brand Colors",
    route: "brand-colours",
  },
];
</script>
<style lang="sass" scoped>
.logo
  height: 60px

.drawerItem
  opacity: 0.7

.line-clamp-2
  display: -webkit-box
  -webkit-line-clamp: 2
  -webkit-box-orient: vertical
  overflow: hidden
  text-overflow: ellipsis
  line-height: 1.2em
  max-height: 2.4em
</style>
