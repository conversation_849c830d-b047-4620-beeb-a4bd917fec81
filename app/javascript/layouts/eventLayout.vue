<template>
  <q-layout view="hHR lpR fFf">
    <Navigation />
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>
<script setup>
import { onMounted } from "vue";
import { useRoute } from "vue-router";
import { useEventStore } from "@/stores/event.js";
import Navigation from "../events/components/Navigation.vue";

const route = useRoute();
const store = useEventStore();

onMounted(async () => {
  // Check if there's an event ID in the route params
  if (route.params.eventId && route.params.eventId !== store.getEventId) {
    // Load this specific event
    await store.loadEvent(route.params.eventId);
  }
});
</script>
