<template>

  <q-layout view="lHh Lpr lFf">
    <!-- Main Content -->
    <q-page-container>
      <q-page padding class="bookings-page">
        <!-- Compact Booking Progress Header -->
        <div class="booking-progress-header">
          <div class="progress-container">
            <!-- Centered Progress Content -->
            <div class="progress-content">
              <!-- Compact Booking Progress Stepper -->
              <q-stepper
                v-model="active"
                color="primary"
                animated
                flat
                alternative-labels
                :contracted="isSmallScreen"
                class="compact-stepper"
              >
                <q-step
                  :name="0"
                  title="Tickets"
                  icon="event_seat"
                  :done="active > 0"
                />
                <q-step
                  v-if="showAttendees"
                  :name="1"
                  title="Attendees"
                  icon="people"
                  :done="active > 1"
                />
                <q-step
                  v-if="chargeable && !readonly"
                  :name="active > 1 && showAttendees ? 2 : 1"
                  title="Payment"
                  icon="payment"
                  :done="active > (showAttendees ? 2 : 1)"
                />
                <q-step
                  v-if="!readonly || declined"
                  :name="active > 1 && showAttendees && chargeable ? 3 : (showAttendees || chargeable ? 2 : 1)"
                  title="Summary"
                  icon="check_circle"
                />
              </q-stepper>

              <!-- Timer positioned absolutely -->
              <div class="timer-section" v-if="timer">
                <q-chip
                  color="warning"
                  text-color="dark"
                  icon="timer"
                  size="sm"
                  class="text-weight-bold"
                >
                  {{ timer }}
                </q-chip>
              </div>
            </div>
          </div>
        </div>
  
        <!-- Main Content Area - Match startpage layout -->
        <div class="main-container">
          <div class="content-column">
            <!-- Debug Info -->
            <!-- <div class="q-pa-md bg-grey-2 q-mb-md" v-if="!isLoading">
              <div class="text-caption">Debug Info:</div>
              <div class="text-caption">Current Route: {{ route.name || 'none' }}</div>
              <div class="text-caption">Route Path: {{ route.path }}</div>
            </div> -->

            <!-- Router View for Booking Steps -->
            <router-view class="booking-step-content" v-slot="{ Component }">
              <div v-if="Component">
                <component :is="Component" />
              </div>
              <div v-else class="q-pa-lg text-center">
                <div class="text-h6">No component loaded</div>
                <div class="text-caption">Route: {{ route.name }} ({{ route.path }})</div>
              </div>
            </router-view>
          </div>
        </div>
  
        <!-- Loading Overlay -->
        <q-inner-loading :showing="isLoading" color="primary">
          <q-spinner-gears size="50px" />
          <div class="q-mt-md text-center">
            Loading booking information...
          </div>
        </q-inner-loading>
  
        <!-- Error Dialog -->
        <q-dialog v-model="showErrorDialog" persistent>
          <q-card style="min-width: 350px">
            <q-card-section>
              <div class="text-h6 text-negative">
                <q-icon name="error" class="q-mr-sm" />
                Booking Error
              </div>
            </q-card-section>
  
            <q-card-section class="q-pt-none">
              {{ errorMessage }}
            </q-card-section>
  
            <q-card-actions align="right">
              <q-btn flat label="OK" color="primary" @click="showErrorDialog = false" />
            </q-card-actions>
          </q-card>
        </q-dialog>
      </q-page>
    </q-page-container>
  </q-layout>

</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
import { useEventStore } from '@/stores/event.js'; // Import event store
import axios from 'axios';
import dayjs from 'dayjs';

// Composables
const route = useRoute();
const router = useRouter();
const $q = useQuasar();
const bookingStore = useBookingStore();
const eventStore = useEventStore(); // Initialize event store

// Reactive state
const isLoading = ref(true);
const showErrorDialog = ref(false);
const errorMessage = ref('');
const eventBooking = ref({}); // Initialize as empty object
const event = ref({}); // Initialize as empty object
const readonly = ref(false);
const declined = ref(false);

// Computed properties
const timer = computed(() => bookingStore.getTimerDisplay);

const isSmallScreen = computed(() => {
    return $q.screen.width < 768; // More appropriate breakpoint for mobile stepper
});

const topLineOverride = computed(() => ({
  background: `linear-gradient(135deg, ${event.value.phcolour || '#1976d2'} 0%, ${adjustColor(event.value.phcolour || '#1976d2', -20)} 100%)`,
  borderTop: `4px solid ${event.value.phcolour || '#1976d2'}`
}));

const active = computed(() => {
  const routeName = route.name;
  if (routeName === 'home') return 0;
  else if (routeName === 'userdetails') return 1;
  else if (routeName === 'payment') return 2;
  else if (routeName === 'summary') return 3;
  return 0;
});

const showAttendees = computed(() => {
  return event.value.show_add_attendees || false;
});

const chargeable = computed(() => bookingStore.getChargeable);

// Create formatters utility to replace Vue 2 filters
const useFormatters = () => {
  const formatDate = (value) => {
    if (value) {
      return dayjs(value).format('DD/MM/YYYY');
    }
  };

  const formatTime = (value) => {
    if (value) {
      return dayjs(value).format('HH:mm');
    }
  };

  return { formatDate, formatTime };
};

const { formatDate, formatTime } = useFormatters();

// Methods
const adjustColor = (color, amount) => {
  // Simple color adjustment function
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
};

const setUpBooking = () => {
  try {
    console.log('Setting up booking for event:', event.value?.id);

    // Validate we have the required data
    if (!event.value) {
      console.error('No event data available for booking setup');
      return;
    }

    const currentEvent = bookingStore.getEvent;
    const currentBooking = bookingStore.getEventBooking;

    if (
      route.name === 'home' &&
      (currentEvent === undefined ||
        event.value?.id !== bookingStore.getEventId ||
        currentBooking === undefined ||
        eventBooking.value?.id === undefined ||
        eventBooking.value?.id !== bookingStore.getEventBookingId ||
        eventBooking.value?.booking_count === 0)
    ) {
      console.log('Resetting booking store...');
      bookingStore.resetBooking();
      bookingStore.setEvent(event.value);
      bookingStore.setEventBooking(eventBooking.value);
      bookingStore.setEditByOrg(window.editbyorg || false);
      bookingStore.setRefPath(window.refPath || '');
      bookingStore.setChargeable(window.chargeable === 'true');
      bookingStore.setPayStart(null);
    }

    // Handle existing bookings
    if (
      eventBooking.value &&
      eventBooking.value.booking_count > 0 &&
      !eventBooking.value.cancelled_at
    ) {
      readonly.value = true;
      bookingStore.setReadOnly(true);
    }

    // Handle cancelled bookings
    if (eventBooking.value && eventBooking.value.cancelled_at) {
      handleCancelledBooking();
    }

    // Handle declined bookings
    if (
      eventBooking.value &&
      eventBooking.value.declined &&
      eventBooking.value.booking_count === 0
    ) {
      handleDeclinedBooking();
    }

    // Handle existing bookings with decline attempt
    if (
      eventBooking.value &&
      eventBooking.value.declined &&
      eventBooking.value.booking_count > 0
    ) {
      handleExistingBookingDecline();
    }

  } catch (error) {
    console.error('Error setting up booking:', error);
    showError('Failed to initialize booking. Please refresh the page and try again.');
  } finally {
    console.log('Booking setup complete, setting isLoading to false');
    isLoading.value = false;
  }
};

const handleCancelledBooking = () => {
  const header = `Hi ${window.booker || 'there'}`;
  const msg = `Your booking for the ${event.value.title} has been cancelled. If you have any questions about this please contact the event organiser: ${event.value.organiser} (${event.value.organiser_email})`;

  $q.dialog({
    title: header,
    message: msg,
    color: 'warning',
    persistent: true
  });
};

const handleDeclinedBooking = () => {
  declined.value = true;
  let messageTitle = 'Please Confirm If You Wish To Decline this Event!';
  let messageHtml = '<p>Please type <strong>your email</strong> into the box below to continue</p>';

  if (eventBooking.value.opted_out) {
    messageTitle = 'Please Confirm If You Wish To Decline this Event AND Opt Out of Further Communications!';
    messageHtml = '<p>You will no longer receive event invites or any other emails concerning events from us</p><p>Please type <strong>your email</strong> into the box below to continue</p>';
  }

  $q.dialog({
    title: messageTitle,
    message: messageHtml,
    prompt: {
      model: '',
      type: 'text'
    },
    cancel: true,
    persistent: true
  }).onOk(data => {
    const userEmail = eventBooking.value.user_email || '';
    if (!data || data.toLowerCase() !== userEmail.toLowerCase()) {
      $q.notify({
        type: 'negative',
        message: 'Please enter your email if you wish to continue!'
      });
      return;
    }

    axios.put(
      '/registered_users/' + eventBooking.value.registered_user_id + '/decline',
      {
        declined: true,
        opted_out: eventBooking.value.opted_out
      }
    ).then(() => {
      if (eventBooking.value.opted_out) {
        $q.dialog({
          title: 'Declined & Opted Out!',
          message: 'You have declined this event, and opted out of further communications regarding this organisers events, the organiser has been notified!',
          color: 'positive'
        });
      } else {
        $q.dialog({
          title: 'Declined!',
          message: 'You have declined this event, the organiser has been notified!',
          color: 'positive'
        });
      }
    });
  });
};

const handleExistingBookingDecline = () => {
  declined.value = true;
  $q.dialog({
    title: 'Cannot decline event',
    message: 'You are already booked on this event, do you wish to cancel your booking?',
    color: 'warning',
    cancel: {
      label: 'Review Booking',
      color: 'primary'
    },
    ok: {
      label: 'Cancel Booking',
      color: 'negative'
    },
    persistent: true
  }).onOk(() => {
    $q.dialog({
      title: 'Are you sure?',
      message: 'This will permanently cancel this booking',
      cancel: true,
      ok: {
        label: 'Yes, remove it!',
        color: 'negative'
      },
      persistent: true
    }).onOk(() => {
      axios.put(`/event_bookings/${eventBooking.value.uuid}/cancel`)
        .then(() => {
          eventBooking.value.booking_count = 0;
          eventBooking.value.cancelled_at = Date.now();
          bookingStore.setEventBooking(eventBooking.value);
          $q.notify({
            type: 'positive',
            message: 'The booking has been cancelled',
            caption: 'Success'
          });
          router.push('summary');
        })
        .catch(() => {
          $q.notify({
            type: 'negative',
            message: 'The booking has not been cancelled',
            caption: 'Error'
          });
        });
    });
  });
};

const showError = (message) => {
  errorMessage.value = message;
  showErrorDialog.value = true;
};

// Lifecycle
onMounted(async () => {
  console.log('📱 BookingLayout mounted, setting up booking...');
  console.log('📊 Window event booking data in layout:', window.eventBookingData);
  console.log('🔧 Current route in layout:', route);

  // Make stores available globally for debugging
  if (process.env.NODE_ENV === 'development') {
    window.eventStore = eventStore;
    window.bookingStore = bookingStore;
    console.log('🔧 Stores available globally: window.eventStore, window.bookingStore');
  }

  isLoading.value = true; // Start loading

  if (window.eventBookingData && Object.keys(window.eventBookingData).length > 0) {
    console.log('Using preloaded eventBookingData:', window.eventBookingData);
    eventBooking.value = window.eventBookingData;

    // Use the event data from the preloaded booking data (no need for additional API call)
    event.value = eventBooking.value.event;

    // Set the event data in both stores
    if (event.value) {
      eventStore.setEvent(event.value); // Set in eventStore for tickets component
      bookingStore.setEvent(event.value); // Set in bookingStore for booking flow
    }

    bookingStore.setEventBooking(eventBooking.value);
    readonly.value = eventBooking.value.booking_count > 0 && !eventBooking.value.cancelled_at;
    bookingStore.setReadOnly(readonly.value);
    setUpBooking();
  } else {
    console.log('No preloaded data, fetching from API...');
    try {
      let fetchedBookingData = null;
      if (route.path.startsWith('/invite/')) {
        const token = route.params.token;
        console.log('Fetching private event booking with token:', token);
        fetchedBookingData = await eventStore.fetchEventBookingData('private', token);
      } else if (route.path.startsWith('/event/')) {
        const eventId = route.params.id;
        const eventName = route.params.name;
        console.log('Fetching public event with ID:', eventId);
        fetchedBookingData = await eventStore.fetchEventBookingData('public', eventId, eventName);
      } else {
        showError('Invalid URL for booking. Please check the link.');
        isLoading.value = false;
        return;
      }

      if (fetchedBookingData) {
        eventBooking.value = fetchedBookingData;
        // Ensure eventStore is fully populated with the event data
        if (eventBooking.value.event?.id) {
          await eventStore.loadEvent(eventBooking.value.event.id);
          event.value = eventStore.getEvent; // Get the fully loaded event from eventStore
        } else {
          event.value = eventBooking.value.event; // Fallback if no event ID
        }
        bookingStore.setEventBooking(eventBooking.value);
        bookingStore.setEvent(event.value); // Set the fully loaded event in bookingStore
        readonly.value = eventBooking.value.booking_count > 0 && !eventBooking.value.cancelled_at;
        bookingStore.setReadOnly(readonly.value);
        setUpBooking();
      } else {
        showError('Failed to load booking data. Please refresh the page.');
      }
    } catch (error) {
      console.error('Error fetching booking data:', error);
      showError('Failed to load booking data. Please check your internet connection or try again later.');
    } finally {
      isLoading.value = false; // End loading
    }
  }
});

// Watch for route changes to update active step
watch(() => route.name, () => {
  // Additional logic for route changes if needed
});
</script>

<style lang="scss" scoped>
.bookings-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 50px); // Account for any layout headers
}

// New compact booking progress header
.booking-progress-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 0.5rem; // Reduced from 1rem
  padding: 0.5rem 0; // Reduced from 0.75rem
}

.progress-container {
  max-width: 800px; // Reduced from 1200px to make container more compact
  margin: 0 auto;
  padding: 0 1rem;

  @media (max-width: $breakpoint-sm-max) {
    padding: 0 0.5rem;
    max-width: 100%; // Full width on mobile
  }
}

.progress-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: $breakpoint-sm-max) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.timer-section {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);

  @media (max-width: $breakpoint-sm-max) {
    position: static;
    transform: none;
    order: 2; // Move timer below stepper on mobile
  }
}

// Main container to match startpage layout
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;

  @media (max-width: $breakpoint-sm-max) {
    padding: 0 0.5rem;
  }
}

.content-column {
  max-width: 66.67%; // Match startpage col-lg-8 (8/12 = 66.67%)
  margin: 0 auto;

  @media (max-width: $breakpoint-lg-max) {
    max-width: 100%; // Full width on smaller screens like startpage col-12
  }
}

.booking-step-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
}

// Timer animation using Quasar's approach
.timer {
  animation: q-pulse 2s infinite;
}

@keyframes q-pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive design for booking progress header
.booking-progress-header {
  @media (max-width: $breakpoint-sm-max) {
    padding: 0.375rem 0; // More compact on mobile
    margin-bottom: 0.375rem; // Reduced spacing
  }

  @media (max-width: $breakpoint-xs-max) {
    padding: 0.25rem 0; // Very compact on extra small
    margin-bottom: 0.25rem; // Minimal spacing
  }
}

// Compact stepper styling - centered with proper width
.compact-stepper {
  max-width: 700px; // Wider for better desktop experience
  min-width: 400px; // Minimum width to prevent cramping
  margin: 0 auto; // Center the stepper

  :deep(.q-stepper__header) {
    padding: 0;
    min-height: auto;
    justify-content: space-between; // Better distribution of steps

    .q-stepper__tab {
      padding: 0.5rem 1rem; // Reduced vertical padding to minimize height
      min-height: 60px; // Reduced height to remove empty space
      flex: 1; // Equal width distribution

      .q-stepper__step {
        width: 36px; // Larger step circles
        height: 36px;
        font-size: 0.875rem;
        margin: 0 auto; // Center the step circle
      }

      .q-stepper__label {
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.25rem; // Reduced spacing between icon and label
        text-align: center;
        white-space: nowrap; // Prevent text wrapping
      }
    }

    .q-stepper__line {
      margin: 0 0.75rem; // More space between steps
      flex-shrink: 0; // Prevent line compression
    }
  }

  // Mobile optimizations
  @media (max-width: $breakpoint-sm-max) {
    max-width: 100%;
    min-width: 320px; // Ensure minimum width on mobile

    :deep(.q-stepper__header) {
      justify-content: space-between; // Better distribution on mobile
      padding: 0 0.5rem; // Add horizontal padding

      .q-stepper__tab {
        padding: 0.375rem 0.25rem; // Reduced vertical padding
        min-height: 55px; // Slightly reduced height
        flex: 1; // Equal distribution

        .q-stepper__step {
          width: 30px; // Slightly larger for better visibility
          height: 30px;
          font-size: 0.75rem;
          margin: 0 auto;
        }

        .q-stepper__label {
          font-size: 0.75rem;
          margin-top: 0.25rem; // Reduced spacing
          text-align: center;
          line-height: 1.1;
        }
      }

      .q-stepper__line {
        margin: 0 0.125rem; // Minimal but present spacing
        flex-shrink: 0;
      }
    }
  }

  // Extra small screens - more compact but still usable
  @media (max-width: $breakpoint-xs-max) {
    min-width: 280px; // Absolute minimum width

    :deep(.q-stepper__header) {
      justify-content: space-between; // Maintain distribution
      padding: 0 0.25rem;

      .q-stepper__tab {
        padding: 0.375rem 0.125rem; // Minimal but functional padding
        min-height: 50px; // Keep reasonable touch target
        flex: 1;

        .q-stepper__step {
          width: 26px; // Slightly larger than before
          height: 26px;
          font-size: 0.7rem;
          margin: 0 auto;
        }

        .q-stepper__label {
          font-size: 0.65rem; // Smaller text but still readable
          line-height: 1.1;
          text-align: center;
          margin-top: 0.25rem;
        }
      }

      .q-stepper__line {
        margin: 0 0.0625rem; // Very minimal spacing
        flex-shrink: 0;
        min-width: 8px; // Ensure line is visible
      }
    }
  }

  // Special handling for contracted mode
  &.q-stepper--contracted {
    :deep(.q-stepper__header) {
      .q-stepper__tab {
        min-width: 60px; // Ensure minimum width in contracted mode

        .q-stepper__step {
          margin: 0 auto; // Center the step icon
        }

        .q-stepper__label {
          display: none; // Hide labels in contracted mode to save space
        }
      }

      .q-stepper__line {
        margin: 0 0.25rem; // Adequate spacing in contracted mode
      }
    }

    // Mobile contracted mode
    @media (max-width: $breakpoint-sm-max) {
      :deep(.q-stepper__header) {
        .q-stepper__tab {
          min-width: 50px; // Smaller minimum on mobile
          padding: 0.5rem 0.125rem;
        }

        .q-stepper__line {
          margin: 0 0.125rem;
        }
      }
    }
  }
}

// Custom scrollbar for better UX
.booking-step-content {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--q-primary);
    border-radius: 3px;

    &:hover {
      background: var(--q-primary-dark);
    }
  }
}
</style>
