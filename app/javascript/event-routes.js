// import Event from '../events/event.vue'
// import EventsMain from '../events/events-main.vue'
// import EventDetails from '../events/details/event-details.vue'
// import Terms from '../events/terms/terms.vue'
// import RegQuestions from '../events/additional/reg-questions.vue'
// import EventPreview from '../events/preview/event-preview.vue'
// import Tickets from '../events/tickets/tickets-creation.vue'
// import UploadContacts from '../events/customise_emails/UploadContacts.vue'
// import Customise from '../events/customise_emails/Start.vue'
// import CustomiseInvite from '../events/customise_emails/customise-invite'
// import CustomiseConfirmation from '../events/customise_emails/customise-confirmation'
// import EmailPreview from '../events/customise_emails/EmailPreview.vue'
// import Payments from '../events/payments/payments.vue'
// import PaymentOptions from '../events/payments/payment-options.vue'
// import GoLive from '../events/payments/go-live.vue'
// import Promote from '../events/promote/promote'
// import PromoteShare from '../events/promote/promote-share'
// import SendInvites from '../events/promote/send-invites'
//
// const routes = [
//     {
//       path: '', component: EventsMain, name: 'events-main',
//
//       children: [
//         {
//           path: '',
//           component: Event,
//           name: 'event',
//           children: [
//             {
//               // EventDetails will be rendered inside User's <router-view>
//               // when /event/:id/event-details is matched
//               // when /event/event-details is matched
//               path: '',
//               component: EventDetails,
//               name: 'event-details'
//             },
//             {
//               path: '/ticket-creation',
//               name: 'ticket-creation',
//               component: Tickets
//             },
//             {
//               path: '/additional',
//               name: 'additional',
//               component: RegQuestions
//             },
//             {
//               path: '/terms',
//               name: 'terms',
//               component: Terms
//             },
//             {
//               path: '/preview',
//               name: 'preview',
//               component: EventPreview
//             },
//             {
//               path: '/contacts',
//               name: 'contacts',
//               component: UploadContacts
//             },
//           ]
//         },
//         {
//           path: '/customise-emails',
//           component: Customise,
//           name: 'customise-emails',
//           children: [
//             {
//               path: '',
//               component: UploadContacts,
//               name: 'upload-contacts'
//             },
//             {
//               path: '/customise-invite',
//               component: CustomiseInvite,
//               name: 'customise-invite'
//             },
//             {
//               path: '/customise-confirmation',
//               component: CustomiseConfirmation,
//               name: 'customise-confirmation'
//             },
//             {
//               path: '/email-preview',
//               component: EmailPreview,
//               name: 'email-preview'
//             }
//           ]
//         },
//         {
//           path: '/payments',
//           component: Payments,
//           name: 'payments',
//           children: [
//             {
//               path: '',
//               component: PaymentOptions,
//               name: 'payment-options'
//             },
//             {
//               path: '/go-live',
//               component: GoLive,
//               name: 'go-live'
//             }
//           ]
//         },
//         {
//           path: '/promote',
//           component: Promote,
//           name: 'promote',
//           children: [
//             {
//               path: '',
//               component: PromoteShare,
//               name: 'promote-share'
//             },
//             {
//               path: '/send-invites',
//               component: SendInvites,
//               name: 'send-invites'
//             },
//           ]
//         }
//       ]
//     },
//     {path: '*', redirect: '/'}
//   ]