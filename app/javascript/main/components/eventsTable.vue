<template>
  <div class="row justify-between items-center q-gutter-md">
    <div class="col-12">
      <span class="text-left text-bold text-h4 q-py-lg">Events</span>
    </div>
    <div class="col-12 col-sm-5">
      <q-btn-group unelevated stretch flat class="filter-container">
        <q-btn
          v-for="option in filterOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :class="{ 'bg-primary text-white': filter === option.value }"
          @click="mainStore.filters.filter = option.value"
        />
      </q-btn-group>
    </div>
    <div class="col-12 col-sm-5 flex justify-end">
      <q-btn-dropdown
        color="primary"
        label="Filter by tags"
        icon="filter_alt"
        outline
      >
        <q-card class="q-pa-none" style="min-width: 250px">
          <q-card-section>
            <div class="text-h6">Filter By Event Tags</div>
          </q-card-section>
          <q-card-section>
            <q-select
              v-model="tagsSelected"
              multiple
              outlined
              dense
              use-chips
              options-dense
              :options="tags"
              class="full-width"
            />
          </q-card-section>
        </q-card>
      </q-btn-dropdown>
    </div>
    <div class="col-12">
      <q-table
        ref="tableRef"
        :rows="events"
        :columns="columns"
        row-key="id"
        :pagination.sync="pagination"
        flat
        bordered
        :rows-per-page-options="[5, 10, 15]"
        class="q-mt-md events-table"
        v-model:pagination="pagination"
        :loading="loading"
        :filter="searchTerm"
        binary-state-sort
        @request="onRequest"
      >
        <template v-slot:top-left>
          <q-input
            v-model="searchTerm"
            outlined
            clearable
            dense
            label="Search by event title"
            class="col-7 q-mb-sm"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </template>
        <template v-slot:top-right>
          <q-select
            v-model="pagination.rowsPerPage"
            :options="[5, 10, 15]"
            label="Rows per page"
            dense
            outlined
            style="width: 150px"
          />
        </template>
        <template v-slot:body-cell-title="props">
          <q-td :props="props">
            <q-tooltip
              anchor="bottom left"
              self="top left"
              :offset="[-5, -10]"
              >{{ props.row.title }}</q-tooltip
            >
            <div class="ellipsis">
              {{ props.row.title }}
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-organiser="props">
          <q-td :props="props">
            <q-tooltip
              anchor="bottom left"
              self="top left"
              :offset="[-5, -10]"
              >{{ props.row.organiser }}</q-tooltip
            >
            <div class="ellipsis">
              {{ props.row.organiser }}
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-status="props">
          <q-td :props="props">
            <event-status :event="props.row" />
          </q-td>
        </template>

        <template v-slot:body-cell-visibility="props">
          <q-td :props="props">
            <q-badge
              :color="props.row.is_public ? 'positive' : 'negative'"
              outline
            >
              {{ props.row.is_public ? "Public" : "Private" }}
            </q-badge>
          </q-td>
        </template>

        <template v-slot:body-cell-actions="props">
          <q-td :props="props">
            <event-actions :row="props.row" :onUpdate="onUpdate" />
          </q-td>
        </template>
      </q-table>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, watch, computed } from "vue";
import axios from "axios";
import { useMainStore } from "../../stores/main.js"; // Import the main store
import { formatDate } from "../utils/formatDate.js";
import EventActions from "./eventActions.vue";
import EventStatus from "./EventStatus.vue";

const mainStore = useMainStore(); // Initialize the main store

const tableRef = ref(null);
const tags = ref([]);
const loading = ref(false);
const pagination = ref({
  sortBy: "datetimefrom",
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 10,
});

// store vars
const events = computed(() => mainStore.events);
const filter = computed(() => mainStore.filters.filter);
const tagsSelected = computed({
  get: () => mainStore.filters.tags,
  set: (value) => (mainStore.filters.tags = value),
});
const searchTerm = computed({
  get: () => mainStore.filters.searchTerm,
  set: (value) => (mainStore.filters.searchTerm = value),
});

const columns = computed(() => [
  { name: "id", label: "Event ID", field: "id", align: "left", sortable: true },
  {
    name: "title",
    label: "Event Name",
    field: "title",
    align: "left",
    sortable: true,
    classes: "ellipsis",
  },
  {
    name: "organiser",
    label: "Organiser Name",
    field: "organiser",
    align: "left",
    sortable: true,
  },
  {
    name: "datetimefrom",
    label: "From",
    field: "datetimefrom",
    align: "left",
    sortable: true,
    format: (val) => formatDate(val),
  },
  {
    name: "datetimeto",
    label: "To",
    field: "datetimeto",
    align: "left",
    sortable: true,
    format: (val) => formatDate(val),
  },
  { name: "status", label: "Status", field: "id", align: "left" },
  {
    name: "visibility",
    label: "Visibility",
    field: "is_public",
    align: "center",
  },
  {
    name: "actions",
    label: "Actions",
    field: "actions",
    align: "center",
    headerStyle: "position: sticky;right:0;background-color:#f5f5f5",
  },
]);

const filterOptions = computed(() => [
  { label: "All", value: "All" },
  { label: "Alive", value: "Alive" },
  { label: "Expired", value: "Expired" },
]);

watch([filter, tagsSelected], () => {
  tableRef.value.requestServerInteraction();
});

const onRequest = (props) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  const search = props.filter;
  loading.value = true;
  axios
    .get("/events/list", {
      params: {
        search,
        filter: filter.value,
        page,
        per_page: rowsPerPage,
        sort: sortBy,
        direction: descending ? "desc" : "asc",
        tags: tagsSelected.value,
      },
    })
    .then((response) => {
      // Extract just the events array from the response
      mainStore.events = response.data.events || []; // Update events in the store

      // set tags
      if (response.data.events) {
        response.data.events.forEach((event) => {
          if (event.tags) {
            event.tags.forEach((tag) => {
              if (!tags.value.includes(tag)) {
                tags.value.push(tag);
              }
            });
          }
        });
      }

      // Handle pagination metadata
      if (response.data.pagy) {
        pagination.value.rowsNumber = response.data.pagy.count;
        pagination.value.page = response.data.pagy.page;
        pagination.value.rowsPerPage = response.data.pagy.items;
      } else {
        // Fallback to header if pagy data not available
        pagination.value.rowsNumber = response.headers["x-total-count"];
        pagination.value.page = page;
        pagination.value.rowsPerPage = rowsPerPage;
      }

      pagination.value.sortBy = sortBy;
      pagination.value.descending = descending;
      loading.value = false;
    })
    .catch((error) => {
      console.error("Error fetching events:", error);
      loading.value = false;
    });
};

const onUpdate = () => {
  tableRef.value.requestServerInteraction();
};

// Lifecycle hooks
onMounted(() => {
  tableRef.value.requestServerInteraction();
});
</script>
<style scoped lang="sass">
.filter-container
  background-color: #ECF4FC;
  justify-content: center;

.events-table
  max-width: 100%;

@media (min-width: $breakpoint-sm-min) // sm breakpoint and greater
    .events-table
        height: calc(100vh - 230px);

@media (max-width: $breakpoint-xs-max) // less than sm breakpoint
    .events-table
        height: calc(100vh - 200px);

.events-table thead th:last-child,
.events-table td:last-child
  background-color: #e0e0e0;

.events-table td:last-child
  position: sticky;
  right: 0;
  z-index: 2; // Ensure the header cell is above the body cells
  background-color: #f5f5f5;

.ellipsis
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px; // Adjust as needed
</style>
