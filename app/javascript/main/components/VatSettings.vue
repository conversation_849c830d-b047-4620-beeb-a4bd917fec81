<template>
  <div>
    <div class="col-12">
      <q-card class="q-mb-md">
        <q-card-section class="q-pb-none">
          <div class="hg-underline">Fill in the details below for VAT Options on your Events</div>
        </q-card-section>

        <q-card-section>
          <q-form @submit="saveVat" ref="vatFormRef">
            <div class="q-mb-md">
              <q-input 
                v-model="vatForm.vatNumber" 
                maxlength="9" 
                label="VAT Number:" 
                placeholder="VAT Number (no spaces)" 
                :rules="[
                  val => !!val || 'Please enter your VAT number',
                  val => val.length === 9 || 'VAT number must be 9 characters'
                ]"
                filled
              >
                <template v-slot:prepend>GB</template>
              </q-input>
            </div>
            
            <div class="q-mb-md">
              <q-input 
                v-model="vatForm.companyName" 
                label="Company Name (will appear on invoice):" 
                placeholder="Company Name"
                filled
              />
            </div>
            
            <div class="q-mb-md">
              <q-input 
                v-model="vatForm.companyNumber" 
                maxlength="20" 
                label="Company Number:" 
                placeholder="Company Number" 
                :rules="[val => !!val || 'Please enter your company number']"
                filled
              />
            </div>
            
            <div class="q-mb-md">
              <q-input 
                v-model="vatForm.companyAddress" 
                type="textarea" 
                rows="4" 
                label="Company Address:" 
                placeholder="Company Address"
                filled
                autogrow
              />
            </div>

            <div class="row q-gutter-sm">
              <q-btn 
                type="submit" 
                color="primary" 
                :loading="saving"
                icon="save"
                label="Save VAT"
              />
              
              <q-btn 
                v-if="showDelete" 
                color="negative" 
                @click="confirmRemoveVat" 
                :loading="deleting"
                icon="delete"
                label="Remove VAT Details"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import axios from 'axios';

const $q = useQuasar();

// Form data and validation
const vatFormRef = ref(null);
const vatForm = reactive({
  vatNumber: '',
  companyNumber: '',
  companyName: '',
  companyAddress: ''
});

// UI state
const showDelete = ref(false);
const saving = ref(false);
const deleting = ref(false);
const eventhash = ref(window.location.pathname);

// Methods
const saveVat = async () => {
  const isValid = await vatFormRef.value.validate();
  
  if (!isValid) {
    $q.notify({
      type: 'warning',
      message: 'Please add a VAT number and company number'
    });
    return;
  }
  
  saving.value = true;
  
  try {
    await axios.post('/vat', {
      vat_number: vatForm.vatNumber,
      company_name: vatForm.companyName,
      company_number: vatForm.companyNumber,
      company_address: vatForm.companyAddress
    });
    
    showDelete.value = true;
    
    $q.notify({
      type: 'positive',
      message: 'VAT Number Saved'
    });
  } catch (error) {
    const errorMessage = error.response?.data?.errors || 'Failed to save VAT details';
    
    $q.notify({
      type: 'negative',
      message: errorMessage
    });
  } finally {
    saving.value = false;
  }
};

const confirmRemoveVat = () => {
  $q.dialog({
    title: 'Warning',
    message: 'Are you sure you want to remove VAT details?',
    cancel: true,
    persistent: true
  }).onOk(() => {
    removeVat(vatForm.vatNumber);
  });
};

const removeVat = async (vatNumber) => {
  if (!vatNumber) return;
  
  deleting.value = true;
  
  try {
    await axios.delete(`/vat/${vatNumber}.json`);
    
    // Reset form
    showDelete.value = false;
    vatForm.vatNumber = '';
    vatForm.companyNumber = '';
    vatForm.companyName = '';
    vatForm.companyAddress = '';
    
    $q.notify({
      type: 'positive',
      message: 'VAT Number Deleted'
    });
  } catch (error) {
    const errorMessage = error.response?.data?.errors || 'Could not delete VAT details';
    
    $q.notify({
      type: 'negative',
      message: errorMessage
    });
  } finally {
    deleting.value = false;
  }
};

// Lifecycle hooks
onMounted(async () => {
  try {
    const response = await axios.get('/vat');
    const vat = response.data;
    
    vatForm.vatNumber = vat.vat_number || '';
    vatForm.companyNumber = vat.company_number || '';
    vatForm.companyName = vat.company_name || '';
    vatForm.companyAddress = vat.company_address || '';
    
    showDelete.value = !!vat.vat_number;
  } catch (error) {
    console.error(error);
    
    $q.notify({
      type: 'negative',
      message: 'Failed to load VAT information'
    });
  }
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}
</style>