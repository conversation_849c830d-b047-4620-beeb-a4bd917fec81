// EventActions.vue
<template>
  <div class="q-gutter-sm">
    <q-btn
      v-for="action in actions"
      :key="action.name"
      round
      flat
      color="primary"
      :icon="action.icon"
      size="sm"
      @click="action.click(row)"
    >
      <q-tooltip>{{ action.label }}</q-tooltip>
    </q-btn>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useQuasar } from "quasar";
import axios from "axios";
const $q = useQuasar();

const props = defineProps({ row: Object, onUpdate: Function });

const actions = computed(() => [
  {
    name: "view",
    label: "Manage",
    icon: "dashboard",
    click: (row) => {
      navigateToEventDashboard(row);
    },
  },
  {
    name: "preview",
    label: "Preview",
    icon: "visibility",
    click: (row) => previewEvent(row),
  },
  {
    name: "edit",
    label: "Edit",
    icon: "edit",
    click: (row) => editEvent(row),
  },
  {
    name: "duplicate",
    label: "Duplicate",
    icon: "content_copy",
    click: (row) => duplicateEvent(row),
  },
  {
    name: "delete",
    label: "Delete",
    icon: "delete",
    color: "negative",
    click: (row) => confirmDeleteEvent(row),
  },
]);

const navigateToEventDashboard = (event) => {
  window.location.href = `/events/unified#/${event.id}`;
};

const previewEvent = (event) => {
  window.location.href = `/events/unified#/preview`;
};

const editEvent = (event) => {
  window.location.href = `/events/unified#/event/${event.id}`;
};

const duplicateEvent = (event) => {
  axios
    .put(`/events/${event.id}/copy`)
    .then((response) => {
      $q.notify({
        type: "positive",
        message: "Event Duplicated",
      });
      props.onUpdate();
      const newId = response?.data?.event_id;
      if (newId) window.location.href = `/events/unified#/event/${newId}`;
    })
    .catch((error) => {
      console.error(error);
      $q.notify({
        type: "negative",
        message: "Could Not Duplicate Event",
      });
    });
};

const confirmDeleteEvent = (event) => {
  // Implement delete confirmation logic here
  $q.dialog({
    title: "Confirm Delete?",
    message: "This will delete the event and all associated data",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    axios
      .delete(`/events/${event.id}`)
      .then(() => {
        props.onUpdate();
        $q.notify({
          type: "positive",
          message: "Event Deleted",
        });
      })
      .catch((error) => {
        console.error(error);
        $q.notify({
          type: "negative",
          message: "Could Not Delete Event",
        });
      });
  });
};
</script>
