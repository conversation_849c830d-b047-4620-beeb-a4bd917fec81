<template>
  <div>
    <q-card class="q-mb-md">
      <q-card-section class="q-pb-none">
        <div class="hg-underline">Add your pixel code below if you wish facebook to track users visiting your event booking pages</div>
      </q-card-section>

      <q-card-section>
        <q-form @submit="updatePixelCode">
          <q-item>
            <q-item-section>
              <q-input
                v-model="form.pixelCode"
                label="Facebook Pixel Code:"
                placeholder="Code"
                filled
              />
            </q-item-section>
          </q-item>

          <div class="button-group q-mt-md">
            <q-btn
              type="submit"
              color="primary"
              :loading="saving"
              icon="save"
              :label="hasPixel ? 'Update Pixel Code' : 'Add Pixel Code'"
            />
            
            <q-btn
              type="button"
              color="negative"
              @click="confirmRemovePixelCode"
              :loading="removing"
              icon="delete"
              label="Remove Pixel Code"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import axios from 'axios';

// Get Quasar instance
const $q = useQuasar();

// Form data
const form = reactive({
  pixelCode: ''
});

// UI state
const hasPixel = ref(false);
const saving = ref(false);
const removing = ref(false);

// Methods
const updatePixelCode = async () => {
  saving.value = true;
  
  try {
    await axios.put('/fb_pixel_code/change.json', {
      code: form.pixelCode
    });
    
    hasPixel.value = true;
    
    $q.notify({
      type: 'positive',
      message: hasPixel.value ? 
        'Your Facebook pixel code has been updated' : 
        'Facebook pixel has been added to your organisation'
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: `There was a problem ${hasPixel.value ? 'updating' : 'adding'} Facebook pixel to your organisation`
    });
  } finally {
    saving.value = false;
  }
};

const confirmRemovePixelCode = () => {
  $q.dialog({
    title: 'Are you sure?',
    message: 'This will remove Facebook pixel from your organisation',
    cancel: true,
    persistent: true,
    ok: {
      label: 'Yes, remove it!',
      color: 'negative'
    }
  }).onOk(() => {
    removePixelCode();
  });
};

const removePixelCode = async () => {
  removing.value = true;
  
  try {
    await axios.put('/fb_pixel_code/remove.json', {
      code: form.pixelCode
    });
    
    form.pixelCode = '';
    hasPixel.value = false;
    
    $q.notify({
      type: 'positive',
      message: 'Facebook pixel has been removed from your organisation'
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'There was a problem removing Facebook pixel from your organisation'
    });
  } finally {
    removing.value = false;
  }
};

// Lifecycle hooks
onMounted(async () => {
  try {
    const response = await axios.get('/fb_pixel_code.json');
    
    if (response.data.code) {
      form.pixelCode = response.data.code;
      hasPixel.value = true;
    }
  } catch (error) {
    console.error('Failed to load Facebook pixel code:', error);
    
    $q.notify({
      type: 'negative',
      message: 'Failed to load Facebook pixel code'
    });
  }
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.button-group {
  display: flex;
  gap: 10px;
}
</style>