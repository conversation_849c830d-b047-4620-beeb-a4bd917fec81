<template>
  <div class="q-gutter-md row">
    <!-- Add Contact Card -->
    <q-card class="q-mb-md">
      <q-card-section class="q-pb-none">
        <div class="hg-underline">Add a contact</div>
      </q-card-section>

      <q-card-section>
        <q-form ref="contactFormRef" @submit="validateDetails" greedy>
          <div class="q-mb-md">
            <q-input
              v-model="orgContact.forename"
              placeholder="Forename"
              :rules="[
                (val) => !!val || 'Please enter a forename',
                (val) =>
                  val.length <= 100 ||
                  'Forename cannot be longer than 100 characters',
              ]"
              outlined
            >
              <template v-slot:prepend>
                <q-icon name="person" />
              </template>
            </q-input>
          </div>

          <div class="q-mb-md">
            <q-input
              v-model="orgContact.surname"
              placeholder="Surname"
              :rules="[
                (val) => !!val || 'Please enter a surname',
                (val) =>
                  val.length <= 100 ||
                  'Surname cannot be longer than 100 characters',
              ]"
              outlined
            >
              <template v-slot:prepend>
                <q-icon name="person" />
              </template>
            </q-input>
          </div>

          <div class="q-mb-md">
            <q-input
              v-model="orgContact.email"
              placeholder="Email Address"
              :rules="[
                (val) => !!val || 'Please enter an email address',
                (val) =>
                  emailRegex.test(val) || 'Please enter a valid email address',
                (val) =>
                  val.length <= 100 ||
                  'Email cannot be longer than 100 characters',
              ]"
              outlined
            >
              <template v-slot:prepend>
                <q-icon name="email" />
              </template>
            </q-input>
          </div>

          <q-btn
            type="submit"
            color="primary"
            :loading="submitting"
            label="Save Details"
          />
        </q-form>
      </q-card-section>
    </q-card>

    <!-- CSV Upload Card -->
    <q-card>
      <q-card-section class="q-pb-none">
        <div class="hg-underline">Upload Contact Details Here</div>
      </q-card-section>

      <q-card-section>
        <q-table
          :rows="csvExample"
          :columns="csvColumns"
          row-key="email"
          hide-bottom
        >
          <template v-slot:top>
            <div class="text-subtitle1 q-mb-md">
              CSV should be in the following format:
            </div>
          </template>
        </q-table>

        <div class="q-my-md">
          <a
            href="https://s3-eu-west-1.amazonaws.com/hg-utility/example-email-csv.csv"
            class="text-primary"
          >
            A sample CSV can be downloaded here.
          </a>
        </div>

        <div class="q-mt-lg">
          <div class="text-subtitle2 q-mb-sm">Upload CSV file</div>

          <q-file
            v-model="selectedFile"
            accept=".csv"
            outlined
            label="Select CSV File"
            @update:model-value="handleFileChange"
          >
            <template v-slot:prepend>
              <q-icon name="attach_file" />
            </template>
          </q-file>

          <div v-if="selectedFile" class="q-mt-sm">
            Selected file: {{ selectedFile.name }}
          </div>

          <q-btn
            v-if="selectedFile"
            color="positive"
            @click="processCsv"
            class="q-mt-md"
            label="Upload Contacts"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { useQuasar } from "quasar";
//import Papa from "papaparse";
import _ from "lodash";
import { useMainStore } from "@/stores/main"; // Import Pinia store
import axios from "axios";

const $q = useQuasar();
const store = useMainStore(); // Use Pinia store

// Form data and validation
const contactFormRef = ref(null);
const orgContact = reactive({
  id: "",
  forename: "",
  surname: "",
  email: "",
});

const submitting = ref(false);
const emailRegex =
  /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/i;

// CSV Upload
const selectedFile = ref(null);
const csvData = ref(null);

// CSV example data and columns
const csvExample = [
  {
    forename: "John",
    surname: "Smith",
    email: "<EMAIL>",
  },
  {
    forename: "Sam",
    surname: "Taylor",
    email: "<EMAIL>",
  },
];

const csvColumns = [
  { name: "forename", align: "left", label: "Forename", field: "forename" },
  { name: "surname", align: "left", label: "Surname", field: "surname" },
  { name: "email", align: "left", label: "Email Address", field: "email" },
];

// Methods
const validateDetails = _.debounce(async () => {
  try {
    await contactFormRef.value.validate();
    saveContact();
  } catch (error) {
    $q.notify({
      message: "Please fix the validation issues",
      type: "negative",
    });
  }
}, 300);

const saveContact = async () => {
  submitting.value = true;

  try {
    const contactDetails = {
      forename: orgContact.forename,
      surname: orgContact.surname,
      email: orgContact.email,
    };

    const response = await axios.post("/organisation_users", {
      org_contact: contactDetails,
    });

    // Add to store
    store.addOrgContact({
      email: response.data.email,
      id: response.data.id,
      forename: response.data.forename,
      surname: response.data.surname,
      tags: [],
      opted_out: null,
    });

    // Reset form
    orgContact.id = "";
    orgContact.forename = "";
    orgContact.surname = "";
    orgContact.email = "";

    // Reset validation
    contactFormRef.value.resetValidation();

    $q.notify({
      message: "Saved Contact Details",
      type: "positive",
    });
  } catch (error) {
    const errMsg = error.response?.data?.error || "Failed to save contact";
    $q.notify({
      message: errMsg,
      type: "negative",
    });
  } finally {
    submitting.value = false;
  }
};

const handleFileChange = (file) => {
  // File is already stored in selectedFile.value
};

const processCsv = () => {
  if (!selectedFile.value) {
    $q.notify({
      message: "Please select a CSV file first",
      type: "warning",
    });
    return;
  }

  //todo: install papaparse if need csv import feature
  /*Papa.parse(selectedFile.value, {
    skipEmptyLines: true,
    beforeFirstChunk: (chunk) => {
      const rows = chunk.split(/\r\n|\r|\n/);
      rows[0] = null;
      if (rows[1] && rows[1].split(",").length > 3) {
        return null;
      } else {
        return rows.join("\r\n");
      }
    },
    complete: (results) => {
      if (results.errors.length > 0) {
        $q.notify({
          message: "Invalid CSV format",
          type: "negative",
        });
      } else {
        csvData.value = results.data;
        uploadContacts();
      }
    },
    error: () => {
      $q.notify({
        message: "This file is not valid",
        type: "negative",
      });
    },
  });*/
};

const uploadContacts = async () => {
  if (!csvData.value || !csvData.value.length) return;

  try {
    const result = await $q.dialog({
      title: "Confirm Upload?",
      message: "This will take a little while if you have lots of contacts!",
      cancel: true,
      persistent: true,
    });

    // Show loading indicator
    const loadingDialog = $q.loading.show({
      message: "Processing contacts...",
    });

    const orgUsers = {
      contact_attributes: [],
    };

    let emailsValid = true;
    let breakFromLoop = false;

    for (const [index, value] of csvData.value.entries()) {
      if (breakFromLoop) break;

      // Clean email
      if (value[2]) {
        value[2] = value[2].replace(/[^\x00-\x7F]/g, "").replace(/ /g, "");
      }

      // Validate row
      if (value[0] === "" || value[1] === "" || !emailRegex.test(value[2])) {
        if (!value[0]) {
          $q.notify({
            message: "The forename is blank",
            type: "negative",
          });
        } else if (!value[1]) {
          $q.notify({
            message: "The surname is blank",
            type: "negative",
          });
        } else if (value[2]) {
          $q.notify({
            message: `The following email is not valid: ${value[2]}`,
            type: "negative",
          });
        } else {
          $q.notify({
            message: "Row has blank email",
            type: "negative",
          });
        }

        emailsValid = false;
        breakFromLoop = true;
      } else {
        // Add valid contact to the list
        orgUsers.contact_attributes.push({
          forename: value[0],
          surname: value[1],
          email: value[2],
        });
      }
    }

    if (emailsValid) {
      try {
        const response = await axios.post(
          "/organisation_users/create_multiple",
          orgUsers
        );

        // Add contacts to store
        response.data.forEach((contactDetails) => {
          store.addOrgContact({
            email: contactDetails.email,
            id: contactDetails.org_user_list_id,
            forename: contactDetails.forename,
            surname: contactDetails.surname,
            tags: [],
            opted_out: null,
          });
        });

        if (response.data.length > 0) {
          $q.notify({
            message: "Saved Email List",
            type: "positive",
          });
        } else {
          $q.notify({
            message: "Contacts Already exist",
            type: "positive",
          });
        }

        // Reset file input
        selectedFile.value = null;
      } catch (error) {
        $q.dialog({
          title: "Error",
          message:
            "Please check the following:<ul><li>File is CSV Format</li><li>All Fields are Complete</li><li>CSV Format is as per example</li></ul>",
          html: true,
        });
      }
    }

    // Close loading
    loadingDialog.hide();
  } catch {
    // User canceled the dialog
    selectedFile.value = null;
  }
};
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}
</style>
