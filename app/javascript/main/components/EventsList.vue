<template>
    <div class="q-pa-md">
        <div class="text-h4 q-mb-md">Events</div>

        <div class="row q-mb-md">
            <div class="col-12 col-md-6">
                <q-input
                    v-model="searchTerm"
                    outlined
                    dense
                    label="Search by event title"
                    @keyup.enter="searchEvents"
                    class="q-mb-sm"
                >
                    <template v-slot:append>
                        <q-icon
                            name="search"
                            @click="searchEvents"
                            class="cursor-pointer"
                        />
                    </template>
                </q-input>
            </div>
        </div>

        <div class="q-mb-md">
            <q-btn
                color="primary"
                :to="{ name: 'new_event' }"
                label="Create New Event"
                icon="add"
            />
        </div>

        <q-card>
            <q-card-section>
                <div v-if="loading" class="q-pa-lg text-center">
                    <q-spinner color="primary" size="3em" />
                    <div class="q-mt-md">Loading events...</div>
                </div>

                <template v-else>
                    <q-table
                        :rows="events"
                        :columns="columns"
                        row-key="id"
                        :rows-per-page-options="[10, 20, 25, 50]"
                        :pagination="pagination"
                        @request="onRequest"
                        :loading="loading"
                        binary-state-sort
                        server-side-sort
                        server-side-pagination
                        class="events-table"
                        no-data-label="No events found"
                    >
                        <template v-slot:body="props">
                            <q-tr :props="props">
                                <q-td key="title" :props="props">
                                    {{ props.row.title }}
                                </q-td>
                                <q-td key="city" :props="props">
                                    {{ props.row.city }}
                                </q-td>
                                <q-td key="date" :props="props">
                                    {{ formatDate(props.row.start) }}
                                </q-td>
                                <q-td key="actions" :props="props">
                                    <div class="row q-col-gutter-sm">
                                        <div>
                                            <q-btn
                                                round
                                                flat
                                                color="primary"
                                                icon="open_in_new"
                                                size="sm"
                                                @click="
                                                    navigateToEventDashboard(
                                                        props.row,
                                                    )
                                                "
                                                class="q-mr-xs"
                                            >
                                                <q-tooltip
                                                    >Open Dashboard</q-tooltip
                                                >
                                            </q-btn>
                                        </div>
                                        <div>
                                            <q-btn
                                                round
                                                flat
                                                color="primary"
                                                icon="visibility"
                                                size="sm"
                                                @click="previewEvent(props.row)"
                                                class="q-mr-xs"
                                            >
                                                <q-tooltip
                                                    >Preview Event</q-tooltip
                                                >
                                            </q-btn>
                                        </div>
                                        <div>
                                            <q-btn
                                                round
                                                flat
                                                color="primary"
                                                icon="edit"
                                                size="sm"
                                                @click="editEvent(props.row)"
                                                class="q-mr-xs"
                                            >
                                                <q-tooltip
                                                    >Edit Event</q-tooltip
                                                >
                                            </q-btn>
                                        </div>
                                        <div>
                                            <q-btn
                                                round
                                                flat
                                                color="primary"
                                                icon="copy"
                                                size="sm"
                                                @click="
                                                    duplicateEvent(props.row)
                                                "
                                                class="q-mr-xs"
                                            >
                                                <q-tooltip
                                                    >Copy Event</q-tooltip
                                                >
                                            </q-btn>
                                        </div>
                                        <div>
                                            <q-btn
                                                round
                                                flat
                                                color="negative"
                                                icon="delete"
                                                size="sm"
                                                @click="
                                                    confirmDeleteEvent(
                                                        props.row,
                                                    )
                                                "
                                            >
                                                <q-tooltip
                                                    >Delete Event</q-tooltip
                                                >
                                            </q-btn>
                                        </div>
                                    </div>
                                </q-td>
                            </q-tr>
                        </template>
                    </q-table>

                    <div
                        v-if="events.length === 0 && !loading"
                        class="text-center q-pa-md"
                    >
                        <p>
                            No events found. Create your first event to get
                            started!
                        </p>
                    </div>
                </template>
            </q-card-section>
        </q-card>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import dayjs from "dayjs";
import axios from 'axios';

import { useRouter } from "vue-router";
const router = useRouter();

const $q = useQuasar();
const events = ref([]);
const searchTerm = ref("");
const loading = ref(false);
const pagination = ref({ 
    page: 1, 
    rowsPerPage: 20,
    rowsNumber: 0
});

// Pagy metadata from backend
const pagyData = ref({});

const loadEvents = async (page = 1, perPage = 20) => {
    loading.value = true;
    try {
        const response = await axios.get('/events/list', {
            params: {
                page: page,
                per_page: perPage
            }
        });

        console.log('API Response:', response.data);
        
        // Ensure events is always an array
        const eventsData = response.data.events || [];
        events.value = Array.isArray(eventsData) ? eventsData : [];
        
        pagyData.value = response.data.pagy || {};
        
        // Update pagination info with fallbacks
        pagination.value = {
            page: pagyData.value.page || 1,
            rowsPerPage: pagyData.value.items || 20,
            rowsNumber: pagyData.value.count || 0
        };
        
    } catch (error) {
        console.error('Error loading events:', error);
        $q.notify({
            type: 'negative',
            message: 'Failed to load events'
        });
    } finally {
        loading.value = false;
    }
};

const onRequest = (props) => {
    const { page, rowsPerPage } = props.pagination;
    loadEvents(page, rowsPerPage);
};

// Load events on component mount
onMounted(() => {
    loadEvents();
});

const columns = [
    {
        name: "title",
        label: "Event Title",
        field: "title",
        align: "left",
        sortable: true,
    },
    {
        name: "city",
        label: "City",
        field: "city",
        align: "left",
        sortable: true,
    },
    {
        name: "date",
        label: "Date",
        field: "start",
        align: "left",
        sortable: true,
    },
    { name: "actions", label: "Actions", align: "center" },
];

const formatDate = (date) => {
    if (!date) return "";
    return dayjs(date).format("DD/MM/YYYY");
};

const searchEvents = () => {
    // Implement search logic here
    loadEvents(1, pagination.value.rowsPerPage);
};

const navigateToEventDashboard = (event) => {};

const previewEvent = (event) => {
    // Implement preview logic here
};

const editEvent = (event) => {
    // Implement edit logic here
};

const duplicateEvent = (event) => {
    // Implement duplicate logic here
};

const confirmDeleteEvent = (event) => {
    // Implement delete confirmation logic here
};
</script>

<style lang="scss">
.events-table {
    width: 100%;

    .q-table__card {
        box-shadow: none;
    }

    // Fix for responsive table layout
    @media (max-width: 599px) {
        .q-table {
            overflow-x: auto;
        }
    }
}

// Ensure buttons in action columns wrap properly
.q-gutter-sm {
    flex-wrap: wrap;
    justify-content: center;
}

// Make sure table takes full width and has proper borders
:deep(.q-table__container) {
    border-radius: 4px;
    overflow: hidden;
}

:deep(.q-table th) {
    font-weight: 500;
}

:deep(.q-table__bottom) {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
