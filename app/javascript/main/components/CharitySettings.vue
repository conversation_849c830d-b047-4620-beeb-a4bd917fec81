<template>
  <q-page>
    <div v-if="store.loading" class="q-pa-xl text-center">
      <q-spinner color="primary" size="3em" class="q-mb-md" />
      <div class="text-h5">Loading payment info...</div>
      <div class="text-body1">
        Please wait while we load the payment settings for you.
      </div>
    </div>
    <div v-else-if="!store.charitySettings">
      <div class="q-pa-xl text-center">
        <div class="text-h5">No payment info loaded</div>
        <div class="text-body1">Please try again</div>
      </div>
    </div>
    <div v-else class="q-pa-md">
      <div class="text-h4 q-mb-md">Charity Settings</div>

      <q-card class="q-mb-lg">
        <q-card-section>
          <div class="text-h6 q-mb-md">Fundraising Options</div>

          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="q-mb-md">
                <q-checkbox
                  v-model="store.charitySettings.enable_charity_fundraising"
                  label="Enable Charity Fundraising for Events"
                />
                <div class="text-caption q-ml-md">
                  When enabled, attendees can make charitable donations during
                  the booking process.
                </div>
              </div>

              <q-input
                v-model="store.charitySettings.charity_name"
                outlined
                label="Charity Name"
                :disable="!store.charitySettings.enable_charity_fundraising"
                class="q-mb-md"
              />

              <q-input
                v-model="store.charitySettings.charity_registration_number"
                outlined
                label="Charity Registration Number"
                :disable="!store.charitySettings.enable_charity_fundraising"
                class="q-mb-md"
              />

              <q-input
                v-model="store.charitySettings.suggested_donation_amount"
                outlined
                label="Suggested Donation Amount (£)"
                type="number"
                :disable="!store.charitySettings.enable_charity_fundraising"
                class="q-mb-md"
                :rules="[(val) => val >= 0 || 'Amount must be positive']"
              />

              <q-input
                v-model="store.charitySettings.charity_description"
                outlined
                label="Charity Description"
                type="textarea"
                :disable="!store.charitySettings.enable_charity_fundraising"
                class="q-mb-md"
              />

              <q-btn
                color="primary"
                @click="saveCharitySettings"
                :loading="store.loading"
                :label="store.loading ? 'Saving...' : 'Save Charity Settings'"
              />
            </div>

            <div class="col-12 col-md-6">
              <q-card class="bg-grey-2">
                <q-card-section>
                  <div class="text-h6">About Charity Settings</div>
                  <p class="q-mb-md">
                    Enable charitable donations during your event bookings to
                    support your chosen charity.
                  </p>
                  <p class="q-mb-md">
                    When attendees book tickets, they will have the option to
                    add a donation to their purchase.
                  </p>
                  <p>
                    Make sure to provide accurate charity information as this
                    will be displayed to attendees during checkout.
                  </p>
                </q-card-section>
              </q-card>

              <q-card class="q-mt-md bg-yellow-1">
                <q-card-section>
                  <div class="text-weight-bold">
                    <q-icon name="info" color="warning" class="q-mr-xs" />
                    Important Note
                  </div>
                  <p>
                    Ensure you have permission from the charity to collect
                    donations on their behalf and have a process in place to
                    transfer the funds collected.
                  </p>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { onMounted } from "vue";
import { useQuasar } from "quasar";

import { useMainStore } from "../../stores/main";

const $q = useQuasar();
const store = useMainStore();

// Methods
const saveCharitySettings = async () => {
  saving.value = true;

  try {
    await store.saveCharitySettings();
    $q.notify({
      type: "positive",
      message: "Charity settings saved successfully!",
    });
  } catch (error) {
    console.error("Failed to save charity settings:", error);

    $q.notify({
      type: "negative",
      message: "Failed to save charity settings. Please try again.",
    });
  } finally {
    saving.value = false;
  }
};

// Lifecycle hooks
onMounted(async () => {
  try {
    await store.fetchCharitySettings();
  } catch (error) {
    console.error("Failed to fetch charity settings:", error);

    $q.notify({
      type: "warning",
      message: "Failed to load charity settings.",
    });
  }
});
</script>

<style lang="scss" scoped>
/* Any component-specific styling */
</style>
