<template>
  <q-badge :color="color">{{ status }}</q-badge>
</template>
<script setup>
import { defineProps, onMounted, ref } from "vue";

const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const color = ref("default");
const status = ref("");

onMounted(() => {
  if (props.event) {
    if (props.event.live) {
      color.value = "secondary";
      status.value = "live";
    } else if (props.event.expired) {
      color.value = "error";
      status.value = "expired";
    } else if (props.event.complete) {
      color.value = "positive";
      status.value = "complete";
    } else {
      color.value = "info";
      status.value = "inprogress";
    }
  }
});
</script>
