<template>
  <div class="text-center">
    <div class="text-subtitle1 q-mb-sm">{{ titleText }}</div>
    <svg 
      class="progress q-mt-sm" 
      version="1.1" 
      xmlns="http://www.w3.org/2000/svg" 
      xmlns:xlink="http://www.w3.org/1999/xlink" 
      x="0px" 
      y="0px" 
      viewBox="0 0 80 80" 
      xml:space="preserve"
    >
      <path 
        class="track" 
        transform="translate(-10 8) rotate(45 50 50)" 
        d="M40,72C22.4,72,8,57.6,8,40C8,22.4,22.4,8,40,8c17.6,0,32,14.4,32,32"
      ></path>
      <text class="display" x="50%" y="60%">{{ dialCount }}</text>
      <path 
        class="fill" 
        :style="dialStyle" 
        transform="translate(-10 8) rotate(45 50 50)" 
        d="M40,72C22.4,72,8,57.6,8,40C8,22.4,22.4,8,40,8c17.6,0,32,14.4,32,32"
      ></path>
    </svg>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
  titleText: {
    type: String,
    required: true
  },
  dialCount: {
    type: Number,
    required: true
  },
  dialTotal: {
    type: Number,
    required: true
  }
});

const max = ref(150.72259521484375);

// Compute the dial styling based on count and total
const dialStyle = computed(() => {
  const percent = 100 / props.dialTotal * props.dialCount;
  const offset = (100 - percent) / 100 * max.value;
  
  return {
    "stroke-dashoffset": offset
  };
});
</script>

<style lang="scss" scoped>
.progress {
  width: 80px;
  height: 80px;
}

.progress .track, .progress .fill {
  fill: none;
  stroke-width: 8;
  transform: rotate(90deg);
  transform-origin: center;
}

.progress .track {
  stroke: #ecf0f1;
}

.progress .fill {
  stroke: var(--q-primary); // Use Quasar primary color variable
  stroke-dasharray: 150.72259521484375;
  stroke-dashoffset: 0;
  transition: stroke-dashoffset 0.5s ease;
}

.progress .display {
  font-size: 20px;
  font-weight: bold;
  fill: var(--q-primary); // Use Quasar primary color variable
  text-anchor: middle;
  dominant-baseline: middle;
}
</style>