<template>
  <q-page>
    <div v-if="store.loading" class="q-pa-xl text-center">
      <q-spinner color="primary" size="3em" class="q-mb-md" />
      <div class="text-h5">Loading payment info...</div>
      <div class="text-body1">
        Please wait while we load the payment settings for you.
      </div>
    </div>
    <div v-else-if="!store.stripeAccount">
      <div class="q-pa-xl text-center">
        <div class="text-h5">No payment info loaded</div>
        <div class="text-body1">Please try again</div>
      </div>
    </div>
    <div v-else class="q-pa-md">
      <div class="text-h4 q-mb-md">Payment Settings</div>

      <!-- Stripe Connect Section -->
      <q-card class="q-mb-lg">
        <q-card-section>
          <div class="text-h6 q-mb-md">Stripe Connect (Recommended)</div>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div v-if="connectStatus.isConnected" class="q-mb-md">
                <q-banner class="bg-positive text-white rounded-borders q-mb-md">
                  <template v-slot:avatar>
                    <q-icon name="check_circle" />
                  </template>
                  Your Stripe account is connected and ready to accept payments!
                </q-banner>
                
                <div class="text-subtitle2 q-mb-sm">Connected Account Details:</div>
                <div class="text-body2 q-mb-xs">
                  <strong>Account ID:</strong> {{ connectStatus.account?.stripe_user_id || 'N/A' }}
                </div>
                <div class="text-body2 q-mb-md">
                  <strong>Account Type:</strong> {{ connectStatus.account?.stripe_account_type || 'N/A' }}
                </div>

                <q-btn
                  color="negative"
                  outline
                  @click="disconnectStripeAccount"
                  :loading="disconnecting"
                  icon="link_off"
                  label="Disconnect Account"
                  class="q-mr-sm"
                />
                <q-btn
                  color="primary"
                  outline
                  @click="refreshConnectStatus"
                  :loading="refreshing"
                  icon="refresh"
                  label="Refresh Status"
                />
              </div>

              <div v-else class="q-mb-md">
                <q-banner class="bg-orange text-white rounded-borders q-mb-md">
                  <template v-slot:avatar>
                    <q-icon name="warning" />
                  </template>
                  Connect your Stripe account to start accepting payments seamlessly.
                </q-banner>

                <q-btn
                  color="primary"
                  @click="connectStripeAccount"
                  :loading="connecting"
                  icon="link"
                  label="Connect with Stripe"
                  class="q-mb-md"
                  size="lg"
                />
              </div>
            </div>

            <div class="col-12 col-md-6">
              <q-card class="bg-blue-1">
                <q-card-section>
                  <div class="text-h6">Why use Stripe Connect?</div>
                  <ul class="q-mb-md">
                    <li>Secure and automatic setup</li>
                    <li>No need to manually copy API keys</li>
                    <li>Simplified onboarding process</li>
                    <li>Automatic updates and maintenance</li>
                    <li>Better security and compliance</li>
                  </ul>
                  <div class="text-caption">
                    Stripe Connect allows us to securely connect to your Stripe account
                    without you having to manually enter API keys.
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Manual API Keys Section -->
      <q-card class="q-mb-lg">
        <q-card-section>
          <div class="text-h6 q-mb-md">
            Manual API Keys Setup
            <q-chip 
              color="orange" 
              text-color="white" 
              size="sm" 
              class="q-ml-sm"
            >
              Alternative Method
            </q-chip>
          </div>
          
          <q-banner 
            v-if="connectStatus.isConnected" 
            class="bg-info text-white rounded-borders q-mb-md"
          >
            <template v-slot:avatar>
              <q-icon name="info" />
            </template>
            You're already connected via Stripe Connect. Manual API keys are not needed.
          </q-banner>

          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model="store.stripeAccount.publishable_key"
                outlined
                label="Stripe Publishable Key"
                class="q-mb-md"
                :disable="connectStatus.isConnected"
              />

              <q-input
                v-model="store.stripeAccount.secret_key"
                outlined
                label="Stripe Secret Key"
                class="q-mb-md"
                :disable="connectStatus.isConnected"
              />

              <div class="q-mb-md">
                <q-checkbox
                  v-model="store.stripeAccount.test_mode"
                  label="Use Test Mode"
                  :disable="connectStatus.isConnected"
                />
              </div>

              <q-btn
                color="primary"
                @click="updatePaymentInfo"
                :loading="store.loading"
                :label="store.loading ? 'Saving...' : 'Save Settings'"
                :disable="connectStatus.isConnected"
              />
            </div>

            <div class="col-12 col-md-6">
              <q-card class="bg-grey-2">
                <q-card-section>
                  <div class="text-h6">About Manual Setup</div>
                  <p class="q-mb-md">
                    If you prefer to set up Stripe manually, you can enter your API
                    keys directly here.
                  </p>
                  <p class="q-mb-md">
                    You'll need to sign up for a Stripe account and get your API
                    keys from the Stripe dashboard.
                  </p>
                  <p class="q-mb-sm">
                    <q-icon name="open_in_new" class="q-mr-xs" />
                    <a
                      href="https://dashboard.stripe.com/apikeys"
                      target="_blank"
                      rel="noopener"
                    >
                      Get your API keys from the Stripe Dashboard
                    </a>
                  </p>
                  <p>
                    <q-icon name="open_in_new" class="q-mr-xs" />
                    <a
                      href="https://stripe.com/docs/keys"
                      target="_blank"
                      rel="noopener"
                    >
                      Learn more about Stripe API keys
                    </a>
                  </p>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { useQuasar } from "quasar";

import { useMainStore } from "../../stores/main";

const $q = useQuasar();
const store = useMainStore();

// Reactive variables for loading states
const connecting = ref(false);
const disconnecting = ref(false);
const refreshing = ref(false);
const connectStatus = ref({
  isConnected: false,
  account: null
});

const updatePaymentInfo = async () => {
  try {
    await store.saveStripeAccount();
    $q.notify({
      type: "positive",
      message: "Payment information updated successfully!",
    });
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Failed to update payment information!",
    });
  }
};

const connectStripeAccount = () => {
  connecting.value = true;
  try {
    // Show notification before redirect
    $q.notify({
      type: "info",
      message: "Redirecting to Stripe Connect...",
    });

    // This will redirect immediately, so code after this won't execute
    store.initiateStripeConnect();
  } catch (error) {
    console.error("Error connecting to Stripe:", error);
    $q.notify({
      type: "negative",
      message: "Failed to connect to Stripe. Please try again.",
    });
    connecting.value = false;
  }
};

const disconnectStripeAccount = async () => {
  disconnecting.value = true;
  try {
    await store.deauthorizeStripeConnect();
    await refreshConnectStatus();
    $q.notify({
      type: "positive",
      message: "Stripe account disconnected successfully!",
    });
  } catch (error) {
    console.error("Error disconnecting Stripe account:", error);
    $q.notify({
      type: "negative",
      message: "Failed to disconnect Stripe account. Please try again.",
    });
  } finally {
    disconnecting.value = false;
  }
};

const refreshConnectStatus = async () => {
  refreshing.value = true;
  try {
    const status = await store.checkStripeConnectStatus();
    connectStatus.value = status;
    await store.fetchStripeAccount(); // Refresh the account data in store
  } catch (error) {
    console.error("Error refreshing Stripe Connect status:", error);
    $q.notify({
      type: "negative",
      message: "Failed to refresh connection status.",
    });
  } finally {
    refreshing.value = false;
  }
};

const checkForRedirectSuccess = () => {
  // Check URL params to see if we're returning from Stripe Connect
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const error = urlParams.get('error');
  
  if (code) {
    $q.notify({
      type: "positive",
      message: "Stripe Connect completed successfully!",
      timeout: 5000
    });
    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname + window.location.hash);
  } else if (error) {
    $q.notify({
      type: "negative",
      message: "Stripe Connect was cancelled or failed.",
      timeout: 5000
    });
    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname + window.location.hash);
  }
};

onMounted(async () => {
  // Check for redirect success/error first
  checkForRedirectSuccess();
  
  // Fetch stripe account data
  await store.fetchStripeAccount();
  console.log("Payment info fetched:", store.stripeAccount);
  
  // Check Stripe Connect status
  await refreshConnectStatus();
});
</script>
