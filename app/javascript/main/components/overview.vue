<template>
  <q-page padding>
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section>Upcoming Events</q-card-section>
          <q-separator />
          <q-card-section v-if="upcomingEvents.length > 0">
            <q-list separator>
              <q-item v-for="event in upcomingEvents" :key="event.id">
                <q-item-section>
                  <div class="text-h6">{{ event.name }}</div>
                  <div class="text-subtitle2">{{ event.date }}</div>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    flat
                    color="primary"
                    label="View Details"
                    @click="viewEvent(event.id)"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
          <q-card-section v-else>
            <div class="text-center text-grey">No upcoming events</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>
<script setup>
import { ref, onMounted } from "vue";

const upcomingEvents = ref([]);
const fetchUpcomingEvents = async () => {
  try {
    const response = await fetch("/api/events/upcoming");
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    upcomingEvents.value = await response.json();
  } catch (error) {
    console.error("Error fetching upcoming events:", error);
  }
};
onMounted(() => {
  fetchUpcomingEvents();
});
const viewEvent = (eventId) => {
  // Navigate to event details page
  window.location.href = `/events/${eventId}`;
};
</script>
