<template>
  <div class="q-pa-md">
    <div class="text-h4 q-mb-sm">
      Brand colours - select your primary brand colour here
    </div>
    <p class="q-mb-md">
      Your brand colour will be used to style your events widget so it matches
      your site
    </p>
    <q-card class="q-mb-md">
      <q-card-actions align="right">
        <q-btn
          color="primary"
          @click="saveDetails"
          :loading="saving"
          :label="saving ? 'Please wait...' : 'Save Brand Colour'"
        />
      </q-card-actions>
      <q-card-section>
        <p class="text-weight-bold q-mb-md">
          Please select your brand colour using the colour picker below
        </p>
        <div class="row q-col-gutter-md items-center">
          <div>
            <q-color
              v-model="store.getBrandColors.primaryColor"
              @update:model-value="setPrimaryColour"
              default-view="palette"
              no-header
            />
          </div>
        </div>

        <div class="q-mt-lg">
          <div class="text-weight-bold q-mb-sm">Preview</div>
          <div
            class="preview-box"
            :style="{ backgroundColor: store.getBrandColors.primaryColor }"
          >
            <span>Sample text with your brand colour</span>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useMainStore } from "../../stores/main";

const $q = useQuasar();
const store = useMainStore();

// Data state
const saving = ref(false);

// Methods

const setPrimaryColour = (value) => {
  store.brandColors.primary = value;
};

const saveDetails = async () => {
  saving.value = true;

  try {
    await store.saveBrandColors();

    $q.notify({
      type: "positive",
      message: "Brand colour updated successfully",
    });
  } catch (error) {
    console.error(error);

    $q.notify({
      type: "negative",
      message:
        "There was a problem changing your brand colour. Try again or contact us if issue persists.",
    });
  } finally {
    saving.value = false;
  }
};

// Lifecycle hooks
onMounted(async () => {
  try {
    await store.fetchBrandColors();
  } catch (error) {
    console.error(error);

    $q.notify({
      type: "warning",
      message: "Failed to load your current brand colour",
    });
  }
});
</script>

<style lang="scss" scoped>
.preview-box {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: white;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
  font-weight: bold;
}
</style>
