<template>
  <q-page>
    <q-card class="q-mb-md">
      <q-card-section class="q-pb-none">
        <div class="hg-underline text-h6">Global Terms and Conditions</div>
      </q-card-section>

      <q-card-section>
        <div class="row q-mb-md items-center">
          <div>
            Here you can put the terms and conditions you wish to apply to all
            events.
          </div>
          <q-btn round flat color="info" size="sm" class="q-ml-sm" icon="info">
            <q-tooltip
              anchor="bottom middle"
              self="top middle"
              :offset="[0, 8]"
              max-width="400px"
            >
              This is where you can set up the global terms and conditions for
              your events. This will be the default terms and conditions for all
              events.
            </q-tooltip>
          </q-btn>
        </div>

        <div class="q-mb-md">
          <q-input
            v-model="store.globalTerms"
            type="textarea"
            rows="10"
            filled
            autogrow
            placeholder="Enter terms and conditions here"
          />
        </div>

        <q-card v-if="store.globalTerms" class="q-mt-md q-mb-md">
          <q-card-section>
            <div class="preview" v-html="store.globalTerms"></div>
          </q-card-section>
        </q-card>

        <q-btn
          type="submit"
          color="primary"
          :loading="store.loading"
          @click="saveTerms"
          :disable="!store.globalTerms"
          label="Save"
        />
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { onMounted } from "vue";
import { useQuasar } from "quasar";

import { useMainStore } from "../../stores/main";

const $q = useQuasar();
const store = useMainStore();

// Methods
const saveTerms = async () => {
  try {
    await store.saveGlobalTerms();

    $q.notify({
      type: "positive",
      message: "Global Terms Saved Successfully",
    });
  } catch (error) {
    console.error(error);

    $q.notify({
      type: "negative",
      message: "Global Terms Not Saved",
    });
  }
};

// Lifecycle hooks
onMounted(async () => {
  try {
    await store.fetchGlobalTerms();
  } catch (error) {
    console.error(error);

    $q.notify({
      type: "negative",
      message: "Failed to load terms and conditions",
    });
  }
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.preview {
  padding: 10px;
  min-height: 100px;
}
</style>
