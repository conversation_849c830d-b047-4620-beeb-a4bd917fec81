<!-- Navigation menu with collapsible sidebar -->
<template>
  <q-layout view="hHh lpR fFf">
    <q-header elevated class="bg-primary text-white">
      <q-toolbar>
        <q-btn dense flat round icon="menu" @click="toggleLeftDrawer" />
        <q-toolbar-title>Events Admin</q-toolbar-title>
        <q-space />
        <NotifyLink />
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      side="left"
      bordered
      :mini="miniState"
      @mouseover="miniState = false"
      @mouseout="miniState = true"
    >
      <q-list padding>
        <q-item-label header>
          <div class="text-center q-py-sm">
            <strong>{{ currentUser }} (#{{ orgId }})</strong>
          </div>
        </q-item-label>

        <q-separator />

        <q-item-label header class="text-center"
          >Business Settings</q-item-label
        >

        <q-item clickable v-ripple @click="goToOld">
          <q-item-section avatar>
            <q-icon name="calendar_month" color="yellow" />
          </q-item-section>
          <q-item-section>
            <span class="text-yellow">View Legacy Events</span>
          </q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'payment-admin' }">
          <q-item-section avatar>
            <q-icon name="credit_card" />
          </q-item-section>
          <q-item-section>Manage Payments</q-item-section>
        </q-item>

        <q-item
          clickable
          v-ripple
          tag="a"
          href="https://dashboard.stripe.com/dashboard"
          target="_blank"
        >
          <q-item-section avatar>
            <q-icon name="fab fa-cc-stripe" />
          </q-item-section>
          <q-item-section>Your Stripe Account</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'events-list' }">
          <q-item-section avatar>
            <q-icon name="dashboard" />
          </q-item-section>
          <q-item-section>View Events List</q-item-section>
        </q-item>

        <q-item
          clickable
          v-ripple
          tag="a"
          :href="`/organisation/${orgId}/events`"
        >
          <q-item-section avatar>
            <q-icon name="share" />
          </q-item-section>
          <q-item-section>View Live Events</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'events-archived' }">
          <q-item-section avatar>
            <q-icon name="archive" />
          </q-item-section>
          <q-item-section>View Expired Events</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'fees-calculator' }">
          <q-item-section avatar>
            <q-icon name="calculate" />
          </q-item-section>
          <q-item-section>Fee Calculator</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'summary-report' }">
          <q-item-section avatar>
            <q-icon name="description" />
          </q-item-section>
          <q-item-section>Summary Report</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'global-terms' }">
          <q-item-section avatar>
            <q-icon name="gavel" />
          </q-item-section>
          <q-item-section>Global Terms</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'charity-settings' }">
          <q-item-section avatar>
            <q-icon name="favorite" />
          </q-item-section>
          <q-item-section>Charity Settings</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'vat-settings' }">
          <q-item-section avatar>
            <q-icon name="payments" />
          </q-item-section>
          <q-item-section>VAT Settings</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'discount-codes' }">
          <q-item-section avatar>
            <q-icon name="local_offer" />
          </q-item-section>
          <q-item-section>Discount Codes</q-item-section>
        </q-item>

        <q-item v-if="website" clickable v-ripple tag="a" :href="website">
          <q-item-section avatar>
            <q-icon name="home" />
          </q-item-section>
          <q-item-section>Your Website</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'fb-pixel-code' }">
          <q-item-section avatar>
            <q-icon name="fab fa-facebook-square" />
          </q-item-section>
          <q-item-section>Facebook Pixel</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'manage-users' }">
          <q-item-section avatar>
            <q-icon name="people" />
          </q-item-section>
          <q-item-section>Manage Contacts</q-item-section>
        </q-item>

        <q-item clickable v-ripple :to="{ name: 'brand-colours' }">
          <q-item-section avatar>
            <q-icon name="palette" />
          </q-item-section>
          <q-item-section>Brand Colors</q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view></router-view>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref, computed } from "vue";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/auth";
import NotifyLink from "@/common/notifications-link.vue";

const $q = useQuasar();
const authStore = useAuthStore();
const leftDrawerOpen = ref(true);
const miniState = ref(true); // Start in mini mode

// Use auth store for current user, fallback to window for other data
const currentUser = computed(
  () => authStore.user?.name || authStore.user?.email || ""
);
const website = window.org_website;
const orgId = window.org_id;

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const goToOld = () => {
  $q.dialog({
    title: "Warning",
    message: "This will redirect you to eventstop. Continue?",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    window.location.href = "https://www.eventstop.co.uk";
  });
};
</script>

<style lang="scss">
.q-drawer {
  .q-item {
    border-radius: 0 32px 32px 0;
    margin: 8px 16px 8px 0;

    &.q-router-link-active {
      color: #006572;
      background: rgba(0, 101, 114, 0.1);
    }
  }
}
</style>
