<template>
  <q-page>
    <div class="summary-report">
      <q-card class="q-mb-lg">
        <q-card-section class="q-pb-none">
          <div class="hg-underline">Summary Report</div>
        </q-card-section>

        <q-card-section>
          <div class="q-mb-md">
            <p>
              Please be aware that the summary report may take some time to be
              generated. If it is taking too long to load you could try changing
              the filter search criteria to be more restrictive.
            </p>
          </div>

          <div class="q-mb-md">
            <DateFilters :showDefault="false" />

            <div class="row q-col-gutter-sm q-my-sm">
              <div class="col-12 col-md-auto">
                <q-select
                  v-model="eventStatusFilter"
                  :options="eventStatusOptions"
                  label="Event Status"
                  dense
                  outlined
                  emit-value
                  map-options
                  option-value="value"
                  option-label="text"
                />
              </div>

              <div class="col-12 col-md-auto">
                <Autocomplete
                  label="All Events"
                  :isAsync="false"
                  :items="eventSearch"
                  :inLine="true"
                />
              </div>

              <div class="col-12 col-md-auto">
                <q-select
                  v-model="selectedEventType"
                  :options="eventTypeOptions"
                  label="Event Type"
                  dense
                  outlined
                  emit-value
                  map-options
                  option-value="value"
                  option-label="text"
                />
              </div>

              <div class="col-12 col-md-auto">
                <q-btn
                  color="primary"
                  flat
                  @click="loadData()"
                  label="Filter"
                />
              </div>

              <div class="col-12 col-md-auto">
                <q-btn color="info" flat @click="clearFilter()" label="Clear" />
              </div>
            </div>

            <div class="text-right">
              <q-btn
                color="positive"
                @click="exportReport()"
                icon="download"
                label="Export Report"
              />
            </div>
          </div>

          <div class="text-center q-my-md" v-if="loading">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-sm">Loading Report...</div>
          </div>

          <q-table
            :rows="report"
            :columns="columns"
            row-key="ev_id"
            separator="cell"
            class="q-mb-md"
          >
            <template v-slot:body-cell-name="props">
              <q-td :props="props">
                <router-link :to="`/dashboard/${props.row.ev_id}`">{{
                  props.row.name
                }}</router-link>
              </q-td>
            </template>

            <template v-slot:body-cell-forecast="props">
              <q-td :props="props">
                {{ formatCurrency(props.row.forecast) }}
              </q-td>
            </template>
          </q-table>

          <div class="totals-row q-mb-md" v-if="lastPage && report.length > 0">
            <div class="total-label">Grand Total</div>
            <div class="total-value">
              {{ formatCurrency(grandTotals.forecast) }}
            </div>
            <div class="total-value">{{ grandTotals.profit }}</div>
            <div class="total-value">{{ grandTotals.stripe }}</div>
            <div class="total-value">{{ grandTotals.hg_fees }}</div>
            <div class="total-value">{{ grandTotals.total_fees }}</div>
            <div class="total-value">{{ grandTotals.revenue }}</div>
          </div>

          <q-pagination
            v-model="selectedPage"
            :max="Math.ceil(totalItems / 100)"
            :max-pages="6"
            boundary-links
            @update:model-value="handlePageChange"
            v-if="totalItems > 100"
          />
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, inject } from "vue";
import { useQuasar } from "quasar";
import { useMainStore } from "@/stores/main";
import axios from "axios";

import DateFilters from "@/main/common/DateFilters.vue";
import Autocomplete from "@/main/common/Autocomplete.vue";
import eventBus from "../eventBus";

// Store and state management
const $q = useQuasar();
const store = useMainStore();

// State variables
const totalItems = ref(0);
const selectedPage = ref(1);
const grandTotals = ref({
  forecast: 0,
  profit: 0,
  stripe: 0,
  hg_fees: 0,
  total_fees: 0,
  revenue: 0,
});

const eventStatusFilter = ref(1);
const eventStatusOptions = ref([
  { text: "All", value: 0 },
  { text: "Live", value: 1 },
  { text: "Expired", value: 2 },
]);

const selectedEventType = ref(0);
const eventTypeOptions = ref([{ text: "All Event Types", value: 0 }]);

const dateFromFilter = ref(null);
const dateToFilter = ref(null);
const eventSearch = ref([]);
const eventSelected = ref(null);
const loading = ref(false);

// Table columns definition
const columns = [
  {
    name: "name",
    align: "left",
    label: "Events",
    field: "name",
    sortable: true,
  },
  {
    name: "forecast",
    align: "left",
    label: "Forecasted Net Ticket Revenue",
    field: "forecast",
    sortable: true,
    headerStyle: "width: 180px",
    headerClasses: "column-with-note",
    classes: "forecast-column",
  },
  {
    name: "profit",
    align: "left",
    label: "Net Revenue",
    field: "profit",
    sortable: true,
  },
  {
    name: "stripe",
    align: "left",
    label: "Stripe Fees",
    field: "stripe",
    sortable: true,
  },
  {
    name: "hg_fees",
    align: "left",
    label: "Application Fees",
    field: "hg_fees",
    sortable: true,
  },
  {
    name: "total_fees",
    align: "left",
    label: "Total Fees",
    field: "total_fees",
    sortable: true,
  },
  {
    name: "revenue",
    align: "left",
    label: "Gross Revenue",
    field: "revenue",
    sortable: true,
  },
];

// Computed properties
const report = computed(() => store.getSummaryReport);

const lastPage = computed(() => {
  return Math.ceil(totalItems.value / 100) === selectedPage.value;
});

// Methods
const formatCurrency = (value) => {
  return value.toLocaleString("en-UK", {
    style: "currency",
    currency: "GBP",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

const loadData = async (page = null) => {
  loading.value = true;

  if (page) {
    selectedPage.value = page;
  }

  try {
    const response = await axios.get(`/dashboard/reports.json`, {
      params: {
        page: selectedPage.value,
        eventStatusFilter: eventStatusFilter.value,
        eventTypeFilter: selectedEventType.value,
        dateFromFilter: dateFromFilter.value,
        dateToFilter: dateToFilter.value,
        eventFilter: eventSelected.value,
      },
    });
    if (response?.data) {
      store.setSummaryReport(response.data.report);
      totalItems.value = response.data.no_of_rows;
      grandTotals.value = response.data.grand_totals;
    }
  } catch (error) {
    $q.notify({
      message: "Failed to load report data",
      type: "negative",
    });
    console.error("Error loading report data:", error);
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page) => {
  loadData(page);
};

const clearFilter = () => {
  eventStatusFilter.value = 1;
  selectedEventType.value = 0;
  eventSelected.value = null;
  eventSearch.value = [];
  dateFromFilter.value = null;
  dateToFilter.value = null;

  if (eventBus) {
    eventBus.emit("clearAutocomplete");
    eventBus.emit("resetDateFilter");
  }

  loadData();
};

const exportReport = () => {
  const params = new URLSearchParams({
    eventStatusFilter: eventStatusFilter.value,
    eventTypeFilter: selectedEventType.value,
    dateFromFilter: dateFromFilter.value || "",
    dateToFilter: dateToFilter.value || "",
    eventFilter: eventSelected.value || "",
  });

  window.location.href = `/dashboard/reports.csv?${params.toString()}`;
};

// Event listeners and component setup
onMounted(async () => {
  // Load event types
  try {
    const response = await axios.get(`/dashboard/get_event_types.json`);
    response.data.type_filter.forEach((type) => {
      eventTypeOptions.value.push({ text: type.name, value: type.name });
    });
  } catch (error) {
    console.error("Error loading event types:", error);
  }

  // Set up event listeners
  if (eventBus) {
    // Handle date filter changes
    eventBus.on("datesChanged", (from, to) => {
      dateFromFilter.value = from;
      dateToFilter.value = to;
    });

    // Handle typeahead events for autocomplete
    eventBus.on("typeahead", async (search) => {
      eventSelected.value = search;
      try {
        const response = await axios.get(
          `/events/typeahead_events?query=${search}&referrer=client`
        );
        eventSearch.value = response.data.map((ev) => ev.title);
      } catch (error) {
        console.error("Error in typeahead search:", error);
      }
    });

    // Handle autocomplete selection
    eventBus.on("autoSelected", (result) => {
      eventSelected.value = result;
    });
  }

  // Initial data load
  loadData();
});
</script>

<style lang="scss">
.summary-report {
  width: 100%;
}

.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.column-with-note {
  .q-table__sort-icon {
    margin-left: 5px;
  }

  &::after {
    content: "If all tickets are full price";
    display: block;
    font-size: 0.8rem;
    font-weight: normal;
    margin-top: 0.25rem;
  }
}

// Replace Bootstrap's grid with Quasar grid system
.totals-row {
  display: grid;
  grid-template-columns: 200px repeat(6, 1fr);
  background-color: var(--q-grey-2);
  border: 1px solid var(--q-grey-3);
  border-top: none;
  padding: 12px 0;
  font-weight: bold;
}

.total-label,
.total-value {
  padding: 0 12px;
}

// Make the table fully responsive
:deep(.q-table) {
  width: 100%;

  @media (max-width: 599px) {
    overflow-x: auto;
  }

  th {
    font-weight: 500;
  }

  td a {
    color: var(--q-primary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

:deep(.q-table__container) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.q-table__bottom) {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
