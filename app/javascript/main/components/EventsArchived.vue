<template>
  <div class="q-pa-md">
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section class="bg-grey-2">
            <div class="text-h6 q-pb-sm q-mb-sm" style="border-bottom: 2px solid #f0f0f0;">
              Expired Events
            </div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md q-mb-md">
              <div class="col-auto">
                <q-input
                  v-model="eventFilter"
                  placeholder="Search Events..."
                  clearable
                  dense
                  outlined
                  class="q-mr-md"
                />
              </div>

              <div class="col-auto">
                <q-btn-dropdown
                  color="primary"
                  label="Filter by tags"
                  icon="filter_alt"
                  outline
                >
                  <q-card class="q-pa-none" style="min-width: 250px">
                    <q-card-section>
                      <div class="text-h6">Filter By Event Tags</div>
                    </q-card-section>
                    <q-card-section>
                      <q-select
                        v-model="tagsSelected"
                        multiple
                        outlined
                        dense
                        use-chips
                        options-dense
                        :options="tagsList"
                        option-value="value"
                        option-label="label"
                        class="full-width"
                        @update:model-value="filterByTags"
                      />
                    </q-card-section>
                  </q-card>
                </q-btn-dropdown>
              </div>
            </div>

            <q-table
              :rows="filteredEvents"
              :columns="columns"
              row-key="id"
              :pagination="{ rowsPerPage: 0 }"
              class="events-table"
            >
              <template v-slot:body-cell-title="props">
                <q-td :props="props">
                  <span style="word-break: break-word">{{ props.row.title }}</span>
                </q-td>
              </template>
              
              <template v-slot:body-cell-dates="props">
                <q-td :props="props">
                  {{ formatDate(props.row.datetimefrom) }}
                  <strong>to</strong> {{ formatDate(props.row.datetimeto) }}
                </q-td>
              </template>
              
              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <q-badge color="negative" outline>Expired</q-badge>
                </q-td>
              </template>
              
              <template v-slot:body-cell-visibility="props">
                <q-td :props="props">
                  <q-badge :color="!props.row.is_public ? 'positive' : 'negative'" outline>
                    {{ !props.row.is_public ? 'Public' : 'Private' }}
                  </q-badge>
                </q-td>
              </template>
              
              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <div class="q-gutter-sm">
                    <q-btn
                      color="primary"
                      size="sm"
                      label="View"
                      @click="navigateToDashboard(props.row.id)"
                    />
                    <q-btn
                      color="primary"
                      size="sm"
                      label="Copy"
                      @click="copyEvent(props.row.id)"
                    />
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
        
        <q-btn 
          color="primary" 
          @click="$router.push({ name: 'events-list' })" 
          class="q-mt-md"
          label="View Live Events"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';
import axios from 'axios';
import _ from 'lodash';

const $q = useQuasar();

// Table columns definition
const columns = [
  { name: 'id', label: 'Event ID', field: 'id', align: 'left', sortable: true },
  { name: 'title', label: 'Event Name', field: 'title', align: 'left', sortable: true },
  { name: 'organiser', label: 'Organiser Name', field: 'organiser', align: 'left', sortable: true },
  { name: 'dates', label: 'Dates', field: 'datetimefrom', align: 'left', sortable: true },
  { name: 'status', label: 'Status', field: 'id', align: 'center' },
  { name: 'visibility', label: 'Visibility', field: 'is_public', align: 'center' },
  { name: 'actions', label: 'Actions', field: 'actions', align: 'center' }
];

// Data reactive state
const events = ref([]);
const eventFilter = ref(null);
const tags = ref([]);
const tagsList = ref([]);
const tagsSelected = ref([]);

// Computed properties
const filteredEvents = computed(() => {
  // Filter events based on search text and tags
  let result = events.value;
  
  // Filter by search text
  if (eventFilter.value) {
    result = result.filter(event => {
      return event.title.toLowerCase().includes(eventFilter.value.toLowerCase());
    });
  }

  // Filter by tags
  if (tagsSelected.value && tagsSelected.value.length > 0) {
    result = result.filter(event => {
      return event.tags && _.intersection(event.tags, tagsSelected.value).length > 0;
    });
  }

  // Sort events by ID
  return result.sort((a, b) => a.id - b.id);
});

// Methods
const formatDate = (date) => {
  if (!date) return '';
  return dayjs(date).format('DD/MM/YYYY');
};

const filterByTags = () => {
  // Force update is not needed in Vue 3 due to improved reactivity
};

const navigateToDashboard = (eventId) => {
  window.location.href = `/dashboard/${eventId}`;
};

const addTagsToList = () => {
  events.value.forEach(event => {
    if (event.tags) {
      event.tags.forEach(tag => {
        if (!tags.value.includes(tag)) {
          tags.value.push(tag);
          tagsList.value.push({ value: tag, label: tag });
        }
      });
    }
  });
};

const copyEvent = (eventId) => {
  $q.dialog({
    title: 'Confirm Copy?',
    message: 'This will copy all required parts of your existing event',
    cancel: true,
    persistent: true
  }).onOk(() => {
    axios.put(`/events/${eventId}/copy.json`)
      .then(response => {
        const newEventId = response.data.event_id;
        window.location.replace(`/events/${newEventId}/edit`);
      })
      .catch(error => {
        console.error(error);
        $q.notify({
          type: 'negative',
          message: 'Could Not Copy Event'
        });
      });
  });
};

// Lifecycle hooks
onMounted(() => {
  // Fetch expired events when the component is mounted
  axios.get('/events/expired')
    .then(response => {
      events.value = response.data;
      addTagsToList();
    })
    .catch(error => {
      console.error(error);
      $q.notify({
        type: 'negative',
        message: 'Failed to load expired events'
      });
    });
});
</script>

<style scoped>
</style>