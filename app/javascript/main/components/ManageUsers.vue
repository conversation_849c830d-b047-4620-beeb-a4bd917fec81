<template>
  <q-page>
    <div class="manage-users">
      <q-card class="q-mb-lg">
        <q-card-section class="q-pb-none">
          <div class="hg-underline">Manage Contacts</div>
        </q-card-section>

        <q-card-section>
          <!-- Filters Section -->
          <div class="q-mb-md">
            <div class="row q-col-gutter-sm q-mb-sm">
              <div class="col-12 col-md-3">
                <q-input
                  v-model="usersFilter"
                  label="Filter by Name / Email"
                  outlined
                  dense
                />
              </div>

              <div class="col-12 col-md-3">
                <q-select
                  v-model="eventTitleFilter"
                  :options="eventOptions"
                  label="Select Event"
                  outlined
                  dense
                  emit-value
                  map-options
                  option-value="value"
                  option-label="text"
                />
              </div>

              <div class="col-12 col-md-3">
                <q-select
                  v-model="optOutFilter"
                  :options="optOutOptions"
                  label="Opt Out Status"
                  outlined
                  dense
                  emit-value
                  map-options
                  option-value="value"
                  option-label="text"
                />
              </div>

              <div class="col-12 col-md-3">
                <q-select
                  v-model="eventStatusFilter"
                  :options="eventStatusOptions"
                  label="Contact Status"
                  outlined
                  dense
                  emit-value
                  map-options
                  option-value="value"
                  option-label="text"
                />
              </div>
            </div>

            <div class="row q-col-gutter-sm q-mb-sm">
              <div class="col-12 col-md-3">
                <label for="dateFrom">From</label>
                <VueDatePicker
                  v-model="dateFromFilter"
                  model-type="format"
                  format="YYYY-MM-DD"
                  input-class-name="date-picker"
                  :enable-time-picker="false"
                />
              </div>

              <div class="col-12 col-md-3">
                <label for="dateTo">To</label>
                <VueDatePicker
                  v-model="dateToFilter"
                  model-type="format"
                  format="YYYY-MM-DD"
                  input-class-name="date-picker"
                  :enable-time-picker="false"
                  :min-date="dateFromFilter"
                />
              </div>

              <div class="col-12 col-md-auto">
                <q-btn
                  color="primary"
                  flat
                  @click="filterOnServer"
                  label="Filter"
                />
              </div>

              <div class="col-12 col-md-auto">
                <q-btn color="info" flat @click="clearFilter" label="Clear" />
              </div>

              <div class="col-12 col-md-auto">
                <q-btn
                  color="primary"
                  @click="showTagsPopup = true"
                  icon="filter_alt"
                  label="Filter by tags"
                />

                <q-dialog v-model="showTagsPopup">
                  <q-card style="width: 300px; max-width: 80vw">
                    <q-card-section>
                      <div class="text-h6">Filter By Contact Tags</div>
                    </q-card-section>

                    <q-card-section>
                      <q-select
                        v-model="tagsSelected"
                        multiple
                        :options="tagsList"
                        label="Select Tags"
                        outlined
                        dense
                        emit-value
                        map-options
                        option-value="value"
                        option-label="label"
                        use-chips
                        @update:model-value="filterOnServer"
                      />
                    </q-card-section>

                    <q-card-actions align="right">
                      <q-btn flat color="primary" v-close-popup label="Close" />
                    </q-card-actions>
                  </q-card>
                </q-dialog>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="row q-col-gutter-sm q-mb-md">
            <div class="col-auto">
              <q-btn
                color="primary"
                @click="showAddContact = !showAddContact"
                label="Add New Contacts"
              />
            </div>

            <div class="col-auto">
              <q-btn
                color="positive"
                @click="exportContacts"
                icon="download"
                label="Export Contacts"
              />
            </div>
          </div>

          <!-- Add Contact Form -->
          <q-slide-transition>
            <div
              v-if="showAddContact"
              class="q-mb-md q-pa-md bg-grey-2 rounded-borders"
            >
              <NewOrgContact />
            </div>
          </q-slide-transition>

          <!-- Contacts Table -->
          <q-table
            :rows="users"
            :columns="columns"
            row-key="id"
            separator="cell"
            class="q-mb-md"
          >
            <template v-slot:body-cell-opted_out="props">
              <q-td :props="props">
                <q-btn
                  v-if="props.row.opted_out"
                  color="positive"
                  size="sm"
                  @click="changeOptOut(false, props.row)"
                  label="Opt In"
                />

                <q-btn
                  v-else
                  color="warning"
                  size="sm"
                  @click="changeOptOut(true, props.row)"
                  label="Opt Out"
                />

                <div class="opt-out-date" v-if="props.row.opted_out">
                  {{ formatDate(props.row.opted_out) }}
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  color="negative"
                  size="sm"
                  @click="removeContact(props.row)"
                  flat
                  icon="delete"
                  round
                />
              </q-td>
            </template>

            <template v-slot:body-cell-tags="props">
              <q-td :props="props">
                <OrgContactTags :contact="props.row" />
              </q-td>
            </template>
          </q-table>

          <div class="text-body1 q-mb-sm">Total Records: {{ totalItems }}</div>

          <q-pagination
            v-model="selectedPage"
            :max="Math.ceil(totalItems / 100)"
            :max-pages="6"
            boundary-links
            @update:model-value="pageChanged"
            v-if="totalItems > 100"
          />
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>
<script setup>
import { ref, computed, onMounted, inject, reactive } from "vue";
import { useQuasar } from "quasar";
import { useMainStore } from "@/stores/main"; // Import the main Pinia store
import axios from "axios";
import dayjs from "dayjs";

import NewOrgContact from "./NewOrgContact.vue";
import OrgContactTags from "./OrgContactTags.vue";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";

// Store and state management
const $q = useQuasar();
const store = useMainStore(); // Use Pinia store
const emitter = inject("emitter", null);
const showAddContact = ref(false);
const showTagsPopup = ref(false);

// State variables
const totalItems = ref(0);
const selectedPage = ref(1);
const usersFilter = ref("");
const tags = ref([]);
const tagsList = ref([]);
const tagsSelected = ref([]);
const eventTitleFilter = ref("0");
const eventOptions = ref([{ text: "All Events", value: "0" }]);
const optOutFilter = ref("0");
const optOutOptions = ref([
  { text: "Opted In/Out", value: "0" },
  { text: "Opted Out", value: "1" },
  { text: "Opted In", value: "2" },
]);
const eventStatusFilter = ref("0");
const eventStatusOptions = ref([
  { text: "All Contacts", value: "0" },
  { text: "Attended Event", value: "1" },
  { text: "Booked on Event", value: "2" },
  { text: "Cancelled Booking", value: "3" },
]);
const dateFromFilter = ref(null);
const dateToFilter = ref(null);

// Form validation tracking
const formSubmitted = ref(false);
const touchedFields = reactive({});

// Mark field as touched
const markFieldAsTouched = (fieldName) => {
  touchedFields[fieldName] = true;
};

// Check if field should show error
const shouldShowError = (fieldName) => {
  return formSubmitted.value || touchedFields[fieldName];
};

// Table columns
const columns = [
  {
    name: "email",
    align: "left",
    label: "Email Address",
    field: "email",
    sortable: true,
  },
  {
    name: "forename",
    align: "left",
    label: "Forename",
    field: "forename",
    sortable: true,
  },
  {
    name: "surname",
    align: "left",
    label: "Surname",
    field: "surname",
    sortable: true,
  },
  {
    name: "opted_out",
    align: "center",
    label: "Opted Out (opt out date)",
    sortable: false,
  },
  {
    name: "actions",
    align: "center",
    label: "Remove Contact",
    sortable: false,
  },
  { name: "tags", align: "left", label: "Tags", sortable: false },
];

// Computed properties
const users = computed(() => store.orgContacts); // Access state from Pinia store

// Methods
const formatDate = (dateString) => {
  if (!dateString) return "";
  return dayjs(dateString).format("DD/MM/YYYY HH:mm");
};

const pageChanged = async (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  try {
    const response = await axios.get("/org_user_lists.json", {
      params: {
        page: selectedPage.value,
        usersFilter: usersFilter.value,
        optOutFilter: optOutFilter.value,
        eventTitleFilter: eventTitleFilter.value,
        tagFilter: tagsSelected.value,
        eventStatusFilter: eventStatusFilter.value,
        dateToFilter: dateToFilter.value,
        dateFromFilter: dateFromFilter.value,
      },
    });

    store.setOrgContacts(response.data.users); // Use Pinia action
    totalItems.value = response.data.total_count;
  } catch (error) {
    $q.notify({
      message: "Failed to load contacts data",
      type: "negative",
    });
    console.error("Error loading contacts data:", error);
  }
};

const filterOnServer = () => {
  pageChanged();
};

const clearFilter = () => {
  usersFilter.value = "";
  eventTitleFilter.value = "0";
  optOutFilter.value = "0";
  eventStatusFilter.value = "0";
  dateFromFilter.value = null;
  dateToFilter.value = null;
  tagsSelected.value = [];
  pageChanged();
};

const addTag = (tag) => {
  if (!tags.value.includes(tag)) {
    tags.value.push(tag);
    tagsList.value.push({ value: tag, label: tag });
  }
};

const exportContacts = () => {
  const params = {
    usersFilter: usersFilter.value,
    optOutFilter: optOutFilter.value,
    eventTitleFilter: eventTitleFilter.value,
    tagFilter: tagsSelected.value,
    eventStatusFilter: eventStatusFilter.value,
    dateToFilter: dateToFilter.value || "",
    dateFromFilter: dateFromFilter.value || "",
  };

  axios
    .get("/org_user_lists/export_contacts.csv", {
      params,
      responseType: "blob",
    })
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "export_contacts.csv");
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((error) => {
      $q.notify({
        type: "negative",
        message: "Failed to export contacts",
      });
      console.error("Error exporting contacts:", error);
    });
};

const changeOptOut = async (add, user) => {
  const text = add
    ? "This user will no longer be able to receive event invites or marketing communications"
    : "This means a user will able to receive invites and other marketing emails, have they given permission for this?";

  $q.dialog({
    title: "Are you sure?",
    message: text,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const response = await axios.put(`/org_user_lists/${user.id}`, {
        is_add_opt_out: add,
      });

      user.opted_out = response.data.opted_out;

      $q.notify({
        type: "positive",
        message: "Opt-Out Updated",
      });
    } catch (error) {
      $q.notify({
        type: "negative",
        message: "Could not update",
      });
      console.error("Error updating opt-out status:", error);
    }
  });
};

const removeContact = async (user) => {
  $q.dialog({
    title: "Are you sure?",
    message: "This will remove this contact from the system",
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      await axios.put(`/organisation_users/${user.id}/delete`);

      store.removeContact(user.id); // Use Pinia action

      $q.notify({
        type: "positive",
        message: "Contact Removed",
      });
    } catch (error) {
      let errorMessage = "Could not remove contact";
      if (error.response && error.response.data && error.response.data.error) {
        errorMessage = error.response.data.error;
      }

      $q.notify({
        type: "negative",
        message: errorMessage,
      });
      console.error("Error removing contact:", error);
    }
  });
};

// Setup and event listeners
onMounted(async () => {
  // Load filter options
  try {
    const response = await axios.get("/org_user_lists/get_filter_options.json");

    // Add event options
    response.data.event_filter.forEach((ev) => {
      eventOptions.value.push({ text: ev.title, value: ev.id.toString() });
    });

    // Add tag options
    response.data.tags.forEach((tag) => {
      tags.value.push(tag);
      tagsList.value.push({ value: tag, label: tag });
    });
  } catch (error) {
    console.error("Error loading filter options:", error);
  }

  // Set up event listener for tag additions
  if (emitter) {
    emitter.on("tagAdded", (tag) => {
      addTag(tag);
    });
  }

  // Initial data load
  pageChanged();
});
</script>
<style scoped>
.manage-users {
  width: 100%;
}

.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.opt-out-date {
  font-size: 0.8rem;
  margin-top: 0.5rem;
  color: #909399;
}
</style>
