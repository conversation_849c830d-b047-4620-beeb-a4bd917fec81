<template>
  <div class="tag-container">
    <q-chip
      v-for="tag in contact.tags"
      :key="tag"
      removable
      @remove="handleClose(tag)"
      class="tag-item"
      color="primary"
      text-color="white"
    >
      {{ tag }}
    </q-chip>

    <q-input
      v-if="inputVisible"
      ref="inputRef"
      v-model="inputValue"
      class="input-new-tag"
      dense
      outlined
      @keyup.enter="handleInputConfirm"
      @blur="handleInputConfirm"
      style="width: 90px"
    />

    <q-btn
      v-else
      class="button-new-tag"
      size="sm"
      flat
      color="primary"
      @click="showInput"
      label="+ New Tag"
    />
  </div>
</template>

<script setup>
import { ref, nextTick, inject } from "vue";
import { useQuasar } from "quasar";
import { useMainStore } from "@/stores/main"; // Import Pinia store
import axios from "axios";

// Props definition
const props = defineProps({
  contact: {
    type: Object,
    required: true,
  },
});

// State variables
const inputVisible = ref(false);
const inputValue = ref("");
const inputRef = ref(null);
const store = useMainStore(); // Use Pinia store
const $q = useQuasar();

// Create event emitter for tag events (replacing EventBus)
const emitter = inject("emitter", null);

// Methods
const handleClose = async (tag) => {
  try {
    const response = await axios.put(
      `/org_user_lists/${props.contact.id}/delete_tag`,
      { tag }
    );

    store.removeContactTag({
      id: response.data.id,
      tag: response.data.tag,
    });

    $q.notify({
      message: "Tag successfully removed",
      type: "positive",
    });
  } catch (error) {
    $q.notify({
      message: "Could not remove tag",
      type: "negative",
    });
  }
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    inputRef.value.focus();
  });
};

const handleInputConfirm = () => {
  if (props.contact.tags.length > 19) {
    $q.notify({
      message: "You can add a maximum of 20 tags!",
      type: "warning",
    });
    inputVisible.value = false;
    inputValue.value = "";
    return;
  }

  const value = inputValue.value.trim();
  if (value) {
    addTag(value.toLowerCase());
  }

  inputVisible.value = false;
  inputValue.value = "";
};

const addTag = async (tag) => {
  try {
    const response = await axios.put(
      `/org_user_lists/${props.contact.id}/add_tag`,
      { tag }
    );

    store.addContactTag({
      id: response.data.id,
      tag: response.data.tag,
    });

    // Emit event for other components (replacing EventBus)
    if (emitter) {
      emitter.emit("tagAdded", response.data.tag);
    }

    $q.notify({
      message: "Tag successfully added",
      type: "positive",
    });
  } catch (error) {
    const errorMessage = error.response?.data?.errors || "Failed to add tag";
    $q.notify({
      message: errorMessage,
      type: "negative",
    });
  }
};
</script>

<style scoped>
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}
</style>
