<template>
  <q-page padding class="discount-codes-container">
    <!-- Create/Edit Discount Code Card -->
    <q-card class="q-mb-lg form-card">
      <q-card-section class="q-pb-none">
        <div class="hg-underline">Manage Organisation Discount Codes</div>
      </q-card-section>

      <q-card-section>
        <q-form 
          ref="discountFormRef"
          @submit="submit"
          class="form-container"
          greedy
        >
          <h5>To create discount codes for your events go to the event: setup payment</h5>
          
          <div class="row q-col-gutter-md">
            <div class="col-12">
              <q-input 
                v-model="codeDetails.description" 
                label="Description:" 
                maxlength="30" 
                placeholder="Enter a description for this discount code"
                :rules="[val => !!val || 'Please enter a description']"
                filled
              />
              
              <q-input 
                v-model="codeDetails.code" 
                label="Discount Code:" 
                class="q-mt-md"
                maxlength="20" 
                placeholder="Enter discount code (e.g. SUMMER10)"
                :rules="[val => !!val || 'Please enter a discount code']"
                filled
              />
            </div>
          </div>
          
          <div class="row q-col-gutter-md q-mt-md">
            <div class="col-12 col-sm-6">
              <q-input 
                v-model.number="codeDetails.amount"
                label="Percentage Discount:"
                type="number"
                :rules="[
                  val => !!val || 'Please enter a discount percentage',
                  val => val >= 0.1 && val <= 99.99 || 'Amount must be between 0.1 and 99.99'
                ]"
                step="0.01"
                min="0.1"
                max="99.99"
                filled
              >
                <template v-slot:hint>
                  e.g. 10%
                </template>
              </q-input>
            </div>
            
            <div class="col-12 col-sm-6">
              <q-input 
                v-model.number="codeDetails.max_uses"
                label="Max number uses:"
                type="number"
                :rules="[val => val >= 1 || 'Max uses must be at least 1']"
                step="1"
                min="1"
                filled
              />
            </div>
          </div>
          
          <div class="row q-col-gutter-md q-mt-md">
            <div class="col-12 col-sm-6">
              <label for="startDate">Code Start Date</label>
              <VueDatePicker
                v-model="codeDetails.start_date"
                model-type="format"
                format="YYYY-MM-DD"
                input-class-name="date-picker"
                :min-date="new Date()"
                :enable-time-picker="false"
              />
            </div>
            
            <div class="col-12 col-sm-6">
              <label for="endDate">Code End Date</label>
              <VueDatePicker
                v-model="codeDetails.end_date"
                model-type="format" 
                format="YYYY-MM-DD"
                input-class-name="date-picker"
                :min-date="new Date()"
                :enable-time-picker="false"
              />
            </div>
          </div>
          
          <div class="q-mt-md">
            <q-btn 
              type="submit" 
              color="primary" 
              :loading="submitting"
              label="Save Discount Code"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>

    <!-- Existing Codes Table Card -->
    <q-card>
      <q-card-section class="q-pb-none">
        <div class="hg-underline">Existing Codes</div>
      </q-card-section>

      <q-card-section>
        <div class="table-container">
          <q-table
            :rows="codes"
            :columns="columns"
            row-key="id"
            separator="cell"
          >
            <template v-slot:body-cell-actions="props">
              <q-td :props="props" class="q-gutter-x-sm">
                <q-btn 
                  color="negative" 
                  icon="delete" 
                  size="sm"
                  round
                  flat
                  @click="confirmRemoveCode(props.row.id, props.rowIndex)"
                />
                <q-btn 
                  color="primary" 
                  icon="edit" 
                  size="sm"
                  round
                  flat
                  @click="updateCode(props.row.id)"
                />
              </q-td>
            </template>
          </q-table>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import axios from 'axios';
import dayjs from 'dayjs';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

// Form data and validation
const discountFormRef = ref(null);
const codeDetails = reactive({
  event_id: null,
  details: null,
  description: null,
  code: null,
  amount: null,
  max_uses: null,
  start_date: null,
  end_date: null
});

// UI state management
const submitting = ref(false);
const codes = ref([]);
const events = ref([]);
const event = ref(null);

// Table columns definition
const columns = [
  { name: 'code', label: 'Code', field: 'code', align: 'left' },
  { name: 'description', label: 'Description', field: 'description', align: 'left' },
  { 
    name: 'start_date', 
    label: 'Start Date', 
    field: 'start_date',
    format: val => formatDate(val),
    align: 'left' 
  },
  { 
    name: 'end_date', 
    label: 'End Date', 
    field: 'end_date',
    format: val => formatDate(val),
    align: 'left' 
  },
  { 
    name: 'inactive', 
    label: 'Disabled', 
    field: 'inactive',
    format: val => val ? 'True' : 'False',
    align: 'left' 
  },
  { 
    name: 'amount', 
    label: 'Amount', 
    field: 'amount',
    format: val => `${val} %`,
    align: 'left' 
  },
  { name: 'max_uses', label: 'Max Uses', field: 'max_uses', align: 'left' },
  { name: 'actions', label: 'Actions', align: 'center' }
];

// Methods
const disablePastDates = (date) => {
  return dayjs(date).isBefore(dayjs(), 'day');
};

const formatDate = (date) => {
  return date ? dayjs(date).format('DD/MM/YYYY') : 'N/A';
};

const submit = async () => {
  const isValid = await discountFormRef.value.validate();
  
  if (!isValid) {
    $q.notify({
      message: 'Please fix the validation issues',
      type: 'negative'
    });
    return;
  }
  
  // Event-specific validations
  if (event.value) {
    if (event.value.close_date && 
        codeDetails.end_date && 
        dayjs(event.value.close_date).isBefore(dayjs(codeDetails.end_date))) {
      $q.notify({
        message: 'Discount Code end date is after event close date',
        type: 'negative'
      });
      return;
    }

    if (event.value.datetimeto && 
        codeDetails.end_date && 
        dayjs(event.value.datetimeto).isBefore(dayjs(codeDetails.end_date))) {
      $q.notify({
        message: 'Discount Code end date is after event end date',
        type: 'negative'
      });
      return;
    }
  }

  // Additional date validations
  if (codeDetails.end_date && codeDetails.start_date) {
    if (dayjs(codeDetails.start_date).isAfter(dayjs(codeDetails.end_date))) {
      $q.notify({
        message: 'End Date Must be After Start Date',
        type: 'negative'
      });
      return;
    }
  }
  
  saveDiscount();
};

const saveDiscount = async () => {
  submitting.value = true;
  
  try {
    // Adjusts by an hour to keep on same day as selected (keeping original behavior)
    const payload = { ...codeDetails };
    
    if (payload.end_date) {
      payload.end_date = dayjs(payload.end_date).add(1, 'hour').toISOString();
    }
    
    if (payload.start_date) {
      payload.start_date = dayjs(payload.start_date).add(1, 'hour').toISOString();
    }
    
    let response;
    
    if (codeDetails.id) {
      response = await axios.put(`/discounts/${codeDetails.id}`, {
        discount: payload
      });
      
      // Update the existing code in the table
      const index = codes.value.findIndex(c => c.id === codeDetails.id);
      if (index !== -1) {
        codes.value[index] = response.data;
      }
    } else {
      response = await axios.post('/discounts', {
        discount: payload
      });
      
      // Add the new code to the table
      codes.value.push(response.data);
    }
    
    $q.notify({
      message: 'Discount Code Details Saved',
      type: 'positive'
    });
    
    // Reset form
    resetForm();
  } catch (error) {
    const errorMessages = error.response?.data?.errors;
    
    if (errorMessages && Array.isArray(errorMessages)) {
      errorMessages.forEach(msg => {
        $q.notify({
          message: msg,
          type: 'negative'
        });
      });
    } else {
      $q.notify({
        message: 'Discount Code Could Not be Saved',
        type: 'negative'
      });
    }
  } finally {
    submitting.value = false;
  }
};

const confirmRemoveCode = (codeId, index) => {
  $q.dialog({
    title: 'Are you sure?',
    message: 'This will permanently remove this discount code!',
    cancel: true,
    persistent: true
  }).onOk(() => {
    removeCode(codeId, index);
  });
};

const removeCode = async (codeId, index) => {
  try {
    await axios.delete(`/discounts/${codeId}`);
    
    codes.value.splice(index, 1);
    
    $q.notify({
      message: 'Discount Code Removed',
      type: 'positive'
    });
    
    resetForm();
  } catch (error) {
    $q.notify({
      message: 'Failed to remove discount code',
      type: 'negative'
    });
  }
};

const updateCode = (codeId) => {
  const code = codes.value.find(c => c.id === parseInt(codeId));
  
  if (code) {
    // Create a copy to avoid direct modification
    const codeCopy = { ...code };
    
    // Convert string dates to Date objects if needed
    if (codeCopy.start_date) {
      codeCopy.start_date = codeCopy.start_date;
    }
    
    if (codeCopy.end_date) {
      codeCopy.end_date = codeCopy.end_date;
    }
    
    // Ensure amount is a number
    codeCopy.amount = Number(codeCopy.amount);
    
    // Update the form
    Object.assign(codeDetails, codeCopy);
  }
};

const resetForm = () => {
  // Reset form data
  Object.assign(codeDetails, {
    id: null,
    event_id: null,
    details: null,
    description: null,
    code: null,
    amount: null,
    max_uses: null,
    start_date: null,
    end_date: null
  });
  
  // Reset form validation
  if (discountFormRef.value) {
    discountFormRef.value.resetValidation();
  }
};

// Lifecycle hooks
onMounted(async () => {
  try {
    const response = await axios.get('/discounts/');
    const discountData = response.data;
    
    if (discountData) {
      events.value = discountData.events || [];
      
      // Add the "ANY EVENT" option
      events.value.unshift({ id: "0", name: "--FOR ANY EVENT--" });
      
      codes.value = discountData.codes || [];
    }
  } catch (error) {
    console.error(error);
    
    $q.notify({
      message: 'Failed to load discount codes',
      type: 'negative'
    });
  }
});
</script>

<style lang="scss">
.discount-codes-container {
  width: 100%;
  max-width: 100%;
}

.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

// Make sure table takes full width and has proper borders
:deep(.q-table) {
  width: 100%;
  
  @media (max-width: 599px) {
    font-size: 0.85rem;
  }
  
  th {
    font-weight: 500;
  }
}

:deep(.q-table__container) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.q-table__bottom) {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
</style>