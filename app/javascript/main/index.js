// Main module entry point
import { createApp } from 'vue';
import { Quasar, Dialog, Notify, Loading } from 'quasar';
import { createPinia } from 'pinia';

// Import Quasar language pack
import quasarLang from 'quasar/lang/en-GB';

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css';
import '@quasar/extras/fontawesome-v6/fontawesome-v6.css';

// Import Quasar css
import 'quasar/src/css/index.sass';

// Import the main component
import MainSummary from './MainSummary.vue';

// Import the router
import router from './router';

// Create the Vue application
const app = createApp(MainSummary);

// Create and use Pinia for state management
const pinia = createPinia();
app.use(pinia);

// Use the router
app.use(router);

// Configure Quasar
app.use(Quasar, {
  plugins: {
    Dialog,
    Notify,
    Loading
  },
  lang: quasarLang,
  config: {
    brand: {
      primary: '#006572',
      secondary: '#b0bec5',
      accent: '#8c9eff',
      dark: '#1a237e',
      positive: '#21BA45',
      negative: '#C10015',
      info: '#31CCEC',
      warning: '#F2C037'
    },
    notify: {
      position: 'top-right',
      timeout: 2500
    }
  }
});

// Mount the application when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const mainElement = document.getElementById('main-app');
  if (mainElement) {
    app.mount(mainElement);
  }
});

// Export the app instance for potential external use
export default app;