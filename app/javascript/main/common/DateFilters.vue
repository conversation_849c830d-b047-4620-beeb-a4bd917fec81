<template>
  <q-form inline class="row q-gutter-sm">
    <div class="col-12 col-sm-auto">
      <q-select
        v-model="selectedRange"
        :options="dateRanges"
        option-value="val"
        option-label="name"
        label="Filter By Date"
        filled
        dense
        emit-value
        map-options
        @update:model-value="setDefaults"
      />
    </div>

    <div v-if="selectedRange == 0" class="col-12 col-sm-auto">
      <q-select
        v-if="quarters.length"
        v-model="selectedQuarter"
        :options="quarters"
        option-value="value"
        option-label="label"
        label="Please select a quarter"
        filled
        dense
        emit-value
        map-options
        @update:model-value="updateQuarter"
      />
    </div>

    <template v-if="selectedRange == 1">
      <div class="col-12 col-sm-auto">
        <label for="start-month">Please select start month</label>
        <VueDatePicker
          v-model="startMonth"
          month-picker
          @update:model-value="updateMonth"
        />
      </div>

      <div class="col-auto self-center hidden xs">to</div>

      <div class="col-12 col-sm-auto">
        <label for="end-month">Please select end month</label>
        <VueDatePicker
          v-model="endMonth"
          month-picker
          @update:model-value="updateMonth"
        />
      </div>
    </template>

    <template v-if="selectedRange == 2">
      <div class="col-12 col-sm-auto">
        <label for="start-date">Please select start date</label>
        <VueDatePicker
          v-model="datefrom"
          model-type="format"
          format="yyyy-MM-dd"
          input-class-name="date-picker"
          @update:model-value="updateDates"
        />
      </div>

      <div class="col-auto self-center hidden xs">to</div>

      <div class="col-12 col-sm-auto">
        <label for="end-date">Please select end date</label>
        <VueDatePicker
          v-model="dateto"
          model-type="format"
          format="yyyy-MM-dd"
          input-class-name="date-picker"
          @update:model-value="updateDates"
        />
      </div>
    </template>
  </q-form>
</template>
<script setup>
import { ref, onMounted } from "vue";
import eventBus from "../eventBus";

import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import advancedFormat from "dayjs/plugin/advancedFormat";
import isBetween from "dayjs/plugin/isBetween";

dayjs.extend(quarterOfYear);
dayjs.extend(advancedFormat);
dayjs.extend(isBetween);

// Props definition
const props = defineProps({
  showDefault: {
    type: Boolean,
    default: true,
  },
});

// State variables
const dateRanges = [
  { val: 0, name: "Quarterly" },
  { val: 1, name: "Monthly" },
  { val: 2, name: "Custom Dates" },
];

const selectedRange = ref(0);
const quarters = ref([]);
const selectedQuarter = ref(4 - dayjs().quarter());

// Dates need to be in YYYY-MM-DD format for Quasar
const datefrom = ref(
  props.showDefault ? dayjs().startOf("quarter").format("YYYY-MM-DD") : ""
);
const dateto = ref(
  props.showDefault ? dayjs().endOf("quarter").format("YYYY-MM-DD") : ""
);

const startMonth = ref({
  month: dayjs().startOf("month").format("MM"),
  year: dayjs().startOf("month").format("YYYY"),
});
const endMonth = ref({
  month: dayjs().endOf("month").format("MM"),
  year: dayjs().endOf("month").format("YYYY"),
});

const setDefaults = (val) => {
  if (val === 0) {
    updateQuarter(selectedQuarter.value);
  }
};

const updateQuarter = (val) => {
  const quarter = dayjs().subtract(val, "quarter");
  datefrom.value = quarter.startOf("quarter").format("YYYY-MM-DD");
  dateto.value = quarter.endOf("quarter").format("YYYY-MM-DD");
  emitUpdateEvent();
};

const updateDates = () => {
  emitUpdateEvent();
};

const updateMonth = () => {
  if (startMonth.value) {
    datefrom.value = dayjs(
      `${startMonth.value.year}-${startMonth.value.month}-01`
    ).format("YYYY-MM-DD");
  }
  if (endMonth.value) {
    dateto.value = dayjs(`${endMonth.value.year}-${endMonth.value.month}-01`)
      .endOf("month")
      .format("YYYY-MM-DD");
  }
  emitUpdateEvent();
};

const emitUpdateEvent = () => {
  if (eventBus) {
    // Convert string dates back to Date objects for consistency with existing code
    const fromDate = datefrom.value ? new Date(datefrom.value) : null;
    const toDate = dateto.value ? new Date(dateto.value) : null;
    eventBus.emit("datesChanged", fromDate, toDate);
  }
};

// Event listeners
onMounted(() => {
  if (eventBus) {
    eventBus.on("resetDateFilter", () => {
      if (props.showDefault) {
        selectedRange.value = 0;
        selectedQuarter.value = 4 - dayjs().quarter();
        setDefaults(selectedRange.value);
      } else {
        datefrom.value = "";
        dateto.value = "";
        selectedRange.value = 2;
      }
    });
  }

  // Initialize quarters options
  quarters.value = [12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0].map((i) => {
    const quarter = dayjs().subtract(i, "quarter");
    return {
      value: i,
      label:
        quarter.startOf("quarter").format("[Q]Q - MMM YY to ") +
        quarter.endOf("quarter").format("MMM YY"),
    };
  });

  // Initialize default values based on props
  if (!props.showDefault) {
    datefrom.value = "";
    dateto.value = "";
    selectedRange.value = 2;
  } else {
    setDefaults(selectedRange.value);
  }
});
</script>

<style scoped>
/* Quasar already handles most styling needs */
</style>
