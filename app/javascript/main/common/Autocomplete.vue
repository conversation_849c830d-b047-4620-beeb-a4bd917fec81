<template>
  <div class="autocomplete-container">
    <label v-if="!inLine" class="autocomplete-label">{{ label }}</label>

    <q-input
      v-model="search"
      @update:model-value="onChange"
      @keydown.down="onArrowDown"
      @keydown.up="onArrowUp"
      @keydown.enter.prevent="onEnter"
      :placeholder="inLine ? label : ''"
      filled
      dense
    >
      <template v-if="isLoading" v-slot:append>
        <q-spinner color="primary" size="sm" />
      </template>
    </q-input>

    <q-menu
      v-model="isOpen"
      fit
      anchor="bottom left"
      self="top left"
      :offset="[0, 5]"
      class="autocomplete-results-container"
    >
      <q-list padding dense>
        <q-item v-if="isLoading" class="loading">
          <q-item-section>
            <q-item-label caption>Loading results...</q-item-label>
          </q-item-section>
        </q-item>
        <q-item
          v-else
          v-for="(result, i) in props.items"
          :key="i"
          clickable
          v-close-popup
          @click="setResult(result)"
          :active="i === arrowCounter"
          active-class="bg-grey-3"
        >
          <q-item-section>{{ result }}</q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import eventBus from "../eventBus";

// Props definition
const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  isAsync: {
    type: Boolean,
    default: false,
  },
  inLine: {
    type: Boolean,
    default: true,
  },
  label: {
    type: String,
    default: "",
  },
});

// State variables
const isOpen = ref(false);
const results = ref([]);
const search = ref("");
const isLoading = ref(false);
const arrowCounter = ref(-1);

// Methods
const onChange = () => {
  // Emit typeahead event
  if (eventBus) {
    eventBus.emit("typeahead", search.value);
  }

  arrowCounter.value = -1;

  // Handle async data
  if (props.isAsync) {
    isLoading.value = true;
  } else {
    // Filter results locally
    filterResults();
    isOpen.value = true;
  }
};

const filterResults = () => {
  results.value = props.items.filter((item) => {
    return item.toLowerCase().indexOf(search.value.toLowerCase()) > -1;
  });
};

const setResult = (result) => {
  if (eventBus) {
    eventBus.emit("autoSelected", result);
  }

  search.value = result;
  isOpen.value = false;
};

const onArrowDown = () => {
  if (arrowCounter.value < results.value.length - 1) {
    arrowCounter.value++;
  }
};

const onArrowUp = () => {
  if (arrowCounter.value > 0) {
    arrowCounter.value--;
  }
};

const onEnter = () => {
  if (arrowCounter.value >= 0 && results.value[arrowCounter.value]) {
    if (eventBus) {
      eventBus.emit("autoSelected", results.value[arrowCounter.value]);
    }

    search.value = results.value[arrowCounter.value];
    isOpen.value = false;
    arrowCounter.value = -1;
  }
};

// Lifecycle hooks
onMounted(() => {
  if (eventBus) {
    eventBus.on("clearAutocomplete", () => {
      search.value = "";
    });
  }
});
</script>

<style scoped>
.autocomplete-container {
  position: relative;
  width: 100%;
}

.autocomplete-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.autocomplete-results-container {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}

.loading {
  color: #909399;
  font-style: italic;
}
</style>
