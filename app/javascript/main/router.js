// Main module router configuration
import { createRouter, createWebHashHistory } from "vue-router";
import { ticketNavigationGuard } from "@/shared/router/ticketRoutesHandler";
import { initRouterDebug } from "@/events/utils/router-debug";

// Import the route components using dynamic imports for code-splitting
const Overview = () => import("./components/overview.vue");
const EventsPage = () => import("@/pages/eventsPage.vue");
//const EventsList = () => import("./components/EventsList.vue");
//const EventsArchived = () => import("./components/EventsArchived.vue");
const PaymentAdmin = () => import("./components/PaymentAdmin.vue");
const GlobalTerms = () => import("./components/GlobalTerms.vue");
const CharitySettings = () => import("./components/CharitySettings.vue");
//const VatSettings = () => import("./components/VatSettings.vue");
//const DiscountCodes = () => import("./components/DiscountCodes.vue");
const ManageUsers = () => import("./components/ManageUsers.vue");
const BrandColours = () => import("./components/BrandColours.vue");
const SummaryReport = () => import("./components/SummaryReport.vue");
// const FeesCalculator = () => import('./components/FeesCalculator.vue');
//const FbPixelCode = () => import("./components/FbPixelCode.vue");

// Define routes with meta information for better organization
const routes = [
  /* {
    path: "/",
    component: Overview,
    name: "overview",
    meta: { title: "Overview", requiresAuth: true },
  },*/
  {
    path: "/",
    component: EventsPage,
    name: "events",
    meta: { title: "Events", requiresAuth: true },
  },
  /*{
    path: "/events-archived",
    component: EventsArchived,
    name: "events-archived",
    meta: { title: "Archived Events", requiresAuth: true },
  },*/
  // {
  //   path: '/fees-calculator',
  //   component: FeesCalculator,
  //   name: 'fees-calculator',
  //   meta: { title: 'Fees Calculator', requiresAuth: true }
  // },
  {
    path: "/global-terms",
    component: GlobalTerms,
    name: "global-terms",
    meta: { title: "Global Terms", requiresAuth: true },
  },
  {
    path: "/charity-settings",
    component: CharitySettings,
    name: "charity-settings",
    meta: { title: "Charity Settings", requiresAuth: true },
  },
  /* {
    path: "/discount-codes",
    component: DiscountCodes,
    name: "discount-codes",
    meta: { title: "Discount Codes", requiresAuth: true },
  },*/
  /*{
    path: "/vat-settings",
    component: VatSettings,
    name: "vat-settings",
    meta: { title: "VAT Settings", requiresAuth: true },
  },*/
  {
    path: "/payment-admin",
    component: PaymentAdmin,
    name: "payment-admin",
    meta: { title: "Payment Administration", requiresAuth: true },
  },
  {
    path: "/payment-admin/fromevent",
    component: PaymentAdmin,
    name: "payment-admin-event",
    meta: { title: "Payment Administration", requiresAuth: true },
  },
  {
    path: "/manage-users",
    component: ManageUsers,
    name: "manage-users",
    meta: { title: "Manage Users", requiresAuth: true },
  },
  {
    path: "/brand-colours",
    component: BrandColours,
    name: "brand-colours",
    meta: { title: "Brand Colors", requiresAuth: true },
  },
  {
    path: "/summary-report",
    component: SummaryReport,
    name: "summary-report",
    meta: { title: "Summary Report", requiresAuth: true },
  },
  /*{
    path: "/fb-pixel-code",
    component: FbPixelCode,
    name: "fb-pixel-code",
    meta: { title: "Facebook Pixel Configuration", requiresAuth: true },
  },*/
  // Catch-all route for 404 errors
  {
    path: "/:pathMatch(.*)*",
    redirect: "/",
  },
];

// Create router instance
export const router = createRouter({
  history: createWebHashHistory(),
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      // Return to the saved position if one exists
      return savedPosition;
    } else {
      // Scroll to top on new navigation
      return { top: 0 };
    }
  },
  routes,
});

// Initialize router debugging
initRouterDebug(router);

// Navigation guards
router.beforeEach(ticketNavigationGuard);

router.beforeEach((to, from, next) => {
  // Update page title based on route metadata
  const title = to.meta.title;
  if (title) {
    document.title = `HGEvents - ${title}`;
  }

  // Auth check can be implemented here
  // if (to.meta.requiresAuth && !isAuthenticated()) {
  //   return { name: 'login' };
  // }

  // Always proceed with navigation
  next();
});

// Handle navigation errors
router.onError((error) => {
  console.error("Navigation error:", error);
  // Implement error handling as needed
});

export default router;
