<!-- src/pages/Registration.vue -->
<template>
  <q-page padding class="bg-grey-1">
    <div class="row justify-center items-center">
      <div class="col-12 col-sm-10 col-md-6 col-lg-4">
        <q-card class="q-pa-lg" flat bordered>
          <!-- Registration Form -->
          <div v-if="!showConfirmation">
            <q-card-section>
              <div class="text-h6 text-center">Create Account</div>
            </q-card-section>

            <q-card-section>
              <q-form @submit="onSubmit">
                <q-input
                  v-model="form.email"
                  type="email"
                  label="Email"
                  :rules="emailRules"
                  outlined
                  :error="!!errors.email"
                  :error-message="errors.email"
                />

                <q-input
                  v-model="form.name"
                  type="text"
                  label="Organisation Name"
                  :rules="organizationNameRules"
                  outlined
                  :error="!!errors.name"
                  :error-message="errors.name"
                />

                <q-input
                  v-model="form.password"
                  type="password"
                  label="Password"
                  :rules="passwordRules"
                  outlined
                  :error="!!errors.password"
                  :error-message="errors.password"
                />

                <q-input
                  v-model="form.passwordConfirmation"
                  type="password"
                  label="Confirm Password"
                  :rules="passwordConfirmationRules"
                  outlined
                  :error="!!errors.password_confirmation"
                  :error-message="errors.password_confirmation"
                />

                <q-btn
                  :loading="loading"
                  type="submit"
                  color="primary"
                  class="full-width"
                  size="md"
                >
                  Create Account
                </q-btn>
              </q-form>
            </q-card-section>

            <q-card-section class="text-center">
              Already have an account?
              <router-link to="/login" class="text-primary"
                >Sign in</router-link
              >
            </q-card-section>
          </div>

          <!-- Confirmation Message -->
          <div v-else>
            <q-card-section class="text-center">
              <q-icon
                name="mark_email_read"
                size="4rem"
                color="primary"
                class="q-mb-md"
              />
              <div class="text-h6 q-mb-sm">Check Your Email</div>
              <div class="text-body1 q-mb-lg">
                We've sent a confirmation email to
                <strong>{{ confirmationEmail }}</strong
                >. Please click the link in the email to activate your account.
              </div>
            </q-card-section>

            <q-card-section>
              <q-btn
                :loading="loading"
                @click="resendConfirmation"
                color="primary"
                outline
                class="full-width q-mb-md"
                size="md"
              >
                Resend Confirmation Email
              </q-btn>

              <q-btn
                @click="goToLogin"
                color="grey"
                flat
                class="full-width"
                size="md"
              >
                Back to Login
              </q-btn>
            </q-card-section>
          </div>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/auth";

const $q = useQuasar();
const router = useRouter();
const authStore = useAuthStore();

const loading = ref(false);
const showConfirmation = ref(false);
const confirmationEmail = ref("");
const form = reactive({
  email: "",
  name: "",
  password: "",
  passwordConfirmation: "",
});

const errors = reactive({
  email: "",
  name: "",
  password: "",
  password_confirmation: "",
});

// Validation rules
const emailRules = [
  (val) => !!val || "Email is required",
  (val) => /.+@.+\..+/.test(val) || "Please enter a valid email",
];

const organizationNameRules = [
  (val) => !!val || "Organisation name is required",
  (val) => val.length >= 2 || "Organisation name must be at least 2 characters",
];

const passwordRules = [
  (val) => !!val || "Password is required",
  (val) => val.length >= 6 || "Password must be at least 6 characters",
];

const passwordConfirmationRules = [
  (val) => !!val || "Password confirmation is required",
  (val) => val === form.password || "Passwords do not match",
];

const clearErrors = () => {
  errors.email = "";
  errors.name = "";
  errors.password = "";
  errors.password_confirmation = "";
};

const onSubmit = async () => {
  clearErrors();
  loading.value = true;

  try {
    const response = await authStore.register(
      form.email,
      form.password,
      form.passwordConfirmation,
      form.name
    );

    if (response.success) {
      if (response.requires_confirmation) {
        // Show confirmation message instead of redirecting
        confirmationEmail.value = form.email;
        showConfirmation.value = true;
        $q.notify({
          type: "info",
          message:
            response.message ||
            "Please check your email to confirm your account.",
        });
      } else {
        // User is immediately active, redirect to dashboard
        $q.notify({
          type: "positive",
          message: response.message || "Account created successfully!",
        });
        router.push("/dashboard");
      }
    }
  } catch (error) {
    const errorData = error.response?.data;

    if (errorData?.errors) {
      // Handle validation errors from Rails
      if (Array.isArray(errorData.errors)) {
        // If errors is an array of strings
        errorData.errors.forEach((errorMsg) => {
          if (errorMsg.toLowerCase().includes("email")) {
            errors.email = errorMsg;
          } else if (
            errorMsg.toLowerCase().includes("name") ||
            errorMsg.toLowerCase().includes("organisation")
          ) {
            errors.name = errorMsg;
          } else if (errorMsg.toLowerCase().includes("password")) {
            errors.password = errorMsg;
          }
        });
      } else if (typeof errorData.errors === "object") {
        // If errors is an object with field keys
        Object.keys(errorData.errors).forEach((field) => {
          if (errors.hasOwnProperty(field)) {
            errors[field] = Array.isArray(errorData.errors[field])
              ? errorData.errors[field].join(", ")
              : errorData.errors[field];
          }
        });
      }

      $q.notify({
        type: "negative",
        message: "Please correct the errors below",
      });
    } else {
      $q.notify({
        type: "negative",
        message: errorData?.message || "Registration failed. Please try again.",
      });
    }
  } finally {
    loading.value = false;
  }
};

const resendConfirmation = async () => {
  loading.value = true;

  try {
    const response = await authStore.resendConfirmation(
      confirmationEmail.value
    );

    if (response.success) {
      $q.notify({
        type: "positive",
        message: "Confirmation email sent successfully!",
      });
    }
  } catch (error) {
    const errorData = error.response?.data;
    $q.notify({
      type: "negative",
      message:
        errorData?.message ||
        "Failed to send confirmation email. Please try again.",
    });
  } finally {
    loading.value = false;
  }
};

const goToLogin = () => {
  router.push("/login");
};
</script>

<style lang="scss" scoped>
.text-primary {
  text-decoration: none;
  font-weight: 500;
}

.text-primary:hover {
  text-decoration: underline;
}
</style>
