<!-- src/pages/ResendConfirmation.vue -->
<template>
  <q-page padding class="bg-grey-1">
    <div class="row justify-center items-center">
      <div class="col-12 col-sm-10 col-md-6 col-lg-4">
        <q-card class="q-pa-lg" flat bordered>
          <q-card-section>
            <div class="text-h6 text-center">Resend Confirmation Email</div>
            <div class="text-body2 text-center text-grey-7 q-mt-sm">
              Enter your email address to receive a new confirmation email
            </div>
          </q-card-section>

          <q-card-section>
            <q-form @submit="onSubmit">
              <q-input
                v-model="form.email"
                type="email"
                label="Email Address"
                :rules="emailRules"
                outlined
                :error="!!errors.email"
                :error-message="errors.email"
                prepend-inner-icon="mail"
              />

              <q-btn
                :loading="loading"
                type="submit"
                color="primary"
                class="full-width"
                size="md"
              >
                Send Confirmation Email
              </q-btn>
            </q-form>
          </q-card-section>

          <q-card-section class="text-center">
            <router-link to="/login" class="text-primary"
              >Back to Login</router-link
            >
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/auth";

const $q = useQuasar();
const router = useRouter();
const authStore = useAuthStore();

const loading = ref(false);
const form = reactive({
  email: "",
});

const errors = reactive({
  email: "",
});

// Validation rules
const emailRules = [
  (val) => !!val || "Email is required",
  (val) => /.+@.+\..+/.test(val) || "Please enter a valid email",
];

const clearErrors = () => {
  errors.email = "";
};

const onSubmit = async () => {
  clearErrors();
  loading.value = true;

  try {
    const response = await authStore.resendConfirmation(form.email);

    if (response.success) {
      $q.notify({
        type: "positive",
        message: response.message || "Confirmation email sent successfully!",
      });

      // Redirect to login after successful send
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    }
  } catch (error) {
    const errorData = error.response?.data;

    if (errorData?.errors) {
      if (Array.isArray(errorData.errors)) {
        errorData.errors.forEach((errorMsg) => {
          if (errorMsg.toLowerCase().includes("email")) {
            errors.email = errorMsg;
          }
        });
      } else if (typeof errorData.errors === "object") {
        Object.keys(errorData.errors).forEach((field) => {
          if (errors.hasOwnProperty(field)) {
            errors[field] = Array.isArray(errorData.errors[field])
              ? errorData.errors[field].join(", ")
              : errorData.errors[field];
          }
        });
      }

      $q.notify({
        type: "negative",
        message: "Please correct the errors below",
      });
    } else {
      $q.notify({
        type: "negative",
        message:
          errorData?.message ||
          "Failed to send confirmation email. Please try again.",
      });
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.text-primary {
  text-decoration: none;
  font-weight: 500;
}

.text-primary:hover {
  text-decoration: underline;
}
</style>
