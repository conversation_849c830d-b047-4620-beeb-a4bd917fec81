<!-- src/pages/ForgotPassword.vue -->
<template>
  <q-page padding class="bg-grey-1">
    <div class="row justify-center items-center">
      <div class="col-12 col-sm-10 col-md-6 col-lg-4">
        <q-card class="q-pa-lg" flat bordered>
          <q-card-section>
            <div class="text-h6">Reset Password</div>
          </q-card-section>

          <q-card-section>
            <q-form @submit="onSubmit">
              <q-input
                v-model="email"
                type="email"
                label="Email"
                :rules="[(val) => !!val || 'Email is required']"
                outlined
              />

              <q-btn
                :loading="authStore.passwordResetLoading"
                type="submit"
                color="primary"
                class="full-width"
              >
                Send Reset Instructions
              </q-btn>
            </q-form>
          </q-card-section>

          <q-card-section class="text-center">
            <router-link to="/login">Back to Login</router-link>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/auth";

const $q = useQuasar();
const router = useRouter();
const authStore = useAuthStore();

const email = ref("");

const onSubmit = async () => {
  try {
    const response = await authStore.requestPasswordReset(email.value);
    if (response.success) {
      $q.notify({
        type: "positive",
        message: response.message,
      });
      router.push("/login");
    }
  } catch (error) {
    $q.notify({
      type: "negative",
      message: error.response?.data?.errors?.join(", ") || "An error occurred",
    });
  }
};
</script>
