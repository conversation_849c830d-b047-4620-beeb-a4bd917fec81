<!-- src/pages/ConfirmEmail.vue -->
<template>
  <q-page padding class="bg-grey-1">
    <div class="row justify-center items-center">
      <div class="col-12 col-sm-10 col-md-6 col-lg-4">
        <q-card class="q-pa-lg" flat bordered>
          <q-card-section>
            <div class="text-h6">Email Confirmation</div>
          </q-card-section>

          <q-card-section v-if="loading">
            <div class="text-center">
              <q-spinner size="lg" />
              <div class="q-mt-md">Confirming your email...</div>
            </div>
          </q-card-section>

          <q-card-section v-else-if="confirmed">
            <div class="text-positive text-center">
              <q-icon name="check_circle" size="lg" class="q-mb-md" />
              <div class="text-h6">Email Confirmed!</div>
              <p>
                Your account has been successfully confirmed. You can now sign
                in.
              </p>
            </div>
            <q-btn to="/login" color="primary" class="full-width q-mt-md">
              Go to Login
            </q-btn>
          </q-card-section>

          <q-card-section v-else>
            <div class="text-negative text-center">
              <q-icon name="error" size="lg" class="q-mb-md" />
              <div class="text-h6">Confirmation Failed</div>
              <p>{{ errorMessage }}</p>
            </div>
            <q-btn to="/register" color="primary" class="full-width q-mt-md">
              Back to Registration
            </q-btn>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/auth";

const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const loading = ref(true);
const confirmed = ref(false);
const errorMessage = ref("");

onMounted(async () => {
  const token = route.params.token;

  if (!token) {
    loading.value = false;
    errorMessage.value = "No confirmation token provided";
    return;
  }

  try {
    const response = await authStore.confirmEmail(token);

    if (response.success) {
      confirmed.value = true;
      $q.notify({
        type: "positive",
        message: "Email confirmed successfully!",
      });
    } else {
      errorMessage.value = response.message || "Confirmation failed";
    }
  } catch (error) {
    console.error("Email confirmation error:", error);
    errorMessage.value =
      error.response?.data?.message || "An error occurred during confirmation";
  } finally {
    loading.value = false;
  }
});
</script>
