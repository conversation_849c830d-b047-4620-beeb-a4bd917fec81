<!-- src/pages/ResetPassword.vue -->
<template>
  <q-page padding class="flex flex-center">
    <q-card class="card q-pa-lg" flat bordered>
      <q-card-section>
        <div class="text-h6">Set New Password</div>
      </q-card-section>

      <q-card-section v-if="!tokenValid">
        <div class="text-negative">
          The password reset link is invalid or has expired.
        </div>
        <q-btn to="/forgot-password" color="primary" class="q-mt-md">
          Request New Reset Link
        </q-btn>
      </q-card-section>

      <q-card-section v-else>
        <q-form @submit="onSubmit">
          <q-input
            v-model="password"
            type="password"
            label="New Password"
            :rules="[
              (val) =>
                val.length >= 6 || 'Password must be at least 6 characters',
            ]"
            outlined
          />

          <q-input
            v-model="passwordConfirmation"
            type="password"
            label="Confirm Password"
            :rules="[(val) => val === password || 'Passwords do not match']"
            outlined
          />

          <q-btn
            :loading="loading"
            type="submit"
            color="primary"
            class="full-width"
          >
            Reset Password
          </q-btn>
        </q-form>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/auth";

const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const token = ref(route.params.token);

debugger;
const tokenValid = ref(false);
const loading = ref(false);
const password = ref("");
const passwordConfirmation = ref("");

const validateToken = async () => {
  try {
    const response = await authStore.validateResetToken(token.value);
    tokenValid.value = response.success;
  } catch (error) {
    tokenValid.value = false;
  }
};

const onSubmit = async () => {
  loading.value = true;
  try {
    const response = await authStore.resetPassword(
      token.value,
      password.value,
      passwordConfirmation.value
    );

    if (response.success) {
      $q.notify({
        type: "positive",
        message: response.message,
      });
      router.push("/login"); // or wherever you want to redirect
    }
  } catch (error) {
    $q.notify({
      type: "negative",
      message: error.response?.data?.errors?.join(", ") || "An error occurred",
    });
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (token.value) {
    validateToken();
  }
});
</script>
<style lang="scss" scoped>
.card {
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

/* Responsive card width */
@media (max-width: $breakpoint-lg-min) {
  .card {
    width: 50%;
  }
}
@media (max-width: $breakpoint-md-min) {
  .card {
    width: 70%;
  }
}
@media (max-width: $breakpoint-sm-max) {
  .card {
    width: 95%;
    box-shadow: none;
    border: none;
  }
}
</style>
