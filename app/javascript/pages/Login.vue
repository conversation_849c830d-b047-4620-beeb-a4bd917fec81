<template>
  <q-page padding class="bg-grey-1">
    <div class="row justify-center items-center">
      <div class="col-12 col-sm-10 col-md-6 col-lg-4">
        <q-card class="q-pa-lg" flat bordered>
          <!-- Logo Section -->
          <q-card-section class="text-center q-pb-none">
            <div class="text-h4 text-primary q-mb-md">Welcome Back</div>
            <div class="text-subtitle1 text-grey-7">
              Sign in to your account
            </div>
          </q-card-section>

          <!-- Login Form -->
          <q-card-section>
            <q-form @submit="onSubmit" @reset="onReset">
              <!-- Email Input -->
              <q-input
                v-model="form.email"
                type="email"
                label="Email Address"
                outlined
                :rules="emailRules"
                :loading="loading"
                autocomplete="email"
                prepend-inner-icon="mail"
                lazy-rules
              />

              <!-- Password Input -->
              <q-input
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                label="Password"
                outlined
                :rules="passwordRules"
                :loading="loading"
                autocomplete="current-password"
                prepend-inner-icon="lock"
                :append-inner-icon="
                  showPassword ? 'visibility_off' : 'visibility'
                "
                @click:append-inner="showPassword = !showPassword"
                lazy-rules
              />

              <!-- Remember Me -->
              <q-checkbox
                v-model="form.rememberMe"
                label="Remember me"
                color="primary"
              />

              <!-- Submit Button -->
              <q-btn
                type="submit"
                color="primary"
                size="lg"
                class="full-width q-mt-md"
                :loading="loading"
                :disable="!isFormValid"
                no-caps
              >
                Sign In
                <template v-slot:loading>
                  <q-spinner-hourglass class="on-left" />
                  Signing in...
                </template>
              </q-btn>

              <!-- Forgot Password Link -->
              <div class="text-center q-mt-md">
                <q-btn
                  flat
                  color="primary"
                  no-caps
                  to="/forgot-password"
                  class="text-caption"
                >
                  Forgot your password?
                </q-btn>
              </div>

              <!-- Resend Confirmation -->
              <div v-if="showResendConfirmation" class="text-center q-mt-md">
                <q-banner class="bg-orange-1 text-orange-9 q-mb-md" rounded>
                  <template v-slot:avatar>
                    <q-icon name="warning" color="orange" />
                  </template>
                  Your email address has not been confirmed.
                  <template v-slot:action>
                    <q-btn
                      flat
                      color="orange"
                      label="Resend Email"
                      @click="resendConfirmation"
                      :loading="loading"
                      size="sm"
                    />
                  </template>
                </q-banner>
              </div>
            </q-form>
          </q-card-section>

          <!-- Sign Up Link -->
          <q-separator class="q-my-md" />

          <q-card-section class="text-center">
            <div class="text-caption text-grey-7">
              Don't have an account?
              <q-btn
                flat
                color="primary"
                no-caps
                to="/register"
                class="text-caption q-pa-none"
              >
                Sign up here
              </q-btn>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/auth";

// Router and Quasar
const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();

// Reactive state
const loading = ref(false);
const showPassword = ref(false);
const showResendConfirmation = ref(false);
const unconfirmedEmail = ref("");

// Form data
const form = ref({
  email: "",
  password: "",
  rememberMe: false,
});

// Validation rules
const emailRules = [
  (val) => !!val || "Email is required",
  (val) => /.+@.+\..+/.test(val) || "Please enter a valid email address",
];

const passwordRules = [
  (val) => !!val || "Password is required",
  (val) => val.length >= 6 || "Password must be at least 6 characters",
];

// Computed properties
const isFormValid = computed(() => {
  return (
    form.value.email &&
    form.value.password &&
    isValidEmail(form.value.email) &&
    form.value.password.length >= 6
  );
});

// Helper functions
const isValidEmail = (email) => {
  return /.+@.+\..+/.test(email);
};

// Form submission
const onSubmit = async () => {
  if (!isFormValid.value) return;

  loading.value = true;

  try {
    const response = await authStore.login(
      form.value.email,
      form.value.password,
      form.value.rememberMe
    );

    if (response.success) {
      $q.notify({
        type: "positive",
        message: "Welcome back!",
        position: "top",
      });

      // Redirect to intended page or dashboard
      const redirectTo = router.currentRoute.value.query.redirect;
      if (redirectTo) {
        // Use Vue router for Vue routes
        router.push(redirectTo);
      } else {
        // Redirect to Rails dashboard using window.location
        window.location.href = "/dashboard";
      }
    } else {
      throw new Error(response.message || "Login failed");
    }
  } catch (error) {
    console.error("Login error:", error);

    let errorMessage = "Invalid email or password";
    let showResendOption = false;

    if (error.response?.status === 401) {
      errorMessage = error.response.data.message || "Invalid email or password";

      // Check if this is an unconfirmed user error
      if (errorMessage.includes("not been confirmed")) {
        showResendOption = true;
        unconfirmedEmail.value = form.value.email;
        showResendConfirmation.value = true;
      }
    } else if (error.response?.status === 422) {
      errorMessage = error.response.data.error || "Invalid credentials";
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    $q.notify({
      type: "negative",
      message: errorMessage,
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// Reset form
const onReset = () => {
  form.value = {
    email: "",
    password: "",
    rememberMe: false,
  };
  showPassword.value = false;
  showResendConfirmation.value = false;
  unconfirmedEmail.value = "";
};

const resendConfirmation = async () => {
  if (!unconfirmedEmail.value) return;

  loading.value = true;

  try {
    const response = await authStore.resendConfirmation(unconfirmedEmail.value);

    if (response.success) {
      $q.notify({
        type: "positive",
        message: "Confirmation email sent successfully!",
        position: "top",
      });

      showResendConfirmation.value = false;
    }
  } catch (error) {
    console.error("Resend confirmation error:", error);

    $q.notify({
      type: "negative",
      message:
        error.response?.data?.message ||
        "Failed to send confirmation email. Please try again.",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// Lifecycle
onMounted(() => {
  // Check if user is already logged in
  // You might want to redirect them away from login page

  // Pre-fill email from URL query if available
  const email = router.currentRoute.value.query.email;
  if (email) {
    form.value.email = email;
  }
});
</script>

<style lang="scss" scoped>
/* Custom input styles */
:deep(.q-field--outlined .q-field__control) {
  border-radius: 8px;
}

:deep(.q-btn) {
  border-radius: 8px;
  font-weight: 500;
}

/* Loading spinner customization */
:deep(.q-spinner-hourglass) {
  color: white;
}
</style>
