<script setup>
import indigoEvents from "@/assets/IndigoEvents.png";
import stepsImg from "@/assets/steps.png";
import sarahImg from "@/assets/sarah.jpeg";
import eventsImg from "@/assets/events.png";
import inspectorImg from "@/assets/inspector.png";
import geoffImg from "@/assets/geoff.png";
import greggsImg from "@/assets/greggs.png";
import nepicImg from "@/assets/nepic.png";
import boschImg from "@/assets/bosch.png";
import holidayInnImg from "@/assets/holidayinn.png";
import mercureImg from "@/assets/mercure.png";
import manchesterImg from "@/assets/manchester.jpg";
import midwaveImg from "@/assets/midwave.jpg";
import pennyImg from "@/assets/penny.png";
//feature icons
import track from "@/assets/track.png";
import payments from "@/assets/payments.png";
import delegate from "@/assets/delegate.png";
import build from "@/assets/build.png";

import { ref, computed } from "vue";

const testimonialSlide = ref("<PERSON>");

//testimonials
const testimonials = [
  {
    name: "<PERSON>",
    position: "Events Manager at Greggs",
    feedback:
      "I have used I-Spark Events for three events and cannot fault it. We mainly use this to collate responses for attendance and choices – such as menus or seminars. Before using I-Spark Events, I would spend days calling individuals to find out this information, now I can do it at the push of a button! The time it saves is incredible – for me days and I would recommend this system to any other event organisers.",
  },
  {
    name: "Natalie",
    position: "Events Manager at Headland Festival Group",
    feedback: `Due to the variety of events we host, we required a range of tickets; anything from one to three options per event.
              EventStop makes it easy to set up as many  options as needed from our side, but also means the whole booking process for our customers is just as simple.
              If you’re looking for a tool that’s quick from start to finish for everyone using it, this is definitely the system for you!`,
  },
  {
    name: "Nicola McBean",
    position: "CEO Autism Parents Together",
    feedback: `We have used Event Stop to organise our charity events for over 1 year now. We find it incredibly useful and easy to use tool which allows us to organise our events quickly and efficiently.
              With the onset of COVID- 19 we needed to adapt all of our events and fundraising and move them online.  Event Stop helped us to do this immediately.
              It's great to know we can rely on Event Stop to help us stay in touch with our members and sponsors easily, even in these testing times.`,
  },
];

//key features
const keyFeatures = [
  {
    title: "SIMPLE STEPS TO BUILD AN EVENT",
    image: build,
  },
  {
    title: "DELEGATE ONLINE REGISTRATION",
    image: delegate,
  },
  {
    title: "PAYMENTS ONLINE",
    image: payments,
  },
  {
    title: "TRACK, MONITOR, REPORTS",
    image: track,
  },
];
</script>
<style lang="scss">
.icon-container {
  border: 2px solid $secondary;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  width: 190px;
}
.image {
  width: 100%;
  height: auto;
}
.overview {
  @media screen and (min-width: $breakpoint-md-min) {
    width: 40%;
  }
}
.accent {
  color: $secondary;
}
</style>
<template>
  <q-page>
    <q-scroll-area class="fit">
      <!-- <router-view /> -->
      <!-- START OF THE PAGE -->
      <div class="row q-mt-sm">
        <div class="col-12">
          <div
            :style="
              $q.screen.gt.sm
                ? 'background: no-repeat right/cover url(/images/IndigoEvents.png); width: 100%; min-height: 800px;'
                : ''
            "
          >
            <div class="q-mx-xl overview">
              <div
                class="text-left text-primary text-weight-bold q-pb-xl"
                style="font-size: 38px; line-height: normal"
              >
                A flexible event booking system with a personal service offering
                the latest technology and a dedicated team
              </div>
              <div class="text-left text-h5 text-weight-bold text-primary">
                Plan and manage your event <br />
                with I-Spark Events
              </div>
              <q-separator
                color="secondary"
                class="q-my-lg"
                style="width: 20%"
              />
              <div class="text-left text-subtitle1">
                I-Spark Events offers a flexible event booking system combined
                with personalized service. Utilizing the latest technology,
                their dedicated team helps you plan and manage your events
                efficiently. With an easy-to-use platform, you can create and
                customize your event page, incorporating your brand identity,
                while also handling revenue and logistics effortlessly.
              </div>
            </div>
          </div>
        </div>
        <div class="col-12" v-if="$q.screen.lt.md">
          <q-img :src="indigoEvents" alt="indigo-events-banner" class="image" />
        </div>
      </div>

      <!-- CREATE YOUR EVENT -->

      <div class="row q-mt-xl q-mb-xl q-gutter-xl justify-center items-center">
        <div class="col-8 col-md">
          <div
            class="text-center text-h3 text-primary text-weight-bold q-my-xl"
          >
            Create your event in 4 simple online steps
          </div>
        </div>
        <div class="col-12 q-mb-xl">
          <q-img :src="stepsImg" alt="steps-outline" class="image" />
        </div>
      </div>

      <!-- SCALE YOUR EVENT -->
      <div
        class="row q-mt-xl q-mb-xl justify-center items-center"
        style="border-radius: 45px; background-color: #dddceb"
      >
        <div class="col-8 col-md">
          <div
            class="text-center text-h3 text-weight-bold text-primary q-mt-xl q-pt-xl"
          >
            Scale your event with ease
          </div>

          <div
            class="text-center text-h5 text-primary text-weight-regular q-mt-xl q-mb-xl"
          >
            Our event planning service is there every step <br />
            of the way, helping you organise, market and <br />
            manage an event to remember.
            <div class="div">
              <q-btn
                elevated
                class="q-mt-xl"
                size="lg"
                color="secondary"
                label="Find Out More"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="row q-mt-xl q-mb-xl q-gutter-md justify-center items-center">
        <div class="col-8 col-md-6 q-mb-sm center">
          <q-img :src="sarahImg" alt="woman planning event" class="image" />
        </div>
      </div>

      <!-- KEY FEATURES -->
      <div
        class="fit row wrap q-gutter-lg q-mt-xl q-mb-xl q-ml-sm justify-center items-center"
      >
        <div
          class="col-12 text-center text-h3 text-weight-bold text-primary q-mb-lg"
        >
          I-Spark Events Key Features
        </div>
        <div
          v-for="(feature, index) in keyFeatures"
          :key="index"
          class="col-12 col-sm-6 col-md-4 col-lg-3 q-pa-md flex items-center justify-center"
        >
          <div class="column items-center justify-center">
            <q-img
              :src="feature.image"
              alt="events"
              class="col image"
              style="width: 200px; height: 200px"
            />
            <div
              class="col text-h6 text-primary text-center text-weight-bold q-mb-xl"
            >
              {{ feature.title }}
            </div>
          </div>
        </div>
      </div>

      <!-- OVERVIEW TEXT -->
      <div class="row q-gutter-sm q-ml-sm justify-center items-center">
        <div class="col-3">
          <q-separator color="accent" class="q-my-sm" />
        </div>
        <div class="col-12">
          <div
            class="text-center text-h6 text-primary text-weight-regular q-mt-sm q-mb-md"
          >
            Our experienced team combines extensive<br />knowledge and expertise
            to deliver<br />
            cutting-edge technology tailored to your needs.<br /><br />
            We understand that every project is unique,<br />
            which is why our solutions are personalized<br />
            to fit your specific requirements.<br /><br />
            Unlike many software companies, we provide<br />
            direct access to the people behind the<br />
            technology, ensuring you have the support<br />
            you need every step of the way.<br /><br />
            We take pride in our journey and look forward<br />
            to continuing to innovate, create, and solve<br />
            challenges with passion and dedication.
          </div>
        </div>
        <div class="col-3">
          <q-separator color="accent" class="q-my-sm" />
        </div>
      </div>

      <div class="row q-mt-xl q-mb-xl q-gutter-md justify-center items-center">
        <div class="col-8 col-md-4 q-mb-sm center">
          <q-img :src="geoffImg" alt="geoff" class="image" />
        </div>
      </div>

      <!-- CLIENT LIST -->
      <div
        class="row q-gutter-md q-mt-xl q-mb-xl q-ml-sm justify-center items-center"
      >
        <div
          class="col-12 text-center text-h3 text-primary text-weight-bold text-underline q-mt-xl q-mb-xl"
        >
          Our Clients
        </div>

        <div
          class="col-12 col-sm-4 col-lg-auto flex justify-center items-center"
        >
          <div class="icon-container">
            <q-img
              :src="greggsImg"
              style="height: 150px; width: 150px"
              class="self-center"
            />
          </div>
        </div>
        <div
          class="col-12 col-sm-4 col-lg-auto flex justify-center items-center"
        >
          <div class="icon-container">
            <q-img :src="holidayInnImg" style="height: 110px; width: 135px" />
          </div>
        </div>
        <div
          class="col-12 col-sm-4 col-lg-auto flex justify-center items-center"
        >
          <div class="icon-container">
            <q-img :src="nepicImg" style="height: 130px; width: 130px" />
          </div>
        </div>
        <div
          class="col-12 col-sm-4 col-lg-auto flex justify-center items-center"
        >
          <div class="icon-container">
            <q-img :src="mercureImg" style="height: 100px; width: 150px" />
          </div>
        </div>
        <div
          class="col-12 col-sm-4 col-lg-auto flex justify-center items-center"
        >
          <div class="icon-container">
            <q-img
              :src="boschImg"
              style="height: 30px; width: 134px"
              class="self-center"
            />
          </div>
        </div>
        <div
          class="col-12 col-sm-4 col-lg-auto flex justify-center items-center"
        >
          <div class="icon-container">
            <q-img :src="manchesterImg" style="height: 100px; width: 150px" />
          </div>
        </div>
      </div>

      <!-- spacing -->
      <div class="q-my-xl" style="height: 40px"></div>

      <!-- TESTIMONIALS -->
      <div
        class="row q-my-xl justify-center"
        style="border-radius: 45px; background-color: #dddceb"
      >
        <div class="col-12">
          <div class="text-h3 text-weight-bold q-pt-xl text-center">
            WHAT OUR <br /><span class="accent">CUSTOMERS SAY</span>
          </div>
        </div>
        <div class="col-12 flex justify-center">
          <q-carousel
            v-model="testimonialSlide"
            transition-prev="slide-right"
            transition-next="slide-left"
            animated
            control-color="primary"
            class="rounded-borders bg-transparent text-primary"
            navigation
            arrows
            padding
            infinite
          >
            <q-carousel-slide
              v-for="(testimonial, index) in testimonials"
              :value="index"
              :name="testimonial.name"
              :key="index"
              class="column no-wrap flex-center"
            >
              <div
                class="text-h6 text-primary text-weight-regular q-mt-xl text-center"
              >
                {{ testimonial.feedback }}
              </div>
              <div
                class="text-h5 text-weight-bold text-primary q-mt-lg q-mb-xl text-center"
              >
                {{ testimonial.name }}, {{ testimonial.position }}
              </div>
            </q-carousel-slide>
          </q-carousel>
        </div>
      </div>

      <!-- BOOK A DEMO -->

      <div class="row q-px-md items-center justify-center">
        <div class="col-12 col-sm-6">
          <div class="column items-center justify-center">
            <div
              class="col-12 text-center text-h5 text-primary text-weight-regular q-my-lg"
            >
              Book a demo or speak <br />
              to one of our team today<br />
            </div>
            <div class="col-3">
              <q-btn
                elevated
                icon-right="arrow_forward"
                label="Book a Demo"
                color="secondary"
                size="xl"
                style="border-radius: 30px"
              />
            </div>
          </div>
        </div>
        <div class="col-12 col-sm-6">
          <q-img :src="pennyImg" class="image" />
        </div>
      </div>
    </q-scroll-area>
  </q-page>
</template>
