<template>
  <div>
    <events-filter @filterEvents="filterEvents"></events-filter>

    <div class="container">
      <div class="events">
        <h2>Upcoming {{orgName}} events</h2>

        <div>
          <event-deck v-if="pageEvents.length > 0" :events="pageEvents.slice(0, 6)"></event-deck>
          <event-deck v-if="pageEvents.length > 6" :events="pageEvents.slice(6)"></event-deck>
          <h3 v-if="events.length === 0">No Events found.</h3>
        </div>

        <q-pagination
          v-if="events.length > 0"
          v-model="currentPage"
          :max="Math.ceil(events_count / 6)"
          @update:model-value="handlePageChange"
          class="event-pagination flex justify-center q-mt-md"
          direction-links
          boundary-links
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import eventDeck from "@/splash/events-card-deck.vue";
import eventsFilter from "@/splash/events-filter.vue";

// Props
const props = defineProps({
  orgId: {
    type: [String, Number],
    required: true
  },
  orgName: {
    type: String,
    required: true
  }
});

// Reactive state
const events = ref([]);
const currentPage = ref(1);
const events_count = ref(0);
const filter = ref({
  title: "",
  date_on: null,
  location: null,
  tag: null,
  type: null,
  cost: 'all'
});

// Computed properties
const pageEvents = computed(() => {
  const start = (currentPage.value - 1) * 6;
  const end = (currentPage.value * 6);
  if (currentPage.value * 6 <= events.value.length) {
    return events.value.slice(start, end);
  } else {
    return events.value.slice(start);
  }
});

// Methods
const filterEvents = (newFilter) => {
  filter.value = newFilter;
  getOrgEvents();
};

const handlePageChange = (page) => {
  currentPage.value = page;
  getOrgEvents(page);
};

const getOrgEvents = async (page = 1) => {
  if (page) {
    currentPage.value = page;
  }

  try {
    const response = await axios.get(`/organisation/${props.orgId}/event_details`, {
      params: {
        title: filter.value.title,
        date_on: filter.value.date_on,
        location: filter.value.location,
        tag: filter.value.tag,
        type: filter.value.type,
        cost: filter.value.cost
      }
    });
    
    events.value = response.data.events;
    events_count.value = response.data.events_count;
  } catch (error) {
    console.error("Error fetching events:", error);
  }
};

// Lifecycle hooks
onMounted(() => {
  getOrgEvents();
});
</script>

<style>
.event-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.el-pagination.is-background .el-pager li:not(.is-disabled).is-active {
  background-color: #ff9500;
  color: #fff;
}

.el-pagination.is-background .el-pager li:not(.is-disabled):hover {
  color: #ff9500;
}

.el-pagination.is-background button:not(:disabled) {
  color: #606266;
}

.el-pagination.is-background button:not(:disabled):hover {
  color: #ff9500;
}
</style>