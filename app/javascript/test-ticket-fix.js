// Test script to verify ticket navigation fixes
// Run this in the browser console to test the navigation guards

/**
 * Test ticket navigation
 * This function simulates direct navigation to the ticket creation page
 * It mimics different navigation paths to ensure our fix works in all scenarios
 */
function testTicketNavigation() {
  console.log('%c===== TESTING TICKET NAVIGATION FIXES =====', 'background:#333; color:#ff9; padding:5px; font-size:14px');
  
  // Get the current router instance
  const router = window?.$nuxt?.$router || window?.router;
  if (!router) {
    console.error('Router not found - make sure this script runs in the app context');
    return;
  }
  
  // Get event ID from store or localStorage
  const eventId = localStorage.getItem('hg-current-event-id');
  if (!eventId) {
    console.error('No event ID found - please select an event first');
    return;
  }

  console.log(`Testing navigation to ticket-creation for event ${eventId}`);
  
  // Test 1: Direct navigation to ticket-creation
  console.log('Test 1: Direct router navigation with navigation guard');
  router.push({ name: 'ticket-creation', params: { eventId } });
  
  // The remaining tests require the functions to be imported in the browser context
  // These will emit warnings if the functions aren't available
  
  // Test 2: Navigation with preparation
  setTimeout(() => {
    if (typeof prepareForTicketNavigation === 'function') {
      console.log('Test 2: Navigation with prepareForTicketNavigation');
      prepareForTicketNavigation(eventId);
      
      setTimeout(() => {
        router.push({ name: 'ticket-creation', params: { eventId } });
      }, 10);
    } else {
      console.warn('prepareForTicketNavigation function not available in this context');
    }
  }, 2000);
  
  console.log('%c===== TESTS STARTED =====', 'background:#333; color:#ff9; padding:5px; font-size:14px');
}

// Export for potential use in modules
export default testTicketNavigation;

// Auto-execute if run directly in browser console
if (typeof window !== 'undefined') {
  console.log('Run testTicketNavigation() to test the navigation fixes');
}
