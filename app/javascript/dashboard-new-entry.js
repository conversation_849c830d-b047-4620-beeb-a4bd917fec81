import { createApp } from 'vue';
import { createPinia } from 'pinia';
import PrimeVue from 'primevue/config';
import 'primevue/resources/themes/saga-blue/theme.css';
import 'primevue/resources/primevue.min.css';
import 'primeicons/primeicons.css';
import './tailwind.css';
import MainDashboard from './dashboard-new/main.vue';

const app = createApp(MainDashboard);
app.use(createPinia());
app.use(PrimeVue);
app.mount('#dashboard-new-root');
