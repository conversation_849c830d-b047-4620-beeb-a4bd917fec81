import { createRouter, createWebHashHistory } from "vue-router";
import { reactive, readonly } from "vue";

import mainPage from "@/pages/mainPage.vue";

const state = reactive({
  currentRoute: null,
  previousRoute: null,
  isNavigating: false,
});

// Actions for router state management
const actions = {
  setCurrentRoute(route) {
    state.previousRoute = state.currentRoute;
    state.currentRoute = route;
  },
  setNavigating(value) {
    state.isNavigating = value;
  },
};

// Define routes
const routes = [
  {
    path: "/",
    component: mainPage,
  },
  {
    path: '/login',
    component: () => import('@/pages/Login.vue')
  },
  {
    path: '/register',
    component: () => import('@/pages/Registration.vue')
  },
  {
    path: '/forgot-password',
    component: () => import('@/pages/ForgotPassword.vue')
  },
  {
    path: '/reset-password/:token',
    component: () => import('@/pages/ResetPassword.vue')
  },
  {
    path: '/confirm-email/:token',
    component: () => import('@/pages/ConfirmEmail.vue')
  },
  {
    path: '/resend-confirmation',
    component: () => import('@/pages/ResendConfirmation.vue')
  }
];

// Create router instance
export const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// Setup navigation guards to track route changes
router.beforeEach((to, from, next) => {
  actions.setNavigating(true);
  next();
});

router.afterEach((to) => {
  actions.setCurrentRoute(to);
  actions.setNavigating(false);
});

export const routerStore = {
  state: readonly(state),
  ...actions,
  getCurrentRouteName: () => state.currentRoute?.name,
  getPreviousRouteName: () => state.previousRoute?.name,
  isActiveRoute: (name) => state.currentRoute?.name === name,
  isChildOfRoute: (parentName) => {
    return state.currentRoute?.matched.some(
      (record) => record.name === parentName
    );
  },
};

export { routes };
