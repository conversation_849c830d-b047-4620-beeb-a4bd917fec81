/* Import Montserrat font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Global font configuration for Quasar apps */
* {
  font-family: 'Montserrat', sans-serif;
}

body {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

/* Quasar component font overrides */
.q-btn {
  font-family: 'Montserrat', sans-serif;
}

.q-field {
  font-family: 'Montserrat', sans-serif;
}

.q-item {
  font-family: 'Montserrat', sans-serif;
}

.q-card {
  font-family: 'Montserrat', sans-serif;
}

.q-dialog {
  font-family: 'Montserrat', sans-serif;
}

.q-menu {
  font-family: 'Montserrat', sans-serif;
}

.q-table {
  font-family: 'Montserrat', sans-serif;
}

.q-tab {
  font-family: 'Montserrat', sans-serif;
}

.q-stepper {
  font-family: 'Montserrat', sans-serif;
}
