// Cost calculation utilities converted from Vue 2 mixin to Composition API
// Original: app/javascript-old/book/common/cost-calcs.js

/**
 * Get VAT rate for a ticket
 * @param {Object} ticket - The ticket object
 * @param {number} defaultVatRate - Default VAT rate percentage (default: 20)
 * @returns {number} VAT rate as decimal (e.g., 0.2 for 20%)
 */
const getVatRate = (ticket, defaultVatRate = 20) => {
  let vatRate = defaultVatRate / 100;
  if (ticket.vat_rate) {
    vatRate = ticket.vat_rate.rate / 100;
  }
  return vatRate;
};

/**
 * Calculate discount amount for a ticket
 * @param {Object} ticket - The ticket object
 * @param {boolean} discountValid - Whether early bird discount is valid
 * @param {boolean} forSingleTicket - Calculate for single ticket or total quantity
 * @returns {number} Discount amount
 */
const ticketDiscountAmount = (ticket, discountValid = false, forSingleTicket = false) => {
  if (!ticket.discount_type) {
    return 0;
  }

  let ticketCost = ticket.cost_b;
  if (discountValid) {
    ticketCost = ticket.cost_a;
  }

  if (!forSingleTicket) {
    ticketCost = ticketCost * ticket.quantity_tickets;
  }

  if (ticket.discount_type === "percentage") {
    const discAmount = (ticket.discount_amount / 100) * ticketCost;
    ticket.total_discount = discAmount;
    return discAmount;
  } else {
    const discAmount = ticket.discount_amount * ticket.quantity_tickets;
    const finalCost = ticketCost - discAmount;
    if (finalCost < 0) {
      return 0;
    } else {
      ticket.total_discount = discAmount;
      return discAmount;
    }
  }
};

/**
 * Calculate cost for a single ticket
 * @param {Object} ticket - The ticket object
 * @param {boolean} discountValid - Whether early bird discount is valid
 * @param {boolean} forSingleTicket - Calculate for single ticket or total quantity
 * @returns {number} Ticket cost after discounts
 */
const ticketCost = (ticket, discountValid = false, forSingleTicket = false) => {
  let cost = ticket.cost_b;
  if (discountValid) {
    cost = ticket.cost_a;
  }
  return cost - ticketDiscountAmount(ticket, discountValid, forSingleTicket);
};

/**
 * Calculate VAT exclusive amount for a ticket
 * @param {Object} ticket - The ticket object
 * @param {boolean} discountValid - Whether early bird discount is valid
 * @param {boolean} forSingleTicket - Calculate for single ticket or total quantity
 * @returns {number} VAT exclusive amount
 */
const ticketVatExc = (ticket, discountValid = false, forSingleTicket = false) => {
  let cost = ticket.cost_b;
  if (discountValid) {
    cost = ticket.cost_a;
  }
  const vatRate = getVatRate(ticket);
  return (cost - ticketDiscountAmount(ticket, discountValid, forSingleTicket)) * vatRate;
};

/**
 * Calculate total cost for all tickets
 * @param {Array} tickets - Array of ticket objects
 * @param {boolean} discountValid - Whether early bird discount is valid
 * @param {boolean} forSingleTicket - Calculate for single ticket or total quantity
 * @returns {number} Total cost
 */
const totalCost = (tickets, discountValid = false, forSingleTicket = false) => {
  let total = 0;

  tickets.forEach(ticket => {
    let cost = ticket.cost_b;
    if (discountValid) {
      cost = ticket.cost_a;
    }

    total += (cost - ticketDiscountAmount(ticket, discountValid, forSingleTicket)) * ticket.quantity_tickets;
  });

  return total;
};

/**
 * Calculate total cost with event-level discount
 * @param {number} total - Base total cost
 * @param {Object} eventBooking - Event booking object with discount info
 * @returns {number} Total cost with discount applied
 */
const totalCostWithDiscount = (total, eventBooking) => {
  if (eventBooking.discount_code_id) {
    total = total - (total / 100) * eventBooking.discount_percentage;
  }
  return total;
};

/**
 * Calculate total cost for non-card payments
 * @param {Array} tickets - Array of ticket objects
 * @param {number} discountPercentage - Discount percentage
 * @param {boolean} discountValid - Whether early bird discount is valid
 * @param {boolean} forSingleTicket - Calculate for single ticket or total quantity
 * @returns {number} Total cost for non-card payments
 */
const totalCostForNonCard = (tickets, discountPercentage, discountValid = false, forSingleTicket = false) => {
  let total = 0;

  tickets.forEach(ticket => {
    let cost = ticket.cost_b;
    if (discountValid) {
      cost = ticket.cost_a;
    }

    total += (cost - ticketDiscountAmount(ticket, discountValid, forSingleTicket)) * ticket.quantity_tickets;
  });

  if (discountPercentage) {
    const subtotal = total;
    total = total - (subtotal / 100) * discountPercentage;
  }

  return total;
};

/**
 * Calculate total VAT exclusive amount
 * @param {Array} tickets - Array of ticket objects
 * @param {boolean} discountValid - Whether early bird discount is valid
 * @param {boolean} forSingleTicket - Calculate for single ticket or total quantity
 * @returns {number} Total VAT exclusive amount
 */
const totalVatExc = (tickets, discountValid = false, forSingleTicket = false) => {
  let total = 0.0;

  tickets.forEach(ticket => {
    let cost = ticket.cost_b;
    if (discountValid) {
      cost = ticket.cost_a;
    }

    total += ((cost - ticketDiscountAmount(ticket, discountValid, forSingleTicket)) *
              (getVatRate(ticket))) * ticket.quantity_tickets;
  });

  return total;
};

// Export all functions as default object (to match the import in summary.vue)
export default {
  getVatRate,
  ticketDiscountAmount,
  ticketCost,
  ticketVatExc,
  totalCost,
  totalCostWithDiscount,
  totalCostForNonCard,
  totalVatExc
};