<template>
	<div class="row" v-if="event">
		<div class="col-12 mx-auto">
			<div class="row items-center q-gutter-sm">
				<a :href="'https://www.facebook.com/sharer/sharer.php?u='+ eventUrl" target="_blank">
					<q-img :src="'https://s3-eu-west-1.amazonaws.com/' + imageBucket + '/Facebook.png'" style="width:33px; height:33px" />
				</a>
				<a :href="'https://twitter.com/intent/tweet?text=I%20going%20to%20' + event.title + '%20event.%20Come%20see%20it%20here%3A&url='+ eventUrl" target="_blank">
					<q-img :src="'https://s3-eu-west-1.amazonaws.com/' + imageBucket + '/Twitter.png'" style="width:33px; height:33px" />
				</a>
				<a :href="'https://www.linkedin.com/shareArticle?mini=true&url=' + eventUrl" target="_blank">
					<q-img :src="'https://s3-eu-west-1.amazonaws.com/'+ imageBucket + '/LinkedIn.png'" style="width:33px; height:33px" />
				</a>
				<a :href="'mailto:' + event.organiser_email + '?&body=Hey,%20I%20just%20signed%20up%20to%20an%20event,%20Come%20check%20it%20out%20at%20' + eventUrl">
					<q-img :src="'https://s3-eu-west-1.amazonaws.com/'+ imageBucket + '/Email.png'" style="width:33px; height:33px" />
				</a>
			</div>
			</div>
	</div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
});

// Access global variables defined in the app
const imageBucket = ref(window.appImageBucket);
const root_url = window.root_url;

const eventUrl = computed(() => {
  if (props.event.custom_url) {
    return root_url + props.event.custom_url;
  } else {
    return root_url + 'event/' + props.event.id + '/' + 
      props.event.title.toLowerCase().replace(/[\\!&|?$%@':;.<>/"=+\s]/g, '_');
  }
});
</script>