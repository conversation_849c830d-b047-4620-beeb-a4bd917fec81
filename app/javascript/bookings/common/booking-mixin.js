import dayjs from 'dayjs';
import PaymentBus from '../payments/payment-bus';

export default {
    methods: {
        updateBooking(mode) {
            console.log(mode);
            var self = this;
            this.eventBooking = this.$store.getters.getEventBooking;
            this.eventBooking.booking_amendment = dayjs().format();

            var event_booking = this.eventBooking;

            event_booking.registered_user_attributes = this.$store.getters.getBookerDetails;

            event_booking.package_bookings_attributes = this.eventBooking.package_bookings;

            var package_bookings = event_booking.package_bookings_attributes;

            var tickets = this.$store.getters.getSelectedTickets;

            // Updates the options and attendees
            tickets.forEach(function(ticket) {
                package_bookings.forEach(booking => {
                    booking.registered_users_attributes =
                        booking.registered_users;
                    if (booking.package_id == ticket.id) {
                        booking.sub_options_selected = ticket.optionsSelected;
                    }
                });
            });

            // Needs the token for the fees
            var booking = {
                event_booking: event_booking,
                booking_token: self.$store.getters.getBookingToken
            };

            if (this.eventBooking && this.eventBooking.uuid) {
                this.$http
                    .put(
                        '/event_bookings/' + this.eventBooking.uuid + '.json',
                        booking
                    )
                    .then(
                        function() {
                            self.$swal(
                                'Details Updated!',
                                'You have updated the booking details!',
                                'success'
                            ).then(() => {
                                var editByOrg = mode == 'edit';

                                if (editByOrg == true) {
                                    window.location =
                                        '/dashboard/' +
                                        self.event.id +
                                        '#/attendees';
                                } else {
                                    self.$router.push({
                                        name: 'summary'
                                    });
                                }
                            });
                        },
                        function(response) {
                            self.failedBooking(response);
                        }
                    )
                    .catch(function(error) {
                        console.log(error);
                    });
            }
        },

        completeBooking(
            isEuCard,
            paymentType,
            stripeToken,
            freeFromTicket = false
        ) {
            var _ithis = this;

            var tickets = this.$store.getters.getSelectedTickets;

            var event_booking = {};

            event_booking.registered_user_attributes = this.$store.getters.getBookerDetails;

            event_booking.registered_user_attributes.event_id = this.event.id;

            event_booking.event_id = this.event.id;

            event_booking.free_booking = freeFromTicket;

            event_booking.package_bookings_attributes = [];
            event_booking.payment_type = paymentType;

            // if (this.discountCodeId) {
            //   event_booking.discount_code_id = this.discountCodeId;
            //   event_booking.discount_percentage = this.discountAmount;
            // }

            tickets.forEach(function(ticket) {
                let registeredUsers = [];

                ticket.attendees.forEach(function(attendee) {
                    if (attendee.registered_user_attributes) {
                        attendee.registered_user_attributes.user_type =
                            'attendee';
                        attendee.registered_user_attributes.event_id =
                            _ithis.event.id;
                        registeredUsers.push(
                            attendee.registered_user_attributes
                        );
                    }
                });

                let discountType = 0;
                if (ticket.discount_code_id) {
                    if (ticket.discount_type != 'percentage') {
                        discountType = 1;
                    }
                }

                const package_booking = {
                    package_id: ticket.id,
                    quantity_tickets: ticket.quantity_tickets,
                    registered_users_attributes: registeredUsers,
                    sub_options_selected: ticket.optionsSelected,
                    discount_code_id: ticket.discount_code_id,
                    discount_amount: ticket.discount_amount,
                    discount_type: ticket.discount_type
                };

                event_booking.package_bookings_attributes.push(package_booking);
            });

            // Needs the token for the fees
            const booking = {
                event_booking: event_booking,
                completing: true, // this will prove this is about to be paid and should remove the  booking if not paid within 12 minutes.
                booking_token: _ithis.$store.getters.getBookingToken
            };

            if (this.eventBooking && this.eventBooking.uuid) {
                this.$http
                    .put(
                        '/event_bookings/' + this.eventBooking.uuid + '.json',
                        booking
                    )
                    .then(function(response) {
                        _ithis.successfulBooking(response);
                    })
                    .catch(function(error) {
                        _ithis.failedBooking(error);
                    });
            } else {
                this.$http
                    .post('/event_bookings/', booking)
                    .then(function(response) {
                        _ithis.successfulBooking(
                            response,
                            _ithis.event_booking
                        );
                    })
                    .catch(function(error) {
                        _ithis.failedBooking(error);
                    });
            }
        },

        warnTime(t) {
            var chargeable = this.$store.getters.getChargeable;
            if (!chargeable) {
                alert('free event');
                return true;
            }

            if (t) {
                console.log(dayjs(t));
                var dur = (10 - dayjs().diff(dayjs(t), 'minute')).toString();

                var msg =
                    'You have ' + dur + ' minutes before form becomes invalid';
            } else {
                var msg = '10 minutes before form becomes invalid';
            }
            this.$notify.success({
                message: msg,
                title: 'Please complete form straight away'
            });
        },

        payBooking(isEuCard, paymentType, isUpdate = false) {
            var tickets = this.$store.getters.getSelectedTickets;
            var _ithis = this;
            let eb = this.$store.getters.getEventBooking;
            let event_booking = {};
            event_booking.id = eb.id;
            if (this.discountCodeId) {
                event_booking.discount_code_id = this.discountCodeId;
                event_booking.discount_percentage = this.discountAmount;
            }
            event_booking.package_bookings_attributes = [];
            eb.package_bookings.forEach(function(pkg) {
                tickets.forEach(function(ticket) {
                    if (ticket.id == pkg.package_id) {
                        let discountType = 0;
                        if (ticket.discount_code_id) {
                            if (ticket.discount_type != 'percentage') {
                                discountType = 1;
                            }
                        }
                        (pkg.discount_code_id = ticket.discount_code_id),
                            (pkg.discount_amount = ticket.discount_amount),
                            (pkg.discount_type = discountType);
                        event_booking.package_bookings_attributes.push(pkg);
                    }
                });
            });

            var booking = {
                booking_token: _ithis.$store.getters.getBookingToken,
                is_eu_card: isEuCard,
                payment_type: paymentType,
                isUpdate: isUpdate
            };

            this.$http
                .put(
                    '/event_bookings/' +
                        this.eventBooking.uuid +
                        '/setup_pay_card' +
                        '.json',
                    { booking: booking, event_booking: event_booking }
                )
                .then(function(response) {
                    _ithis.successfulPaymentIntent(response);
                })
                .catch(function(error) {
                    _ithis.failedBooking(error);
                });
        },

        successfulBooking(response) {
            console.log('successfulBooking');
            let event_booking = response.data;
            console.log(event_booking);
            //event_booking.discount_code_id = data.discount_code_id;
            //event_booking.discount_percentage = data.discount_percentage;
            //event_booking.app_fees_total = data.app_fees_total;

            //If we book again these will be cleared on the server
            event_booking.cancelled_at = null;
            event_booking.cancelled_by = null;
            this.$notify.success({
                message: 'Event Booking Saved',
                title: 'Success'
            });

            this.$store.commit('setEventBooking', event_booking);
            var event = this.$store.getters.getEvent;
            var chargeable = this.$store.getters.getChargeable;

            if (
                chargeable &&
                event_booking.free_booking === false &&
                event_booking.payment_type != 'bacs' &&
                event_booking.payment_type != 'cheque'
            ) {
                this.$router.push({
                    name: 'payment'
                });
            } else {
                console.log('route to summary');
                this.$router.push('/summaryfree');
            }
        },

        successfulPaymentIntent(response) {
            this.showCardPaymentForm = true;
            if (response.data.payment_passed) {
                PaymentBus.$emit(
                    'completeIntent',
                    response.data.client_secret,
                    response.data.stripe_key
                );
            } else {
                this.$notify.error({
                    message: response.data.card_error_message,
                    title: 'Payment Issue'
                });
            }
        },

        // successfulPayment(response) {
        //     var event_booking = this.$store.getters.getEventBooking;
        //     console.log(event_booking);
        //     const data = response.data;
        //     event_booking.booking_count = 1;
        //
        //     event_booking.payment_status = data.payment_status;
        //     event_booking.discount_code_id = data.discount_code_id;
        //     event_booking.discount_percentage = data.discount_percentage;
        //     event_booking.app_fees_total = data.app_fees_total;
        //     event_booking.payment_type = data.payment_type;
        //     this.paymentStatus = 'paid';
        //
        //     //If we book again these will be cleared on the server
        //     event_booking.cancelled_at = null;
        //     event_booking.cancelled_by = null;
        //     this.$notify.success({
        //         message:
        //             this.paymentMethod != 'Card'
        //                 ? 'booking confirmed'
        //                 : 'Event Booking Paid',
        //         title: 'Success'
        //     });
        //     this.$store.commit('setEventBooking', event_booking);
        //     this.$store.commit('setInProgress', false);
        //     this.$router.push({
        //         name: 'summary'
        //     });
        // },

        failedBooking(error) {
            console.log('failedBooking');
            this.paymentSubmitted = false;
            this.paymentStatus = 'unpaid';

            if (
                error.response &&
                error.response.data &&
                error.response.data.errors
            ) {
                this.$notify.error({
                    message: error.response.data.errors[0],
                    title: 'Problem making booking'
                });
            } else {
                this.$notify.error({
                    message: 'Event Booking Not Saved',
                    title: 'Problem making booking'
                });
            }
        }
    }
};
