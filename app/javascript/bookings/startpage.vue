<template>
  <div class="booking-page">
    <div class="booking-container">
      <div class="row justify-center">
        <!-- Main Content Column -->
        <div class="col-12 col-xl-10 col-lg-11 col-md-11 col-sm-11">
          <!-- Event Info -->
          <info 
            v-if="event" 
            :event="event"
            class="section-spacing"
          />
          
          <!-- Debug Info -->
          <div v-if="showDebug" class="debug-info q-pa-sm bg-grey-2 q-mb-md">
            <div class="text-caption">🏠 Startpage Debug:</div>
            <div class="text-caption">Event exists: {{ !!event }}</div>
            <div class="text-caption">Event ID: {{ event?.id }}</div>
            <div class="text-caption">Event booking exists: {{ !!eventBooking }}</div>
            <div class="text-caption">Event booking ID: {{ eventBooking?.id }}</div>
          </div>

          <!-- Tickets Section -->
          <tickets
            v-if="event"
            :eventBooking="eventBooking"
            class="section-spacing"
          />

          <!-- No Event Warning -->
          <div v-else class="no-event-warning q-pa-md">
            <q-card class="bg-warning text-dark">
              <q-card-section>
                <div class="text-h6">⚠️ No Event Data</div>
                <div class="text-body2">Event data is not available on the startpage.</div>
              </q-card-section>
            </q-card>
          </div>
          
          <!-- Booker Form - Only show when tickets are selected -->
          <booker-form
            v-if="event && eventBooking && hasSelectedTickets"
            :event="event"
            :regUserId="eventBooking.registered_user_id"
            class="section-spacing"
          />
          
          <!-- Action Buttons - Only show when tickets are selected -->
          <bookButtonPanel
            v-if="event && !expired"
            class="section-spacing"
          />

          <!-- Debug Info for Button Panel -->
          <div v-if="showDebug" class="debug-info q-pa-sm bg-orange-2 q-mb-md">
            <div class="text-caption">🎫 Button Panel Debug:</div>
            <div class="text-caption">Event exists: {{ !!event }}</div>
            <div class="text-caption">Expired: {{ expired }}</div>
            <div class="text-caption">hasSelectedTickets: {{ hasSelectedTickets }}</div>
            <div class="text-caption">Should show panel: {{ event && !expired && hasSelectedTickets }}</div>
          </div>
        </div>

        <!-- Optional Sidebar Column - Only show if there's content -->
        <div v-if="false" class="col-12 col-lg-3">
          <div class="sidebar-content">
            <!-- Related Events -->
            <!-- <org-events
              v-if="event && event.organisation_id && event.organisation_id != 3251"
              :event="event"
              class="sidebar-section"
            /> -->

            <!-- Sponsors -->
            <!-- <sponsor-view
              v-if="event"
              :event="event"
              class="sidebar-section"
            /> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
import info from "./main_booking/info.vue";
import tickets from "./main_booking/tickets.vue"; // New improved version
import bookerForm from "./main_booking/booker-form.vue";
import bookButtonPanel from "./main_booking/book-button-panel.vue";
// import orgEvents from "../common/org-events.vue";
// import sponsorView from "../common/sponsor-view.vue";

import dayjs from "dayjs";

// Composables
const $q = useQuasar();
const bookingStore = useBookingStore();

// Reactive state
const expired = ref(false);
const closed = ref(false);
const isDevelopment = process.env.NODE_ENV === 'development';
const showDebug = ref(false);

// Computed properties
const event = computed(() => {
  // For startpage, we want the event from bookingStore (set by BookingLayout)
  // but we should ensure eventStore also has the data for tickets component
  const bookingEvent = bookingStore.getEvent;
  console.log('📋 Startpage event from bookingStore:', bookingEvent);
  return bookingEvent;
});
const eventBooking = computed(() => bookingStore.getEventBooking);

// Check if any tickets are selected
const hasSelectedTickets = computed(() => {
  const selectedTickets = bookingStore.getSelectedTickets;
  const hasValidSelection = bookingStore.hasValidTicketSelection;

  console.log('🎫 hasSelectedTickets check:', {
    selectedTickets,
    type: typeof selectedTickets,
    isArray: Array.isArray(selectedTickets),
    length: selectedTickets?.length,
    hasValidSelection,
    storeGetter: bookingStore.hasValidTicketSelection
  });

  // More robust check - ensure we have valid tickets with quantities > 0
  if (!selectedTickets || !Array.isArray(selectedTickets) || selectedTickets.length === 0) {
    console.log('🎫 No valid tickets: null, not array, or empty');
    return false;
  }

  const hasTicketsWithQuantity = selectedTickets.some(ticket =>
    ticket && typeof ticket.quantity_tickets === 'number' && ticket.quantity_tickets > 0
  );

  console.log('🎫 Final hasSelectedTickets result:', hasTicketsWithQuantity);
  return hasTicketsWithQuantity;
});

// Methods
const checkEventExpiration = () => {
  if (!event.value || !event.value.datetimeto) return;

  expired.value = dayjs(event.value.datetimeto).isBefore(dayjs(), "hour");

  if (expired.value) {
    $q.dialog({
      title: "This event has expired and can no longer be booked",
      color: "warning",
      ok: {
        color: "warning",
        label: "OK"
      }
    });
  }

  if (event.value.close_date) {
    closed.value = dayjs(event.value.close_date).isBefore(dayjs(), "day");

    if (closed.value) {
      expired.value = true;
      $q.dialog({
        title: "This event has closed and can no longer be booked",
        color: "warning",
        ok: {
          color: "warning",
          label: "OK"
        }
      });
    }
  }
};

// Watchers
watch(
  event,
  (newEvent) => {
    console.log('Startpage: Event changed:', newEvent);
    console.log('Startpage: Event tickets:', newEvent?.tickets);
    console.log('Startpage: Event ticket_groups:', newEvent?.ticket_groups);
    if (newEvent && newEvent.datetimeto && !expired.value) {
      checkEventExpiration();
    }
  },
  { immediate: true }
);

watch(
  eventBooking,
  (newEventBooking) => {
    console.log('Startpage: Event booking changed:', newEventBooking);
  },
  { immediate: true }
);

// Lifecycle
onMounted(() => {
  console.log('🏠 Startpage mounted');
  console.log('🎪 Initial event:', event.value);
  console.log('📋 Initial eventBooking:', eventBooking.value);
  
  // Handle URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const referrer = urlParams.get('referrer');
  if (referrer) {
    bookingStore.setExtWebsite(document.referrer);
  }
  bookingStore.setExpired(expired.value);
});
</script>

<style scoped>
.booking-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 0;
}

.booking-container {
  max-width: 1600px; /* Much wider container for desktop */
  margin: 0 auto; /* Center the container */
  padding: 2rem 1rem; /* Add padding around the container */
  width: 100%;
}

.section-spacing {
  margin-bottom: 2rem; /* Better spacing between components on desktop */
  min-height: 50px; /* Ensure some height */
}

/* Main content area improvements */
.col-12.col-xl-9.col-lg-10.col-md-11 {
  padding: 0 1rem; /* Add horizontal padding to content */
}

/* Desktop-specific improvements */
@media (min-width: 1024px) {
  .section-spacing {
    margin-bottom: 2.5rem; /* More generous spacing on desktop */
  }

  .booking-container {
    padding: 3rem 2rem; /* More padding on desktop */
  }
}

.sidebar-content {
  position: sticky;
  top: 2rem;
}

.sidebar-section {
  margin-bottom: 1.5rem;
}

/* Responsive adjustments */
@media (min-width: 1400px) {
  .booking-container {
    max-width: 1800px; /* Even wider on very large screens */
    padding: 3rem 2rem;
  }
}

/* Ultra-wide screens */
@media (min-width: 1920px) {
  .booking-container {
    max-width: 2000px; /* Maximum width for ultra-wide screens */
    padding: 4rem 3rem;
  }
}

@media (max-width: 1199px) {
  .booking-container {
    max-width: 1200px;
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 1023px) {
  .booking-container {
    max-width: 100%;
    padding: 1rem;
  }

  .section-spacing {
    margin-bottom: 0.75rem; /* Further reduce spacing on tablet*/
  }

  .sidebar-content {
    position: static;
  }
}

@media (max-width: 767px) {
  .booking-container {
    padding: 0.5rem;
  }

  .section-spacing {
    margin-bottom: 0.5rem; /* Minimal spacing on mobile*/
  }
}

/* Card improvements */
:deep(.q-card) {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: white;
  transition: all 0.3s ease;
}

:deep(.q-card:hover) {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.invalid-feedback {
  display: block !important;
}

.debug-info {
  border-radius: 8px;
  border: 1px solid #ddd;
  font-family: monospace;
  font-size: 0.8rem;

  .text-caption {
    margin: 2px 0;
  }
}

.no-event-warning {
  .q-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}
</style>
