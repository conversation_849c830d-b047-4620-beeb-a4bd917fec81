<template>
  <div class="tickets-simple-wrapper">
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">🎫 Tickets Component (Simple Version)</div>
        
        <!-- Debug Info -->
        <div class="debug-section q-mt-md">
          <div class="text-subtitle2">Debug Information:</div>
          <ul class="text-caption">
            <li>Component mounted: ✅</li>
            <li>Props received: {{ Object.keys(props).length }} props</li>
            <li>EventBooking prop: {{ !!props.eventBooking }}</li>
            <li>EventBooking ID: {{ props.eventBooking?.id || 'N/A' }}</li>
            <li>Event from booking store: {{ !!bookingStore.getEvent }}</li>
            <li>Event from event store: {{ !!eventStore.getEvent }}</li>
            <li>Selected event: {{ !!selectedEvent }}</li>
            <li>Selected event ID: {{ selectedEvent?.id || 'N/A' }}</li>
            <li>Selected event title: {{ selectedEvent?.title || 'N/A' }}</li>
            <li>Tickets count: {{ selectedEvent?.tickets?.length || 0 }}</li>
            <li>Ticket groups count: {{ selectedEvent?.ticket_groups?.length || 0 }}</li>
          </ul>
        </div>

        <!-- Event Info -->
        <div v-if="selectedEvent" class="event-info q-mt-md">
          <div class="text-subtitle2">Event Information:</div>
          <div class="text-body2">
            <strong>{{ selectedEvent.title }}</strong>
          </div>
          <div class="text-caption">
            ID: {{ selectedEvent.id }}
          </div>
        </div>

        <!-- Tickets Info -->
        <div v-if="selectedEvent" class="tickets-info q-mt-md">
          <div class="text-subtitle2">Tickets Available:</div>
          
          <!-- Regular Tickets -->
          <div v-if="selectedEvent.tickets && selectedEvent.tickets.length > 0">
            <div class="text-body2 q-mb-sm">Regular Tickets ({{ selectedEvent.tickets.length }}):</div>
            <ul class="text-caption">
              <li v-for="ticket in selectedEvent.tickets" :key="ticket.id">
                {{ ticket.details }} - Remaining: {{ ticket.tickets_remaining }}
              </li>
            </ul>
          </div>

          <!-- Ticket Groups -->
          <div v-if="selectedEvent.ticket_groups && selectedEvent.ticket_groups.length > 0" class="q-mt-sm">
            <div class="text-body2 q-mb-sm">Ticket Groups ({{ selectedEvent.ticket_groups.length }}):</div>
            <div v-for="group in selectedEvent.ticket_groups" :key="group.id" class="q-ml-md">
              <div class="text-body2">{{ group.description }}</div>
              <ul class="text-caption" v-if="group.packages">
                <li v-for="ticket in group.packages" :key="ticket.id">
                  {{ ticket.details }} - Remaining: {{ ticket.tickets_remaining }}
                </li>
              </ul>
            </div>
          </div>

          <!-- No Tickets -->
          <div v-if="(!selectedEvent.tickets || selectedEvent.tickets.length === 0) && 
                     (!selectedEvent.ticket_groups || selectedEvent.ticket_groups.length === 0)">
            <div class="text-body2 text-warning">No tickets available for this event.</div>
          </div>
        </div>

        <!-- No Event -->
        <div v-else class="no-event q-mt-md">
          <div class="text-body2 text-negative">No event data available.</div>
        </div>

        <!-- Actions -->
        <div class="actions q-mt-md">
          <q-btn 
            color="primary" 
            label="Test Button" 
            @click="testAction"
            class="q-mr-sm"
          />
          <q-btn 
            color="secondary" 
            label="Refresh Data" 
            @click="refreshData"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
import { useEventStore } from '@/stores/event';
import { useQuasar } from 'quasar';

// Stores
const bookingStore = useBookingStore();
const eventStore = useEventStore();
const $q = useQuasar();

// Props
const props = defineProps({
  eventBooking: {
    type: Object,
    default: () => ({})
  }
});

// Computed
const selectedEvent = computed(() => {
  const storeEvent = eventStore.getEvent;
  const bookingEvent = bookingStore.getEvent;
  return storeEvent || bookingEvent;
});

// Methods
const testAction = () => {
  $q.notify({
    message: 'Test button clicked!',
    color: 'positive',
    icon: 'check'
  });
};

const refreshData = () => {
  console.log('🔄 Refreshing data...');
  console.log('EventStore event:', eventStore.getEvent);
  console.log('BookingStore event:', bookingStore.getEvent);
  console.log('Props:', props);
};

// Lifecycle
onMounted(() => {
  console.log('🎫 Simple tickets component mounted');
  console.log('Props:', props);
  console.log('Selected event:', selectedEvent.value);
  refreshData();
});
</script>

<style scoped>
.tickets-simple-wrapper {
  width: 100%;
  max-width: 100%;
}

.debug-section {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.event-info,
.tickets-info {
  background: #f9f9f9;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.no-event {
  background: #fff3cd;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
}

.actions {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}
</style>
