<template>
    <div class="enhanced-template">
        <!-- Hero Section -->
        <div class="hero-section">
            <q-card class="hero-card" flat>
                <div 
                    class="hero-background"
                    :style="{ 'background-image': imageFileURL ? `url(${imageFileURL})` : 'none' }"
                >
                    <div class="hero-overlay">
                        <div class="hero-content">
                            <div class="hero-text">
                                <h1 class="event-title">{{ event.title }}</h1>
                                
                                <!-- Location Info -->
                                <div v-if="!event.remove_location" class="location-info">
                                    <div class="info-item">
                                        <q-icon name="place" />
                                        <span>{{ event.event_address?.address1 }}</span>
                                    </div>
                                    <div class="info-item">
                                        <q-icon name="location_city" />
                                        <span>{{ event.event_address?.city }}</span>
                                    </div>
                                    <div class="info-item">
                                        <q-icon name="local_post_office" />
                                        <span>{{ event.event_address?.postcode }}</span>
                                    </div>
                                    <div v-if="event.event_address?.county" class="info-item">
                                        <q-icon name="map" />
                                        <span>{{ event.event_address.county }}</span>
                                    </div>
                                    <div v-if="event.event_address?.country_code" class="info-item">
                                        <q-icon name="flag" />
                                        <span>{{ countryName }}</span>
                                    </div>
                                </div>

                                <!-- Date/Time Info -->
                                <div class="datetime-info">
                                    <div v-if="!datesEqual" class="date-range">
                                        <div class="info-item">
                                            <q-icon name="event" />
                                            <span><strong>From:</strong> {{ formatDate(event.datetimefrom) }} @ {{ formatTime(event.datetimefrom) }}</span>
                                        </div>
                                        <div class="info-item">
                                            <q-icon name="event" />
                                            <span><strong>To:</strong> {{ formatDate(event.datetimeto) }} @ {{ formatTime(event.datetimeto) }}</span>
                                        </div>
                                    </div>
                                    <div v-else class="single-date">
                                        <div class="info-item">
                                            <q-icon name="event" />
                                            <span>{{ formatDate(event.datetimefrom) }}</span>
                                        </div>
                                        <div class="info-item">
                                            <q-icon name="access_time" />
                                            <span>{{ formatTime(event.datetimefrom) }} - {{ formatTime(event.datetimeto) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Social and Contact -->
                                <div class="hero-actions">
                                    <social-circles
                                        v-if="event.id != 2912"
                                        :event="event"
                                        class="social-links"
                                    />
                                    
                                    <q-btn
                                        :style="{ background: event.buttoncolour, 'border-color': event.buttoncolour }"
                                        :href="mailto"
                                        no-caps
                                        rounded
                                        class="contact-btn"
                                        icon="email"
                                    >
                                        Contact Organiser
                                    </q-btn>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </q-card>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <div class="row q-col-gutter-lg">
                <!-- Venue Card -->
                <div class="col-12 col-md-4">
                    <q-card class="venue-card">
                        <q-card-section>
                            <q-img
                                v-if="event.image1"
                                :src="logoFileURL"
                                alt="Event Logo"
                                class="venue-logo"
                                fit="contain"
                            />

                            <div v-if="!event.remove_location" class="venue-info">
                                <h3 class="venue-title" :style="{ color: event.phcolour }">
                                    <q-icon name="place" class="q-mr-sm" />
                                    {{ event.location }}
                                </h3>

                                <div class="map-container" v-if="!event.international && !event.remove_location">
                                    <maps
                                        name="previewmap"
                                        :postcode="event.event_address.postcode"
                                    />
                                </div>
                            </div>
                        </q-card-section>
                    </q-card>
                </div>

                <!-- Event Details Card -->
                <div class="col-12 col-md-8">
                    <q-card class="details-card">
                        <q-card-section>
                            <h3 class="details-title" :style="{ color: event.phcolour }">
                                About the Event
                            </h3>
                            <div 
                                class="event-description" 
                                :style="{ color: event.textcolour }" 
                                v-html="eventDetailsModified"
                            />
                        </q-card-section>
                    </q-card>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import maps from '@/common/maps.vue';
import dayjs from 'dayjs';
import socialCircles from '@/bookings/common/social-circles.vue';
import { useCountries } from '@/bookings/common/countries';

const props = defineProps({
    event: {
        type: Object,
        required: true
    },
    lat: {
        type: [Number, String],
        default: null
    },
    lng: {
        type: [Number, String],
        default: null
    },
    imageBucket: {
        type: String,
        default: ''
    },
    imageFileURL: {
        type: String,
        default: ''
    },
    logoFileURL: {
        type: String,
        default: ''
    }
});

const { getCountryName } = useCountries();
const eventDetailsModified = ref('');

onMounted(() => {
    eventDetailsModified.value = props.event.details.replaceAll(
        '<p><br></p>',
        '<br>'
    );
});

const showAddress = computed(() => {
    return !props.event.remove_location && props.event.event_address;
});

const countryName = computed(() => {
    if (
        props.event.event_address &&
        props.event.event_address.country_code
    ) {
        return getCountryName(
            props.event.event_address.country_code
        );
    } else {
        return '';
    }
});

const topLineOverride = computed(() => {
    return { borderTop: '4px solid ' + props.event.phcolour };
});

const mailto = computed(() => {
    return (
        'mailto:' +
        props.event.organiser_email +
        '?Subject=Query%20about%20' +
        props.event.title
    );
});

const datesEqual = computed(() => {
    return dayjs(props.event.datetimefrom)
        .isSame(dayjs(props.event.datetimeto), 'day');
});

// Helper functions for date formatting (replacing Vue 2 filters)
const formatDate = (value) => {
    if (value) {
        return dayjs(value).format('DD/MM/YYYY');
    }
    return '';
};

const formatTime = (value) => {
    if (value) {
        return dayjs(value).format('HH:mm');
    }
    return '';
};
</script>

<style scoped>
.enhanced-template {
    width: 100%;
}

/* Hero Section */
.hero-section {
    margin-bottom: 2rem;
}

.hero-card {
    border-radius: 16px;
    overflow: hidden;
    min-height: 400px;
}

.hero-background {
    width: 100%;
    min-height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.hero-overlay {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
    width: 100%;
    height: 100%;
    min-height: 400px;
    display: flex;
    align-items: center;
    padding: 2rem;
}

.hero-content {
    width: 100%;
    max-width: 800px;
}

.hero-text {
    color: white;
}

.event-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1.5rem 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.location-info,
.datetime-info {
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.info-item .q-icon {
    margin-right: 0.75rem;
    font-size: 1.2rem;
    opacity: 0.9;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
}

.contact-btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Content Section */
.content-section {
    margin-bottom: 2rem;
}

.venue-card,
.details-card {
    height: 100%;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.venue-logo {
    max-height: 120px;
    margin-bottom: 1.5rem;
    border-radius: 8px;
}

.venue-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
}

.venue-info {
    margin-top: 1rem;
}

.map-container {
    width: 100%;
    height: 250px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
}

.details-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
}

.event-description {
    font-size: 1rem;
    line-height: 1.6;
}

.event-description :deep(p) {
    margin-bottom: 1rem;
}

.event-description :deep(h1),
.event-description :deep(h2),
.event-description :deep(h3),
.event-description :deep(h4),
.event-description :deep(h5),
.event-description :deep(h6) {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1023px) {
    .hero-overlay {
        padding: 1.5rem;
        min-height: 350px;
    }
    
    .event-title {
        font-size: 2rem;
    }
    
    .hero-content {
        max-width: 100%;
    }
}

@media (max-width: 767px) {
    .hero-overlay {
        padding: 1rem;
        min-height: 300px;
    }
    
    .event-title {
        font-size: 1.75rem;
        margin-bottom: 1rem;
    }
    
    .info-item {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }
    
    .hero-actions {
        margin-top: 1rem;
    }
    
    .contact-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .venue-title {
        font-size: 1.25rem;
    }
    
    .details-title {
        font-size: 1.5rem;
    }
    
    .map-container {
        height: 200px;
    }
}

@media (max-width: 479px) {
    .event-title {
        font-size: 1.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .contact-btn {
        width: 100%;
        text-align: center;
    }
}

/* Social links spacing */
.social-links {
    margin-bottom: 0.5rem;
}
</style>