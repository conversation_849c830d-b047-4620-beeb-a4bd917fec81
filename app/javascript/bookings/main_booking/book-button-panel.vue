<template>
  <div class="book-button-panel">
    <!-- Debug: Only show in development -->
    <!-- <div v-if="isDevelopment" class="debug-panel bg-red-2 q-pa-sm q-mb-md">
      <div class="text-caption">🔧 Book Button Panel Debug</div>
      <div class="text-caption">shouldShowPanel: {{ shouldShowPanel }}</div>
      <div class="text-caption">showBookButton: {{ showBookButton }}</div>
      <div class="text-caption">showUpdateButton: {{ showUpdateButton }}</div>
    </div> -->

    <div v-if="shouldShowPanel">
      <q-card class="booking-actions-card">
        <q-card-section>
          <div class="row q-gutter-md justify-center">
            <q-btn
              v-if="showBookButton"
              color="positive"
              size="lg"
              :loading="loading"
              :disable="!canSubmitBooking"
              @click="handleBook"
              class="q-px-xl book-btn"
            >
              <q-icon name="event_seat" class="q-mr-sm" />
              Book Tickets
            </q-btn>

            <q-btn
              v-if="showUpdateButton"
              color="primary"
              size="lg"
              :loading="loading"
              @click="handleUpdate"
              class="q-px-xl"
            >
              <q-icon name="edit" class="q-mr-sm" />
              Update Details
            </q-btn>
          </div>

          <!-- Validation Messages -->
          <div v-if="validationMessage" class="validation-message q-mt-md text-center">
            <q-chip
              :color="validationMessage.type === 'error' ? 'negative' : 'warning'"
              text-color="white"
              :icon="validationMessage.type === 'error' ? 'error' : 'warning'"
            >
              {{ validationMessage.text }}
            </q-chip>
          </div>

          <!-- Debug buttons - remove in production -->
          <div v-if="isDevelopment" class="q-mt-md text-center">
            <q-btn
              @click="testBookingSubmission"
              color="purple"
              size="sm"
              label="Test Booking API"
              class="q-mr-sm"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <div v-else-if="isDevelopment" class="no-panel-debug bg-yellow-2 q-pa-sm">
      <div class="text-caption">❌ Panel not showing because shouldShowPanel is false</div>
    </div>
  </div>
</template>


<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
import eventBus from '@/shared/eventBus';

const router = useRouter();
const $q = useQuasar();
const bookingStore = useBookingStore();
const loading = ref(false);

const isDevelopment = process.env.NODE_ENV === 'development';
const isDebug = ref(false);

// Computed properties
const shouldShowPanel = computed(() => {
  const hasValidSelection = bookingStore.hasValidTicketSelection;
  const selectedTickets = bookingStore.getSelectedTickets;

  console.log('🎫 Book Button Panel Debug:', {
    hasValidSelection,
    selectedTickets,
    selectedTicketsType: typeof selectedTickets,
    isArray: Array.isArray(selectedTickets),
    length: selectedTickets?.length
  });

  // Temporarily always show for debugging
  // TODO: Change back to: return hasValidSelection;
  return true;
});

const showBookButton = computed(() => {
  return !bookingStore.getReadOnly && !bookingStore.getInProgress;
});

const showUpdateButton = computed(() => {
  return bookingStore.getReadOnly && !bookingStore.getInProgress;
});

const canSubmitBooking = computed(() => {
  return bookingStore.hasValidTicketSelection && !loading.value;
});

const validationMessage = computed(() => {
  if (!bookingStore.hasValidTicketSelection) {
    return {
      type: 'warning',
      text: 'Please select tickets to continue'
    };
  }

  if (loading.value) {
    return {
      type: 'info',
      text: 'Processing your booking...'
    };
  }

  return null;
});

// Context7 pattern: Enhanced validation handling
const handleBook = async () => {
  if (loading.value) return;

  console.log('📝 Book button clicked - starting validation process');
  loading.value = true;

  try {
    // Emit validation request and wait for response
    console.log('📝 Requesting booker form validation...');
    eventBus.emit('validate-booker-form');
  } catch (error) {
    console.error('❌ Error during book process:', error);
    $q.notify({
      type: 'negative',
      message: 'An error occurred. Please try again.',
      position: 'top'
    });
    loading.value = false;
  }
};

const handleUpdate = async () => {
  if (loading.value) return;

  console.log('📝 Update button clicked - starting validation process');
  loading.value = true;

  try {
    // Emit validation request and wait for response
    console.log('📝 Requesting booker form validation...');
    eventBus.emit('validate-booker-form');
  } catch (error) {
    console.error('❌ Error during update process:', error);
    $q.notify({
      type: 'negative',
      message: 'An error occurred. Please try again.',
      position: 'top'
    });
    loading.value = false;
  }
};

// Context7 pattern: Enhanced validation response handler
const handleValidationResponse = async (isValid) => {
  console.log('📝 Book button panel: Received validation result:', isValid);

  if (!isValid) {
    console.log('❌ Validation failed - stopping submission process');

    $q.notify({
      type: 'negative',
      message: 'Please check the highlighted fields and correct any errors before proceeding',
      position: 'top',
      timeout: 6000,
      actions: [
        {
          label: 'Dismiss',
          color: 'white',
          handler: () => {}
        }
      ]
    });

    loading.value = false;
    return;
  }

  console.log('✅ Validation passed - proceeding with submission');

  // Continue with booking/update process
  try {
    if (showBookButton.value) {
      console.log('📋 Submitting booking...');
      const result = await bookingStore.submitBooking();

      console.log('📋 Booking submission result:', result);

      // Update the booking store with the returned booking data
      if (result && result.id) {
        bookingStore.setEventBooking(result);
        console.log('📋 Updated booking store with new booking data');
      }

      $q.notify({
        type: 'positive',
        message: 'Booking submitted successfully!',
        position: 'top'
      });

      // Determine where to redirect based on whether payment is needed
      if (result.redirect_url) {
        console.log('📋 Redirecting to provided URL:', result.redirect_url);
        window.location.href = result.redirect_url;
      } else if (bookingStore.getChargeable && !result.free_booking) {
        console.log('📋 Redirecting to payment page for paid booking');
        router.push('/payment');
      } else {
        console.log('📋 Redirecting to summary page for free booking');
        router.push('/summary');
      }
    } else if (showUpdateButton.value) {
      console.log('📋 Updating booking details...');
      const result = await bookingStore.updateBooking();

      console.log('📋 Booking update result:', result);

      // Update the booking store with the returned booking data
      if (result && result.id) {
        bookingStore.setEventBooking(result);
        console.log('📋 Updated booking store with updated booking data');
      }

      $q.notify({
        type: 'positive',
        message: 'Details updated successfully!',
        position: 'top'
      });

      // For updates, redirect based on the same logic
      if (result.redirect_url) {
        console.log('📋 Redirecting to provided URL:', result.redirect_url);
        window.location.href = result.redirect_url;
      } else if (bookingStore.getChargeable && !result.free_booking) {
        console.log('📋 Redirecting to payment page for paid booking update');
        router.push('/payment');
      } else {
        console.log('📋 Redirecting to summary page for free booking update');
        router.push('/summary');
      }
    }
  } catch (error) {
    console.error('❌ Action failed:', error);
    $q.notify({
      type: 'negative',
      message: error.message || 'An error occurred. Please try again.',
      position: 'top'
    });
  } finally {
    loading.value = false;
  }
};

// Debug function to test booking submission
const testBookingSubmission = async () => {
  console.log('🧪 Testing booking submission directly...');

  try {
    loading.value = true;

    console.log('🧪 Current booking store state:', {
      event: bookingStore.event,
      selectedTickets: bookingStore.getSelectedTickets,
      bookerDetails: bookingStore.bookerDetails,
      hasValidSelection: bookingStore.hasValidTicketSelection
    });

    if (showBookButton.value) {
      console.log('🧪 Testing submitBooking...');
      const result = await bookingStore.submitBooking();
      console.log('🧪 Submit result:', result);

      // Test the redirect logic
      if (result && result.id) {
        bookingStore.setEventBooking(result);
        console.log('🧪 Updated booking store with test booking data');

        if (bookingStore.getChargeable && !result.free_booking) {
          console.log('🧪 Would redirect to payment page');
        } else {
          console.log('🧪 Would redirect to summary page');
        }
      }
    } else if (showUpdateButton.value) {
      console.log('🧪 Testing updateBooking...');
      const result = await bookingStore.updateBooking();
      console.log('🧪 Update result:', result);

      if (result && result.id) {
        bookingStore.setEventBooking(result);
        console.log('🧪 Updated booking store with test update data');
      }
    }

    $q.notify({
      type: 'positive',
      message: 'Test booking submission completed! Check console for details.',
      position: 'top'
    });

  } catch (error) {
    console.error('🧪 Test booking submission failed:', error);
    $q.notify({
      type: 'negative',
      message: `Test failed: ${error.message}`,
      position: 'top'
    });
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  console.log('📝 Book button panel: Setting up validation response listener');
  eventBus.on('booker-form-validated', handleValidationResponse);
});

onUnmounted(() => {
  console.log('📝 Book button panel: Cleaning up validation response listener');
  eventBus.off('booker-form-validated', handleValidationResponse);
});

const oldHandleBook = async () => {
  if (loading.value) return;

  loading.value = true;

  try {
    // First validate the booker form
    // const isFormValid = await validateForm();

    // if (!isFormValid) {
    //   $q.notify({
    //     type: 'negative',
    //     message: 'Please fix the form errors before booking',
    //     position: 'top'
    //   });
    //   return;
    // }

    // Submit the booking
    console.log('📋 Submitting booking...');
    const result = await bookingStore.submitBooking();

    $q.notify({
      type: 'positive',
      message: 'Booking submitted successfully!',
      position: 'top'
    });

    // Navigate to success page or summary
    if (result.redirect_url) {
      window.location.href = result.redirect_url;
    } else {
      router.push('/summary');
    }

  } catch (error) {
    console.error('Booking submission failed:', error);
    $q.notify({
      type: 'negative',
      message: error.message || 'Booking submission failed. Please try again.',
      position: 'top'
    });
  } finally {
    loading.value = false;
  }
};

const oldHandleUpdate = async () => {
  if (loading.value) return;

  loading.value = true;

  try {
    // Validate form first
    // const isFormValid = await validateForm();

    // if (!isFormValid) {
    //   $q.notify({
    //     type: 'negative',
    //     message: 'Please fix the form errors before updating',
    //     position: 'top'
    //   });
    //   return;
    // }

    // Handle update logic here
    console.log('📋 Updating booking details...');
    const result = await bookingStore.updateBooking();

    $q.notify({
      type: 'positive',
      message: 'Details updated successfully!',
      position: 'top'
    });

    // Optional: Navigate or refresh data after update
    if (result.redirect_url) {
      window.location.href = result.redirect_url;
    }

  } catch (error) {
    console.error('Update failed:', error);
    $q.notify({
      type: 'negative',
      message: 'Update failed. Please try again.',
      position: 'top'
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.book-button-panel {
  width: 100%;
  max-width: 100%;
  margin-top: 0; /* Remove top margin to align with section-spacing from parent */
}

.booking-actions-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: white;
  transition: all 0.3s ease;
}

.booking-actions-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.book-btn {
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;
  min-width: 180px;
  padding: 12px 24px;
  font-size: 1rem;
}

.validation-message {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Ensure consistent spacing with other components */
.booking-actions-card .q-card-section {
  padding: 2rem;
}

/* Mobile responsive */
@media (max-width: 600px) {
  .book-btn {
    width: 100%;
    min-width: auto;
  }

  .row {
    flex-direction: column;
  }

  .row > * {
    width: 100%;
  }
}
</style>
