<template>
    <div class="row venue-event">
        <div class="col-12 col-md-5 col-lg-4 col-xl-3 col-venue">
            <q-card class="hg-topline" :style="topLineOverride">
                <q-img
                    v-if="event.image1"
                    :src="logoFileURL"
                    alt="Logo"
                    style="margin: 5px; margin-bottom: 15px; max-height: 200px"
                />

                <q-card-section>
                    <div class="text-left">
                        <q-list v-if="!event.remove_location" dense class="q-mb-md">
                            <q-item>
                                <q-item-section avatar>
                                    <q-icon name="fa fa-map" />
                                </q-item-section>
                                <q-item-section class="wordBreak">{{ event.location }}</q-item-section>
                            </q-item>
                        </q-list>
                        
                        <q-list v-if="showAddress" dense>
                            <q-item>
                                <q-item-section avatar>
                                    <q-icon name="fa fa-road" />
                                </q-item-section>
                                <q-item-section class="wordBreak">{{ event.event_address.address1 }}</q-item-section>
                            </q-item>
                            
                            <q-item>
                                <q-item-section avatar>
                                    <q-icon name="fa fa-building" />
                                </q-item-section>
                                <q-item-section>{{ event.event_address.city }}</q-item-section>
                            </q-item>
                            
                            <q-item>
                                <q-item-section avatar>
                                    <q-icon name="fa fa-map-marker" />
                                </q-item-section>
                                <q-item-section>{{ event.event_address.postcode }}</q-item-section>
                            </q-item>
                            
                            <q-item v-if="event.event_address.county">
                                <q-item-section avatar>
                                    <q-icon name="fa fa-map-marker" />
                                </q-item-section>
                                <q-item-section>{{ event.event_address.county }}</q-item-section>
                            </q-item>
                            
                            <q-item v-if="event.event_address.country_code">
                                <q-item-section avatar>
                                    <q-icon name="fa fa-flag" />
                                </q-item-section>
                                <q-item-section>{{ countryName }}</q-item-section>
                            </q-item>
                        </q-list>
                    </div>
                    <q-separator v-if="!event.remove_location" class="q-my-md" />

                    <q-list v-if="!datesEqual" dense>
                        <q-item>
                            <q-item-section avatar>
                                <q-icon name="fa fa-calendar" />
                            </q-item-section>
                            <q-item-section>
                                <q-item-label class="text-weight-bold">From: </q-item-label>
                                <q-item-label caption>
                                    {{ formatDate(event.datetimefrom) }} @ {{ formatTime(event.datetimefrom) }}
                                </q-item-label>
                            </q-item-section>
                        </q-item>
                            
                        <q-item>
                            <q-item-section avatar>
                                <q-icon name="fa fa-calendar-o" />
                            </q-item-section>
                            <q-item-section>
                                <q-item-label class="text-weight-bold">To: </q-item-label>
                                <q-item-label caption>
                                    {{ formatDate(event.datetimeto) }} @ {{ formatTime(event.datetimeto) }}
                                </q-item-label>
                            </q-item-section>
                        </q-item>
                    </q-list>

                    <q-list v-if="datesEqual" dense>
                        <q-item>
                            <q-item-section avatar>
                                <q-icon name="fa fa-calendar" />
                            </q-item-section>
                            <q-item-section>{{ formatDate(event.datetimefrom) }}</q-item-section>
                        </q-item>
                            
                        <q-item>
                            <q-item-section avatar>
                                <q-icon name="fa fa-clock-o" />
                            </q-item-section>
                            <q-item-section>
                                {{ formatTime(event.datetimefrom) }} - {{ formatTime(event.datetimeto) }}
                            </q-item-section>
                        </q-item>
                    </q-list>

                    <div class="q-mt-lg">
                        <q-btn
                            :style="{
                                'background-color': event.buttoncolour,
                                'border-color': event.buttoncolour,
                            }"
                            color="primary"
                            size="sm"
                            class="q-mb-xl"
                            :href="mailto"
                            label="Email Organiser"
                        />
                    </div>
                    <div>
                        <social-circles
                            v-if="event.id != 2912"
                            :event="event"
                        ></social-circles>
                    </div>
                </q-card-section>
            </q-card>
        </div>
        <div class="col-12 col-md-7 col-lg-8 col-xl-9 col-event">
            <q-card class="hg-topline" :style="topLineOverride">
                <q-card-section>
                    <div class="row">
                        <div class="col-12 col-md-8">
                            <div class="text-h5 text-weight-bold" :style="{ color: event.phcolour }">
                                {{ event.title }}
                            </div>

                            <div class="q-my-md">
                                <div class="text-h6" :style="{ color: event.phcolour }">
                                    About the event:
                                </div>
                            </div>

                            <div
                                id="test"
                                :style="{ color: event.textcolour }"
                                v-html="eventDetailsModified"
                            ></div>
                        </div>
                        <div class="col-12 col-md-4">
                            <div class="text-center" v-if="imageFileURL">
                                <q-img
                                    :src="imageFileURL"
                                    alt="Event Image"
                                    class="q-mb-md"
                                />
                            </div>

                            <div
                                v-if="!event.remove_location"
                                class="text-h6"
                                :style="{ color: event.phcolour }"
                            >
                                Venue - {{ event.location }}
                            </div>

                            <div
                                style="width: 100%; height: 50%"
                                v-if="!event.international && !event.remove_location"
                            >
                                <maps
                                    name="previewmap"
                                    :postcode="event.event_address.postcode"
                                ></maps>
                            </div>
                        </div>
                    </div>
                </q-card-section>
            </q-card>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import maps from '@/common/maps.vue';
import dayjs from 'dayjs';
import socialCircles from '@/bookings/common/social-circles.vue';
import { useCountries } from '@/bookings/common/countries';

const props = defineProps({
    event: {
        type: Object,
        required: true
    },
    lat: {
        type: [Number, String],
        default: null
    },
    lng: {
        type: [Number, String],
        default: null
    },
    imageBucket: {
        type: String,
        default: ''
    },
    imageFileURL: {
        type: String,
        default: ''
    },
    logoFileURL: {
        type: String,
        default: ''
    }
});

const { getCountryName } = useCountries();
const eventDetailsModified = ref('');

onMounted(() => {
    eventDetailsModified.value = props.event.details.replaceAll(
        '<p><br></p>',
        '<br>'
    );
});

const showAddress = computed(() => {
    return !props.event.remove_location && props.event.event_address;
});

const countryName = computed(() => {
    if (
        props.event.event_address &&
        props.event.event_address.country_code
    ) {
        return getCountryName(
            props.event.event_address.country_code
        );
    } else {
        return '';
    }
});

const topLineOverride = computed(() => {
    return { borderTop: '4px solid ' + props.event.phcolour };
});

const mailto = computed(() => {
    return (
        'mailto:' +
        props.event.organiser_email +
        '?Subject=Query%20about%20' +
        props.event.title
    );
});

const datesEqual = computed(() => {
    return dayjs(props.event.datetimefrom)
        .isSame(dayjs(props.event.datetimeto), 'day');
});

// Helper functions for date formatting (replacing Vue 2 filters)
const formatDate = (value) => {
    if (value) {
        return dayjs(value).format('DD/MM/YYYY');
    }
    return '';
};

const formatTime = (value) => {
    if (value) {
        return dayjs(value).format('HH:mm');
    }
    return '';
};
</script>

<style scoped>
.wordBreak {
    word-break: break-word;
}
.hg-topline {
  margin-bottom: 1.5rem;
  border-top: 3px solid var(--q-primary);
}
</style>