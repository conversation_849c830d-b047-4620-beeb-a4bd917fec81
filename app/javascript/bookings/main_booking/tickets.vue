<template>
  <div class="tickets-component-wrapper">
    <!-- Error <PERSON>ry -->
    <div v-if="hasError" class="error-container q-pa-md">
      <q-card class="bg-negative text-white">
        <q-card-section>
          <div class="text-h6">⚠️ Component Error</div>
          <div class="text-body2">{{ errorMessage }}</div>
          <q-btn flat label="Retry" @click="retryLoad" class="q-mt-sm" />
        </q-card-section>
      </q-card>
    </div>

    <!-- Loading State -->
    <div v-else-if="isLoading" class="loading-container q-pa-md">
      <q-card>
        <q-card-section class="text-center">
          <q-spinner size="50px" color="primary" />
          <div class="q-mt-md">Loading event tickets...</div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Main Tickets Content -->
    <div v-else-if="event && !hasError" class="tickets-main-content">
      <q-card class="tickets-card">
        <q-card-section>
          <div class="text-h6 q-mb-md">
            🎫 Event Tickets
            <q-chip
              v-if="event.title"
              :label="event.title"
              color="primary"
              text-color="white"
              class="q-ml-sm"
            />
          </div>

          <!-- Debug Info - Show in development -->
          <!-- <div v-if="showDebug" class="debug-section q-mb-md">
            <div class="text-subtitle2">Debug Information:</div>
            <div class="debug-grid">
              <div>Event ID: {{ event?.id }}</div>
              <div>Tickets: {{ event?.tickets?.length || 0 }}</div>
              <div>Groups: {{ event?.ticket_groups?.length || 0 }}</div>
              <div>Has Early Bird: {{ hasEarlyBird }}</div>
              <div>Early Bird Valid: {{ earlyBirdValid }}</div>
              <div>VATable: {{ vatable }}</div>
            </div>
          </div> -->

          <!-- Regular Tickets Section -->
          <div
            v-if="event.tickets && event.tickets.length > 0"
            class="tickets-section q-mb-md"
          >
            <div class="section-header">
              <div class="text-subtitle1">Available Tickets</div>
              <q-chip
                :label="`${event.tickets.length} ticket${
                  event.tickets.length !== 1 ? 's' : ''
                }`"
                color="secondary"
                size="sm"
              />
            </div>

            <div class="tickets-grid">
              <div
                v-for="ticket in event.tickets"
                :key="ticket.id"
                class="ticket-card"
              >
                <q-card
                  class="ticket-item"
                  :class="{ 'sold-out': ticket.tickets_remaining === 0 }"
                >
                  <q-card-section>
                    <div class="ticket-header">
                      <div class="ticket-icon">
                        <q-icon
                          name="confirmation_number"
                          :color="
                            ticket.tickets_remaining > 0 ? 'primary' : 'grey'
                          "
                          size="md"
                        />
                      </div>
                      <div class="ticket-info">
                        <div class="ticket-name">{{ ticket.details }}</div>
                        <div class="ticket-meta">
                          <span
                            v-if="event.show_tickets_remaining"
                            class="remaining"
                          >
                            {{ ticket.tickets_remaining }} remaining
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- Pricing -->
                    <div v-if="hasPaidTickets" class="ticket-pricing q-mt-sm">
                      <div
                        v-if="hasEarlyBird && earlyBirdValid"
                        class="early-bird-price"
                      >
                        <span class="price-label">Early Bird:</span>
                        <span class="price"
                          >£{{ formatPrice(ticket.cost_a) }}</span
                        >
                      </div>
                      <div
                        v-if="hasPaidTickets"
                        class="regular-price"
                        :class="{
                          'crossed-out': earlyBirdValid && hasEarlyBird,
                        }"
                      >
                        <span class="price-label">{{
                          earlyBirdValid && hasEarlyBird ? "Regular:" : "Price:"
                        }}</span>
                        <span class="price"
                          >£{{ formatPrice(ticket.cost_b) }}</span
                        >
                      </div>
                      <div v-if="vatable" class="vat-info">
                        <small
                          >{{ event.vat_exclusive ? "exc" : "inc" }} VAT</small
                        >
                      </div>
                    </div>

                    <!-- Ticket Selection -->
                    <div class="ticket-selection q-mt-md">
                      <div
                        v-if="!preview && ticket.tickets_remaining > 0"
                        class="selection-controls"
                      >
                        <label class="selection-label">Quantity:</label>
                        <q-select
                          v-model="ticketQuantities[ticket.id]"
                          :options="getTicketOptions(ticket)"
                          @update:model-value="updateTicketSelection(ticket)"
                          dense
                          outlined
                          style="min-width: 100px"
                          emit-value
                          map-options
                        />
                      </div>
                      <div v-else-if="!preview" class="sold-out-badge">
                        <q-badge color="negative" label="Sold Out" />
                      </div>
                      <div v-if="preview" class="preview-mode">
                        <q-btn
                          :disable="ticket.tickets_remaining === 0"
                          color="primary"
                          :label="
                            ticket.tickets_remaining === 0
                              ? 'Sold Out'
                              : 'Select Tickets'
                          "
                          size="sm"
                          class="preview-btn"
                        />
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>

          <!-- Ticket Groups Section -->
          <div
            v-if="event.ticket_groups && event.ticket_groups.length > 0"
            class="ticket-groups-section"
          >
            <div
              v-for="ticketGroup in event.ticket_groups"
              :key="ticketGroup.id"
              class="ticket-group q-mb-md"
            >
              <div class="group-header q-mb-sm">
                <div class="text-h6">{{ ticketGroup.description }}</div>
                <q-chip
                  v-if="ticketGroup.packages"
                  :label="`${ticketGroup.packages.length} ticket${
                    ticketGroup.packages.length !== 1 ? 's' : ''
                  }`"
                  color="accent"
                  size="sm"
                />
              </div>

              <div class="tickets-grid">
                <div
                  v-for="(ticket, index) in ticketGroup.packages"
                  :key="index"
                  class="ticket-card"
                >
                  <q-card
                    class="ticket-item"
                    :class="{ 'sold-out': ticket.tickets_remaining === 0 }"
                  >
                    <q-card-section>
                      <div class="ticket-header">
                        <div class="ticket-icon">
                          <q-icon
                            name="confirmation_number"
                            :color="
                              ticket.tickets_remaining > 0 ? 'accent' : 'grey'
                            "
                            size="md"
                          />
                        </div>
                        <div class="ticket-info">
                          <div class="ticket-name">{{ ticket.details }}</div>
                          <div class="ticket-meta">
                            <span
                              v-if="event.show_tickets_remaining"
                              class="remaining"
                            >
                              {{ ticket.tickets_remaining }} remaining
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Pricing -->
                      <div v-if="hasPaidTickets" class="ticket-pricing q-mt-sm">
                        <div
                          v-if="hasEarlyBird && earlyBirdValid"
                          class="early-bird-price"
                        >
                          <span class="price-label">Early Bird:</span>
                          <span class="price"
                            >£{{ formatPrice(ticket.cost_a) }}</span
                          >
                        </div>
                        <div
                          v-if="hasPaidTickets"
                          class="regular-price"
                          :class="{
                            'crossed-out': earlyBirdValid && hasEarlyBird,
                          }"
                        >
                          <span class="price-label">{{
                            earlyBirdValid && hasEarlyBird
                              ? "Regular:"
                              : "Price:"
                          }}</span>
                          <span class="price"
                            >£{{ formatPrice(ticket.cost_b) }}</span
                          >
                        </div>
                        <div v-if="vatable" class="vat-info">
                          <small
                            >{{
                              event.vat_exclusive ? "exc" : "inc"
                            }}
                            VAT</small
                          >
                        </div>
                      </div>

                      <!-- Ticket Selection -->
                      <div class="ticket-selection q-mt-md">
                        <div
                          v-if="!preview && ticket.tickets_remaining > 0"
                          class="selection-controls"
                        >
                          <label class="selection-label">Quantity:</label>
                          <q-select
                            v-model="ticketQuantities[ticket.id]"
                            :options="getTicketOptions(ticket)"
                            @update:model-value="updateTicketSelection(ticket)"
                            dense
                            outlined
                            style="min-width: 100px"
                            emit-value
                            map-options
                          />
                        </div>
                        <div v-else-if="!preview" class="sold-out-badge">
                          <q-badge color="negative" label="Sold Out" />
                        </div>
                        <div v-if="preview" class="preview-mode">
                          <q-btn
                            :disable="ticket.tickets_remaining === 0"
                            color="accent"
                            :label="
                              ticket.tickets_remaining === 0
                                ? 'Sold Out'
                                : 'Select Tickets'
                            "
                            size="sm"
                            class="preview-btn"
                          />
                        </div>
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>
          </div>

          <!-- No Tickets Available -->
          <div
            v-if="
              (!event.tickets || event.tickets.length === 0) &&
              (!event.ticket_groups || event.ticket_groups.length === 0)
            "
            class="no-tickets-section"
          >
            <q-card class="bg-warning text-dark">
              <q-card-section class="text-center">
                <q-icon name="event_busy" size="3rem" class="q-mb-md" />
                <div class="text-h6">No Tickets Available</div>
                <div class="text-body2">
                  This event currently has no tickets available for booking.
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Selection Summary -->
          <div v-if="hasSelections" class="selection-summary q-mt-lg">
            <q-card class="bg-primary text-white">
              <q-card-section>
                <div class="text-h6">Selected Tickets</div>
                <div
                  v-for="(quantity, ticketId) in selectedTickets"
                  :key="ticketId"
                  class="q-mt-sm"
                >
                  <div class="text-body2">
                    {{ getTicketName(ticketId) }}: {{ quantity }} ticket{{
                      quantity !== 1 ? "s" : ""
                    }}
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- No Event State -->
    <div v-else-if="!hasError && !isLoading" class="no-event-container q-pa-md">
      <q-card class="bg-warning text-dark">
        <q-card-section>
          <div class="text-h6">📋 No Event Data</div>
          <div class="text-body2">
            Event information is not available. Please refresh the page.
          </div>
          <q-btn flat label="Refresh" @click="refreshPage" class="q-mt-sm" />
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, reactive } from "vue";
import { useBookingStore } from "@/shared/stores/bookingStore.js";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";
import { useTicketsMixin } from "./tickets-mixin";
import mitt from "mitt";

// Stores
const bookingStore = useBookingStore();
const eventStore = useEventStore();
const $q = useQuasar();

// Event bus
const eventBus = mitt();

// Props
const props = defineProps({
  eventBooking: {
    type: Object,
    default: () => ({}),
  },
  preview: {
    type: Boolean,
    default: false,
  },
});

// State
const hasError = ref(false);
const errorMessage = ref("");
const isDevelopment = process.env.NODE_ENV === "development";
const showDebug = ref(isDevelopment);
const ticketQuantities = reactive({});
const readonly = ref(false);

// Computed
const isLoading = computed(() => eventStore.isLoading);

// Event data should come from eventStore (has ticket data)
const event = computed(() => {
  const storeEvent = eventStore.getEvent;
  const bookingEvent = bookingStore.getEvent;
  return storeEvent || bookingEvent;
});

// Initialize mixin with error handling
let mixinResult = null;
try {
  mixinResult = useTicketsMixin({ event });
} catch (error) {
  console.error("Error initializing tickets mixin:", error);
  hasError.value = true;
  errorMessage.value = "Failed to initialize ticket display components";
}

const {
  hasEarlyBird = computed(() => false),
  earlyBirdValid = computed(() => false),
  vatable = computed(() => false),
} = mixinResult || {};

// Additional computed properties
const hasPaidTickets = computed(() => {
  if (!event.value || !event.value.tickets) return false;
  return event.value.tickets.some((ticket) => {
    return (
      (ticket.cost_a && +ticket.cost_a > 0) ||
      (ticket.cost_b && +ticket.cost_b > 0)
    );
  });
});

const selectedTickets = computed(() => {
  const selections = {};
  Object.keys(ticketQuantities).forEach((ticketId) => {
    if (ticketQuantities[ticketId] > 0) {
      selections[ticketId] = ticketQuantities[ticketId];
    }
  });
  return selections;
});

const hasSelections = computed(
  () => Object.keys(selectedTickets.value).length > 0
);

// Methods
const formatPrice = (price) => {
  if (typeof price !== "number") {
    price = parseFloat(price) || 0;
  }
  return price.toFixed(2);
};

const getTicketOptions = (ticket) => {
  const maxTickets = Math.min(
    ticket.tickets_remaining || 0,
    ticket.max_allowed || 10
  );
  const options = [];
  for (let i = 0; i <= maxTickets; i++) {
    options.push({
      value: i,
      label: i === 0 ? "None" : `${i} ticket${i !== 1 ? "s" : ""}`,
    });
  }
  return options;
};

const updateTicketSelection = (ticket) => {
  const quantity = ticketQuantities[ticket.id] || 0;

  // Update ticket object
  ticket.quantity_tickets = quantity;

  // Emit to parent for booking store update
  eventBus.emit("setTicket", ticket);

  // Update booking store
  setTicket(ticket);

  console.log("🎫 Ticket selection updated:", {
    ticketId: ticket.id,
    details: ticket.details,
    quantity: quantity,
  });
};

const setTicket = (ticket) => {
  try {
    // Initialize selectedTickets as empty array if null
    let currentSelections = bookingStore.getSelectedTickets;
    if (!Array.isArray(currentSelections)) {
      currentSelections = [];
    } else {
      // Create a copy to avoid mutating the store directly
      currentSelections = [...currentSelections];
    }

    const existingIndex = currentSelections.findIndex(
      (t) => t.id === ticket.id
    );

    if (ticket.quantity_tickets > 0) {
      if (existingIndex >= 0) {
        currentSelections[existingIndex] = { ...ticket };
      } else {
        currentSelections.push({ ...ticket });
      }
    } else {
      if (existingIndex >= 0) {
        currentSelections.splice(existingIndex, 1);
      }
    }

    console.log("🎫 Setting selected tickets in store:", currentSelections);
    bookingStore.setSelectedTickets(currentSelections);

    // Verify the store was updated
    console.log("🎫 Store after update:", {
      selectedTickets: bookingStore.getSelectedTickets,
      hasValidSelection: bookingStore.hasValidTicketSelection,
    });
  } catch (error) {
    console.error("🎫 Error setting ticket selection:", error);
  }
};

const getTicketName = (ticketId) => {
  // Find ticket name by ID
  if (event.value?.tickets) {
    const ticket = event.value.tickets.find((t) => t.id == ticketId);
    if (ticket) return ticket.details;
  }

  if (event.value?.ticket_groups) {
    for (const group of event.value.ticket_groups) {
      if (group.packages) {
        const ticket = group.packages.find((t) => t.id == ticketId);
        if (ticket) return ticket.details;
      }
    }
  }

  return "Unknown Ticket";
};

const retryLoad = () => {
  hasError.value = false;
  errorMessage.value = "";
  nextTick(() => {
    if (!event.value) {
      hasError.value = true;
      errorMessage.value = "Event data still not available after retry";
    }
  });
};

const refreshPage = () => {
  window.location.reload();
};

const completeSelections = () => {
  try {
    const currentSelections = bookingStore.getSelectedTickets || [];

    if (currentSelections.length === 0) {
      $q.notify({
        message: "Please select at least one ticket",
        color: "negative",
        icon: "error",
      });
      return false;
    }

    console.log("🎫 Completing selections with:", currentSelections);
    return true;
  } catch (error) {
    console.error("🎫 Error completing selections:", error);
    $q.notify({
      message: "Error processing ticket selection",
      color: "negative",
      icon: "error",
    });
    return false;
  }
};

// Lifecycle
onMounted(async () => {
  console.log("🎫 New tickets component mounted");

  if (hasError.value) {
    console.log("🎫 Component already in error state, skipping mount");
    return;
  }

  try {
    await nextTick();

    console.log("🎫 Event from computed:", event.value);
    console.log("🎫 Event tickets:", event.value?.tickets);
    console.log("🎫 Event ticket_groups:", event.value?.ticket_groups);

    // Initialize ticket quantities and sync to booking store
    const initialSelections = [];

    if (event.value?.tickets) {
      event.value.tickets.forEach((ticket) => {
        const quantity = ticket.quantity_tickets || 0;
        ticketQuantities[ticket.id] = quantity;

        // If there's an initial quantity, add to selections
        if (quantity > 0) {
          initialSelections.push({ ...ticket, quantity_tickets: quantity });
        }
      });
    }

    if (event.value?.ticket_groups) {
      event.value.ticket_groups.forEach((group) => {
        if (group.packages) {
          group.packages.forEach((ticket) => {
            const quantity = ticket.quantity_tickets || 0;
            ticketQuantities[ticket.id] = quantity;

            // If there's an initial quantity, add to selections
            if (quantity > 0) {
              initialSelections.push({ ...ticket, quantity_tickets: quantity });
            }
          });
        }
      });
    }

    // Sync initial selections to booking store
    console.log("🎫 Initial ticket selections found:", initialSelections);
    if (initialSelections.length > 0) {
      bookingStore.setSelectedTickets(initialSelections);
      console.log("🎫 Synced initial selections to booking store");
    } else {
      // Ensure store starts with empty array, not null
      bookingStore.setSelectedTickets([]);
      console.log("🎫 Initialized booking store with empty selections");
    }
  } catch (error) {
    console.error("🎫 Error in new tickets component onMounted:", error);
    hasError.value = true;
    errorMessage.value = `Component initialization failed: ${error.message}`;
  }
});

// Expose methods
defineExpose({
  completeSelections,
  eventBus,
  retryLoad,
});
</script>

<style lang="scss" scoped>
.tickets-component-wrapper {
  width: 100%;
  max-width: 100%;
  overflow: visible;
  position: relative;
  box-sizing: border-box;
}

.tickets-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: white;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.debug-section {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ddd;
  font-family: monospace;
  font-size: 0.8rem;
}

.debug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.tickets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.ticket-item {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &.sold-out {
    opacity: 0.6;
    background: #f5f5f5;
  }
}

.ticket-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ticket-icon {
  flex-shrink: 0;
  padding: 0.5rem;
  background: rgba(25, 118, 210, 0.1);
  border-radius: 50%;
}

.ticket-info {
  flex: 1;
}

.ticket-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.ticket-meta {
  font-size: 0.9rem;
  color: #666;
}

.remaining {
  color: #27ae60;
  font-weight: 500;
}

.ticket-pricing {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 3px solid #1976d2;
}

.early-bird-price {
  color: #e67e22;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.regular-price {
  color: #2c3e50;
  font-weight: 500;

  &.crossed-out {
    text-decoration: line-through;
    color: #95a5a6;
  }
}

.price-label {
  margin-right: 0.5rem;
}

.price {
  font-size: 1.1rem;
  font-weight: 700;
}

.vat-info {
  color: #7f8c8d;
  margin-top: 0.25rem;
}

.ticket-selection {
  border-top: 1px solid #ecf0f1;
  padding-top: 1rem;
}

.selection-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.selection-label {
  font-weight: 500;
  color: #2c3e50;
  white-space: nowrap;
}

.sold-out-badge {
  text-align: center;
}

.preview-mode {
  text-align: center;
}

.preview-btn {
  pointer-events: none;
  opacity: 0.7;
}

.selection-summary {
  border-top: 2px solid #ecf0f1;
  padding-top: 1rem;
}

.no-tickets-section,
.error-container,
.no-event-container {
  margin: 1rem 0;

  .q-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

.loading-container {
  .q-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }
}

// Responsive design
@media (max-width: 768px) {
  .tickets-grid {
    grid-template-columns: 1fr;
  }

  .section-header,
  .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .ticket-header {
    flex-direction: column;
    text-align: center;
  }

  .selection-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}
</style>
