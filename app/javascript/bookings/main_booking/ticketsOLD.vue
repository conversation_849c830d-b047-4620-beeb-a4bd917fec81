<template>
  <div class="tickets-component-wrapper">
    <!-- Error <PERSON>ry -->
    <div v-if="hasError" class="error-container q-pa-md">
      <q-card class="bg-negative text-white">
        <q-card-section>
          <div class="text-h6">⚠️ Component Error</div>
          <div class="text-body2">{{ errorMessage }}</div>
          <q-btn
            flat
            label="Retry"
            @click="retryLoad"
            class="q-mt-sm"
          />
        </q-card-section>
      </q-card>
    </div>

    <!-- Loading State -->
    <div v-else-if="isLoading" class="loading-container q-pa-md">
      <q-card>
        <q-card-section class="text-center">
          <q-spinner size="50px" color="primary" />
          <div class="q-mt-md">Loading event tickets...</div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Main Tickets Content -->
    <div v-else-if="event && !hasError" class="tickets-main-content">
      <q-card class="tickets-card">
        <q-card-section>
          <div class="text-h6 q-mb-md">
            🎫 Event Tickets
            <q-chip
              v-if="event.title"
              :label="event.title"
              color="primary"
              text-color="white"
              class="q-ml-sm"
            />
          </div>

          <!-- Debug Info - Show in development -->
          <div v-if="showDebug" class="debug-section q-mb-md">
            <div class="text-subtitle2">Debug Information:</div>
            <div class="debug-grid">
              <div>Event ID: {{ event?.id }}</div>
              <div>Tickets: {{ event?.tickets?.length || 0 }}</div>
              <div>Groups: {{ event?.ticket_groups?.length || 0 }}</div>
              <div>Has Early Bird: {{ hasEarlyBird }}</div>
              <div>Early Bird Valid: {{ earlyBirdValid }}</div>
              <div>VATable: {{ vatable }}</div>
            </div>
          </div>

          <!-- Regular Tickets Section -->
          <div v-if="event.tickets && event.tickets.length > 0" class="tickets-section q-mb-md">
            <div class="section-header">
              <div class="text-subtitle1">Available Tickets</div>
              <q-chip
                :label="`${event.tickets.length} ticket${event.tickets.length !== 1 ? 's' : ''}`"
                color="secondary"
                size="sm"
              />
            </div>

            <div class="tickets-grid">
              <div
                v-for="ticket in event.tickets"
                :key="ticket.id"
                class="ticket-card"
              >
                <q-card class="ticket-item" :class="{ 'sold-out': ticket.tickets_remaining === 0 }">
                  <q-card-section>
                    <div class="ticket-header">
                      <div class="ticket-icon">
                        <q-icon
                          name="confirmation_number"
                          :color="ticket.tickets_remaining > 0 ? 'primary' : 'grey'"
                          size="md"
                        />
                      </div>
                      <div class="ticket-info">
                        <div class="ticket-name">{{ ticket.details }}</div>
                        <div class="ticket-meta">
                          <span v-if="event.show_tickets_remaining" class="remaining">
                            {{ ticket.tickets_remaining }} remaining
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- Pricing -->
                    <div class="ticket-pricing q-mt-sm">
                      <div v-if="hasEarlyBird && earlyBirdValid" class="early-bird-price">
                        <span class="price-label">Early Bird:</span>
                        <span class="price">£{{ formatPrice(ticket.cost_a) }}</span>
                      </div>
                      <div v-if="hasPaidTickets" class="regular-price" :class="{ 'crossed-out': earlyBirdValid && hasEarlyBird }">
                        <span class="price-label">{{ earlyBirdValid && hasEarlyBird ? 'Regular:' : 'Price:' }}</span>
                        <span class="price">£{{ formatPrice(ticket.cost_b) }}</span>
                      </div>
                      <div v-if="vatable" class="vat-info">
                        <small>{{ event.vat_exclusive ? 'exc' : 'inc' }} VAT</small>
                      </div>
                    </div>

                    <!-- Ticket Selection -->
                    <div class="ticket-selection q-mt-md">
                      <div v-if="ticket.tickets_remaining > 0" class="selection-controls">
                        <label class="selection-label">Quantity:</label>
                        <q-select
                          v-model="ticketQuantities[ticket.id]"
                          :options="getTicketOptions(ticket)"
                          @update:model-value="updateTicketSelection(ticket)"
                          dense
                          outlined
                          style="min-width: 100px"
                          emit-value
                          map-options
                        />
                      </div>
                      <div v-else class="sold-out-badge">
                        <q-badge color="negative" label="Sold Out" />
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>

      <!-- Ticket groups -->
      <q-card
        class="hg-topline headerGroup q-mb-md"
        :style="topLineOverride"
        v-for="ticketGroup in event.ticket_groups"
        :key="ticketGroup.id"
      >
        <q-card-section class="q-pb-none">
          <div class="row">
            <div class="col-sm-12">
              <div class="text-h5">{{ ticketGroup.description }}</div>
            </div>
          </div>
        </q-card-section>
        <q-card-section>
          <table class="table-tickets full-width">
            <thead>
              <tr>
                <th></th>
                <th :style="{ color: event.phcolor }">Ticket Name</th>

                <th
                  v-if="hasEarlyBird"
                  :style="
                    earlyBirdValid
                      ? { color: event.phcolor }
                      : { color: 'gray', 'text-decoration': 'line-through' }
                  "
                >
                  Early Bird Cost ({{
                    event.vat_exclusive ? "exc" : "inc"
                  }}
                  VAT)
                  <q-btn flat round dense color="info" icon="fa fa-info-circle">
                    <q-menu anchor="top start" :offset="[0, 8]">
                      <q-card style="min-width: 200px">
                        <q-card-section>
                          <div class="text-subtitle1">Dates Valid</div>
                          <div>{{ earlyBirdInfo }}</div>
                        </q-card-section>
                      </q-card>
                    </q-menu>
                  </q-btn>
                </th>

                <th
                  v-if="hasPaidTickets"
                  :style="
                    earlyBirdValid && hasEarlyBird
                      ? { color: 'gray' }
                      : { color: event.phcolor }
                  "
                >
                  Cost ({{ event.vat_exclusive ? "exc" : "inc" }} VAT)
                  <q-btn
                    v-if="earlyBirdValid"
                    flat
                    round
                    dense
                    color="info"
                    icon="fa fa-info-circle"
                  >
                    <q-menu anchor="top start" :offset="[0, 8]">
                      <q-card style="min-width: 200px">
                        <q-card-section>
                          <div class="text-subtitle1">Dates Valid</div>
                          <div>{{ earlyBirdInfoExp }}</div>
                        </q-card-section>
                      </q-card>
                    </q-menu>
                  </q-btn>
                </th>

                <th v-if="vatable">VAT Amount</th>
                <th v-if="vatable">Total to Pay</th>
                <th
                  :style="{ color: event.phcolor }"
                  v-if="event.show_tickets_remaining"
                >Tickets Remaining</th>
                <th>Ticket Date & Time</th>
                <th :style="{ color: event.phcolor }">Number of tickets</th>
              </tr>
            </thead>
            <tbody>
              <ticket-row
                v-for="(ticket, index) in ticketGroup.packages"
                :key="index"
                :disabled="readonly"
                :event="event"
                :ticket="ticket"
              />
            </tbody>
          </table>
        </q-card-section>
          </q-card>
        </div>
      </div>
    </div>

    <!-- No Event State -->
    <div v-else-if="!hasError && !isLoading" class="no-event-container q-pa-md">
      <q-card class="bg-warning text-dark">
        <q-card-section>
          <div class="text-h6">📋 No Event Data</div>
          <div class="text-body2">Event information is not available. Please refresh the page.</div>
          <q-btn
            flat
            label="Refresh"
            @click="refreshPage"
            class="q-mt-sm"
          />
        </q-card-section>
      </q-card>
    </div>

    <!-- Fallback - should never be reached -->
    <div v-else class="fallback-container q-pa-md">
      <q-card class="bg-info text-white">
        <q-card-section>
          <div class="text-h6">🔄 Component State</div>
          <div class="text-body2">
            Loading: {{ isLoading }}, Error: {{ hasError }}, Event: {{ !!event }}
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useBookingStore } from "@/shared/stores/bookingStore.js";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";
import dayjs from "dayjs";
import ticketRow from "./ticket-row.vue";
import { useTicketsMixin } from "./tickets-mixin";
import mitt from "mitt";

// Initialize stores
const bookingStore = useBookingStore();
const eventStore = useEventStore();

// Event data should come from eventStore (has ticket data)
// But selections go to bookingStore (manages booking state)
const event = computed(() => {
  const storeEvent = eventStore.getEvent;
  const bookingEvent = bookingStore.getEvent;

  // Primary source: eventStore (has complete ticket data)
  // Fallback: bookingStore (for compatibility)
  const selectedEvent = storeEvent || bookingEvent;

  console.log('Tickets component - Event selection:', {
    eventStoreEvent: !!storeEvent,
    bookingStoreEvent: !!bookingEvent,
    selectedEvent: !!selectedEvent,
    selectedEventId: selectedEvent?.id,
    ticketsCount: selectedEvent?.tickets?.length || 0,
    ticketGroupsCount: selectedEvent?.ticket_groups?.length || 0
  });

  return selectedEvent;
});

// Create event bus instance - export it so other components can use it
const eventBus = mitt();

const $q = useQuasar();

const props = defineProps({
  eventBooking: {
    type: Object,
    default: () => ({}),
  }
});

// Error handling state
const hasError = ref(false);
const errorMessage = ref('');
const isLoading = computed(() => eventStore.isLoading || bookingStore.isLoading);
const showDebug = ref(process.env.NODE_ENV === 'development');

// Initialize mixin with error handling
let mixinResult = null;
try {
  mixinResult = useTicketsMixin({ event });
} catch (error) {
  console.error('Error initializing tickets mixin:', error);
  hasError.value = true;
  errorMessage.value = 'Failed to initialize ticket display components';
}

const {
  hasEarlyBird = computed(() => false),
  earlyBirdValid = computed(() => false),
  earlyBirdInfo = computed(() => ''),
  earlyBirdInfoExp = computed(() => ''),
  vatable = computed(() => false),
  topLineOverride = computed(() => ({})),
} = mixinResult || {};

// State
const previewOnly = ref(false);
const editMode = ref(true);
const selectedTickets = ref([]);
const vatRate = ref(window.vatRate || 20);
const readonly = ref(false); // Set to false for booking flow
const editable = ref(window.location.search.includes("mode=edit"));

// Computed properties
const hasPaidTickets = computed(() => {
  if (!event.value || !event.value.tickets) return false;
  const result = event.value.tickets.some((ticket) => {
    const hasCost = (ticket.cost_a && +ticket.cost_a > 0) || (ticket.cost_b && +ticket.cost_b > 0);
    return hasCost;
  });
  return result;
});

// Computed property for ticket groups
const ticketGroup = computed(() => {
  if (!event.value) return { packages: [] };
  return {
    packages: event.value.tickets || [],
  };
});

onMounted(async () => {
  console.log('🎫 Tickets component mounting');

  // Early exit if already in error state
  if (hasError.value) {
    console.log('🎫 Component already in error state, skipping mount');
    return;
  }

  try {
    // Wait for next tick to ensure all reactive dependencies are set up
    await nextTick();

    console.log('🎫 Event from computed:', event.value);
    console.log('🎫 Event tickets:', event.value?.tickets);
    console.log('🎫 Event ticket_groups:', event.value?.ticket_groups);
    console.log('🎫 Number of tickets:', event.value?.tickets?.length || 0);
    console.log('🎫 Booking store event:', bookingStore.getEvent);
    console.log('🎫 Event store event:', eventStore.getEvent);
    console.log('🎫 Mixin result:', mixinResult);

    // Validate event data
    if (!event.value) {
      console.warn('🎫 No event data available in tickets component');
      hasError.value = true;
      errorMessage.value = 'No event data available';
      return;
    }

    if (readonly.value) {
      showPrevBookingData();
    }

    // Listen for setTicket events
    eventBus.on("setTicket", (ticket) => {
      setTicket(ticket);
    });

    // Check available tickets with error handling
    if (event.value) {
      let ticketsRemainingTotal = 0;

      try {
        if (event.value.tickets && Array.isArray(event.value.tickets)) {
          event.value.tickets.forEach((ticket) => {
            if (ticket && typeof ticket.tickets_remaining === 'number') {
              ticketsRemainingTotal += ticket.tickets_remaining;
            }
          });
        }

        if (event.value.ticket_groups && Array.isArray(event.value.ticket_groups)) {
          event.value.ticket_groups.forEach((tg) => {
            if (tg && tg.packages && Array.isArray(tg.packages)) {
              tg.packages.forEach((ticket) => {
                if (ticket && typeof ticket.tickets_remaining === 'number') {
                  ticketsRemainingTotal += ticket.tickets_remaining;
                }
              });
            }
          });
        }

        if (ticketsRemainingTotal === 0) {
          $q.notify({
            message: "There are no tickets remaining for this event!",
            color: "warning",
            icon: "warning",
          });
        }
      } catch (ticketError) {
        console.error('🎫 Error processing ticket data:', ticketError);
        hasError.value = true;
        errorMessage.value = 'Error processing ticket information';
      }
    }
  } catch (error) {
    console.error('🎫 Error in tickets component onMounted:', error);
    hasError.value = true;
    errorMessage.value = `Component initialization failed: ${error.message}`;
  }
});

// Add watcher for event changes
watch(
  event,
  (newEvent) => {
    console.log('Tickets component: Event changed:', newEvent);
    console.log('Tickets component: Event tickets:', newEvent?.tickets);
    console.log('Tickets component: Event ticket_groups:', newEvent?.ticket_groups);
  },
  { immediate: true, deep: true }
);

// Clean up event listeners when component is unmounted
onUnmounted(() => {
  eventBus.off("setTicket");
});

// Methods
function showPrevBookingData() {
  // Load previous booking data if in readonly mode
  console.log('🎫 Loading previous booking data...');
}

function setTicket(ticket) {
  // Handle ticket selection/update - save to booking store
  console.log('🎫 Setting ticket selection:', ticket);

  try {
    // Update the selected tickets in booking store
    const currentSelections = bookingStore.getSelectedTickets || [];

    // Find existing selection for this ticket
    const existingIndex = currentSelections.findIndex(t => t.id === ticket.id);

    if (ticket.quantity_tickets > 0) {
      // Add or update selection
      if (existingIndex >= 0) {
        currentSelections[existingIndex] = { ...ticket };
      } else {
        currentSelections.push({ ...ticket });
      }
    } else {
      // Remove selection if quantity is 0
      if (existingIndex >= 0) {
        currentSelections.splice(existingIndex, 1);
      }
    }

    // Save updated selections to booking store
    bookingStore.setSelectedTickets(currentSelections);

    console.log('🎫 Updated ticket selections:', currentSelections);
  } catch (error) {
    console.error('🎫 Error setting ticket selection:', error);
    $q.notify({
      message: "Error updating ticket selection",
      color: "negative",
      icon: "error",
    });
  }
}

function completeSelections() {
  try {
    const currentSelections = bookingStore.getSelectedTickets || [];

    if (currentSelections.length === 0) {
      $q.notify({
        message: "Please select at least one ticket",
        color: "negative",
        icon: "error",
      });
      return false;
    }

    // Selections are already saved in bookingStore via setTicket()
    console.log('🎫 Completing selections with:', currentSelections);
    return true;
  } catch (error) {
    console.error('🎫 Error completing selections:', error);
    $q.notify({
      message: "Error processing ticket selection",
      color: "negative",
      icon: "error",
    });
    return false;
  }
}

function retryLoad() {
  hasError.value = false;
  errorMessage.value = '';
  // Force re-evaluation of computed properties
  nextTick(() => {
    if (!event.value) {
      hasError.value = true;
      errorMessage.value = 'Event data still not available after retry';
    }
  });
}

function refreshPage() {
  window.location.reload();
}

// Expose the method and eventBus to parent components
defineExpose({
  completeSelections,
  eventBus,
  retryLoad,
});
</script>

<style lang="scss">
// Component wrapper to prevent layout issues
.tickets-component-wrapper {
  width: 100%;
  max-width: 100%;
  overflow: visible;
  position: relative;

  // Ensure it doesn't break out of container
  box-sizing: border-box;
}

.tickets-main-content {
  width: 100%;
  max-width: 100%;
}

.hg-topline {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }
}

.headerGroup {
  margin-bottom: 1.5rem;
  
  .text-h5 {
    font-weight: 600;
    margin: 0;
  }
}

.table-tickets {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  border-radius: 8px;
  overflow: hidden;
  
  th, td {
    padding: 1rem 0.75rem;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
  }
  
  th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    font-size: 0.9rem;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  tbody tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: rgba(0, 123, 255, 0.04);
    }
    
    &:last-child td {
      border-bottom: none;
    }
  }
  
  td {
    font-size: 0.95rem;
    
    &.tickets {
      font-weight: 500;
      color: #2c3e50;
    }
    
    &.hide-cell {
      width: 60px;
      text-align: center;
    }
    
    strong {
      color: #495057;
    }
  }
  
  // Column widths for better alignment
  th:first-child, td:first-child { 
    width: 60px; 
    text-align: center;
  }
  
  th:nth-child(2), td:nth-child(2) { 
    min-width: 200px; 
    max-width: 300px;
  }
  
  th:nth-child(3), td:nth-child(3) { 
    width: 140px; 
    text-align: right;
  }
  
  th:nth-child(4), td:nth-child(4) { 
    width: 140px; 
    text-align: right;
  }
  
  th:nth-child(5), td:nth-child(5) { 
    width: 120px; 
    text-align: right;
  }
  
  th:nth-child(6), td:nth-child(6) { 
    width: 140px; 
    text-align: right;
  }
  
  th:nth-child(7), td:nth-child(7) { 
    width: 120px; 
    text-align: center;
  }
  
  th:nth-child(8), td:nth-child(8) { 
    width: 160px; 
  }
  
  th:last-child, td:last-child { 
    width: 160px; 
    text-align: center;
  }
}

.ticket-select {
  min-width: 100px;
  
  :deep(.q-field__control) {
    border-radius: 6px;
  }
}

.ticketround {
  border-radius: 50%;
  background: #ff9500;
  color: white;
  padding: 8px;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
}

// Responsive design
@media (max-width: 1200px) {
  .table-tickets {
    font-size: 0.9rem;
    
    th, td {
      padding: 0.75rem 0.5rem;
    }
    
    th:nth-child(3), td:nth-child(3),
    th:nth-child(4), td:nth-child(4),
    th:nth-child(5), td:nth-child(5),
    th:nth-child(6), td:nth-child(6) {
      width: 120px;
    }
  }
}

@media (max-width: 1023px) {
  .table-tickets {
    display: block;
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    
    table {
      min-width: 1000px;
    }
  }
}

@media (max-width: 767px) {
  .table-tickets {
    th, td {
      padding: 0.5rem 0.25rem;
      font-size: 0.85rem;
    }
    
    .event-title {
      font-size: 0.8rem;
    }
  }
  
  .hg-topline {
    margin-bottom: 1rem;
  }
}

// Loading and error states
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.error-container,
.no-event-container {
  margin: 1rem 0;

  .q-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

.debug-info {
  border-radius: 8px;
  border: 1px solid #ddd;
  font-family: monospace;
  font-size: 0.8rem;

  .text-caption {
    margin: 2px 0;
  }
}
</style>



