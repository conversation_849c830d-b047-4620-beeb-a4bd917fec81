<template>
  <tr v-if="ticket.package_options && ticket.package_options.length > 0">
    <td colspan="6">
      <div class="q-mt-md">
        <q-card 
          v-for="(option, index) in menuOptions" 
          :key="option.id"
          class="q-mb-sm"
        >
            <q-card-section class="bg-grey-2" :style="underlineOverride">
              {{option.title}}
            </q-card-section>
            <q-card-section>
              <q-option-group
                v-model="optionsSelected[index]"
                :options="option.package_sub_options.map(subOption => ({
                  label: subOption.title,
                  value: subOption.id
                }))"
                type="radio"
                @update:model-value="updateTicket"
              />
            </q-card-section>
        </q-card>
      </div>
    </td>
  </tr>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';

const props = defineProps({
  ticket: {
    type: Object,
    required: true
  },
  event: {
    type: Object,
    required: true
  }
});

// State
const free_event = ref(false);
const optionsSelected = ref([]);
const readonly = ref(false);
const menuOptions = ref([]);

// Computed
const underlineOverride = computed(() => {
  return {
    borderBottom: "4px solid " + props.event.phcolour
  };
});

// Initialize component
onMounted(() => {
  // Set options selected to match what comes from the package options
  if (props.ticket.sub_options_selected) {
    optionsSelected.value = [...props.ticket.sub_options_selected];
  }

  if (props.ticket.package_options && props.ticket.package_options.length > 0) {
    menuOptions.value = [...props.ticket.package_options];
    menuOptions.value.sort((a, b) => (a.id > b.id) ? 1 : ((b.id > a.id) ? -1 : 0));
  }
});

// Methods
function updateTicket() {
  props.ticket.optionsSelected = optionsSelected.value;
}

// Watch for changes in ticket's sub options
watch(() => props.ticket.sub_options_selected, (newValue) => {
  if (newValue) {
    optionsSelected.value = [...newValue];
  }
}, { deep: true });
</script>