<template>
  <div class="info-simple-wrapper">
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">🏠 Event Information (Simple Version)</div>
        
        <!-- Debug Info -->
        <div class="debug-section q-mt-md">
          <div class="text-subtitle2">Debug Information:</div>
          <ul class="text-caption">
            <li>Component mounted: ✅</li>
            <li>Props received: {{ Object.keys(props).length }} props</li>
            <li>Event prop: {{ !!props.event }}</li>
            <li>Event ID: {{ props.event?.id || 'N/A' }}</li>
            <li>Event title: {{ props.event?.title || 'N/A' }}</li>
            <li>Event store loading: {{ eventStore.isLoading }}</li>
          </ul>
        </div>

        <!-- Event Info -->
        <div v-if="props.event" class="event-info q-mt-md">
          <div class="text-h5 q-mb-md">{{ props.event.title }}</div>
          
          <div class="event-details">
            <div v-if="props.event.datetimefrom" class="detail-item">
              <q-icon name="event" class="q-mr-sm" />
              <span><strong>Date:</strong> {{ formatDate(props.event.datetimefrom) }}</span>
            </div>
            
            <div v-if="props.event.location" class="detail-item">
              <q-icon name="place" class="q-mr-sm" />
              <span><strong>Location:</strong> {{ props.event.location }}</span>
            </div>
            
            <div v-if="props.event.organiser_email" class="detail-item">
              <q-icon name="email" class="q-mr-sm" />
              <span><strong>Contact:</strong> {{ props.event.organiser_email }}</span>
            </div>
          </div>

          <!-- Simple Social Share -->
          <div class="social-section q-mt-lg">
            <div class="text-subtitle2 q-mb-sm">Share this event:</div>
            <div class="social-buttons">
              <q-btn 
                color="primary" 
                icon="share" 
                label="Facebook"
                :href="'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(window.location.href)"
                target="_blank"
                class="q-mr-sm q-mb-sm"
              />
              <q-btn 
                color="info" 
                icon="share" 
                label="Twitter"
                :href="'https://twitter.com/intent/tweet?text=' + encodeURIComponent('Check out this event: ' + props.event.title)"
                target="_blank"
                class="q-mr-sm q-mb-sm"
              />
              <q-btn 
                color="secondary" 
                icon="email" 
                label="Email"
                :href="'mailto:?subject=' + encodeURIComponent('Check out this event: ' + props.event.title)"
                class="q-mr-sm q-mb-sm"
              />
            </div>
          </div>
        </div>

        <!-- No Event -->
        <div v-else class="no-event q-mt-md">
          <div class="text-body2 text-negative">No event data available.</div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useEventStore } from '@/stores/event';

const eventStore = useEventStore();

const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

console.log('🏠 Simple Info component mounted');
console.log('🏠 Props:', props);
console.log('🏠 Event:', props.event);
</script>

<style scoped>
.info-simple-wrapper {
  width: 100%;
  max-width: 100%;
}

.debug-section {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.event-info {
  background: #f9f9f9;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.social-section {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.no-event {
  background: #fff3cd;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
}
</style>
