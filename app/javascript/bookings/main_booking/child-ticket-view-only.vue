<template>
  <tr v-if="ticket">
    <td class="hide-cell">
      <q-icon name="fa fa-ticket" class="ticketround" aria-hidden="true" />
    </td>

    <td class="hide-cell">{{ticket.details}}</td>

    <td :data-label="ticket.details" :colspan="colspan" v-if="!ticket.tickets_remaining == 0">
        Bookable on the next page
    </td>

    <td :colspan="colspan" class="textalignment" v-if="ticket.tickets_remaining == 0">
      <q-badge color="negative" label="Sold Out" />
    </td>
  </tr>
</template>

<script setup>
import { computed } from 'vue';
import { useEventStore } from '@/stores/event';
import { useTicketsMixin } from './tickets-mixin';

const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  ticket: {
    type: Object,
    required: true
  }
});

const store = useEventStore();
const { hasEarlyBird, vatable } = useTicketsMixin(props);

// Computed properties
const hasPaidTickets = computed(() => store.getChargeable);

const colspan = computed(() => {
  let colspanValue = 1;

  if (hasEarlyBird.value) {
    colspanValue += 1;
  }

  if (hasPaidTickets.value) {
    colspanValue += 1;
  }
 
  if (vatable.value) {
    colspanValue += 2;
  }

  if (props.event.show_tickets_remaining) {
    colspanValue += 1;
  }

  return colspanValue;
});
</script>

<style scoped>
.ticketround {
  border-radius: 20px;
  border-width: 10px;
  background: #ff9500;
  color: white;
  padding: 6px;
  font-size: 20px;
}
</style>