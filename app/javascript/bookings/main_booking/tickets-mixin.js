import { computed, toRefs } from 'vue';
import dayjs from 'dayjs';

// Convert the Vue 2 mixin to a Vue 3 composable function
export function useTicketsMixin(props) {
  // Safely extract event from props
  let event;
  try {
    if (props && typeof props === 'object') {
      if (props.event) {
        // Direct event prop
        event = toRefs(props).event;
      } else if (props.value && props.value.event) {
        // Nested event prop
        event = toRefs(props.value).event;
      } else {
        // Fallback - create a reactive ref
        event = computed(() => props.event || null);
      }
    } else {
      console.warn('useTicketsMixin: Invalid props provided');
      event = computed(() => null);
    }
  } catch (error) {
    console.error('useTicketsMixin: Error extracting event from props:', error);
    event = computed(() => null);
  }

  const earlyBirdValid = computed(() => {
    try {
      if (!event.value || !event.value.datetime_eb) return false;
      return dayjs(event.value.datetime_eb).isAfter(new Date());
    } catch (error) {
      console.warn('useTicketsMixin: Error checking early bird validity:', error);
      return false;
    }
  });

  const hasEarlyBird = computed(() => {
    try {
      if (!event.value) return false;
      return !!event.value.datetime_eb;
    } catch (error) {
      console.warn('useTicketsMixin: Error checking early bird existence:', error);
      return false;
    }
  });

  const vatable = computed(() => {
    try {
      if (!event.value) return false;
      return !!(event.value.show_vat && event.value.vat_exclusive);
    } catch (error) {
      console.warn('useTicketsMixin: Error checking vat status:', error);
      return false;
    }
  });

  const earlyBirdInfo = computed(() => {
    if (!event.value || !event.value.datetime_eb) return "";
    return (
      "Up To " +
      dayjs(event.value.datetime_eb).format("DD/MM/YYYY") +
      " at " +
      dayjs(event.value.datetime_eb).format("h:mm a")
    );
  });

  const earlyBirdInfoExp = computed(() => {
    if (!event.value || !event.value.datetime_eb) return "";
    return (
      "After " +
      dayjs(event.value.datetime_eb).format("DD/MM/YYYY") +
      " at " +
      dayjs(event.value.datetime_eb).format("h:mm a")
    );
  });

  const topLineOverride = computed(() => {
    if (!event.value) return {};
    return { borderTop: "4px solid " + event.value.phcolour };
  });

  const showVatInTitle = computed(() => {
    if (!event.value || !event.value.show_vat) return "";
    return "(" + (event.value.vat_exclusive ? "exc" : "inc") + " VAT)";
  });

  return {
    earlyBirdValid,
    hasEarlyBird,
    vatable,
    earlyBirdInfo,
    earlyBirdInfoExp,
    topLineOverride,
    showVatInTitle
  };
}