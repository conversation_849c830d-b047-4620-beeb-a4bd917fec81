<template>
  <div class="row">
    <div class="col-12">
      <q-card class="hg-topline q-mb-md" :style="topLineOverride">
        <q-card-section class="bg-grey-2">
          <div class="text-h6">Terms & Conditions</div>
        </q-card-section>

        <q-card-section>
          <div v-html="terms_string"></div>
          
          <div class="q-mt-md" v-if="(terms && terms.length > 0) || !preview">
            <q-checkbox 
              v-model="accepted"
              label="I agree to the terms and conditions"
              :disable="readonly || preview"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useEventStore } from '@/stores/event';
import { useQuasar } from 'quasar';
import axios from 'axios';

// Define props
const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  noredirect: {
    type: Boolean,
    default: false
  },
  preview: {
    type: Boolean,
    default: false
  }
});

// Initialize Quasar and store
const $q = useQuasar();
const store = useEventStore();

// Reactive data
const terms = ref([]);
const terms_string = ref("");
const accepted = ref(false);
const readonly = computed(() => store.getReadOnly);

// Computed properties
const topLineOverride = computed(() => {
  return { borderTop: "4px solid " + props.event.phcolour };
});

// Methods
const loadTerms = () => {
  axios.get(`/events/${props.event.id}/terms`)
    .then(response => {
      terms.value = response.data;
      terms_string.value = renderTerms();
    })
    .catch(error => {
      console.error("Error loading terms:", error);
      $q.notify({
        color: 'negative',
        message: 'Failed to load terms & conditions',
        icon: 'error'
      });
    });
};

const renderTerms = () => {
  if (!terms.value || terms.value.length === 0) {
    return "<p>No terms have been added for this event.</p>";
  }
  
  if (props.event.global_terms) {
    return `<div>${props.event.global_terms}</div>`;
  }
  
  let termsContent = "";
  terms.value.forEach(term => {
    termsContent += `<div><h4>${term.title}</h4><div>${term.description}</div></div>`;
  });
  
  return termsContent;
};

const completeTerms = () => {
  if (terms.value.length > 0 && !accepted.value) {
    $q.notify({
      color: 'negative',
      message: 'You need to accept the terms and conditions',
      icon: 'error'
    });
    return false;
  }
  
  return true;
};

// Lifecycle hooks
onMounted(() => {
  loadTerms();
});

// Watch for changes
watch(() => terms.value, () => {
  terms_string.value = renderTerms();
});

// Expose methods to parent component
defineExpose({
  completeTerms
});
</script>

<style lang="scss">
.hg-topline {
  margin-bottom: 1.5rem;
  border-top: 3px solid var(--q-primary);
}
</style>