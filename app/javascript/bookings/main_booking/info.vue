<template>
  <div class="info-wrapper">
    <!-- Debug Info -->
    <!-- <div v-if="showDebug" class="debug-info q-pa-sm bg-grey-2 q-mb-md">
      <div class="text-caption">🏠 Info Component Debug:</div>
      <div class="text-caption">Event exists: {{ !!event }}</div>
      <div class="text-caption">Event ID: {{ event?.id }}</div>
      <div class="text-caption">Event title: {{ event?.title }}</div>
      <div class="text-caption">Loading: {{ eventStore.isLoading }}</div>
    </div> -->

    <!-- Loading State -->
    <div v-if="eventStore.isLoading" class="loading-container">
      <q-card class="loading-card">
        <q-card-section class="text-center">
          <q-spinner size="50px" color="primary" />
          <div class="q-mt-md text-body1">Loading event information...</div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Event Information -->
    <div v-else-if="event" class="event-info-container">
      <!-- Hero Section -->
      <q-card class="hero-card">
        <div class="hero-background" :style="heroBackgroundStyle">
          <div class="hero-overlay">
            <div class="hero-content">
              <div class="event-header">
                <div class="title-with-logo">
                  <q-img
                    v-if="logoFileURL"
                    :src="logoFileURL"
                    alt="Event Logo"
                    class="event-title-logo"
                    fit="contain"
                  />
                  <h1 class="event-title">{{ event.title }}</h1>
                </div>
                <div v-if="event.subtitle" class="event-subtitle">
                  {{ event.subtitle }}
                </div>
              </div>

              <!-- Key Event Info & Actions - 2 Column Layout -->
              <div class="event-info-actions-grid">
                <!-- Left Column: Date & Time -->
                <div class="event-key-info">
                  <div class="info-section">
                    <div class="info-group">
                      <q-icon name="event" class="info-icon" />
                      <div class="info-content">
                        <div class="info-label">Date</div>
                        <div class="info-value">
                          <span v-if="!datesEqual">
                            {{ formatDate(event.datetimefrom) }} -
                            {{ formatDate(event.datetimeto) }}
                          </span>
                          <span v-else>
                            {{ formatDate(event.datetimefrom) }}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div class="info-group">
                      <q-icon name="access_time" class="info-icon" />
                      <div class="info-content">
                        <div class="info-label">Time</div>
                        <div class="info-value">
                          {{ formatTime(event.datetimefrom) }} -
                          {{ formatTime(event.datetimeto) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Right Column: Contact & Social Actions -->
                <div class="hero-actions">
                  <!-- Contact Button -->
                  <q-btn
                    :href="mailto"
                    no-caps
                    rounded
                    unelevated
                    class="contact-btn"
                    :style="{
                      backgroundColor: event.buttoncolour || '#1976d2',
                      color: 'white',
                    }"
                  >
                    <q-icon name="email" class="q-mr-sm" />
                    Contact Organiser
                  </q-btn>

                  <!-- Social Share -->
                  <div class="social-share">
                    <div class="social-label">Share this event:</div>
                    <div class="social-buttons">
                      <q-btn
                        round
                        unelevated
                        class="social-btn facebook"
                        :href="facebookShareUrl"
                        target="_blank"
                        aria-label="Share on Facebook"
                      >
                        <q-icon name="fab fa-facebook-f" />
                      </q-btn>

                      <q-btn
                        round
                        unelevated
                        class="social-btn twitter"
                        :href="twitterShareUrl"
                        target="_blank"
                        aria-label="Share on X (formerly Twitter)"
                      >
                        <q-icon name="fab fa-x-twitter" />
                      </q-btn>

                      <q-btn
                        round
                        unelevated
                        class="social-btn linkedin"
                        :href="linkedinShareUrl"
                        target="_blank"
                        aria-label="Share on LinkedIn"
                      >
                        <q-icon name="fab fa-linkedin-in" />
                      </q-btn>

                      <q-btn
                        round
                        unelevated
                        class="social-btn email"
                        :href="emailShareUrl"
                        aria-label="Share via Email"
                      >
                        <q-icon name="email" />
                      </q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-card>

      <!-- Event Details Section -->
      <div class="content-grid q-mt-lg">
        <!-- Event Description -->
        <q-card class="description-card">
          <q-card-section>
            <div class="text-h6 q-mb-md">
              <q-icon name="info" class="q-mr-sm" />
              About This Event
            </div>

            <!-- Show actual content from database if it exists -->
            <div
              v-if="event.details && event.details.trim()"
              class="event-description"
              v-html="event.details"
            ></div>

            <!-- Enhanced placeholder only for truly missing content -->
            <div v-else class="minimal-content-placeholder">
              <div class="placeholder-content">
                <q-icon
                  name="event_note"
                  size="3rem"
                  color="grey-4"
                  class="q-mb-md"
                />
                <div class="text-body1 text-grey-6 q-mb-md">
                  {{
                    event.details && event.details.trim()
                      ? event.details
                      : "Event details will be provided closer to the date."
                  }}
                </div>
                <div class="placeholder-features">
                  <div class="feature-item">
                    <q-icon name="schedule" color="primary" class="q-mr-sm" />
                    <span class="text-body2"
                      >Event timing and schedule information</span
                    >
                  </div>
                  <div class="feature-item">
                    <q-icon name="group" color="primary" class="q-mr-sm" />
                    <span class="text-body2"
                      >What to expect and who to meet</span
                    >
                  </div>
                  <div class="feature-item">
                    <q-icon
                      name="local_activity"
                      color="primary"
                      class="q-mr-sm"
                    />
                    <span class="text-body2"
                      >Activities and special features</span
                    >
                  </div>
                </div>
                <div class="contact-organiser-cta q-mt-lg">
                  <q-btn
                    v-if="event.organiser_email"
                    outline
                    color="primary"
                    icon="email"
                    label="Contact organiser for more details"
                    :href="mailto"
                    class="full-width"
                  />
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Enhanced Location Section -->
        <q-card class="location-card">
          <q-card-section>
            <div class="text-h6 q-mb-md">
              <q-icon name="place" class="q-mr-sm" />
              Event Location
            </div>

            <!-- Location with full address -->
            <div
              v-if="!event.remove_location && event.event_address"
              class="location-content"
            >
              <div class="location-header q-mb-md">
                <div class="location-name">
                  {{ event.location || "Event Venue" }}
                </div>
                <div class="location-address">
                  <div class="address-line">
                    <q-icon
                      name="location_on"
                      color="primary"
                      class="q-mr-sm"
                    />
                    {{ event.event_address.address1 }}
                  </div>
                  <div class="address-line">
                    <q-icon
                      name="location_city"
                      color="primary"
                      class="q-mr-sm"
                    />
                    {{ event.event_address.city }},
                    {{ event.event_address.postcode }}
                  </div>
                  <div v-if="event.event_address.county" class="address-line">
                    <q-icon name="map" color="primary" class="q-mr-sm" />
                    {{ event.event_address.county }}
                  </div>
                  <div
                    v-if="event.event_address.country_code"
                    class="address-line"
                  >
                    <q-icon name="flag" color="primary" class="q-mr-sm" />
                    {{ countryName }}
                  </div>
                </div>
              </div>

              <!-- Interactive Map -->
              <div class="map-container q-mb-md">
                <maps
                  v-if="event.event_address.postcode"
                  :postcode="event.event_address.postcode"
                  :lat="event.event_address.latitude"
                  :lng="event.event_address.longitude"
                  :name="`event-map-${event.id}`"
                />
              </div>

              <!-- Action Buttons -->
              <div class="location-actions">
                <q-btn
                  :href="googleMapsUrl"
                  target="_blank"
                  color="primary"
                  unelevated
                  no-caps
                  class="action-btn"
                >
                  <q-icon name="directions" class="q-mr-sm" />
                  Get Directions
                </q-btn>

                <q-btn
                  :href="appleMapsUrl"
                  target="_blank"
                  outline
                  color="primary"
                  no-caps
                  class="action-btn"
                >
                  <q-icon name="map" class="q-mr-sm" />
                  Open in Maps
                </q-btn>
              </div>
            </div>

            <!-- Location without address (venue name only) -->
            <div
              v-else-if="!event.remove_location && event.location"
              class="location-content"
            >
              <div class="location-placeholder">
                <q-icon
                  name="place"
                  size="3rem"
                  color="primary"
                  class="q-mb-md"
                />
                <div class="location-name q-mb-md">{{ event.location }}</div>
                <div class="text-body2 text-grey-6 q-mb-lg">
                  Full address details will be provided closer to the event
                  date.
                </div>
                <q-btn
                  v-if="event.organiser_email"
                  outline
                  color="primary"
                  icon="email"
                  label="Contact for location details"
                  :href="mailto"
                  class="full-width"
                />
              </div>
            </div>

            <!-- Venue to be confirmed -->
            <div v-else class="location-content">
              <div class="location-placeholder">
                <q-icon
                  name="location_searching"
                  size="3rem"
                  color="orange"
                  class="q-mb-md"
                />
                <div class="text-h6 q-mb-md text-orange">
                  Venue To Be Confirmed
                </div>
                <div class="text-body2 text-grey-6 q-mb-lg">
                  The event location is being finalised. You'll receive venue
                  details via email once confirmed.
                </div>
                <div class="venue-features">
                  <div class="feature-item">
                    <q-icon
                      name="notifications"
                      color="primary"
                      class="q-mr-sm"
                    />
                    <span class="text-body2"
                      >Email notification when venue is confirmed</span
                    >
                  </div>
                  <div class="feature-item">
                    <q-icon
                      name="access_time"
                      color="primary"
                      class="q-mr-sm"
                    />
                    <span class="text-body2"
                      >Typically confirmed 1-2 weeks before event</span
                    >
                  </div>
                  <div class="feature-item">
                    <q-icon
                      name="local_parking"
                      color="primary"
                      class="q-mr-sm"
                    />
                    <span class="text-body2"
                      >Parking and accessibility info will be included</span
                    >
                  </div>
                </div>
                <q-btn
                  v-if="event.organiser_email"
                  outline
                  color="primary"
                  icon="email"
                  label="Contact organiser about venue"
                  :href="mailto"
                  class="full-width q-mt-md"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- No Event State -->
    <div v-else class="no-event-container">
      <q-card class="bg-warning text-dark">
        <q-card-section class="text-center">
          <q-icon name="event_busy" size="3rem" class="q-mb-md" />
          <div class="text-h6">Event Information Unavailable</div>
          <div class="text-body2">
            Unable to load event details at this time.
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useEventStore } from "@/stores/event";
import maps from "@/common/maps.vue";
import dayjs from "dayjs";

const eventStore = useEventStore();

// Simple country name fallback
const getCountryName = (countryCode) => {
  const countries = {
    GB: "United Kingdom",
    US: "United States",
    CA: "Canada",
    AU: "Australia",
    DE: "Germany",
    FR: "France",
    ES: "Spain",
    IT: "Italy",
    NL: "Netherlands",
    IE: "Ireland",
  };
  return countries[countryCode] || countryCode;
};

const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const event = computed(() => props.event);
const imageBucket = ref(window.appImageBucket || "");
const showDebug = ref(process.env.NODE_ENV === "development");

// Add console logging for debugging
console.log("🏠 Info component initialized");
console.log("🏠 Event prop:", props.event);
console.log("🏠 Event store loading:", eventStore.isLoading);

// Default placeholder image for events without a background image
const defaultEventImage =
  "https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80";

// Image URLs
const logoFileURL = computed(() => {
  if (event.value?.image1) {
    return `https://s3-eu-west-1.amazonaws.com/${imageBucket.value}/${
      event.value.id
    }/${event.value.image1}?${Math.random()}`;
  }
  return "";
});

const imageFileURL = computed(() => {
  if (event.value?.image2) {
    return `https://s3-eu-west-1.amazonaws.com/${imageBucket.value}/${
      event.value.id
    }/${event.value.image2}?${Math.random()}`;
  }
  return "";
});

// Hero background with fallback to placeholder
const heroBackgroundStyle = computed(() => {
  const backgroundImage = imageFileURL.value || defaultEventImage;
  return {
    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url(${backgroundImage})`,
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  };
});

// Date formatting
const datesEqual = computed(() => {
  if (!event.value?.datetimefrom || !event.value?.datetimeto) return true;
  return (
    dayjs(event.value.datetimefrom).format("YYYY-MM-DD") ===
    dayjs(event.value.datetimeto).format("YYYY-MM-DD")
  );
});

const formatDate = (dateString) => {
  if (!dateString) return "";
  return dayjs(dateString).format("dddd, MMMM D, YYYY");
};

const formatTime = (dateString) => {
  if (!dateString) return "";
  return dayjs(dateString).format("h:mm A");
};

// Country name
const countryName = computed(() => {
  if (!event.value?.event_address?.country_code) return "";
  return getCountryName(event.value.event_address.country_code);
});

// Contact email
const mailto = computed(() => {
  if (!event.value?.organiser_email) return "";
  return `mailto:${event.value.organiser_email}?subject=Enquiry about ${event.value.title}`;
});

// Social sharing URLs
const eventUrl = computed(() => {
  if (!event.value) return "";

  const baseUrl = window.location.origin;
  if (event.value.custom_url) {
    return `${baseUrl}/${event.value.custom_url}`;
  }

  const sanitizedTitle = event.value.title
    .toLowerCase()
    .replace(/[!?&|$%@':;.<>=+"\\\/\s]/g, "_");
  return `${baseUrl}/event/${event.value.id}/${sanitizedTitle}`;
});

const facebookShareUrl = computed(() => {
  return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
    eventUrl.value
  )}`;
});

const twitterShareUrl = computed(() => {
  const text = `I'm going to ${event.value?.title} event. Come see it here:`;
  return `https://x.com/intent/tweet?text=${encodeURIComponent(
    text
  )}&url=${encodeURIComponent(eventUrl.value)}`;
});

const linkedinShareUrl = computed(() => {
  return `https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(
    eventUrl.value
  )}`;
});

const emailShareUrl = computed(() => {
  const subject = `Check out this event: ${event.value?.title}`;
  const body = `Hey, I just signed up to an event. Come check it out at ${eventUrl.value}`;
  return `mailto:?subject=${encodeURIComponent(
    subject
  )}&body=${encodeURIComponent(body)}`;
});

// Map URLs
const fullAddress = computed(() => {
  if (!event.value?.event_address) return "";

  const addr = event.value.event_address;
  const parts = [
    addr.address1,
    addr.city,
    addr.postcode,
    addr.county,
    countryName.value,
  ].filter(Boolean);

  return parts.join(", ");
});

const googleMapsEmbedUrl = computed(() => {
  if (!fullAddress.value) return "";
  const query = encodeURIComponent(fullAddress.value);
  return `https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${query}`;
});

const googleMapsUrl = computed(() => {
  if (!fullAddress.value) return "";
  const query = encodeURIComponent(fullAddress.value);
  return `https://www.google.com/maps/search/?api=1&query=${query}`;
});

const appleMapsUrl = computed(() => {
  if (!fullAddress.value) return "";
  const query = encodeURIComponent(fullAddress.value);
  return `https://maps.apple.com/?q=${query}`;
});

// Methods
const openInGoogleMaps = () => {
  if (googleMapsUrl.value) {
    window.open(googleMapsUrl.value, "_blank");
  }
};
</script>

<style lang="scss" scoped>
.info-wrapper {
  width: 100%;
  max-width: 100%;
  padding: 0; // Remove padding to align with other components

  @media (max-width: 768px) {
    padding: 0;
  }
}

.debug-info {
  border-radius: 8px;
  border: 1px solid #ddd;
  font-family: monospace;
  font-size: 0.8rem;

  .text-caption {
    margin: 2px 0;
  }
}

.loading-container,
.no-event-container {
  margin: 1rem 0;

  .q-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }
}

.loading-card {
  padding: 2rem;
}

.event-info-container {
  width: 100%;
  margin: 0; // Remove horizontal margins to align with other components
  padding-bottom: 1rem; // Reduce bottom padding

  @media (max-width: 768px) {
    margin: 0;
    padding-bottom: 0.5rem;
  }
}

.hero-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.16);
    transform: translateY(-2px);
  }
}

.hero-background {
  min-height: 400px;
  position: relative;
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    min-height: 300px;
  }
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  align-items: center;
  padding: 3rem;

  @media (max-width: 768px) {
    padding: 2rem;
  }

  @media (max-width: 480px) {
    padding: 1.5rem;
  }
}

.hero-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
}

.event-header {
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    margin-bottom: 1.5rem;
  }
}

.title-with-logo {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 1rem;
    flex-direction: column;
    align-items: flex-start;
  }
}

.event-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  flex: 1;

  @media (max-width: 768px) {
    font-size: 2rem;
  }

  @media (max-width: 480px) {
    font-size: 1.5rem;
  }
}

.event-title-logo {
  max-height: 80px;
  max-width: 120px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.9);
  padding: 0.5rem;

  @media (max-width: 768px) {
    max-height: 60px;
    max-width: 100px;
  }

  @media (max-width: 480px) {
    max-height: 50px;
    max-width: 80px;
  }
}

.event-subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  font-weight: 300;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}

// New 2-column grid container for event info and actions
.event-info-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
  align-items: start;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
  }
}

.event-key-info {
  // Remove the previous grid layout since it's now handled by the parent
  margin-bottom: 0;
}

.info-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-group {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-icon {
  font-size: 1.5rem;
  color: #fff;
  opacity: 0.9;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.4;
}

.address-details {
  margin-top: 0.5rem;
  opacity: 0.9;

  div {
    margin-bottom: 0.25rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: flex-start;
  justify-self: end; // Align to the right side of the grid column

  @media (max-width: 768px) {
    gap: 1rem;
    justify-self: stretch; // Full width on mobile
    align-items: center; // Center align on mobile
  }
}

.contact-btn {
  font-size: 1rem;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  text-transform: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  }
}

.social-share {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.social-label {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.social-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.social-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &.facebook {
    background-color: #1877f2;
    color: white;
  }

  &.twitter {
    background-color: #000000;
    color: white;
  }

  &.linkedin {
    background-color: #0077b5;
    color: white;
  }

  &.email {
    background-color: #34495e;
    color: white;
  }
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
}

.description-card,
.map-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.map-card {
  .location-details {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 3px solid #1976d2;
  }

  .location-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }

  .location-address {
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 0.25rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.map-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;

  @media (max-width: 480px) {
    flex-direction: column;

    .q-btn {
      width: 100%;
    }
  }
}

.event-description {
  line-height: 1.6;
  color: #2c3e50;

  :deep(p) {
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  :deep(h1, h2, h3, h4, h5, h6) {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: #2c3e50;

    &:first-child {
      margin-top: 0;
    }
  }

  :deep(ul, ol) {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  :deep(li) {
    margin-bottom: 0.5rem;
  }
}

// Animation for component appearance
.info-wrapper {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .event-info-actions-grid {
    gap: 1.5rem;
  }

  .social-buttons {
    justify-content: center;
  }

  .hero-actions {
    align-items: center;
    text-align: center;
  }

  .contact-btn {
    padding: 0.625rem 1.5rem;
    font-size: 0.875rem;
  }

  .social-btn {
    width: 44px;
    height: 44px;
  }
}

// Enhanced Description Card Styling
.description-card {
  .minimal-content-placeholder {
    text-align: center;
    padding: 2rem 1rem;

    .placeholder-content {
      max-width: 400px;
      margin: 0 auto;
    }

    .placeholder-features {
      text-align: left;
      margin: 1.5rem 0;

      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: rgba(25, 118, 210, 0.05);
        border-radius: 8px;
        border-left: 3px solid #1976d2;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .contact-organiser-cta {
      border-top: 1px solid #e0e0e0;
      padding-top: 1rem;
    }
  }
}

// Enhanced Location Card Styling
.location-card {
  .location-content {
    .location-header {
      .location-name {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
      }

      .location-address {
        .address-line {
          display: flex;
          align-items: center;
          margin-bottom: 0.75rem;
          padding: 0.5rem;
          background: rgba(25, 118, 210, 0.05);
          border-radius: 6px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .map-container {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .event-map {
        height: 300px;
        width: 100%;
      }
    }

    .location-actions {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;

      .action-btn {
        flex: 1;
        min-width: 140px;
      }

      @media (max-width: 600px) {
        flex-direction: column;

        .action-btn {
          width: 100%;
        }
      }
    }

    .location-placeholder {
      text-align: center;
      padding: 2rem 1rem;

      .location-name {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2c3e50;
      }

      .venue-features {
        text-align: left;
        margin: 1.5rem 0;

        .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 1rem;
          padding: 0.75rem;
          background: rgba(255, 152, 0, 0.05);
          border-radius: 8px;
          border-left: 3px solid #ff9800;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
