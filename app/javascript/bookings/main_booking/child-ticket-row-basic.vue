<template>
    <tr v-if="ticket">
        <td class="hide-cell">
            <q-icon name="fa fa-ticket" class="ticketround" aria-hidden="true" />
        </td>

        <td data-label="Ticket details" class="tickets">{{ticket.details}}</td>

        <td data-label="Early Bird Cost" data-th="Early Bird Cost" v-if="hasEarlyBird">
            <div :style="!earlyBirdValid ? {'text-decoration': 'line-through', 'color': 'gray'} :{}">
                {{ formatCurrency(ticket.cost_a, '£') }}
            </div>
        </td>

        <td data-label="Cost" data-th="Cost" v-if="hasPaidTickets">
            <div :style="earlyBirdValid ? {'text-decoration': 'line-through', 'color': 'gray'} :{}">
                {{ formatCurrency(ticket.cost_b, '£') }}
            </div>
        </td>

        <td data-label="Vat Amount" data-th="Vat Amount" v-if="vatable">
            {{ formatCurrency(calcVatAmount(ticket), '£') }}
        </td>

        <td v-if="vatable" data-th="Total Remaining" data-label="Total Remaining">
            {{ formatCurrency(calcPricePlusVat(ticket), '£') }}
        </td>

        <td data-label="Tickets Remaining" data-th="Tickets Remaining" v-if="event.show_tickets_remaining">
            <strong>{{ticket.tickets_remaining}}</strong>
        </td>

        <td data-label="Ticket Date & Time" data-th="Ticket Date & Time" class="ticket-date-time">
          <span v-html="ticketTimeAndDate"></span>
        </td> 

        <td data-label="Number of tickets" v-if="ticket.tickets_remaining > 0">
            <q-select
                v-if="ticket.group_amount > 1"
                :disable="disabled"
                class="ticket-select"
                style="width: 100px !important"
                v-model="ticket.quantity_tickets"
                :options="minTicketNumberRange().map(n => ({value: n, label: displayTicketAmount(n, ticket.group_amount)}))"
                @update:model-value="setTicket(ticket)"
                dense
                outlined
                emit-value
                map-options
            />

            <q-select
                v-if="ticket.group_amount == 1"
                :disable="disabled"
                class="ticket-select"
                style="width: 100px !important"
                v-model="ticket.quantity_tickets"
                :options="minTicketNumberRange().map(n => ({value: n, label: n}))"
                @update:model-value="setTicket(ticket)"
                dense
                outlined
                emit-value
                map-options
            />
        </td>

        <td class="textalignment" v-if="ticket.tickets_remaining == 0">
            <q-badge color="negative" label="Sold Out" />
        </td>
    </tr>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useEventStore } from '@/stores/event';
import dayjs from 'dayjs';
import mitt from 'mitt';
import { useTicketsMixin } from './tickets-mixin';

// Event bus for component communication
const eventBus = mitt();

const props = defineProps({
    event: {
        type: Object,
        required: true
    },
    ticket: {
        type: Object,
        required: true
    },
    disabled: {
        type: Boolean,
        default: false
    }
});

const store = useEventStore();
const { hasEarlyBird, earlyBirdValid, vatable } = useTicketsMixin(props);

// State
const vatRate = ref(window.vatRate);
const ticketsSelected = ref(0);
const discountValid = ref(false);
const startTime = ref(null);
const endTime = ref(null);

// Computed properties
const hasPaidTickets = computed(() => store.getChargeable);

const ticketTimeAndDate = computed(() => {
    if (startTime.value && endTime.value) {
        return `${startTime.value} <br/> ${endTime.value}`;
    } else if (startTime.value && !endTime.value) {
        return startTime.value;
    } else {
        return '';
    }
});

// Initialize component
onMounted(() => {
    if (props.event && props.event.datetime_eb) {
        discountValid.value = dayjs(props.event.datetime_eb).isAfter(dayjs());
    }

    if (!props.ticket.quantity_tickets) {
        props.ticket.quantity_tickets = 0;
    }

    if (props.ticket.tickets_remaining === null) {
        props.ticket.tickets_remaining = props.ticket.ticket_no;
    }

    if (props.ticket.start_time) {
        const sdt = new Date(props.ticket.start_time);
        startTime.value = dayjs(sdt).format('DD/MM/YYYY - HH:mm');
    }

    if (props.ticket.end_time) {
        const edt = new Date(props.ticket.end_time);
        endTime.value = dayjs(edt).format('DD/MM/YYYY - HH:mm');
    }
});

// Methods
function displayTicketAmount(n, group_amount) {
    if (n === 0) {
        return n;
    } else {
        return n + ' x ' + group_amount;
    }
}

function getVatRate(ticket) {
    if (ticket.vat_rate) {
        vatRate.value = ticket.vat_rate.rate;
    }
    return vatRate.value;
}

function calcVatAmount(ticket) {
    const vatRateValue = getVatRate(ticket) / 100;
    if (discountValid.value) {
        return +ticket.cost_a * vatRateValue;
    } else {
        return +ticket.cost_b * vatRateValue;
    }
}

function calcPricePlusVat(ticket) {
    if (discountValid.value) {
        return +ticket.cost_a + calcVatAmount(ticket);
    } else {
        return +ticket.cost_b + calcVatAmount(ticket);
    }
}

function minTicketNumberRange() {
    const total = Math.min(
        props.ticket.tickets_remaining,
        props.ticket.max_allowed
    );

    const rangeArray = [];
    for (let i = 0; i < total + 1; i++) {
        rangeArray.push(i);
    }
    return rangeArray;
}

function setTicket(ticket) {
    ticket.from_basic = true;
    eventBus.emit('setTicket', ticket);
    ticketsSelected.value = ticket.quantity_tickets;
}

function minTicketNumberRangeChild(ticket) {
    const total = Math.min(ticket.tickets_remaining, ticket.max_allowed);

    const rangeArray = [];
    for (let i = 0; i < total + 1; i++) {
        rangeArray.push(i);
    }
    return rangeArray;
}

// Helper function to format currency (replacing Vue filter)
function formatCurrency(value, symbol = '£') {
    if (typeof value !== 'number') {
        value = parseFloat(value);
    }
    return symbol + value.toFixed(2);
}
</script>

<style scoped>
.ticketround {
    border-radius: 20px;
    border-width: 10px;
    background: #ff9500;
    color: white;
    padding: 6px;
    font-size: 20px;
}

.sub {
    background-color: white !important;
    color: black;
}

.ticket-select {
    width: 100px;
}
</style>