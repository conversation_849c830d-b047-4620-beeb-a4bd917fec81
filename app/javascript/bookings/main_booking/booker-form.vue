<template>
  <div class="booker-form-wrapper">
    <q-card class="booker-form-card">
      <q-card-section>
        <div class="form-header q-mb-lg">
          <div class="text-h6">
            👤 Your Details
            <q-chip
              label="Required for booking"
              color="primary"
              text-color="white"
              size="sm"
              class="q-ml-sm"
            />
          </div>
          <div class="text-body2 text-grey-7">
            Please provide your details to complete the booking
          </div>
          <!-- Debug button - remove in production -->
          <q-btn
            v-if="isDevelopment"
            @click="testValidation"
            color="orange"
            size="sm"
            label="Test Validation"
            class="q-mt-sm"
          />
        </div>

        <!-- Preview Notice -->
        <div v-if="preview" class="preview-notice q-mb-lg">
          <q-banner class="bg-blue-1 text-blue-8" rounded>
            <template v-slot:avatar>
              <q-icon name="visibility" color="blue" />
            </template>
            This is a preview of the booking form that attendees will see
          </q-banner>
        </div>

        <q-form
          ref="formRef"
          @submit.prevent="saveBookingDetails"
          id="bookerForm"
          no-error-focus
          class="booker-form"
        >
          <div class="form-grid">
            <div class="form-column" v-for="col in formColumns" :key="col">
              <!-- Title Field -->
              <div class="form-field" v-if="showFieldForColumn(col, 'a')">
                <q-select
                  ref="titleField"
                  v-model="registrationResponses.title"
                  :rules="preview ? [] : validationRules.title"
                  :options="titleOptions"
                  label="Title"
                  outlined
                  class="modern-input"
                  :disable="preview"
                />
              </div>

              <!-- Forename Field -->
              <div class="form-field" v-if="showFieldForColumn(col, 'b')">
                <q-input
                  ref="forenameField"
                  v-model="registrationResponses.forename"
                  label="Forename"
                  outlined
                  :rules="preview ? [] : validationRules.forename"
                  maxlength="100"
                  class="modern-input"
                  :disable="preview"
                />
              </div>

              <!-- Surname Field -->
              <div class="form-field" v-if="showFieldForColumn(col, 'c')">
                <q-input
                  ref="surnameField"
                  v-model="registrationResponses.surname"
                  label="Surname"
                  outlined
                  :rules="preview ? [] : validationRules.surname"
                  maxlength="100"
                  class="modern-input"
                  :disable="preview"
                />
              </div>

              <!-- Company Field -->
              <div
                class="form-field"
                v-if="event?.company_field && showFieldForColumn(col, 'd')"
              >
                <q-input
                  ref="companyField"
                  v-model="registrationResponses.company"
                  label="Company"
                  outlined
                  :rules="
                    preview
                      ? []
                      : event?.company_field
                      ? validationRules.company
                      : []
                  "
                  maxlength="100"
                  class="modern-input"
                  :disable="preview"
                />
              </div>

              <!-- Job Title Field -->
              <div
                class="form-field"
                v-if="
                  event?.job_description_field && showFieldForColumn(col, 'e')
                "
              >
                <q-input
                  v-model="registrationResponses.job_description"
                  label="Job Title"
                  outlined
                  maxlength="100"
                  class="modern-input"
                  :disable="preview"
                />
              </div>

              <!-- Email Field -->
              <div class="form-field" v-if="showFieldForColumn(col, 'f')">
                <q-input
                  ref="emailField"
                  v-model.trim="registrationResponses.email"
                  label="Email"
                  outlined
                  :rules="preview ? [] : validationRules.email"
                  maxlength="200"
                  type="email"
                  class="modern-input"
                  :disable="preview"
                />
              </div>

              <div
                class="form-field"
                v-if="event?.phone_field && showFieldForColumn(col, 'g')"
              >
                <q-input
                  ref="phoneField"
                  v-model="registrationResponses.phone"
                  label="Phone Number"
                  outlined
                  :rules="
                    preview
                      ? []
                      : event?.phone_field
                      ? validationRules.phone
                      : []
                  "
                  maxlength="15"
                  class="modern-input"
                  :disable="preview"
                />
              </div>

              <div
                class="q-mb-md"
                v-if="event?.address1_field && showFieldForColumn(col, 'h')"
              >
                <q-input
                  v-model="registrationResponses.address1"
                  label="Address 1"
                  outlined
                  maxlength="100"
                  :disable="preview"
                />
              </div>

              <div
                class="q-mb-md"
                v-if="event?.address2_field && showFieldForColumn(col, 'i')"
              >
                <q-input
                  v-model="registrationResponses.address2"
                  label="Address 2"
                  outlined
                  maxlength="100"
                  :disable="preview"
                />
              </div>

              <div
                class="q-mb-md"
                v-if="event?.town_field && showFieldForColumn(col, 'j')"
              >
                <q-input
                  v-model="registrationResponses.town"
                  label="Town/City"
                  outlined
                  maxlength="100"
                  :disable="preview"
                />
              </div>

              <div
                class="q-mb-md"
                v-if="event?.county_field && showFieldForColumn(col, 'k')"
              >
                <q-input
                  v-model="registrationResponses.county"
                  label="County"
                  outlined
                  maxlength="60"
                  :disable="preview"
                />
              </div>

              <div
                class="q-mb-md"
                v-if="event?.postcode_field && showFieldForColumn(col, 'l')"
              >
                <q-input
                  v-model="registrationResponses.postcode"
                  label="Postcode"
                  outlined
                  maxlength="8"
                  :disable="preview"
                />
              </div>

              <div
                class="q-mb-md"
                v-if="event?.country_field && showFieldForColumn(col, 'm')"
              >
                <q-select
                  v-model="registrationResponses.country"
                  :options="countries"
                  label="Country"
                  outlined
                  :disable="preview"
                />
              </div>

              <div
                class="q-mb-md"
                v-if="event?.po_number_field && showFieldForColumn(col, 'n')"
              >
                <q-input
                  v-model="registrationResponses.po_number"
                  label="PO Number"
                  outlined
                  :disable="preview"
                  maxlength="20"
                />
              </div>

              <template v-for="(cus_field, idx) in regFields || []" :key="idx">
                <template v-if="showFieldForColumn(col, 'o' + idx)">
                  <div
                    class="q-mb-md"
                    v-if="
                      cus_field?.question_type == 'text' ||
                      cus_field?.question_type == 'number'
                    "
                  >
                    <q-input
                      v-if="
                        !preview &&
                        registrationResponses
                          .registered_user_responses_attributes[idx]
                      "
                      v-model="
                        registrationResponses
                          .registered_user_responses_attributes[idx].text
                      "
                      :label="cus_field?.title"
                      outlined
                      :rules="
                        event?.booker_questions_mandatory
                          ? [
                              (val) =>
                                !!val ||
                                (cus_field?.title || 'Field') + ' is required',
                            ]
                          : []
                      "
                      maxlength="200"
                      :type="cus_field?.question_type"
                      :error="customFieldErrors[idx]?.error"
                      :error-message="customFieldErrors[idx]?.message"
                    />
                    <q-input
                      v-if="preview"
                      :label="cus_field?.title"
                      outlined
                      maxlength="200"
                      :type="cus_field?.question_type"
                    />
                  </div>

                  <div class="q-mb-md" v-else>
                    <q-select
                      v-if="
                        !preview &&
                        registrationResponses
                          .registered_user_responses_attributes[idx]
                      "
                      v-model="
                        registrationResponses
                          .registered_user_responses_attributes[idx]
                          .selected_option
                      "
                      :label="cus_field?.title"
                      outlined
                      :options="cus_field?.options"
                      :rules="
                        event?.booker_questions_mandatory
                          ? [
                              (val) =>
                                !!val ||
                                (cus_field?.title || 'Field') + ' is required',
                            ]
                          : []
                      "
                      :error="customFieldErrors[idx]?.error"
                      :error-message="customFieldErrors[idx]?.message"
                    />
                    <q-select
                      v-if="preview"
                      :label="cus_field?.title"
                      outlined
                      :options="cus_field?.options"
                    />
                  </div>
                </template>
              </template>
            </div>
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  reactive,
  onMounted,
  onUnmounted,
  provide,
  getCurrentInstance,
} from "vue";
import { useEventStore } from "@/stores/event";
import { useBookingStore } from "@/stores/booking";
import { useQuasar } from "quasar";
import mitt from "mitt";
import axios from "axios";
import { useCountries } from "@/bookings/common/countries";
import eventBus from "@/shared/eventBus";

// Define props
const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
  preview: {
    type: Boolean,
    default: false,
  },
  regUserId: {
    type: [Number, String],
    default: null,
  },
});

// Initialize Quasar, store, and other utilities
const $q = useQuasar();
const store = useBookingStore();
const event = computed(() => props.event);

// Ensure event is loaded when component mounts
onMounted(async () => {
  try {
    setupForm();
  } catch (error) {
    console.error("Failed to load event:", error);
  }
});

const { countries } = useCountries();

// Environment and state management
const isDevelopment = process.env.NODE_ENV === "development";
const posArray = ref([]);
const fieldsCount = ref(4);
const splitValue = ref(0);
const registrationResponses = reactive({
  title: null,
  forename: "",
  surname: "",
  company: "",
  job_description: "",
  email: "",
  phone: "",
  address1: "",
  address2: "",
  town: "",
  county: "",
  postcode: "",
  country: "",
  po_number: "",
  registered_user_responses_attributes: [],
});

const regFields = ref([]);
const readonly = computed(() => store.getReadOnly);
const viewonly = ref(false);
const submitted = ref(false);
const formColumns = ref([1, 2]);
const customFieldErrors = ref({});

// Title options
const titleOptions = [
  { label: "--Please Select A Title --", value: null },
  { label: "Mr", value: "Mr" },
  { label: "Mrs", value: "Mrs" },
  { label: "Miss", value: "Miss" },
  { label: "Ms", value: "Ms" },
  { label: "Doctor", value: "Dr" },
  { label: "Professor", value: "Prof" },
  { label: "Other", value: "Other" },
];

// Form validation using Quasar's built-in validation
const formRef = ref(null);

// Individual field refs for better validation control
const titleField = ref(null);
const forenameField = ref(null);
const surnameField = ref(null);
const emailField = ref(null);
const companyField = ref(null);
const phoneField = ref(null);

// Get current component instance for accessing $refs
const instance = getCurrentInstance();

// Validation rules for Quasar inputs
const validationRules = {
  title: [
    (val) => {
      console.log("🔍 Title validation - value:", val);
      const isValid = !!val && val !== null;
      console.log("📋 Title validation result:", isValid);
      return isValid || "Please select a title";
    },
  ],
  forename: [
    (val) => {
      console.log("🔍 Forename validation - value:", val);
      const isValid = !!val;
      console.log("📋 Forename validation result:", isValid);
      return isValid || "Forename is required";
    },
    (val) => {
      if (!val) return true; // Skip length check if empty (handled by first rule)
      const isValid = val.length >= 2;
      console.log("📋 Forename length validation result:", isValid);
      return isValid || "Forename must be at least 2 characters";
    },
  ],
  surname: [
    (val) => {
      console.log("🔍 Surname validation - value:", val);
      const isValid = !!val;
      console.log("📋 Surname validation result:", isValid);
      return isValid || "Surname is required";
    },
    (val) => {
      if (!val) return true;
      const isValid = val.length >= 2;
      console.log("📋 Surname length validation result:", isValid);
      return isValid || "Surname must be at least 2 characters";
    },
  ],
  email: [
    (val) => {
      console.log("🔍 Email validation - value:", val);
      const isValid = !!val;
      console.log("📋 Email validation result:", isValid);
      return isValid || "Email is required";
    },
    (val) => {
      if (!val) return true;
      const isValid = validateEmail(val);
      console.log("📋 Email format validation result:", isValid);
      return isValid || "Please enter a valid email address";
    },
  ],
  company: [
    (val) => {
      console.log("🔍 Company validation - value:", val);
      const isValid = !!val;
      console.log("📋 Company validation result:", isValid);
      return isValid || "Company is required";
    },
  ],
  phone: [
    (val) => {
      console.log("🔍 Phone validation - value:", val);
      const isValid = !!val;
      console.log("📋 Phone validation result:", isValid);
      return isValid || "Phone number is required";
    },
    (val) => {
      if (!val) return true;
      const isValid = val.length >= 10;
      console.log("📋 Phone length validation result:", isValid);
      return isValid || "Please enter a valid phone number";
    },
  ],
};

// Computed properties
const topLineOverride = computed(() => {
  return { borderTop: "4px solid " + (event.value?.phcolour || "#FF9500") };
});

// Methods
function showFieldForColumn(col, position) {
  const pos = posit(position);

  if (pos === 0) {
    return true;
  }

  if (col === 1) {
    return pos <= splitValue.value;
  } else {
    return pos > splitValue.value;
  }
}

function posit(posName) {
  if (posArray.value.indexOf(posName) === -1) {
    posArray.value.push(posName);
  }
  return posArray.value.indexOf(posName) + 1;
}

function validateEmail(val) {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val);
}

// Debug function to test validation
async function testValidation() {
  console.log("🧪 Testing validation manually...");
  console.log("📋 Current form values:", {
    title: registrationResponses.title,
    forename: registrationResponses.forename,
    surname: registrationResponses.surname,
    email: registrationResponses.email,
    company: registrationResponses.company,
    phone: registrationResponses.phone,
  });

  console.log("📋 Field refs status:", {
    titleField: !!titleField.value,
    forenameField: !!forenameField.value,
    surnameField: !!surnameField.value,
    emailField: !!emailField.value,
    companyField: !!companyField.value,
    phoneField: !!phoneField.value,
    formRef: !!formRef.value,
  });

  const result = await saveBookingDetails();
  console.log("🧪 Test validation result:", result);
}

async function saveBookingDetails() {
  console.log("🔍 Starting comprehensive form validation...");

  // Context7 pattern: Comprehensive validation with proper error handling
  let hasErrors = false;
  const validationResults = [];
  const errorFields = [];

  // Helper function to validate a field
  const validateField = async (fieldRef, fieldName, isRequired = true) => {
    if (!isRequired) {
      console.log(`⏭️ Skipping ${fieldName} - not required`);
      return true;
    }

    if (!fieldRef || typeof fieldRef.validate !== "function") {
      console.log(`⚠️ ${fieldName} field ref not found or no validate method`);
      return !isRequired; // If not required and no ref, consider valid
    }

    try {
      console.log(`🔍 Validating ${fieldName}...`);
      const isValid = await fieldRef.validate();
      validationResults.push({ field: fieldName, valid: isValid });

      if (!isValid) {
        console.log(`❌ ${fieldName} validation failed`);
        errorFields.push(fieldName);
        hasErrors = true;
      } else {
        console.log(`✅ ${fieldName} validation passed`);
      }

      return isValid;
    } catch (error) {
      console.error(`❌ Error validating ${fieldName}:`, error);
      errorFields.push(fieldName);
      hasErrors = true;
      return false;
    }
  };

  // Validate all required fields
  await validateField(titleField.value, "title", true);
  await validateField(forenameField.value, "forename", true);
  await validateField(surnameField.value, "surname", true);
  await validateField(emailField.value, "email", true);
  await validateField(
    companyField.value,
    "company",
    event.value?.company_field
  );
  await validateField(phoneField.value, "phone", event.value?.phone_field);

  // Also try form-level validation as backup
  try {
    const formValid = await formRef.value?.validate();
    console.log("📋 Form-level validation result:", formValid);
    if (!formValid) {
      hasErrors = true;
    }
  } catch (error) {
    console.error("❌ Form-level validation error:", error);
    hasErrors = true;
  }

  console.log("📋 Validation summary:", {
    validationResults,
    errorFields,
    hasErrors,
    totalFieldsValidated: validationResults.length,
  });

  if (hasErrors) {
    console.log(
      "❌ Form validation failed - showing errors and preventing submit"
    );

    // Ensure DOM updates with error states
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Scroll to first error field
    scrollToFirstError();

    // Show user-friendly error message
    $q.notify({
      type: "negative",
      message: `Please fix the errors in the following fields: ${errorFields.join(
        ", "
      )}`,
      position: "top",
      timeout: 5000,
      actions: [
        {
          label: "Dismiss",
          color: "white",
          handler: () => {},
        },
      ],
    });

    return false;
  }

  console.log("✅ All validation passed, saving booker details...");
  store.setBookerDetails(registrationResponses);
  return true;
}

// Helper function to validate individual fields and ensure error highlighting
async function validateIndividualFields() {
  console.log("🔍 Starting individual field validation...");

  // Get refs using the component instance
  const refs = instance?.refs || {};
  console.log("📋 Available refs:", Object.keys(refs));

  const fieldNames = [
    "titleField",
    "forenameField",
    "surnameField",
    "emailField",
    "companyField",
    "phoneField",
  ];

  const validationPromises = fieldNames.map(async (fieldName) => {
    const field = refs[fieldName];
    if (field && typeof field.validate === "function") {
      console.log(`🔍 Validating field: ${fieldName}`);
      try {
        const result = await field.validate();
        console.log(`📋 ${fieldName} validation result:`, result);
        return result;
      } catch (error) {
        console.error(`❌ Error validating ${fieldName}:`, error);
        return false;
      }
    } else {
      console.log(
        `⚠️ Field ${fieldName} not found or doesn't have validate method`
      );
      return true;
    }
  });

  const results = await Promise.all(validationPromises);
  console.log("📋 All individual field validation results:", results);
}

// Helper function to scroll to the first error field
function scrollToFirstError() {
  // Find the first field with an error
  const errorField = document.querySelector(".q-field--error");
  if (errorField) {
    errorField.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });

    // Focus the input if possible
    const input = errorField.querySelector("input, select, textarea");
    if (input) {
      setTimeout(() => input.focus(), 300);
    }
  }
}

// Provide the validation function to child components (like book-button-panel)
provide("validateBookerForm", saveBookingDetails);

// Context7 pattern: Validation handler
const handleValidationRequest = async () => {
  console.log("🔍 Booker form: Validation requested");

  try {
    const isValid = await saveBookingDetails();
    console.log("📋 Booker form: Validation result:", isValid);

    // Emit result back to requester
    eventBus.emit("booker-form-validated", isValid);

    return isValid;
  } catch (error) {
    console.error("❌ Booker form: Validation error:", error);
    eventBus.emit("booker-form-validated", false);
    return false;
  }
};

onMounted(() => {
  console.log("🔍 Booker form: Setting up validation listener");
  eventBus.on("validate-booker-form", handleValidationRequest);
});

onUnmounted(() => {
  console.log("🔍 Booker form: Cleaning up validation listener");
  eventBus.off("validate-booker-form", handleValidationRequest);
});

function setupForm() {
  posArray.value = [];
  regFields.value = [];
  fieldsCount.value = 4; // Reset this if calling from outside
  splitValue.value = 0;

  if (!registrationResponses.registered_user_responses_attributes) {
    registrationResponses.registered_user_responses_attributes = [];
  }

  // Count fields for column calculation
  if (event.value?.company_field) fieldsCount.value++;
  if (event.value?.job_description_field) fieldsCount.value++;
  if (event.value?.phone_field) fieldsCount.value++;
  if (event.value?.address1_field) fieldsCount.value++;
  if (event.value?.address2_field) fieldsCount.value++;
  if (event.value?.town_field) fieldsCount.value++;
  if (event.value?.county_field) fieldsCount.value++;
  if (event.value?.country_field) fieldsCount.value++;
  if (event.value?.po_number_field) fieldsCount.value++;

  const customFields =
    event.value?.registration_fields?.filter(
      (field) => field.field_type === "booker" && !field._destroy
    ) || [];

  fieldsCount.value = fieldsCount.value + customFields.length;
  splitValue.value = Math.ceil(fieldsCount.value / 2);

  if (customFields && customFields.length > 0) {
    // Sort custom fields by order index
    const sortedFields = [...customFields].sort((n1, n2) => {
      if (n1.order_index > n2.order_index) return 1;
      if (n1.order_index < n2.order_index) return -1;
      return 0;
    });

    // Ensure responses are in the correct order
    const result = [];
    if (
      registrationResponses.registered_user_responses_attributes &&
      registrationResponses.registered_user_responses_attributes.length > 0
    ) {
      sortedFields.forEach((field) => {
        const match =
          registrationResponses.registered_user_responses_attributes.find(
            (resp) => resp.registration_field_id === field.id
          );
        if (match) {
          result.push(match);
        }
      });
    }

    if (result.length > 0) {
      registrationResponses.registered_user_responses_attributes = result;
    }

    // Prepare custom fields
    sortedFields.forEach((customField, index) => {
      // Ensure we have a response object for this field
      if (!registrationResponses.registered_user_responses_attributes[index]) {
        registrationResponses.registered_user_responses_attributes[index] = {
          registration_field_id: customField.id,
          text: null,
          selected_option: null,
        };
      }

      // Only add booker fields to regFields
      if (customField.field_type === "booker") {
        const regField = {
          order_index: customField.order_index,
          field_id: customField.id,
          title: customField.title,
          field_type: customField.field_type,
          question_type: customField.question_type,
          mandatory: customField.mandatory,
          min_length: customField.min_length,
          max_length: customField.max_length,
          options: customField.options,
        };

        regFields.value[index] = regField;
      }
    });
  }
}

// Lifecycle hooks
onMounted(() => {
  if (props.regUserId) {
    axios
      .get(`/registered_users/${props.regUserId}/delegate_details/`)
      .then((response) => {
        Object.assign(registrationResponses, response.data);
        setupForm();
      })
      .catch((error) => {
        console.error("Error fetching user details:", error);
        $q.notify({
          message: "Failed to load user details",
          color: "negative",
          icon: "error",
        });
        setupForm();
      });
  } else {
    setupForm();
  }
});

// Initialize form immediately to prevent template errors
setupForm();

// Expose methods to parent
defineExpose({
  saveBookingDetails,
});
</script>

<style lang="scss" scoped>
.booker-form-wrapper {
  width: 100%;
  max-width: 100%;
  margin-top: 0; // Remove top margin to align with section-spacing from parent
}

.booker-form-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: white;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.form-header {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 1rem;

  .text-h6 {
    display: flex;
    align-items: center;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-field {
  position: relative;
}

.modern-input {
  :deep(.q-field__control) {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1976d2;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
    }
  }

  :deep(.q-field--focused .q-field__control) {
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
  }

  :deep(.q-field__label) {
    color: #666;
    font-weight: 500;
  }

  :deep(.q-field--focused .q-field__label) {
    color: #1976d2;
  }

  // Enhanced error state styling
  :deep(.q-field--error .q-field__control) {
    border-color: #f44336 !important;
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2) !important;
    animation: shake 0.5s ease-in-out;
  }

  :deep(.q-field--error .q-field__label) {
    color: #f44336 !important;
  }

  :deep(.q-field__messages) {
    font-weight: 500;
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-column {
    gap: 1rem;
  }

  .form-header {
    .text-h6 {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
}

// Animation for form appearance
.booker-form-wrapper {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

/* Preview mode styling */
.preview-notice {
  text-align: center;
}

:deep(.q-field--disabled) {
  opacity: 0.7;
}

:deep(.q-field--disabled .q-field__control) {
  background-color: #f5f5f5;
}
</style>
