<template>
  <div v-if="eventStore.isLoading" class="flex flex-center q-pa-md">
    <q-spinner size="50px" color="primary" />
    <span class="q-ml-md">Loading event data...</span>
  </div>
  <div v-else-if="tickets && tickets.length > 0" class="row">
    <div class="col-12">
      <q-card class="hg-topline q-mb-md" :style="topLineOverride">
        <q-card-section class="bg-grey-2">
          <div class="text-h6">Tickets Selected</div>
          <small v-if="event?.fees_pass_on && hasPaidTickets"
            >* (fees will be shown on payment screen)</small
          >
        </q-card-section>
        <q-card-section>
          <q-table
            flat
            bordered
            :rows="tickets"
            :columns="getColumns()"
            :pagination="{ rowsPerPage: 0 }"
            hide-pagination
            hide-bottom
          >
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td v-if="hasGroups" :props="props" auto-width>
                  {{ props.row.group_name || "" }}
                </q-td>
                <q-td auto-width>
                  <q-icon
                    name="fa fa-ticket"
                    :class="props.row.child_ticket ? '' : 'ticketround'"
                    :style="
                      props.row.child_ticket
                        ? {}
                        : { backgroundColor: event?.phcolour }
                    "
                  />
                </q-td>
                <q-td :props="props">
                  {{ props.row.details }}
                </q-td>
                <q-td v-if="hasPaidTickets" :props="props">
                  {{ formatCurrency(ticketCost(props.row, discountValid)) }}
                </q-td>
                <q-td
                  v-if="
                    hasPaidTickets && event?.show_vat && event?.vat_exclusive
                  "
                  :props="props"
                >
                  {{ formatCurrency(ticketVatExc(props.row, discountValid)) }}
                </q-td>
                <q-td :props="props">
                  {{ props.row.quantity_tickets }}
                </q-td>
              </q-tr>
            </template>
            <template v-slot:bottom-row v-if="hasPaidTickets">
              <q-tr>
                <q-td v-if="hasGroups" class="text-right" :style="actualCostBG">
                  &nbsp;
                </q-td>
                <q-td class="text-right" :style="actualCostBG"> &nbsp; </q-td>
                <q-td class="text-right" :style="actualCostBG">
                  <span class="text-weight-bold">TICKET TOTAL</span>
                </q-td>
                <q-td class="text-right" :style="actualCostBG">
                  {{ formatCurrency(totalCost(tickets, discountValid)) }}
                </q-td>
                <q-td
                  v-if="event.vat_exclusive"
                  class="text-right"
                  :style="actualCostBG"
                >
                  {{
                    formatCurrency(totalVatExc(tickets, discountValid) * 100)
                  }}
                </q-td>
                <q-td :style="actualCostBG">&nbsp;</q-td>
              </q-tr>
            </template>
          </q-table>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watchEffect } from "vue";
import { useEventStore } from "@/stores/event";
import { useEventStore as useBookingEventStore } from "@/stores/event";
// import { required, email } from '@vuelidate/validators';

import dayjs from "dayjs";
import { orderBy } from "lodash"; // Using lodash-es to replace vue2-filters

// Define props
const props = defineProps({
  // eventId: {
  //   type: [String, Number],
  //   required: false,
  // },
  headerText: {
    type: String,
    default: "",
  },
});

// Initialize stores
const eventStore = useEventStore();
const store = useBookingEventStore();
const event = computed(() => eventStore.getEvent);

// Ensure event is loaded when component mounts
onMounted(async () => {
  // if (props.eventId) {
  //   await eventStore.ensureEventLoaded(props.eventId);
  // }
});

// Reactive data
const tickets = ref([]);
const discountValid = ref(false);
const vatAmount = ref(20);

// Computed properties
const hasPaidTickets = computed(() => store.getChargeable);

const hasGroups = computed(() => {
  let counter = 0;

  tickets.value.forEach((ticket) => {
    if (ticket.ticket_group_id) {
      counter += 1;
    }
  });

  return counter > 0;
});

const topLineOverride = computed(() => {
  return { borderTop: "4px solid " + (event.value?.phcolour || "#FF9500") };
});

const actualCostBG = computed(() => {
  return { backgroundColor: event.value?.phcolour || "#FF9500", color: "#fff" };
});

const showVatInTitle = computed(() => {
  if (event.value?.show_vat) {
    return "(" + (event.value.vat_exclusive ? "exc" : "inc") + " VAT)";
  } else {
    return "";
  }
});

// Methods
function getColumns() {
  return [
    ...(hasGroups.value
      ? [
          {
            name: "group",
            label: "Group Name",
            field: "group_name",
            align: "left",
          },
        ]
      : []),
    {
      name: "icon",
      label: "",
      field: "icon",
      align: "left",
      style: "width: 2%",
    },
    { name: "name", label: "Ticket Name", field: "details", align: "left" },
    ...(hasPaidTickets.value
      ? [
          {
            name: "cost",
            label: `Ticket Cost ${showVatInTitle.value}`,
            field: "cost",
            align: "left",
          },
        ]
      : []),
    ...(hasPaidTickets.value &&
    event.value?.show_vat &&
    event.value?.vat_exclusive
      ? [
          {
            name: "vat",
            label: "Ticket VAT Amount",
            field: "vat",
            align: "left",
          },
        ]
      : []),
    {
      name: "quantity",
      label: "No of Tickets",
      field: "quantity_tickets",
      align: "left",
    },
  ];
}

function formatCurrency(value) {
  return new Intl.NumberFormat("en-GB", {
    style: "currency",
    currency: "GBP",
  }).format(value);
}

// Cost calculation methods (adapted from the mixins)
function ticketCost(ticket, earlyBirdValid) {
  if (ticket.cost === 0 || ticket.cost === null) return 0;

  if (earlyBirdValid && ticket.early_bird && ticket.early_bird > 0) {
    return ticket.quantity_tickets * ticket.early_bird;
  }

  return ticket.quantity_tickets * ticket.cost;
}

function ticketVatExc(ticket, earlyBirdValid) {
  if (ticket.cost === 0 || ticket.cost === null) return 0;

  let cost = ticket.cost;

  if (earlyBirdValid && ticket.early_bird && ticket.early_bird > 0) {
    cost = ticket.early_bird;
  }

  return ticket.quantity_tickets * (cost * (vatAmount.value / 100));
}

function totalCost(tickets, earlyBirdValid) {
  let total = 0;

  tickets.forEach((ticket) => {
    total += ticketCost(ticket, earlyBirdValid);
  });

  return total;
}

function totalVatExc(tickets, earlyBirdValid) {
  let total = 0;

  tickets.forEach((ticket) => {
    total += ticketVatExc(ticket, earlyBirdValid);
  });

  return total;
}

// Lifecycle
onMounted(() => {
  tickets.value = store.getSelectedTickets || [];
  vatAmount.value = window.vatAmount || 20;

  if (props.event && props.event.datetime_eb) {
    discountValid.value = dayjs(props.event.datetime_eb).isAfter(dayjs());
  }
});

// Watch for store changes
watchEffect(() => {
  tickets.value = store.getSelectedTickets || [];
});
</script>

<style lang="scss">
.hg-topline {
  border-top: 3px solid var(--q-primary);
}

.ticketround {
  border-radius: 20px;
  border-width: 10px;
  color: white;
  padding: 6px;
  font-size: 20px;
}

.sub {
  background-color: white !important;
  color: black;
}
</style>
