<template>
    <tr v-if="ticket && event && !hasError">
        <td class="hide-cell">
            <q-icon
                name="fa fa-ticket"
                class="ticketround"
                :style="{ backgroundColor: event.phcolour || event.phcolor || '#FF9500' }"
                aria-hidden="true"
            ></q-icon>
        </td>

        <td data-label="Ticket name" class="tickets">
            {{ ticket.details }}
        </td>

        <td
            data-label="Early Bird Cost"
            data-th="Early Bird Cost"
            v-if="hasEarlyBird"
        >
            <div
                :style="
                    !earlyBirdValid
                        ? {
                              'text-decoration': 'line-through',
                              color: 'gray',
                          }
                        : {}
                "
            >
                {{ formatCurrency(ticket.cost_a, '£') }}
            </div>
        </td>

        <td data-label="Cost" data-th="Cost" v-if="hasPaidTickets">
            <div
                :style="
                    earlyBirdValid
                        ? {
                              'text-decoration': 'line-through',
                              color: 'gray',
                          }
                        : {}
                "
            >
                {{ formatCurrency(ticket.cost_b, '£') }}
            </div>
        </td>

        <td data-label="Vat Amount" data-th="Vat Amount" v-if="vatable">
            {{ formatCurrency(calcVatAmount(ticket), '£') }}
        </td>

        <td
            data-label="Total Remaining"
            v-if="vatable"
            data-th="Total Remaining"
        >
            {{ formatCurrency(calcPricePlusVat(ticket), '£') }}
        </td>

        <td
            data-label="Tickets Remaining"
            data-th="Tickets Remaining"
            v-if="event.show_tickets_remaining"
        >
            <strong>{{ ticket.tickets_remaining }}</strong>
        </td>

        <td
            data-label="Ticket Date & Time"
            data-th="Ticket Date & Time"
            class="ticket-date-time"
        >
            <span v-html="ticketTimeAndDate"></span>
        </td>

        <td
            data-label="Number of tickets"
            v-if="ticket.tickets_remaining > 0"
        >
            <q-select
                v-if="ticket.group_amount > 1"
                :disable="disabled"
                class="ticket-select"
                style="width: 100px !important"
                v-model="quantityTickets"
                :options="minTicketNumberRange().map(n => ({value: n, label: displayTicketAmount(n, ticket.group_amount)}))"
                @update:model-value="setTicket(ticket)"
                dense
                outlined
                emit-value
                map-options
            />

            <q-select
                v-if="ticket.group_amount == 1"
                :disable="disabled"
                class="ticket-select"
                style="width: 100px !important"
                v-model="quantityTickets"
                :options="minTicketNumberRange().map(n => ({value: n, label: n}))"
                @update:model-value="setTicket(ticket)"
                dense
                outlined
                emit-value
                map-options
            />
        </td>

        <td
            data-label="SOLD OUT"
            class="textalignment"
            v-if="ticket.tickets_remaining == 0"
        >
            <q-badge color="negative" label="Sold Out" />
        </td>
    </tr>

    <!-- Child tickets as separate rows -->
    <child-ticket
        v-if="showChild && !event.show_add_attendees"
        v-for="(childTicket, idx) in ticket.child_tickets"
        :key="idx"
        :event="event"
        :ticket="childTicket"
        :disabled="disabled"
    ></child-ticket>
    <child-ticket-view-only
        v-if="showChild && event.show_add_attendees"
        v-for="(childTicket, idx) in ticket.child_tickets"
        :key="idx"
        :event="event"
        :ticket="childTicket"
    ></child-ticket-view-only>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useEventStore } from '@/stores/event';
import dayjs from 'dayjs';
import mitt from 'mitt';
// import menuItems from './menu-items.vue';
import childTicket from './child-ticket-row-basic.vue';
import childTicketViewOnly from './child-ticket-view-only.vue';
import { useTicketsMixin } from './tickets-mixin';

// Event bus for component communication
const eventBus = mitt();

const props = defineProps({
    event: {
        type: Object,
        required: true
    },
    ticket: {
        type: Object,
        required: true
    },
    disabled: {
        type: Boolean,
        default: false
    }
});

const store = useEventStore();

// Error handling
const hasError = ref(false);
const errorMessage = ref('');

// Initialize mixin with error handling
let mixinResult = null;
try {
  mixinResult = useTicketsMixin(props);
} catch (error) {
  console.error('Error initializing ticket-row mixin:', error);
  hasError.value = true;
  errorMessage.value = 'Failed to initialize ticket row';
}

const {
  hasEarlyBird = computed(() => false),
  earlyBirdValid = computed(() => false),
  vatable = computed(() => false)
} = mixinResult || {};

// State
const vatRate = ref(window.vatRate || 20);
const quantityTickets = ref(0);
const discountValid = ref(false);
const editMode = ref(store.getReadOnly);
const startTime = ref(null);
const endTime = ref(null);

// Computed properties
const hasPaidTickets = computed(() => {
    if (!props.event || !props.event.tickets) return false;
    const result = props.event.tickets.some((ticket) => {
        return (ticket.cost_a && +ticket.cost_a > 0) || (ticket.cost_b && +ticket.cost_b > 0);
    });
    return result;
});

const showChild = computed(() => quantityTickets.value > 0);

const ticketTimeAndDate = computed(() => {
    if (startTime.value && endTime.value) {
        return `${startTime.value} <br/> ${endTime.value}`;
    } else if (startTime.value && !endTime.value) {
        return startTime.value;
    } else {
        return '';
    }
});

// Initialize component
onMounted(() => {
    try {
        console.log('🎟️ ticket-row mounted. Full ticket prop:', props.ticket);
        console.log('🎟️ Ticket details:', props.ticket?.details);
        console.log('🎟️ Ticket cost_a:', props.ticket?.cost_a, 'type:', typeof props.ticket?.cost_a);
        console.log('🎟️ Ticket cost_b:', props.ticket?.cost_b, 'type:', typeof props.ticket?.cost_b);
        console.log('🎟️ Ticket tickets_remaining:', props.ticket?.tickets_remaining);
        console.log('🎟️ Event vat_exclusive:', props.event?.vat_exclusive);
        console.log('🎟️ hasPaidTickets computed:', hasPaidTickets.value);
        console.log('🎟️ hasEarlyBird computed:', hasEarlyBird.value);
        console.log('🎟️ earlyBirdValid computed:', earlyBirdValid.value);
        console.log('🎟️ vatable computed:', vatable.value);

        // Validate required props
        if (!props.ticket) {
            throw new Error('Ticket prop is required but not provided');
        }
        if (!props.event) {
            throw new Error('Event prop is required but not provided');
        }

        if (props.event && props.event.datetime_eb) {
            discountValid.value = dayjs(props.event.datetime_eb).isAfter(dayjs());
        }

        if (!props.ticket.quantity_tickets) {
            props.ticket.quantity_tickets = 0;
        }

        quantityTickets.value = props.ticket.quantity_tickets || 0;

        if (editMode.value) {
            props.ticket.tickets_remaining = (props.ticket.tickets_remaining || 0) + (props.ticket.quantity_tickets || 0);
        }

        if (props.ticket.tickets_remaining === null || props.ticket.tickets_remaining === undefined) {
            props.ticket.tickets_remaining = props.ticket.ticket_no || 0;
        }

        if (props.ticket.start_time) {
            try {
                const sdt = new Date(props.ticket.start_time);
                startTime.value = dayjs(sdt).format('DD/MM/YYYY - HH:mm');
            } catch (dateError) {
                console.warn('🎟️ Error parsing start_time:', dateError);
            }
        }

        if (props.ticket.end_time) {
            try {
                const edt = new Date(props.ticket.end_time);
                endTime.value = dayjs(edt).format('DD/MM/YYYY - HH:mm');
            } catch (dateError) {
                console.warn('🎟️ Error parsing end_time:', dateError);
            }
        }
    } catch (error) {
        console.error('🎟️ Error in ticket-row onMounted:', error);
        hasError.value = true;
        errorMessage.value = `Ticket row initialization failed: ${error.message}`;
    }
});

// Methods
function displayTicketAmount(n, group_amount) {
    if (n === 0) {
        return n;
    } else {
        return n + ' x ' + group_amount;
    }
}

function childTicketsRemaining(childTicket) {
    if (!childTicket.quantity_tickets) {
        childTicket.quantity_tickets = 0;
    }
    if (!childTicket.tickets_remaining) {
        return 0;
    } else {
        return childTicket.tickets_remaining;
    }
}

function getVatRate(ticket) {
    if (ticket.vat_rate) {
        vatRate.value = ticket.vat_rate.rate;
    }
    return vatRate.value;
}

function calcVatAmount(ticket) {
    const vatRateValue = getVatRate(ticket) / 100;
    //TODO why is cost_a etc a string??
    if (earlyBirdValid.value) {
        return +ticket.cost_a * vatRateValue;
    } else {
        return +ticket.cost_b * vatRateValue;
    }
}

function calcPricePlusVat(ticket) {
    if (earlyBirdValid.value) {
        return +ticket.cost_a + calcVatAmount(ticket);
    } else {
        return +ticket.cost_b + calcVatAmount(ticket);
    }
}

function minTicketNumberRange() {
    const total = Math.min(
        props.ticket.tickets_remaining,
        props.ticket.max_allowed
    );

    const rangeArray = [];
    for (let i = 0; i < total + 1; i++) {
        rangeArray.push(i);
    }
    return rangeArray;
}

function setTicket(ticket) {
    try {
        // Update the ticket's quantity
        ticket.quantity_tickets = quantityTickets.value;

        // Emit to parent tickets component for booking store update
        eventBus.emit('setTicket', ticket);

        console.log('🎟️ Ticket selection updated:', {
            ticketId: ticket.id,
            details: ticket.details,
            quantity: quantityTickets.value
        });
    } catch (error) {
        console.error('🎟️ Error setting ticket:', error);
    }
}

function minTicketNumberRangeChild(ticket) {
    const total = Math.min(ticket.tickets_remaining, ticket.max_allowed);

    const rangeArray = [];
    for (let i = 0; i < total + 1; i++) {
        rangeArray.push(i);
    }
    return rangeArray;
}

// Helper function to format currency (replacing Vue filter)
function formatCurrency(value, symbol = '£') {
    if (typeof value !== 'number') {
        value = parseFloat(value);
    }
    return symbol + value.toFixed(2);
}
</script>

<style scoped>
.ticketround {
    border-radius: 20px;
    border-width: 10px;
    background: #ff9500;
    color: white;
    padding: 6px;
    font-size: 20px;
}

.sub {
    background-color: white !important;
    color: black;
}

</style>