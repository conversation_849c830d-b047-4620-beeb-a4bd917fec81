<template>
	<div class="user-details-page q-pa-md">
		<page-summary v-if="event" :event="event" headerText="Ticket Summary"></page-summary>
		<attendees-booking></attendees-booking>
	</div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
import pageSummary from '../main_booking/page-summary.vue';
import attendeesBooking from './attendees-booking.vue';

const router = useRouter();
const store = useBookingStore();

// Get event from store
const event = computed(() => store.getEvent);

// Check if event exists, if not redirect to home
onMounted(() => {
  if (!event.value) {
    router.push('home');
  }
});
</script>