<template>
  <div class="attendees-booking">
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="text-h6 q-mb-md">Attendee Details</div>
        
        <div v-for="(attendee, index) in attendees" :key="index" class="attendee-form q-mb-lg">
          <div class="text-subtitle1 q-mb-md">
            Attendee {{ index + 1 }}
            <q-btn 
              v-if="attendees.length > 1"
              flat 
              round 
              color="negative" 
              icon="delete" 
              size="sm"
              @click="removeAttendee(index)"
              class="q-ml-sm"
            />
          </div>
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model="attendee.forename"
                label="First Name *"
                outlined
                :rules="[val => !!val || 'First name is required']"
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model="attendee.surname"
                label="Last Name *"
                outlined
                :rules="[val => !!val || 'Last name is required']"
              />
            </div>
            
            <div class="col-12">
              <q-input
                v-model="attendee.email"
                label="Email *"
                type="email"
                outlined
                :rules="[
                  val => !!val || 'Email is required',
                  val => /.+@.+\..+/.test(val) || 'Please enter a valid email'
                ]"
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model="attendee.company"
                label="Company"
                outlined
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model="attendee.phone"
                label="Phone"
                outlined
              />
            </div>
          </div>
          
          <q-separator v-if="index < attendees.length - 1" class="q-mt-lg" />
        </div>
        
        <div class="row justify-between q-mt-lg">
          <q-btn
            flat
            color="primary"
            icon="add"
            label="Add Another Attendee"
            @click="addAttendee"
          />
          
          <q-btn
            color="primary"
            label="Continue"
            @click="saveAttendees"
            :disable="!isValid"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useBookingStore } from '@/shared/stores/bookingStore.js';

const bookingStore = useBookingStore();

// Reactive data
const attendees = ref([
  {
    forename: '',
    surname: '',
    email: '',
    company: '',
    phone: ''
  }
]);

// Computed properties
const isValid = computed(() => {
  return attendees.value.every(attendee => 
    attendee.forename && 
    attendee.surname && 
    attendee.email && 
    /.+@.+\..+/.test(attendee.email)
  );
});

// Methods
const addAttendee = () => {
  attendees.value.push({
    forename: '',
    surname: '',
    email: '',
    company: '',
    phone: ''
  });
};

const removeAttendee = (index) => {
  if (attendees.value.length > 1) {
    attendees.value.splice(index, 1);
  }
};

const saveAttendees = () => {
  if (isValid.value) {
    // Save attendees to store
    bookingStore.setAttendees(attendees.value);
    
    // Emit event or navigate to next step
    // This would depend on your booking flow
  }
};

// Lifecycle
onMounted(() => {
  // Load existing attendees if any
  const existingAttendees = bookingStore.getAttendees;
  if (existingAttendees && existingAttendees.length > 0) {
    attendees.value = [...existingAttendees];
  }
});
</script>

<style scoped>
.attendees-booking {
  max-width: 800px;
  margin: 0 auto;
}

.attendee-form {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  background: #fafafa;
}
</style>
