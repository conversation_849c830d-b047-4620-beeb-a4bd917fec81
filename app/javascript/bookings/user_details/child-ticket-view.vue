<template>
  <q-card v-if="ticket.child_tickets && ticket.child_tickets.length > 0" class="q-mb-md">
    <q-card-section>
      <q-table
        flat
        bordered
        class="table-tickets"
        :rows="[]"
        :columns="getColumns()"
        :pagination="{rowsPerPage: 0}"
        hide-pagination
        hide-bottom
      >
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th></q-th>
            <q-th :style="{'color': event.phcolor}">Sub-Ticket Name</q-th>

            <q-th 
              v-if="hasEarlyBird"
              :style="earlyBirdValid ? {'color': event.phcolor} : {'color': 'gray', 'text-decoration': 'line-through'}"
            >
              Early Bird Cost ({{event.vat_exclusive ? 'exc' : 'inc'}} VAT)
              <q-btn flat round dense color="info" icon="fa fa-info-circle">
                <q-menu anchor="top start" :offset="[0, 8]">
                  <q-card style="min-width: 200px">
                    <q-card-section>
                      <div class="text-subtitle1">Dates Valid</div>
                      <div>{{ earlyBirdInfo }}</div>
                    </q-card-section>
                  </q-card>
                </q-menu>
              </q-btn>
            </q-th>

            <q-th 
              v-if="hasPaidTickets"
              :style="earlyBirdValid && hasEarlyBird ? {'color': 'gray'} : {'color': event.phcolor}"
            >
              Cost ({{event.vat_exclusive ? 'exc' : 'inc'}} VAT)
              <q-btn v-if="earlyBirdValid" flat round dense color="info" icon="fa fa-info-circle">
                <q-menu anchor="top start" :offset="[0, 8]">
                  <q-card style="min-width: 200px">
                    <q-card-section>
                      <div class="text-subtitle1">Dates Valid</div>
                      <div>{{ earlyBirdInfoExp }}</div>
                    </q-card-section>
                  </q-card>
                </q-menu>
              </q-btn>
            </q-th>

            <q-th v-if="vatable">VAT Amount</q-th>
            <q-th v-if="vatable">Total to Pay</q-th>
            <q-th :style="{color: event.phcolor}" v-if="event.show_tickets_remaining">Tickets Remaining</q-th>
            <q-th :style="{color: event.phcolor}">Ticket Date & Time</q-th>
            <q-th :style="{color: event.phcolor}">Number of tickets</q-th>
          </q-tr>
        </template>
        
        <template v-slot:body>
          <child-ticket 
            v-for="(childTicket, idx) in ticket.child_tickets" 
            :key="idx" 
            :event="event" 
            :attendee="attendee" 
            :ticket="childTicket" 
            :disabled="disabled"
            @setTicket="assignTicket"
          ></child-ticket>
        </template>
      </q-table>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'pinia';
import { useEventBus } from '../../events/event-bus';
import childTicketRow from '../main_booking/child-ticket-row.vue';
import { useTicketsMixin } from '../main_booking/tickets-mixin';

const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  ticket: {
    type: Object,
    required: true
  },
  attendee: {
    type: Object,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const eventBus = useEventBus();
const store = useStore();
const { hasEarlyBird, earlyBirdValid, earlyBirdInfo, earlyBirdInfoExp, vatable } = useTicketsMixin(props);

// State
const regUser = ref(null);
const assignedTicket = ref({});
const assignedNumber = ref(0);

// Computed properties
const hasPaidTickets = computed(() => store.getChargeable);

// Method to get columns configuration for q-table
function getColumns() {
  return [
    { name: 'icon', label: '', field: 'icon' },
    { name: 'name', label: 'Sub-Ticket Name', field: 'name' },
    ...(hasEarlyBird.value ? [{ name: 'earlyBird', label: 'Early Bird Cost', field: 'earlyBird' }] : []),
    ...(hasPaidTickets.value ? [{ name: 'cost', label: 'Cost', field: 'cost' }] : []),
    ...(vatable.value ? [
      { name: 'vat', label: 'VAT Amount', field: 'vat' },
      { name: 'total', label: 'Total to Pay', field: 'total' },
    ] : []),
    ...(props.event.show_tickets_remaining ? [{ name: 'remaining', label: 'Tickets Remaining', field: 'remaining' }] : []),
    { name: 'datetime', label: 'Ticket Date & Time', field: 'datetime' },
    { name: 'quantity', label: 'Number of tickets', field: 'quantity' },
  ];
}

onMounted(() => {
  regUser.value = props.attendee.registered_user_attributes;
  if (!regUser.value.assign_subtickets_attributes) {
    regUser.value.assign_subtickets_attributes = [];
  }
});

// Methods
function assignTicket(childTicket, quantity_tickets) {
  if (quantity_tickets > 0) {
    assignedTicket.value = {
      package_id: childTicket.id,
      quantity_assigned: quantity_tickets,
      attendee_idx: props.attendee.idx
    };

    regUser.value.assign_subtickets_attributes.push(assignedTicket.value);
    store.setAssignedChildTicket(assignedTicket.value);
  } else {
    let subIndex = -1;
    regUser.value.assign_subtickets_attributes.forEach((subTicketAssigned, idx) => {
      if (
        subTicketAssigned.package_id === childTicket.id && 
        subTicketAssigned.attendee_idx === props.attendee.idx
      ) {
        subIndex = idx;
        store.removeAssignedChildTicket(subTicketAssigned);
      }
    });
    
    if (subIndex > -1) {
      regUser.value.assign_subtickets_attributes.splice(subIndex, 1);
    }
  }

  eventBus.emit("setTicket", childTicket);
}
</script>

<style scoped>
.table-tickets {
  width: 100%;
}
</style>