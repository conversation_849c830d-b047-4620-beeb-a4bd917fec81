<template>
  <div class="q-mb-md" v-if="ticket && ticket.package_options.length > 0">
    <q-card v-for="(option, index) in ticket.package_options" :key="index" class="q-mb-sm">
      <q-card-section class="bg-grey-2" :style="underlineOverride">
        <div class="text-weight-bold">{{option.title}}</div>
      </q-card-section>
      <q-card-section>
        <q-option-group
          v-model="optionsSelected[index]"
          :options="option.package_sub_options.map(subOption => ({
            label: subOption.title,
            value: subOption.id
          }))"
          type="radio"
          @update:model-value="updateTicket"
        />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useStore } from 'pinia';

const props = defineProps({
  ticket: {
    type: Object,
    required: true
  },
  attendee: {
    type: Object,
    required: true
  },
  attendeeIndex: {
    type: Number,
    required: true
  }
});

const store = useStore();
const optionsSelected = ref([]);
const columnSize = ref('col-4');

// Get event from store
const event = computed(() => store.getEvent);

const underlineOverride = computed(() => {
  return {
    borderBottom: "4px solid " + event.value.phcolour
  };
});

onMounted(() => {
  if (
    props.attendee.registered_user_attributes &&
    props.attendee.registered_user_attributes.menu_options_selected
  ) {
    optionsSelected.value = [...props.attendee.registered_user_attributes.menu_options_selected];
  }

  if (
    !props.ticket.package_options ||
    props.ticket.package_options.length === 1
  ) {
    columnSize.value = "col-12";
  } else if (props.ticket.package_options.length === 2) {
    columnSize.value = "col-6";
  }
});

// Watch for changes in attendee properties
watch(() => props.attendee.registered_user_attributes?.menu_options_selected, (newVal) => {
  if (newVal) {
    optionsSelected.value = [...newVal];
  }
}, { deep: true });

function updateTicket() {
  props.attendee.registered_user_attributes.menu_options_selected = optionsSelected.value;
}
</script>