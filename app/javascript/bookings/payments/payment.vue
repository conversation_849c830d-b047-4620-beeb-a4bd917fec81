<template>
  <div v-if="event && hasPaidTickets" class="payment-component-wrapper">
    <!-- Payment Header -->
    <div class="payment-header q-mb-xl">
      <div class="text-h5 text-weight-bold text-grey-8 q-mb-sm">
        <q-icon name="payment" class="q-mr-sm" />
        Complete Your Payment
      </div>
      <div class="text-body1 text-grey-6">
        Review your order and provide payment details to complete your booking
      </div>
    </div>

    <div class="payment-grid">
      <!-- Order Summary Card -->
      <div class="order-summary-section">
        <q-card class="modern-card">
          <q-card-section class="card-header">
            <div class="row items-center justify-between">
              <div class="text-h6 text-weight-bold">
                <q-icon name="receipt" class="q-mr-sm" />
                Booking Summary
              </div>
              <q-btn
                flat
                color="primary"
                icon="edit"
                label="Amend Booking"
                @click="goBackToBooking"
                class="amend-booking-btn"
              />
            </div>
          </q-card-section>

          <q-card-section class="q-pa-lg">
            <!-- Ticket Items -->
            <div class="ticket-items">
              <div v-for="(ticket, index) in tickets" :key="index" class="ticket-item">
                <div class="ticket-details">
                  <div class="ticket-name">{{ ticket.details }}</div>
                  <div class="ticket-calculation">
                    {{ ticket.quantity_tickets }} ×
                    <span class="price">
                      £{{ eventBooking?.early_bird_valid ? parseFloat(ticket.cost_a).toFixed(2) : parseFloat(ticket.cost_b).toFixed(2) }}
                    </span>
                  </div>
                </div>
                <div class="ticket-total">
                  £{{ ticketCost(ticket) }}
                </div>
              </div>
            </div>

            <!-- Event Discount -->
            <div v-if="eventDiscount" class="discount-item">
              <div class="discount-details">
                <q-icon name="local_offer" class="q-mr-sm text-positive" />
                Event discount applied
              </div>
              <div class="discount-amount text-positive">
                -£{{ eventDiscount.toFixed(2) }}
              </div>
            </div>

            <!-- Subtotal -->
            <div class="order-total">
              <div class="total-label">Total</div>
              <div class="total-amount">£{{ subtotal }}</div>
            </div>
          </q-card-section>

          <!-- Amend Booking Section -->
          <q-card-section class="amend-booking-section q-pt-lg" style="border-top: 1px solid #e0e0e0;">
            <div class="text-body2 text-grey-6 q-mb-md">
              Need to change your booking details?
            </div>
            <q-btn
              outline
              color="primary"
              icon="edit"
              label="Go Back & Amend Booking"
              @click="goBackToBooking"
              class="full-width"
            />
          </q-card-section>
        </q-card>

        <!-- Discount Code Card -->
        <q-card class="modern-card q-mt-lg">
          <q-card-section class="card-header">
            <div class="text-h6 text-weight-bold">
              <q-icon name="local_offer" class="q-mr-sm" />
              Discount Code
            </div>
          </q-card-section>

          <q-card-section class="q-pa-lg">
            <q-input
              placeholder="Enter discount code"
              v-model="discountCode"
              outlined
              class="modern-input"
            >
              <template v-slot:append>
                <q-btn
                  color="primary"
                  flat
                  icon="add"
                  @click="applyDiscountCode"
                  :disable="!discountCode"
                  class="apply-discount-btn"
                >
                  Apply
                </q-btn>
              </template>
            </q-input>
          </q-card-section>
        </q-card>
      </div>

      <!-- Payment Details Section -->
      <div class="payment-details-section">
        <!-- Payment Methods -->
        <q-card v-if="subtotal > 0" class="modern-card">
          <q-card-section class="card-header">
            <div class="text-h6 text-weight-bold">
              <q-icon name="payment" class="q-mr-sm" />
              Payment Method
            </div>
            <div class="text-body2 text-grey-6 q-mt-sm">
              Choose your preferred payment method
            </div>
          </q-card-section>

          <!-- Payment Method Selection -->
          <q-card-section class="q-pa-lg">
            <div class="payment-methods q-mb-lg">
              <q-option-group
                v-model="selectedPaymentMethod"
                :options="paymentMethodOptions"
                color="primary"
                inline
                @update:model-value="onPaymentMethodChange"
              />
            </div>


          </q-card-section>

          <!-- Card Payment Section -->
          <q-card-section v-if="selectedPaymentMethod === 'card'" class="card-header">
            <div class="text-h6 text-weight-bold">
              <q-icon name="credit_card" class="q-mr-sm" />
              Card Payment
            </div>
            <div class="text-body2 text-grey-6 q-mt-sm">
              Secure payment powered by Stripe
            </div>
          </q-card-section>

          <!-- Card Payment Form -->
          <q-card-section v-if="selectedPaymentMethod === 'card'" class="q-pa-lg">
            <!-- Debug info in development -->
            <!-- <div class="payment-debug q-mb-md" v-if="isDevelopment">
              <div class="text-caption">🔧 Payment Debug:</div>
              <div class="text-caption">Booking ID: {{ eventBooking?.id || 'Missing' }}</div>
              <div class="text-caption">Stripe Key: {{ pk ? 'Present' : 'Missing' }}</div>
              <div class="text-caption">Client Secret: {{ elementsOptions.clientSecret ? 'Present' : 'Missing' }}</div>
              <div class="text-caption">Stripe Loaded: {{ stripeLoaded }}</div>
              <div class="text-caption">Payment Method: {{ selectedPaymentMethod }}</div>
              <div class="q-mt-sm">
                <q-btn
                  size="sm"
                  color="orange"
                  @click="testPaymentIntent"
                  label="Test Payment Intent"
                />
              </div>
            </div> -->

            <!-- Show Stripe Elements using direct Stripe.js -->
            <div v-if="stripeLoaded && pk" class="payment-form">
              <!-- Direct Stripe Elements container -->
              <div
                id="card-element"
                class="stripe-element"
                v-show="elementsOptions.clientSecret"
              ></div>

              <!-- Card errors will be displayed here -->
              <div id="card-errors" role="alert" class="stripe-errors q-mt-sm"></div>

              <!-- Show message if no client secret yet -->
              <div v-if="!elementsOptions.clientSecret" class="q-mt-md text-center">
                <q-icon name="info" color="info" />
                <div class="text-caption text-grey-6">
                  Setting up payment form...
                </div>
              </div>
            </div>

            <!-- Show error if no Stripe key -->
            <div v-else-if="!pk" class="payment-error">
              <q-icon name="error" color="negative" size="2em" />
              <div class="q-mt-md text-negative">Stripe public key not configured</div>
            </div>

            <!-- Show loading if Stripe not loaded yet -->
            <div v-else class="payment-loading">
              <q-spinner color="primary" size="2em" />
              <div class="q-mt-md text-grey-6">Loading Stripe...</div>
            </div>
          </q-card-section>

          <!-- BACS Payment Information -->
          <q-card-section v-if="selectedPaymentMethod === 'bacs'" class="q-pa-lg">
            <div class="bacs-info">
              <div class="text-center q-mb-lg">
                <q-icon name="account_balance" size="3em" color="primary" />
                <div class="text-h6 q-mt-md">Bank Transfer (BACS)</div>
                <div class="text-body2 text-grey-6">
                  Complete your payment via bank transfer
                </div>
              </div>

              <q-banner class="bg-blue-1 text-blue-8 q-mb-lg">
                <template v-slot:avatar>
                  <q-icon name="info" color="blue" />
                </template>
                <div class="text-weight-medium">Payment Instructions</div>
                <div class="q-mt-sm">
                  Your booking will be confirmed once we receive your bank transfer.
                  Please allow 1-3 business days for processing.
                </div>
              </q-banner>

              <div class="bank-details q-pa-md bg-grey-1 rounded-borders">
                <div class="text-weight-bold q-mb-md">Bank Transfer Details:</div>
                <div class="bank-detail-row">
                  <span class="label">Account Name:</span>
                  <span class="value">{{ event?.payment_option?.account_name || 'Account details will be provided' }}</span>
                </div>
                <div class="bank-detail-row">
                  <span class="label">Account Number:</span>
                  <span class="value">{{ event?.payment_option?.account_number || 'Will be provided after booking' }}</span>
                </div>
                <div class="bank-detail-row">
                  <span class="label">Sort Code:</span>
                  <span class="value">{{ event?.payment_option?.sort_code || 'Will be provided after booking' }}</span>
                </div>
                <div class="bank-detail-row">
                  <span class="label">Reference:</span>
                  <span class="value">{{ eventBooking?.id ? `Booking-${eventBooking.id}` : 'Will be provided after booking' }}</span>
                </div>
                <div class="bank-detail-row">
                  <span class="label">Amount:</span>
                  <span class="value text-weight-bold">£{{ subtotal }}</span>
                </div>
              </div>
            </div>
          </q-card-section>

          <q-card-section class="payment-actions">
            <!-- Card Payment Button -->
            <q-btn
              v-if="selectedPaymentMethod === 'card' && elementsOptions.clientSecret"
              :disable="paymentSubmitted"
              :loading="paymentSubmitted"
              @click="pay"
              color="positive"
              size="lg"
              class="full-width payment-btn"
            >
              <q-icon name="lock" class="q-mr-sm" />
              <span v-if="paymentSubmitted">Processing Payment...</span>
              <span v-else>Pay £{{ subtotal }}</span>
            </q-btn>

            <!-- BACS Payment Button -->
            <q-btn
              v-if="selectedPaymentMethod === 'bacs'"
              :disable="paymentSubmitted"
              :loading="paymentSubmitted"
              @click="confirmBacsPayment"
              color="primary"
              size="lg"
              class="full-width payment-btn"
            >
              <q-icon name="account_balance" class="q-mr-sm" />
              <span v-if="paymentSubmitted">Confirming Booking...</span>
              <span v-else>Confirm Bank Transfer Booking</span>
            </q-btn>
          </q-card-section>
        </q-card>

        <!-- Free Booking -->
        <q-card v-else class="modern-card">
          <q-card-section class="card-header">
            <div class="text-h6 text-weight-bold">
              <q-icon name="card_giftcard" class="q-mr-sm" />
              Free Booking
            </div>
            <div class="text-body2 text-grey-6 q-mt-sm">
              No payment required for this booking
            </div>
          </q-card-section>

          <q-card-section class="q-pa-lg">
            <div class="free-booking-message">
              <q-icon name="check_circle" color="positive" size="3em" class="q-mb-md" />
              <div class="text-h6 text-weight-medium q-mb-sm">Congratulations!</div>
              <div class="text-body1 text-grey-7">
                This booking is completely free. Click the button below to complete your registration.
              </div>
            </div>
          </q-card-section>

          <q-card-section class="payment-actions">
            <q-btn
              @click="pay"
              color="positive"
              size="lg"
              class="full-width payment-btn"
            >
              <q-icon name="check_circle" class="q-mr-sm" />
              Complete Free Booking
            </q-btn>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, onBeforeRouteLeave } from 'vue-router';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
// Using Stripe.js directly instead of Vue wrapper
import discountInput from './discount-input.vue';
import axios from 'axios';
import { useQuasar } from 'quasar';

const router = useRouter();
const store = useBookingStore();
const $q = useQuasar();

// Environment and state
const isDevelopment = process.env.NODE_ENV === 'development';

// State
const paymentIntent = ref(null);
const paymentSubmitted = ref(false);
const pk = ref(window.stripePubKey);
const stripeConnectId = ref(null);
const paymentType = ref('card');
const discountCode = ref(null);
const eventDiscount = ref(0);
const stripeLoaded = ref(false);

// Payment method selection
const selectedPaymentMethod = ref('card');
const paymentMethodOptions = ref([
  {
    label: 'Credit/Debit Card',
    value: 'card',
    icon: 'credit_card'
  },
  {
    label: 'Bank Transfer (BACS)',
    value: 'bacs',
    icon: 'account_balance'
  }
]);

// Direct Stripe.js implementation
const stripe = ref(null);
const elements = ref(null);
const cardElement = ref(null);
const cardElementMounted = ref(false);

// Stripe Elements options
const elementsOptions = ref({
  clientSecret: null,
  appearance: {
    theme: 'stripe',
    variables: {
      colorPrimary: '#1976d2',
      colorBackground: '#ffffff',
      colorText: '#2c3e50',
      colorDanger: '#f44336',
      fontFamily: 'Roboto, sans-serif',
      spacingUnit: '4px',
      borderRadius: '8px',
    }
  },
});

// Instance options for Stripe (for Connect accounts)
const instanceOptions = computed(() => ({
  stripeAccount: stripeConnectId.value,
}));

// Card element specific options
const cardElementOptions = ref({
  style: {
    base: {
      fontSize: '16px',
      color: '#2c3e50',
      '::placeholder': {
        color: '#aab7c4',
      },
    },
    invalid: {
      color: '#f44336',
      iconColor: '#f44336',
    },
  },
  hidePostalCode: true,
});

const confirmParams = ref({
  return_url: window.location.href.replace('#/payment', '#/summary'),
});

// Get data from store
const event = computed(() => store.getEvent);
const tickets = computed(() => store.getSelectedTickets);
const eventBooking = computed(() => store.getEventBooking);
const hasPaidTickets = computed(() => store.getChargeable);

// Calculate subtotal
const subtotal = computed(() => {
  let total = [];
  Object.entries(tickets.value).forEach(([key, val]) => {
    if (eventBooking.value?.early_bird_valid) {
      total.push(val.quantity_tickets * val.cost_a);
    } else {
      total.push(val.quantity_tickets * val.cost_b);
    }
  });
  
  let subtotalValue = parseFloat(total.reduce((total, num) => total + num, 0));
  if (eventDiscount.value) {
    subtotalValue -= eventDiscount.value;
  }
  return subtotalValue.toFixed(2);
});

// Navigation guard
onBeforeRouteLeave((to, from, next) => {
  if (to.name === 'home') {
    $q.dialog({
      title: 'Confirmation',
      message: 'Are you sure you want to leave this page? If you do, it will take you back to the start of the booking process.',
      cancel: true,
      persistent: true
    }).onOk(() => {
      next();
      location.reload();
    }).onCancel(() => {
      next(false);
    });
  } else {
    next();
  }
});

// Watch for subtotal changes (e.g., when discounts are applied)
watch(subtotal, async (newSubtotal, oldSubtotal) => {
  if (newSubtotal !== oldSubtotal && paymentIntent.value) {
    console.log('💳 Subtotal changed, updating payment intent:', { oldSubtotal, newSubtotal });
    await updatePaymentIntent();
  }
});

// Initialize Stripe Elements
async function initializeStripe() {
  if (!pk.value) {
    console.error('💳 No Stripe public key available');
    return;
  }

  try {
    console.log('💳 Initializing Stripe with key:', pk.value ? 'Present' : 'Missing');

    // Initialize Stripe
    const stripeOptions = stripeConnectId.value ? { stripeAccount: stripeConnectId.value } : {};
    stripe.value = window.Stripe(pk.value, stripeOptions);

    if (!stripe.value) {
      throw new Error('Failed to initialize Stripe');
    }

    console.log('💳 Stripe initialized successfully');
    stripeLoaded.value = true;

    // Don't initialize Elements here - wait for payment intent to be created
    console.log('💳 Stripe ready, waiting for payment intent...');
  } catch (error) {
    console.error('💳 Error initializing Stripe:', error);
  }
}

// Initialize Stripe Elements
async function initializeElements() {
  if (!stripe.value || !elementsOptions.value.clientSecret) {
    console.log('💳 Cannot initialize elements - missing stripe or client secret');
    return;
  }

  try {
    console.log('💳 Initializing Stripe Elements with client secret');

    // Create elements instance with proper options
    const elementsConfig = {
      clientSecret: elementsOptions.value.clientSecret,
      appearance: {
        theme: 'stripe',
        variables: {
          colorPrimary: '#1976d2',
          colorBackground: '#ffffff',
          colorText: '#2c3e50',
          colorDanger: '#f44336',
          fontFamily: 'Roboto, sans-serif',
          spacingUnit: '4px',
          borderRadius: '8px',
        }
      }
    };

    elements.value = stripe.value.elements(elementsConfig);

    // Create a simpler card element instead of payment element
    cardElement.value = elements.value.create('card', {
      style: {
        base: {
          fontSize: '16px',
          color: '#2c3e50',
          '::placeholder': {
            color: '#aab7c4',
          },
        },
        invalid: {
          color: '#f44336',
          iconColor: '#f44336',
        },
      },
      hidePostalCode: true,
    });

    // Mount the card element
    cardElement.value.mount('#card-element');
    cardElementMounted.value = true;

    // Listen for changes
    cardElement.value.on('change', handleChange);
    cardElement.value.on('ready', handleReady);

    console.log('💳 Stripe Elements initialized and mounted successfully');
  } catch (error) {
    console.error('💳 Error initializing Stripe Elements:', error);

    // Show error to user
    const errorElement = document.getElementById('card-errors');
    if (errorElement) {
      errorElement.textContent = 'Failed to load payment form. Please refresh the page.';
    }
  }
}

// Watch for booking changes to update payment intent
watch([subtotal, () => eventBooking.value?.id], async ([newSubtotal, newBookingId], [oldSubtotal, oldBookingId]) => {
  console.log('💳 Booking or subtotal changed:', {
    oldSubtotal,
    newSubtotal,
    oldBookingId,
    newBookingId,
    paymentMethod: selectedPaymentMethod.value
  });

  // If subtotal changed and we have a booking, update payment intent for card payments
  if (newBookingId && newSubtotal !== oldSubtotal && selectedPaymentMethod.value === 'card') {
    console.log('💳 Subtotal changed, updating payment intent...');
    await updatePaymentIntent();
  }
}, { deep: true });

// Watch for client secret changes to initialize Stripe Elements
watch(() => elementsOptions.value.clientSecret, async (newClientSecret, oldClientSecret) => {
  console.log('💳 Client secret changed:', {
    oldClientSecret: oldClientSecret ? 'present' : 'missing',
    newClientSecret: newClientSecret ? 'present' : 'missing',
    stripeLoaded: stripeLoaded.value,
    cardElementMounted: cardElementMounted.value
  });

  // Initialize Elements when we get a client secret and Stripe is loaded
  if (newClientSecret && stripeLoaded.value && !cardElementMounted.value && selectedPaymentMethod.value === 'card') {
    console.log('💳 Client secret available, initializing Stripe Elements...');
    await initializeElements();
  }
});

// Initialize component
onMounted(async () => {
  console.log('💳 Payment component mounted');
  console.log('💳 Initial state check:', {
    hasEvent: !!event.value,
    hasEventBooking: !!eventBooking.value,
    eventBookingId: eventBooking.value?.id,
    hasSelectedTickets: !!tickets.value?.length,
    subtotal: subtotal.value,
    hasPaidTickets: hasPaidTickets.value,
    currentURL: window.location.href,
    urlParams: Object.fromEntries(new URLSearchParams(window.location.search).entries())
  });

  // Check if returning from booking amendment
  const returningFromPayment = sessionStorage.getItem('returning_from_payment');
  const paymentBookingId = sessionStorage.getItem('payment_booking_id');

  if (returningFromPayment === 'true' && paymentBookingId) {
    console.log('💳 Detected return from booking amendment');
    sessionStorage.removeItem('returning_from_payment');
    sessionStorage.removeItem('payment_booking_id');

    $q.notify({
      type: 'positive',
      message: 'Booking updated! Payment amount will be refreshed.',
      position: 'top'
    });
  }

  // Initialize Stripe
  await initializeStripe();

  // Wait a moment for the store to be fully initialized
  await new Promise(resolve => setTimeout(resolve, 100));

  if (eventBooking.value?.id) {
    // Only get payment intent for card payments
    if (selectedPaymentMethod.value === 'card') {
      await getPaymentIntent();
    }
  } else {
    // Try to get booking ID from URL params or other sources
    const urlParams = new URLSearchParams(window.location.search);
    const bookingIdFromUrl = urlParams.get('booking_id');

    if (bookingIdFromUrl) {
      console.log('💳 Found booking ID in URL:', bookingIdFromUrl);
      // Try to load the booking data
      try {
        const response = await axios.get(`/event_bookings/${bookingIdFromUrl}`);
        console.log('💳 Successfully loaded booking from URL:', response.data);
        store.setEventBooking(response.data);
        await getPaymentIntent();
      } catch (error) {
        console.error('💳 Failed to load booking from URL:', error);
        console.error('💳 Error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });
        redirectToBookingPage();
      }
    } else {
      console.error('💳 No event booking found in store or URL parameters');
      console.error('💳 Current store state:', {
        hasEventBooking: !!eventBooking.value,
        eventBookingId: eventBooking.value?.id,
        hasEvent: !!event.value,
        eventId: event.value?.id,
        urlParams: Object.fromEntries(urlParams.entries())
      });
      redirectToBookingPage();
    }
  }
});

function redirectToBookingPage() {
  console.log('💳 Redirecting to booking page - clearing store data to prevent redirect loop');

  // Clear the booking store to prevent automatic redirect back to payment
  store.resetBooking();

  $q.notify({
    type: 'warning',
    message: 'No booking found. Please start the booking process again.',
    position: 'top'
  });

  setTimeout(() => {
    // Force a page reload to ensure clean state
    window.location.href = window.location.origin + window.location.pathname;
  }, 2000);
}

// Debug function to test payment intent creation
async function testPaymentIntent() {
  console.log('🧪 Testing payment intent creation...');
  console.log('🧪 Current state:', {
    eventBooking: eventBooking.value,
    subtotal: subtotal.value,
    pk: pk.value
  });

  try {
    await getPaymentIntent();
    $q.notify({
      type: 'positive',
      message: 'Payment intent test completed! Check console for details.',
      position: 'top'
    });
  } catch (error) {
    console.error('🧪 Payment intent test failed:', error);
    $q.notify({
      type: 'negative',
      message: 'Payment intent test failed. Check console for details.',
      position: 'top'
    });
  }
}

// Methods
function handleChange(event) {
  console.log('💳 Stripe element change:', event);

  // Display any errors
  const displayError = document.getElementById('card-errors');
  if (event.error) {
    displayError.textContent = event.error.message;
  } else {
    displayError.textContent = '';
  }

  // Set payment type
  paymentType.value = 'card';
}

function handleReady(element) {
  console.log('💳 Stripe element ready:', element);
}

// Handle payment method selection change
function onPaymentMethodChange(newMethod) {
  console.log('💳 Payment method changed to:', newMethod);
  selectedPaymentMethod.value = newMethod;
  paymentType.value = newMethod;

  // If switching to card and we have a client secret, initialize Stripe Elements
  if (newMethod === 'card' && stripe.value && elementsOptions.value.clientSecret && !cardElementMounted.value) {
    initializeElements();
  }
}

// Handle BACS payment confirmation
async function confirmBacsPayment() {
  paymentSubmitted.value = true;

  try {
    console.log('💳 Confirming BACS payment for booking:', eventBooking.value.id);

    // Set payment type in the booking
    store.setEventBookingPaymentType('bacs');

    // Update the booking with BACS payment type
    const response = await axios.put(`/event_bookings/${eventBooking.value.id}`, {
      event_booking: {
        payment_type: 'bacs'
      }
    });

    console.log('💳 BACS booking confirmed:', response.data);

    $q.notify({
      type: 'positive',
      message: 'Booking confirmed! Bank transfer details have been sent to your email.',
      position: 'top'
    });

    // Redirect to summary page
    router.push({ name: 'summary' });

  } catch (error) {
    console.error('💳 Error confirming BACS payment:', error);
    paymentSubmitted.value = false;

    $q.notify({
      type: 'negative',
      message: error.response?.data?.error || 'Failed to confirm booking. Please try again.',
      position: 'top'
    });
  }
}

// Go back to booking page to amend details
function goBackToBooking() {
  console.log('💳 Going back to amend booking');

  // Store that we're coming from payment page
  sessionStorage.setItem('returning_from_payment', 'true');
  sessionStorage.setItem('payment_booking_id', eventBooking.value?.id);

  $q.notify({
    type: 'info',
    message: 'Returning to booking page. Your payment will be updated when you save changes.',
    position: 'top'
  });

  // Navigate back to the booking page
  router.push('/');
}

async function getPaymentIntent() {
  try {
    console.log('💳 Getting payment intent for booking:', eventBooking.value?.id);
    console.log('💳 Current booking store state:', {
      eventBooking: eventBooking.value,
      hasBookingId: !!eventBooking.value?.id,
      subtotal: subtotal.value,
      hasPaidTickets: hasPaidTickets.value
    });

    if (!eventBooking.value?.id) {
      console.error('💳 No event booking ID available - cannot initialize payment');
      $q.notify({
        type: 'warning',
        message: 'No booking found. Please complete the booking process first.',
        position: 'top'
      });
      return;
    }

    console.log('💳 Making request to:', '/get_payment_intent/' + eventBooking.value.id);
    const response = await axios.get('/get_payment_intent/' + eventBooking.value.id);
    console.log('💳 Payment intent response:', response.data);

    if (response.data.intent === null) {
      console.log('💳 No existing payment intent, creating new one...');
      await createPaymentIntent();
    } else {
      console.log('💳 Using existing payment intent');
      paymentIntent.value = response.data.intent;
      stripeConnectId.value = response.data.connect_account_id;
      elementsOptions.value.clientSecret = paymentIntent.value.client_secret;

      console.log('💳 Payment intent setup complete:', {
        intentId: paymentIntent.value.id,
        clientSecret: paymentIntent.value.client_secret ? 'present' : 'missing',
        connectAccount: stripeConnectId.value
      });

      // Elements will be initialized automatically by the watcher
    }
  } catch (error) {
    console.error('💳 Error getting payment intent:', error);
    console.error('💳 Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    let errorMessage = 'Failed to initialize payment. Please refresh and try again.';

    if (error.response?.status === 404) {
      errorMessage = 'Booking not found. Please complete the booking process first.';
    } else if (error.response?.status === 500) {
      errorMessage = 'Server error. Please try again in a moment.';
    } else if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
    }

    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'top'
    });
  }
}

async function createPaymentIntent() {
  try {
    console.log('💳 Creating payment intent for booking:', eventBooking.value.id);
    console.log('💳 Current subtotal:', subtotal.value);

    const response = await axios.post('/create_payment_intent/' + eventBooking.value.id, {
      amount: subtotal.value
    });

    console.log('💳 Created payment intent:', response.data);

    paymentIntent.value = response.data.intent;
    stripeConnectId.value = response.data.connect_account_id;
    elementsOptions.value.clientSecret = paymentIntent.value.client_secret;

    console.log('💳 Payment intent created successfully:', {
      intentId: paymentIntent.value.id,
      amount: paymentIntent.value.amount,
      clientSecret: paymentIntent.value.client_secret ? 'present' : 'missing'
    });

    // Elements will be initialized automatically by the watcher
  } catch (error) {
    console.error('💳 Error creating payment intent:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to create payment. Please refresh and try again.',
      position: 'top'
    });
  }
}

async function updatePaymentIntent() {
  try {
    if (!paymentIntent.value?.id) {
      console.log('💳 No payment intent to update, creating new one...');
      await createPaymentIntent();
      return;
    }

    console.log('💳 Updating payment intent:', {
      intentId: paymentIntent.value.id,
      oldAmount: paymentIntent.value.amount,
      newAmount: subtotal.value
    });

    const response = await axios.put('/update_payment_intent/' + paymentIntent.value.id, {
      event_booking_id: eventBooking.value.id,
      amount: subtotal.value
    });

    console.log('💳 Payment intent updated:', response.data);

    paymentIntent.value = response.data.intent;
    elementsOptions.value.clientSecret = paymentIntent.value.client_secret;

    console.log('💳 Payment intent update complete:', {
      intentId: paymentIntent.value.id,
      newAmount: paymentIntent.value.amount
    });
  } catch (error) {
    console.error('💳 Error updating payment intent:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to update payment amount. Please refresh and try again.',
      position: 'top'
    });
  }
}

async function pay() {
  if (Number(subtotal.value) > 0) {
    paymentSubmitted.value = true;

    try {
      console.log('💳 Processing card payment...');

      if (!stripe.value || !elements.value) {
        throw new Error('Stripe not properly initialized');
      }

      // Set payment type to be consumed by summary page
      store.setEventBookingPaymentType(paymentType.value);

      // Confirm the payment with card element
      const { error, paymentIntent: confirmedPaymentIntent } = await stripe.value.confirmCardPayment(
        elementsOptions.value.clientSecret,
        {
          payment_method: {
            card: cardElement.value,
          }
        }
      );

      if (error) {
        console.error('💳 Payment failed:', error);
        handlePayError(error);
      } else if (confirmedPaymentIntent && confirmedPaymentIntent.status === 'succeeded') {
        console.log('💳 Payment succeeded:', confirmedPaymentIntent);
        $q.notify({
          type: 'positive',
          message: 'Payment successful!',
          position: 'top'
        });
        router.push({ name: 'summary' });
      }
    } catch (error) {
      console.error('💳 Payment error:', error);
      handlePayError(error);
    }
  } else {
    // Handle free booking
    cancelPaymentAndCompleteBooking();
  }
}

async function cancelPaymentAndCompleteBooking() {
  try {
    await axios.put('/cancel_payment_intent/' + paymentIntent.value.id, { 
      event_booking_id: eventBooking.value.id 
    });
    
    await axios.put('/complete_booking/' + eventBooking.value.id);
    router.push({ name: 'summary' });
  } catch (error) {
    console.error('Error completing free booking:', error);
  }
}

function handlePayError(error) {
  paymentSubmitted.value = false;
  $q.notify({
    type: 'negative',
    message: error.message || 'Payment failed. Please try again.',
    position: 'top'
  });
}

function ticketCost(ticket) {
  let cost = 0;
  if (eventBooking.value?.early_bird_valid) {
    cost = ticket.cost_a;
  } else {
    cost = ticket.cost_b;
  }
  return (ticket.quantity_tickets * cost).toFixed(2);
}

async function applyDiscountCode() {
  try {
    const response = await axios.put('/discounts/' + event.value.id + '/apply_discount', { 
      event_booking: eventBooking.value, 
      code: discountCode.value, 
      packages: tickets.value.map(x => x.id) 
    });
    
    const details = response.data.discount_details;
    const earlyBird = response.data.discount_details.early_bird;

    // Handle ticket-specific discount
    if (details.discount.event_or_ticket === true) {
      const ticket = tickets.value.find(t => t.id === details.ticket.id);
      if (ticket) {
        const cost = details.new_cost;
        if (earlyBird) {
          ticket.cost_a = details.new_cost;
        } else {
          ticket.cost_b = details.new_cost;
        }
        
        $q.notify({
          color: 'positive',
          message: `${ticket.details} new cost is: £${parseFloat(cost).toFixed(2)}`,
          icon: 'check_circle'
        });

        const pb_discount = response.data.discount_details.package_booking_discount;

        store.setSelectedTickets(tickets.value);
        store.setPackageBookingDiscount(pb_discount);
        discountCode.value = null;
      } else {
        $q.notify({
          color: 'negative',
          message: 'This code does not apply to a ticket you have booked',
          icon: 'error'
        });
      }
    } 
    // Handle event-level discount
    else {
      const eb_discount = response.data.discount_details.event_booking_discount;
      store.setEventBookingDiscount(eb_discount);
      eventDiscount.value = ((Number(subtotal.value) / 100) * details.new_cost);
      
      $q.notify({
        color: 'positive',
        message: `Event's new cost is: £${subtotal.value}`,
        icon: 'check_circle'
      });
    }
    
    if (Number(subtotal.value) > 0) {
      updatePaymentIntent();
    }
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: error.response?.data?.error || 'Error applying discount code',
      icon: 'error'
    });
  }
}
</script>

<style lang="scss" scoped>
.payment-component-wrapper {
  width: 100%;
  max-width: 100%;
  overflow: visible;
  position: relative;
  box-sizing: border-box;
}

.payment-header {
  text-align: center;
  padding: 2rem 1rem;

  .text-h5 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }
}

.payment-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  align-items: stretch; // This makes both columns match height

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    align-items: start; // Reset for mobile
  }
}

// Left side - Order Summary Section
.order-summary-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

// Right side - Payment Details Section
.payment-details-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;

  // Make the main payment card stretch to fill available space
  .modern-card {
    display: flex;
    flex-direction: column;

    &:first-child {
      flex: 1; // This will make the payment card grow to fill space
      min-height: 0; // Allow flex to work properly
    }

    .q-card__section {
      &:last-child {
        flex-grow: 1; // Make the last section grow to fill card
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
}

.modern-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: white;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.card-header {
  border-bottom: 2px solid #f0f0f0;
  padding: 1.5rem 2rem;

  .text-h6 {
    color: #2c3e50;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }
}

// Order Summary Styles
.ticket-items {
  margin-bottom: 1.5rem;
}

.ticket-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.ticket-details {
  flex: 1;
}

.ticket-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.ticket-calculation {
  color: #666;
  font-size: 0.9rem;
}

.price {
  font-weight: 600;
  color: #1976d2;
}

.ticket-total {
  font-weight: 700;
  color: #2c3e50;
  font-size: 1.1rem;
}

.discount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 1rem;
}

.discount-details {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.discount-amount {
  font-weight: 700;
  font-size: 1.1rem;
}

.order-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0 0;
  border-top: 2px solid #e0e0e0;
  margin-top: 1.5rem;
}

.total-label {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
}

.total-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1976d2;
}

// Payment Form Styles
.payment-form {
  margin: 1rem 0;
}

.stripe-element {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
  background: white;
  min-height: 40px;

  &:hover {
    border-color: #1976d2;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
  }

  &:focus-within {
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
  }
}

.stripe-errors {
  color: #f44336;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.payment-loading {
  text-align: center;
  padding: 2rem;
}

.payment-actions {
  padding: 1.5rem 2rem;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.payment-btn {
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Discount Code Styles
.modern-input {
  :deep(.q-field__control) {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1976d2;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
    }
  }

  :deep(.q-field--focused .q-field__control) {
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
  }

  :deep(.q-field__label) {
    color: #666;
    font-weight: 500;
  }

  :deep(.q-field--focused .q-field__label) {
    color: #1976d2;
  }
}

.apply-discount-btn {
  font-weight: 600;
  text-transform: none;
}

// Free Booking Styles
.free-booking-message {
  text-align: center;
  padding: 2rem 1rem;
}

// BACS Payment Styles
.bacs-info {
  .bank-details {
    border: 1px solid #e0e0e0;

    .bank-detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-weight: 600;
        color: #666;
        min-width: 120px;
      }

      .value {
        color: #2c3e50;
        text-align: right;
        flex: 1;
      }
    }
  }
}

// Payment Method Selection
.payment-methods {
  width: 100%;

  :deep(.q-option-group) {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }

  :deep(.q-radio) {
    margin: 0;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 0;

    &:hover {
      border-color: #1976d2;
      background: rgba(25, 118, 210, 0.02);
    }
  }

  :deep(.q-radio--checked) {
    border-color: #1976d2;
    background: rgba(25, 118, 210, 0.05);
  }

  :deep(.q-radio__label) {
    font-weight: 500;
    color: #2c3e50;
    width: 100%;
    text-align: center;
  }
}

// Amend Booking Styles
.amend-booking-btn {
  font-size: 0.875rem;

  @media (max-width: 600px) {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;

    .q-btn__content {
      flex-direction: column;
      gap: 0.25rem;
    }
  }
}

.amend-booking-section {
  .q-btn {
    border-style: dashed;
    border-width: 2px;

    &:hover {
      background: rgba(25, 118, 210, 0.05);
      border-style: solid;
    }
  }
}

// Payment Options Styles
.payment-options {
  :deep(.q-radio) {
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1976d2;
      background: rgba(25, 118, 210, 0.02);
    }
  }

  :deep(.q-radio--checked) {
    border-color: #1976d2;
    background: rgba(25, 118, 210, 0.05);
  }
}

.payment-option-label {
  display: flex;
  align-items: center;
  font-weight: 500;
}

// Responsive Design
@media (max-width: 768px) {
  .payment-header {
    padding: 1rem;
  }

  .card-header {
    padding: 1rem;
  }

  .modern-card .q-card-section {
    padding: 1rem !important;
  }

  .payment-actions {
    padding: 1rem !important;
  }

  .ticket-item {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .ticket-total {
    align-self: flex-end;
  }
}
</style>