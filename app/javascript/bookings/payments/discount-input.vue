<template>
  <q-card class="q-mb-md no-padding">
    <q-card-section class="q-py-sm cursor-pointer" @click="showCodePanel = !showCodePanel">
      <div class="row items-center">
        <q-icon 
          :name="showCodePanel ? 'remove' : 'add'" 
          color="negative" 
          size="sm" 
          class="q-mr-sm"
        />
        <div>Add discount code</div>
      </div>
    </q-card-section>

    <q-slide-transition>
      <q-card-section v-show="showCodePanel">
        <form @submit.prevent="submitCode()" novalidate>
          <q-input
            v-model="discountCode"
            placeholder="Enter Code"
            outlined
            dense
            class="q-mb-xs"
            :error="!valid && submitted"
            :error-message="!valid && submitted ? 'Please add a valid code' : ''"
          >
            <template v-slot:append>
              <q-btn
                type="submit"
                :style="{
                  'background-color': event.buttoncolour,
                  'border-color': event.buttoncolour,
                }"
                color="primary"
                label="Apply"
                dense
              />
            </template>
          </q-input>
          
          <div class="q-mt-sm">
            <div v-if="code && !code.ticket_id" class="text-subtitle2">
              Discount of: {{ code.value }}% will be applied
            </div>
            <div v-if="ticketCodes && ticketCodes.length > 0" class="text-subtitle2">
              Discount applied to ticket, see above for details
            </div>
          </div>
        </form>
      </q-card-section>
    </q-slide-transition>
  </q-card>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
import axios from 'axios';

// Using Quasar's event system instead of custom eventBus
const store = useBookingStore();
const $q = useQuasar();

// State
const valid = ref(false);
const submitted = ref(false);
const discountCode = ref('');
const code = ref(null);
const showCodePanel = ref(false);
const ticketCodes = ref([]);

// Get data from store
const tickets = computed(() => store.getSelectedTickets);
const event = computed(() => store.getEvent);
const bookerDetails = computed(() => store.getBookerDetails);

// Emits
const emit = defineEmits(['show-stripe-form', 'update-fees', 'discount-applied']);

// Methods
async function submitCode() {
  submitted.value = true;

  if (!discountCode.value) {
    $q.notify({
      color: 'negative',
      message: 'Please add a valid code!',
      icon: 'error'
    });
    valid.value = false;
    submitted.value = false;
    return;
  }

  valid.value = true;
  emit('show-stripe-form', false);

  try {
    const response = await axios.put(`/discounts/${event.value.id}/check_valid`, {
      code: discountCode.value,
      packages: tickets.value,
      ticketCodes: ticketCodes.value,
      bookerEmail: bookerDetails.value.email
    });

    const responseCode = response.data.code;

    if (responseCode.ticket_id) {
      // Add discount to specific ticket
      ticketCodes.value.push(responseCode.id);
      
      if (!addDiscountToTicket(responseCode)) {
        $q.notify({
          color: 'negative',
          message: 'Code not valid for selected tickets!',
          icon: 'error'
        });
        emit('show-stripe-form', true);
        return;
      }
      
      // Update fees and booking token
      emit('update-fees', response.data.fees);
      store.setFees(response.data.fees);
      store.setBookingToken(response.data.booking_token);

      // Check if booking is now free
      if (response.data.fees.free_booking) {
        setFreeBooking();
      } else {
        // Emit event for recalculating costs and updating payment method
        $q.bus.emit('getBookingIntent', true, 'card', false);
        
        $q.notify({
          color: 'positive',
          message: 'Code applied successfully!',
          icon: 'check_circle'
        });
      }
    } else {
      // Apply discount to entire booking
      code.value = responseCode;
      
      // Update fees and booking token
      emit('update-fees', response.data.fees);
      store.setFees(response.data.fees);
      store.setBookingToken(response.data.booking_token);
      
      // Emit event for discount code ID
      emit('discount-applied', code.value.id, code.value.value);

      // Check if booking is now free
      if (response.data.fees.free_booking) {
        setFreeBooking();
      } else {
        // Emit event for recalculating costs and updating payment method
        $q.bus.emit('getBookingIntent', true, 'card', false);
        
        $q.notify({
          color: 'positive',
          message: 'Code applied successfully!',
          icon: 'check_circle'
        });
      }
    }
    
    // Clear input field
    discountCode.value = '';
    
  } catch (error) {
    const response = error.response;
    if (response?.data?.errors) {
      response.data.errors.forEach(errorMsg => {
        $q.notify({
          color: 'negative',
          message: errorMsg,
          icon: 'error'
        });
      });
    } else {
      $q.notify({
        color: 'negative',
        message: 'Code could not be applied!',
        icon: 'error'
      });
    }
  } finally {
    emit('show-stripe-form', true);
    submitted.value = false;
  }
}

function setFreeBooking() {
  $q.dialog({
    title: 'Success!',
    message: 'You no longer need to pay for this event!',
    persistent: true,
    ok: {
      color: 'primary',
      label: 'OK'
    }
  });
  emit('discount-applied', null, '100');
}

function addDiscountToTicket(code) {
  const ticket = tickets.value.find(ticket => ticket.id === code.ticket_id);
  if (ticket) {
    ticket.discount_code_id = code.id;
    ticket.discount_type = code.discount_type;
    ticket.discount_amount = code.value;
    return true;
  } else {
    return false;
  }
}
</script>