<template>
  <div v-if="eventBooking" class="row q-col-gutter-md">
    <div class="col-12">
      <div v-if="eventBooking.booking_count > 0" class="summary">
        <q-card class="q-mb-md">
          <q-card-section class="bg-grey-2" :style="underlineOverride">
            <div class="text-h6">Thank you for booking your tickets for this Event.</div>
          </q-card-section>
          <q-card-section>
            <div>
              <p>If you wish to make any other changes, please contact your event organiser</p>
              <p>You can view your booking by clicking below:</p>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn
              :style="{
                'background-color': event.buttoncolour,
                'border-color': event.buttoncolour,
              }"
              color="primary"
              :href="'mailto:' + event.organiser_email + '?Subject=Query%20about%20' + event.title"
              label="Email Organiser"
              no-caps
            />
            <q-btn
              v-if="orgWebsite"
              :style="{
                'background-color': event.buttoncolour,
                'border-color': event.buttoncolour,
              }"
              color="primary"
              :href="orgWebsite"
              label="Back"
              no-caps
            />
          </q-card-actions>
        </q-card>
        
        <q-card class="q-mb-md">
          <q-card-section class="bg-grey-2" :style="underlineOverride">
            <div class="text-h6">Click below to cancel your event booking</div>
          </q-card-section>
          <q-card-section>
            <div>
              <p>Terms and Conditions will apply to cancellations</p>
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn
              :style="{
                'background-color': event.buttoncolour,
                'border-color': event.buttoncolour,
              }"
              color="primary"
              @click="cancelBooking()"
              label="Cancel My Booking"
              no-caps
            />
          </q-card-actions>
        </q-card>
      </div>

      <q-card v-if="eventBooking.cancelled_at" class="q-mb-md">
        <q-card-section class="text-center bg-negative text-white">
          <div class="text-h6">This Event Booking Has Been Cancelled</div>
        </q-card-section>
      </q-card>
    </div>

    <div v-if="eventBooking.payment_type='bacs'" class="col-12">
      <q-card class="q-mb-md">
        <q-card-section class="bg-grey-2" :style="underlineOverride">
          <div class="text-h6">BACS payments usually take 3-5 days to process</div>
        </q-card-section>
        <q-card-section>
          Once payment is successfully processed, we will send your confirmation email with your tickets.
        </q-card-section>
      </q-card>
    </div>

    <div class="col-12">
      <q-card class="q-mb-md">
        <q-card-section class="bg-grey-2" :style="underlineOverride">
          <div class="text-h6">Share this event</div>
        </q-card-section>
        <q-card-section>
          <social-circles :event="event"></social-circles>
        </q-card-section>
      </q-card>
      
      <q-card v-if="event.acc_enabled" class="q-mb-md">
        <q-card-section class="bg-grey-2" :style="underlineOverride">
          <div class="text-h6">Click below to book accommodation</div>
        </q-card-section>
        <q-card-actions align="center">
          <q-btn
            :style="{
              'background-color': event.buttoncolour,
              'border-color': event.buttoncolour,
            }"
            color="primary"
            href="https://www.booking.com/index.en-gb.html?aid=1593432"
            target="_blank"
            label="Book Accommodation"
            no-caps
          />
        </q-card-actions>
      </q-card>
    </div>

    <div class="col-12">
      <q-card class="q-mb-md">
        <q-card-section class="bg-grey-2" :style="underlineOverride">
          <div class="text-h6">Tickets Booked</div>
        </q-card-section>
        <q-card-section>
          <q-table
            flat
            bordered
            :rows="tickets.slice().sort((a, b) => a.id - b.id)"
            :columns="ticketColumns"
            :pagination="{rowsPerPage: 0}"
            hide-pagination
            hide-bottom
          >
            <template v-slot:header="props">
              <q-tr :props="props">
                <q-th v-if="event.ticket_groups">Group Name</q-th>
                <q-th>Ticket Name</q-th>
                <q-th v-if="hasPaidTickets">Ticket Cost {{ showVatInTitle }}</q-th>
                <q-th v-if="hasPaidTickets && event.show_vat && event.vat_exclusive">
                  Ticket VAT Amount
                </q-th>
                <q-th>No of Tickets</q-th>
                <q-th>Date & Time</q-th>
                <q-th></q-th>
              </q-tr>
            </template>

            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td v-if="event.ticket_groups">
                  <strong v-if="ticketHeaderName(props.row)">
                    {{ ticketHeaderName(props.row) }}
                  </strong>
                </q-td>
                <q-td>{{ props.row.details }}</q-td>
                <q-td v-if="hasPaidTickets">
                  {{ formatCurrency(ticketCost(props.row, discountValid, true)) }}
                  <div class="text-caption" v-if="discountTicketText(props.row)">
                    {{ discountTicketText(props.row) }}
                  </div>
                </q-td>
                <q-td v-if="hasPaidTickets && event.show_vat && event.vat_exclusive">
                  {{ formatCurrency(ticketVatExc(props.row, discountValid, true)) }}
                </q-td>
                <q-td>{{ props.row.quantity_tickets }}</q-td>
                <q-td>
                  <span v-html="ticketTimeAndDate(props.row)"></span>
                </q-td>
                <q-td>
                  <q-btn
                    :style="{
                      'background-color': event.buttoncolour,
                      'border-color': event.buttoncolour,
                    }"
                    color="primary"
                    size="sm"
                    @click="exportCalendar(props.row.id)"
                    icon="fa fa-calendar"
                    label="Add To Calendar"
                    no-caps
                  />
                </q-td>
              </q-tr>
            </template>

            <template v-slot:bottom v-if="hasPaidTickets">
              <q-tr :style="actualCostBG" class="text-white">
                <q-td colspan="2">TICKET TOTAL:</q-td>
                <q-td v-if="eventBooking.discount_code_id">
                  {{ formatCurrency(totalCostWithDiscount(totalCost(tickets, discountValid, true), eventBooking)) }} 
                  After Discount of {{ parseInt(eventBooking.discount_percentage) }}% Applied
                </q-td>
                <q-td v-else>
                  {{ formatCurrency(totalCost(tickets, discountValid, true)) }}
                </q-td>
                <q-td v-if="event.vat_exclusive">
                  {{ formatCurrency((totalVatExc(tickets, discountValid, true) * 100)) }}
                </q-td>
                <q-td colspan="2"></q-td>
              </q-tr>
            </template>
          </q-table>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            :style="{
              'background-color': event.buttoncolour,
              'border-color': event.buttoncolour,
            }"
            color="primary"
            @click="exportCalendar()"
            icon="fa fa-calendar"
            label="Add All To Calendar"
            no-caps
          />
        </q-card-actions>
      </q-card>
    </div>

    <div v-if="hasPayments && paidEvent" class="col-12">
      <q-card class="q-mb-md">
        <q-card-section class="bg-grey-2" :style="underlineOverride">
          <div class="text-h6">Payments Summary</div>
        </q-card-section>

        <q-card-section>
          <q-table
            flat
            bordered
            :rows="paymentsMade"
            :columns="paymentColumns"
            :pagination="{rowsPerPage: 0}"
            hide-pagination
            hide-bottom
          >
            <template v-slot:body-cell-amount="props">
              <q-td :props="props" :class="{ 'text-strike text-grey': props.row.refunded }">
                {{ formatCurrency(props.value) }}
              </q-td>
            </template>
            <template v-slot:body-cell-discount_amount="props">
              <q-td :props="props">
                {{ formatCurrency(props.value) }}
              </q-td>
            </template>
            <template v-slot:body-cell-total_fees="props">
              <q-td :props="props">
                {{ formatCurrency(props.value) }}
              </q-td>
            </template>
            <template v-slot:body-cell-payment_datetime="props">
              <q-td :props="props">
                {{ formatDate(props.value) }}
              </q-td>
            </template>
            <template v-slot:body-cell-payment_type="props">
              <q-td :props="props">
                {{ props.value == "stripe" ? "Credit/Debit Card" : props.value }}
              </q-td>
            </template>
            <template v-slot:body-cell-refunded="props">
              <q-td :props="props">
                {{ props.value ? "Yes" : "No" }}
              </q-td>
            </template>
          </q-table>
        </q-card-section>
      </q-card>
    </div>

    <div v-if="fees && fees.free_booking && hasPaidTickets" class="col-12">
      <q-card class="q-mb-md">
        <q-card-section class="bg-grey-2" :style="underlineOverride">
          <div class="text-h6">Free Booking</div>
        </q-card-section>

        <q-card-section>
          The discount code was successful giving you a free booking for this event!
        </q-card-section>
      </q-card>
    </div>

    <div class="col-12">
      <org-events :event="event"></org-events>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, onBeforeRouteLeave } from 'vue-router';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';
import axios from 'axios';
// Removed lodash-es dependency - using native JavaScript sorting instead
import orgEventsVue from '../../common/org-events.vue';
import socialCircles from '../common/social-circles.vue';
import { useBookingStore } from '@/shared/stores/bookingStore.js';
// Import the cost-calcs utils (converted from mixin)
import costCalcUtils from '../common/cost-calcs';

// Component setup
const router = useRouter();
const $q = useQuasar();

// Setup stores
const bookingStore = useBookingStore();

// Store derived values
const event = computed(() => bookingStore.getEvent);
const hasPaidTickets = computed(() => bookingStore.getChargeable);
const fees = computed(() => bookingStore.getFees);
const orgWebsite = computed(() => bookingStore.getExtWebsite);
const eventBooking = computed(() => bookingStore.getEventBooking);
const tickets = computed(() => bookingStore.getSelectedTickets || []);

// Route guard equivalent for Vue 3
onBeforeRouteLeave((to, from, next) => {
  // Prevents going back to payments, need to ensure doesn't break other areas
  next(false);
});

// State
const paymentSubmitted = ref(false);
const paymentsMade = ref([]);
const paymentStatus = ref(null);

// Computed properties
const showVatInTitle = computed(() => {
  if (event.value.show_vat) {
    return "(" + (event.value.vat_exclusive ? "exc" : "inc") + " VAT)";
  } else {
    return "";
  }
});

const paidEvent = computed(() => {
  if (paymentStatus.value == "unpaid" || paymentStatus.value == "refunded") {
    return false;
  } else {
    return true;
  }
});

const underlineOverride = computed(() => {
  return {
    borderBottom: "4px solid " + event.value.phcolour,
  };
});

const actualCostBG = computed(() => {
  return { backgroundColor: event.value.phcolour };
});

const iconColor = computed(() => {
  return {
    color: event.value.phcolour,
  };
});

const hasPayments = computed(() => {
  return paymentsMade.value.length > 0;
});

const discountValid = computed(() => {
  if (event.value && event.value.datetime_eb) {
    return dayjs(event.value.datetime_eb).isAfter(new Date());
  }
  return false;
});

const refundable = computed(() => {
  const status = eventBooking.value.payment_status;
  if (status == "paid" || status == "part_paid") {
    return true;
  }
  return false;
});

// Dynamic columns
const ticketColumns = computed(() => {
  return [
    ...(event.value.ticket_groups ? [{ name: 'group', label: 'Group Name', field: 'ticket_group_id' }] : []),
    { name: 'details', label: 'Ticket Name', field: 'details' },
    ...(hasPaidTickets.value ? [{ name: 'cost', label: `Ticket Cost ${showVatInTitle.value}`, field: 'cost_a' }] : []),
    ...(hasPaidTickets.value && event.value.show_vat && event.value.vat_exclusive ? 
      [{ name: 'vat', label: 'Ticket VAT Amount', field: 'vat_amount' }] : []),
    { name: 'quantity', label: 'No of Tickets', field: 'quantity_tickets' },
    { name: 'datetime', label: 'Date & Time', field: 'start_time' },
    { name: 'actions', label: '', field: 'id' }
  ];
});

const paymentColumns = computed(() => {
  return [
    { name: 'amount', label: 'Total Amount Paid', field: 'amount' },
    ...(eventBooking.value.discount_code_id ? [{ name: 'discount_amount', label: 'Discounted By', field: 'discount_amount' }] : []),
    ...(event.value.fees_pass_on ? [{ name: 'total_fees', label: 'Total Fees', field: 'total_fees' }] : []),
    ...(event.value.show_vat && event.value.vat_exclusive ? [{ name: 'vat_amount', label: 'Total VAT Amount', field: 'vat_amount' }] : []),
    { name: 'payment_datetime', label: 'Payment Date', field: 'payment_datetime' },
    { name: 'payment_type', label: 'Payment Type', field: 'payment_type' },
    { name: 'refunded', label: 'Refunded', field: 'refunded' }
  ];
});

// Apply cost calculation methods from the imported utils
// Assuming these are the methods from your costCalcs mixin
const {
  ticketCost,
  ticketVatExc,
  totalCost,
  totalCostWithDiscount,
  totalVatExc
} = costCalcUtils;

// Methods
const formatCurrency = (value, symbol = '£') => {
  if (typeof value !== 'number') {
    value = parseFloat(value);
  }
  return symbol + value.toFixed(2);
};

const formatDate = (value) => {
  if (value) {
    return dayjs(value).format('DD/MM/YYYY');
  }
  return '';
};

const getPaymentsMade = () => {
  const urlWithID = `/payments/${eventBooking.value.uuid}/get_payments/`;
  
  axios
    .get(urlWithID)
    .then((response) => {
      paymentsMade.value = response.data.payments;
      paymentStatus.value = response.data.payment_status;
    })
    .catch((e) => {
      $q.notify({
        color: 'negative',
        message: 'Error getting payments',
        icon: 'error'
      });
    });
};

const discountTicketText = (ticket) => {
  if (ticket.discount_code_id) {
    if (ticket.discount_type == "percentage") {
      return "Discount: ( " + ticket.discount_amount + "% )";
    } else {
      return "Discount: ( £" + ticket.discount_amount + " )";
    }
  } else {
    return "";
  }
};

const ticketHeaderName = (ticket) => {
  let groupDesc = null;
  if (event.value.ticket_groups) {
    // forEach won't return or break
    for (let i = 0; i < event.value.ticket_groups.length; i++) {
      const tg = event.value.ticket_groups[i];
      if (tg.id == ticket.ticket_group_id) {
        groupDesc = tg.description;
        break;
      }
    }
  }
  return groupDesc;
};

const ticketTimeAndDate = (ticket) => {
  let startTime = null;
  let endTime = null;

  if (ticket.start_time) {
    const sdt = new Date(ticket.start_time);
    startTime = dayjs(sdt).format("DD/MM/YYYY HH:mm A");
  }

  if (ticket.end_time) {
    const edt = new Date(ticket.end_time);
    endTime = dayjs(edt).format("DD/MM/YYYY HH:mm A");
  }

  if (startTime && endTime) {
    return `${startTime} <br/> ${endTime}`;
  } else if (startTime && !endTime) {
    return startTime;
  } else {
    return "";
  }
};

const exportCalendar = (ticket_id = null) => {
  $q.dialog({
    title: "Would you like to download details of your event?",
    message: ".ics files can be imported into your calendar.",
    cancel: true,
    persistent: true
  }).onOk(() => {
    const config = {
      responseType: "blob",
    };
    
    axios.post(`/ics_export/${eventBooking.value.uuid}`, { ticket_id: ticket_id }, config).then(
      function(response) {
        forceFileDownload(response);
      },
      function(error) {
        $q.notify({
          color: 'negative',
          message: 'Problem downloading ics file',
          icon: 'error'
        });
      }
    );
  });
};

const forceFileDownload = (response) => {
  console.log(response);
  const url = window.URL.createObjectURL(new Blob([response.data], { type: "text/calendar" }));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", `${event.value.title}.ics`);
  document.body.appendChild(link);
  link.click();
};

const cancelBooking = () => {
  $q.dialog({
    title: "Are you sure?",
    message: "This will cancel the booking!",
    cancel: true,
    persistent: true
  }).onOk(() => {
    const uuid = eventBooking.value.uuid;
    
    axios.put(`/event_bookings/${uuid}/cancel`).then(
      function() {
        bookingStore.cancelBooking();
        $q.notify({
          color: 'positive',
          message: 'The booking has been cancelled',
          icon: 'check_circle'
        });
      },
      function() {
        $q.notify({
          color: 'negative',
          message: 'The booking has not been cancelled',
          icon: 'error'
        });
      }
    );
  });
};

// Setup logic (equivalent to created lifecycle hook)
onMounted(() => {
  getPaymentsMade();
});
</script>

<style>
.icon-styling {
  float: right;
  padding-top: 5px;
  font-size: large;
}

.text-strike {
  text-decoration: line-through;
}
</style>
