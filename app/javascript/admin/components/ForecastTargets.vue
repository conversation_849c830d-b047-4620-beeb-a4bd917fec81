<template>
  <div class="col-12">
    <div class="wrapper">
      <q-card class="q-mb-md">
        <q-card-section>
          <label for="yeardd">Select Target Year</label>
          <q-select
            v-if="selectedYear"
            @update:model-value="changeYearData"
            id="yearTarget"
            name="yearTarget"
            v-model="selectedYear"
            :options="targetYears"
            option-label="description"
            option-value="id"
            map-options
            emit-value
            outlined
            dense
            style="max-width: 300px"
          />
        </q-card-section>
      </q-card>

      <q-form @submit="validateForecastTargets" class="q-gutter-md">
        <q-card>
          <q-table
            :rows="tableRows"
            :columns="columns"
            row-key="month"
            flat
            bordered
            hide-pagination
            :pagination="{ rowsPerPage: 0 }"
          >
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td key="month" :props="props">{{ props.row.label }}</q-td>
                <q-td key="target" :props="props">
                  <q-input
                    type="number"
                    v-model="forecastTarget[props.row.field]"
                    dense
                    outlined
                    :error="!!errors[props.row.field]"
                    :error-message="errors[props.row.field]"
                  />
                </q-td>
              </q-tr>
            </template>
          </q-table>
          
          <q-card-actions align="right" class="q-pa-md">
            <q-btn 
              color="primary" 
              label="Update Forecast" 
              type="submit"
            />
          </q-card-actions>
        </q-card>
      </q-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';

// Initialize Quasar
const $q = useQuasar();

// Component data
const targetData = ref({});
const forecastTarget = reactive({});
const targetYears = ref(null);
const selectedYear = ref(null);
const forecastActual = ref(null);
const errors = reactive({});

// Define months array for table rows
const months = [
  { label: 'April', field: 'april' },
  { label: 'May', field: 'may' },
  { label: 'June', field: 'june' },
  { label: 'July', field: 'july' },
  { label: 'August', field: 'august' },
  { label: 'September', field: 'september' },
  { label: 'October', field: 'october' },
  { label: 'November', field: 'november' },
  { label: 'December', field: 'december' },
  { label: 'January', field: 'january' },
  { label: 'February', field: 'february' },
  { label: 'March', field: 'march' }
];

// Define table columns
const columns = [
  { name: 'month', align: 'left', label: 'Month', field: 'label', sortable: true },
  { name: 'target', align: 'left', label: 'Target Revenue', field: 'field' }
];

// Create a computed property for table rows
const tableRows = computed(() => {
  return months;
});

// Methods
const changeYearData = async () => {
  try {
    const response = await axios.get('/target_years/' + selectedYear.value);
    Object.assign(forecastTarget, response.data.forecast_target);
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'The targets for this year could not be retrieved',
      icon: 'error'
    });
  }
};

const validateField = (field, value) => {
  if (!value || value === '') {
    errors[field] = 'This field is required';
    return false;
  }
  
  const numValue = parseFloat(value);
  if (isNaN(numValue) || numValue <= 0) {
    errors[field] = 'Please enter a positive number';
    return false;
  }
  
  // Check if it has maximum 2 decimal places
  const decimalPlaces = (value.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    errors[field] = 'Please enter a number with maximum 2 decimal places';
    return false;
  }
  
  return true;
};

const validateForecastTargets = () => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key]);
  
  let isValid = true;
  
  months.forEach(month => {
    if (!validateField(month.field, forecastTarget[month.field])) {
      isValid = false;
    }
  });
  
  if (!isValid) {
    $q.notify({
      color: 'negative',
      message: 'Please ensure you add numeric values to all fields',
      icon: 'warning'
    });
    return;
  }
  
  saveForecastTargets();
};

const saveForecastTargets = async () => {
  forecastTarget.target_year_id = selectedYear.value;
  
  try {
    let response;
    
    if (forecastTarget.id) {
      response = await axios.put('/target_years/' + forecastTarget.id, {
        forecast_target: forecastTarget
      });
    } else {
      response = await axios.post('/target_years', {
        forecast_target: forecastTarget
      });
    }
    
    Object.assign(forecastTarget, response.data.forecast_target);
    
    $q.notify({
      color: 'positive',
      message: 'The targets have been updated!',
      icon: 'check_circle'
    });
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'The targets could not be updated!',
      icon: 'error'
    });
  }
};

// Fetch data on mount
onMounted(async () => {
  try {
    const response = await axios.get('/target_years');
    targetData.value = response.data.forecast_data;
    targetYears.value = targetData.value.target_years;
    Object.assign(forecastTarget, targetData.value.forecast_target || {});
    selectedYear.value = targetData.value.current_year;
  } catch (error) {
    console.error('Error fetching target years:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load target years',
      icon: 'error'
    });
  }
});
</script>