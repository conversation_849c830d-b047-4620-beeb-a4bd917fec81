<template>
  <div class="row">
    <div class="col-12 mx-auto">
      <q-card class="q-mb-md">
        <q-card-section class="text-h6 hg-underline">
          Audits
        </q-card-section>

        <q-card-section>
          <q-table
            :rows="audits"
            :columns="columns"
            row-key="id"
            flat
            bordered
            :pagination="{ rowsPerPage: 10 }"
          >
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td key="item_id" :props="props">{{ props.row.item_id }}</q-td>
                <q-td key="item_type" :props="props">{{ props.row.item_type }}</q-td>
                <q-td key="event" :props="props">{{ props.row.event }}</q-td>
                <q-td key="whodunnit" :props="props">{{ props.row.whodunnit }}</q-td>
                <q-td key="object" :props="props">
                  <q-btn 
                    v-if="props.row.object" 
                    color="primary" 
                    @click="showAuditObject(props.row)" 
                    size="sm"
                    label="Info"
                  />
                </q-td>
                <q-td key="created_at" :props="props">{{ formatDate(props.row.created_at) }}</q-td>
                <q-td key="ip" :props="props">{{ props.row.ip }}</q-td>
                <q-td key="user_agent" :props="props">{{ props.row.user_agent }}</q-td>
              </q-tr>
            </template>
          </q-table>

          <q-dialog v-model="dialogVisible[selectedAudit?.id]" v-if="selectedAudit">
            <q-card style="min-width: 350px">
              <q-card-section class="row items-center q-pb-none">
                <div class="text-h6">Object Details - ID: {{ selectedAudit.id }}</div>
                <q-space />
                <q-btn icon="close" flat round dense v-close-popup />
              </q-card-section>

              <q-card-section>
                <pre style="white-space: pre-wrap; word-break: break-word;">{{ selectedAudit.object }}</pre>
              </q-card-section>

              <q-card-actions align="right">
                <q-btn flat label="Close" color="primary" v-close-popup />
              </q-card-actions>
            </q-card>
          </q-dialog>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import axios from 'axios';
import dayjs from 'dayjs';

// Component data
const audits = ref([]);
const dialogVisible = reactive({});
const selectedAudit = ref(null);

// Define table columns
const columns = [
  { name: 'item_id', align: 'left', label: 'Audit Item ID', field: 'item_id', sortable: true },
  { name: 'item_type', align: 'left', label: 'Audit Item Type', field: 'item_type', sortable: true },
  { name: 'event', align: 'left', label: 'Audit Event', field: 'event', sortable: true },
  { name: 'whodunnit', align: 'left', label: 'Audit UserID', field: 'whodunnit', sortable: true },
  { name: 'object', align: 'center', label: 'Audit Object', field: 'object' },
  { name: 'created_at', align: 'left', label: 'Audit Created At', field: 'created_at', sortable: true },
  { name: 'ip', align: 'left', label: 'Audit IP', field: 'ip', sortable: true },
  { name: 'user_agent', align: 'left', label: 'Audit User Agent', field: 'user_agent', sortable: true }
];

// Methods
const showAuditObject = (audit) => {
  selectedAudit.value = audit;
  dialogVisible[audit.id] = true;
};

const formatDate = (date) => {
  if (!date) return '';
  return dayjs(date).format('DD/MM/YYYY HH:mm');
};

// Fetch audit data
onMounted(async () => {
  try {
    const response = await axios.get("/get_audits/");
    audits.value = response.data;
    
    // Initialize dialog visibility state for each audit
    audits.value.forEach(audit => {
      dialogVisible[audit.id] = false;
    });
  } catch (error) {
    console.error('Error fetching audit data:', error);
  }
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 1px solid #e0e0e0;
}
</style>