<template>
  <div class="col-md-12">
    <q-card class="q-mb-md">
      <q-card-section id="title_fee_overrides" class="text-h6 hg-underline">
        Fees Overrides
      </q-card-section>
      
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col">
            <div class="q-mb-md">
              <label id="title_org_id" class="q-mb-sm block">Organisation ID</label>

              <q-select
                @update:model-value="getEvents"
                id="org_id"
                v-model="overrideForm.organisation_id"
                :options="organisationOptions"
                option-label="text"
                option-value="value"
                map-options
                emit-value
                outlined
                dense
                placeholder="Choose Organisation"
              >
                <template v-slot:prepend>
                  <q-icon name="fa fa-building" />
                </template>
              </q-select>
            </div>

            <div class="q-mb-md">
              <label id="title_event_id" class="q-mb-sm block">Event ID</label>

              <q-select
                :disable="!overrideForm.organisation_id && !events.length"
                id="ev_id"
                v-model="overrideForm.event_id"
                :options="events"
                option-label="name"
                option-value="id"
                outlined
                dense
                placeholder="Choose Organisation First"
              >
                <template v-slot:prepend>
                  <q-icon name="fa fa-clipboard" />
                </template>
              </q-select>
            </div>
          </div>

          <div class="col">
            <div class="q-mb-md">
              <label id="title_app_perc" class="q-mb-sm block">Application Fee Percentage</label>

              <q-input
                id="fee_percent"
                type="number"
                min="0"
                max="99.99"
                maxlength="5"
                v-model="overrideForm.hg_fees"
                placeholder="Application Fee %"
                outlined
                dense
              >
                <template v-slot:prepend>
                  <q-icon name="fa fa-money" />
                </template>
              </q-input>
            </div>

            <div class="q-mb-md">
              <label id="title_fee_add" class="q-mb-sm block">Application Fee Additional</label>
              
              <q-input
                id="fee_extra"
                type="number"
                min="0"
                max="100"
                maxlength="5"
                v-model="overrideForm.hg_fees_add"
                placeholder="Application Fee Additional"
                outlined
                dense
              >
                <template v-slot:prepend>
                  <q-icon name="fa fa-plus-square" />
                </template>
              </q-input>
            </div>
          </div>

          <div class="col flex items-center">
            <q-btn
              id="save_fee"
              color="primary"
              @click="saveOverrides"
              label="Save Fee Overrides"
            />
          </div>
        </div>
      </q-card-section>

      <q-table
        id="fee_table"
        :rows="overrides"
        :columns="columns"
        row-key="id"
        flat
        bordered
        :pagination="{ rowsPerPage: 0 }"
        v-if="overrides.length > 0"
      >
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td key="organisation" :props="props">
              {{ props.row.organisation.name }} ({{ props.row.organisation.parent_id }})
            </q-td>
            <q-td key="event_id" :props="props">{{ props.row.event_id }}</q-td>
            <q-td key="hg_fees" :props="props">{{ props.row.hg_fees }}</q-td>
            <q-td key="hg_fees_add" :props="props">{{ props.row.hg_fees_add }}</q-td>
            <q-td key="hg_fee_cap" :props="props">{{ props.row.hg_fee_cap }}</q-td>
            <q-td key="actions" :props="props">
              <q-btn
                color="negative"
                size="sm"
                @click="deleteOverrides(props.row)"
                label="Delete Overrides"
              />
            </q-td>
          </q-tr>
        </template>
      </q-table>

      <q-card-section v-if="overrides.length === 0" class="text-center">
        <strong>Please Add Fees</strong>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';

// Initialize Quasar
const $q = useQuasar();

// Component data
const overrides = ref([]);
const overrideForm = reactive({});
const events = ref([]);
const organisationOptions = ref([{ text: 'All Organisations', value: 0 }]);

// Define table columns
const columns = [
  { name: 'organisation', align: 'left', label: 'Organisation', field: row => row.organisation.name, sortable: true },
  { name: 'event_id', align: 'left', label: 'Event ID', field: 'event_id', sortable: true },
  { name: 'hg_fees', align: 'left', label: 'HG Fees %', field: 'hg_fees', sortable: true },
  { name: 'hg_fees_add', align: 'left', label: 'HG Fees Additional', field: 'hg_fees_add', sortable: true },
  { name: 'hg_fee_cap', align: 'left', label: 'HG Fees Cap', field: 'hg_fee_cap', sortable: true },
  { name: 'actions', align: 'center', label: 'Delete', field: 'actions' }
];

// Methods
const getEvents = async (orgId) => {
  try {
    const response = await axios.get('/hg_admin/' + orgId + '/get_org_events');
    events.value = response.data.events;
  } catch (error) {
    console.error('Error fetching events:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load events',
      icon: 'error'
    });
  }
};

const checkInValidPercent = (feeAmount) => {
  if (feeAmount == undefined || feeAmount < 0.01 || feeAmount > 99.99) {
    return true;
  }
  return false;
};

const deleteOverrides = async (override) => {
  try {
    $q.dialog({
      title: 'Delete Fee Overrides',
      message: 'Are you sure you wish to delete these fee overrides?',
      cancel: true,
      persistent: true
    }).onOk(async () => {
      try {
        await axios.delete("/fee_overrides/" + override.id + ".json");
        
        $q.notify({
          color: 'positive',
          message: 'Fees Deleted Successfully!',
          icon: 'check_circle'
        });
        
        const overIndex = overrides.value.indexOf(override);
        if (overIndex > -1) {
          overrides.value.splice(overIndex, 1);
        }
        
        // Reset the form
        Object.keys(overrideForm).forEach(key => delete overrideForm[key]);
      } catch (error) {
        $q.notify({
          color: 'negative',
          message: 'Please Refresh Page and Try again',
          icon: 'error'
        });
      }
    });
  } catch (error) {
    console.error('Error in deleteOverrides:', error);
  }
};

const saveOverrides = async () => {
  try {
    $q.dialog({
      title: 'Are you sure you wish to change the fees charged?',
      message: 'This will set the fees charged going forward!',
      cancel: true,
      persistent: true
    }).onOk(async () => {
      if (
        checkInValidPercent(overrideForm.hg_fees) ||
        checkInValidPercent(overrideForm.hg_fees_add)
      ) {
        $q.notify({
          color: 'negative',
          message: 'Please enter valid fee amounts!',
          icon: 'error'
        });
        return;
      }
      
      try {
        const response = await axios.post('/fee_overrides', {
          fee_override: overrideForm
        });
        
        $q.notify({
          color: 'positive',
          message: 'Fees Saved Successfully!',
          icon: 'check_circle'
        });
        
        overrides.value = response.data;
        
        // Reset the form
        Object.keys(overrideForm).forEach(key => delete overrideForm[key]);
      } catch (error) {
        if (error.response?.data?.messages) {
          error.response.data.messages.forEach(message => {
            $q.notify({
              color: 'negative',
              message: message,
              icon: 'error'
            });
          });
        } else {
          $q.notify({
            color: 'negative',
            message: 'Fees not Saved!',
            icon: 'error'
          });
        }
      }
    });
  } catch (error) {
    console.error('Error in saveOverrides:', error);
  }
};

// Fetch initial data
onMounted(async () => {
  try {
    const [overridesResponse, orgsResponse] = await Promise.all([
      axios.get("/get_fee_overrides"),
      axios.get('/hg_admin/get_org_details.json')
    ]);
    
    overrides.value = overridesResponse.data;
    
    orgsResponse.data.org_filter.forEach((org) => {
      organisationOptions.value.push({
        text: org.name,
        value: org.id,
      });
    });
  } catch (error) {
    console.error('Error fetching initial data:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load initial data',
      icon: 'error'
    });
  }
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 1px solid #e0e0e0;
}

.block {
  display: block;
}
</style>