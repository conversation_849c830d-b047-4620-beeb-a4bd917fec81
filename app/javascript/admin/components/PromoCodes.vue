<template>
  <div class="row">
    <div class="col-12">
      <q-card class="q-mb-md">
        <q-card-section class="text-h6 hg-underline">
          Manage Discount Codes
        </q-card-section>

        <q-card-section>
          <div class="col-md-6 col-12">
            <q-form @submit="submit" class="q-gutter-md">
              <div class="col-12">
                <autocomplete 
                  label="Organisation" 
                  :isAsync="false" 
                  :items="orgSearch" 
                  :inLine="false"
                  @typeahead="onTypeahead"
                  @auto-selected="onAutoSelected"
                ></autocomplete>

                <q-input
                  v-model="codeDetails.description"
                  label="Description"
                  id="description"
                  maxlength="30"
                  :error="!!errors.description"
                  :error-message="errors.description"
                  outlined
                  class="q-my-sm"
                />

                <q-input
                  v-model="codeDetails.code"
                  label="Discount Code"
                  id="discount_code"
                  maxlength="20"
                  :error="!!errors.code"
                  :error-message="errors.code"
                  outlined
                  class="q-my-sm"
                />
              </div>
              
              <div class="col-12">
                <q-input
                  v-model.number="codeDetails.amount"
                  label="Amount"
                  id="discount_amount"
                  :error="!!errors.amount"
                  :error-message="errors.amount"
                  outlined
                  class="q-my-sm"
                  type="number"
                >
                  <template v-slot:prepend>
                    <q-icon name="percent" />
                  </template>
                </q-input>
              </div>

              <q-btn id="disc_button" color="primary" type="submit" label="Save Discount Code" />
            </q-form>
          </div>
        </q-card-section>
      </q-card>

      <q-card>
        <q-card-section id="header_exs_codes" class="text-h6 hg-underline">
          Existing Codes
        </q-card-section>

        <q-card-section>
          <q-table
            id="code_table"
            :rows="codes"
            :columns="columns"
            row-key="id"
            flat
            bordered
          >
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td key="code" :props="props">{{ props.row.code }}</q-td>
                <q-td key="organisation" :props="props">
                  {{ props.row.organisation ? props.row.organisation : 'n/a' }}
                </q-td>
                <q-td key="description" :props="props">{{ props.row.description }}</q-td>
                <q-td key="amount" :props="props">{{ props.row.amount }} %</q-td>
                <q-td key="actions" :props="props">
                  <q-btn color="negative" size="sm" @click="removeCode(props.row.id, props.rowIndex)" label="Delete Code" />
                </q-td>
              </q-tr>
            </template>
          </q-table>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';
import Autocomplete from '@/common/autocomplete.vue';

// Initialize Quasar
const $q = useQuasar();

// Component data
const codes = ref([]);
const codeDetails = reactive({
  discount_type: 'percentage'
});
const submitted = ref(false);
const orgSearch = ref([]);
const errors = reactive({});

// Define table columns
const columns = [
  { name: 'code', align: 'left', label: 'Code', field: 'code', sortable: true },
  { name: 'organisation', align: 'left', label: 'Organisation', field: 'organisation', sortable: true },
  { name: 'description', align: 'left', label: 'Description', field: 'description', sortable: true },
  { name: 'amount', align: 'left', label: 'Amount', field: 'amount', sortable: true },
  { name: 'actions', align: 'center', label: 'Actions', field: 'actions' }
];

// Methods
const validateForm = () => {
  // Clear errors
  Object.keys(errors).forEach(key => delete errors[key]);
  let isValid = true;
  
  // Validate description
  if (!codeDetails.description) {
    errors.description = 'Description is required';
    isValid = false;
  }
  
  // Validate code
  if (!codeDetails.code) {
    errors.code = 'Discount code is required';
    isValid = false;
  }
  
  // Validate amount
  if (!codeDetails.amount) {
    errors.amount = 'Amount is required';
    isValid = false;
  } else {
    const amount = parseFloat(codeDetails.amount);
    if (isNaN(amount)) {
      errors.amount = 'Amount must be a number';
      isValid = false;
    } else if (amount < 0.1) {
      errors.amount = 'Amount must be at least 0.1%';
      isValid = false;
    } else if (amount > 100) {
      errors.amount = 'Amount cannot exceed 100%';
      isValid = false;
    }
    
    // Check decimal places
    const decimalPlaces = (codeDetails.amount.toString().split('.')[1] || '').length;
    if (decimalPlaces > 2) {
      errors.amount = 'Amount can have at most 2 decimal places';
      isValid = false;
    }
  }
  
  return isValid;
};

const submit = async () => {
  submitted.value = true;
  
  if (!validateForm()) {
    $q.notify({
      color: 'negative',
      message: 'Please fix the validation issues',
      icon: 'warning'
    });
    return;
  }
  
  await savePromoCodes();
};

const savePromoCodes = async () => {
  codeDetails.one_off_code = true;
  
  try {
    const response = await axios.post("/discounts", {
      discount: codeDetails
    });
    
    $q.notify({
      color: 'positive',
      message: 'Discount Code Details Saved',
      icon: 'check_circle'
    });
    
    if (!codeDetails.id) {
      codes.value.push(response.data);
    }
    
    // Reset form
    Object.keys(codeDetails).forEach(key => {
      if (key !== 'discount_type') {
        delete codeDetails[key];
      }
    });
    
    submitted.value = false;
  } catch (error) {
    if (error.response?.data?.errors) {
      error.response.data.errors.forEach(e => {
        $q.notify({
          color: 'negative',
          message: e,
          icon: 'error'
        });
      });
    } else {
      $q.notify({
        color: 'negative',
        message: 'Discount Code Could Not be Saved',
        icon: 'error'
      });
    }
  }
};

const removeCode = async (code_id, idx) => {
  try {
    $q.dialog({
      title: 'Are you sure?',
      message: 'This will permanently remove this discount code!',
      cancel: true,
      persistent: true
    }).onOk(async () => {
      try {
        await axios.delete("/discounts/" + code_id);
        
        $q.notify({
          color: 'positive',
          message: 'Discount Code Removed!',
          icon: 'check_circle'
        });
        
        codes.value.splice(idx, 1);
      } catch (error) {
        $q.notify({
          color: 'negative',
          message: 'Failed to remove discount code',
          icon: 'error'
        });
      }
    });
  } catch (error) {
    console.error('Error in removeCode:', error);
  }
};

const onTypeahead = async (search) => {
  codeDetails.org_name = search;
  
  try {
    const response = await axios.get(`/discounts/typeahead_organisations.json?query=${search}`);
    
    orgSearch.value = [];
    response.data.forEach(org => {
      orgSearch.value.push(org.name);
    });
  } catch (error) {
    console.error('Error fetching organisation search:', error);
  }
};

const onAutoSelected = (result) => {
  codeDetails.org_name = result;
};

// Fetch initial data
onMounted(async () => {
  try {
    const response = await axios.get("/discounts/get_one_off_codes");
    codes.value = response.data.codes;
  } catch (error) {
    console.error('Error fetching discount codes:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load discount codes',
      icon: 'error'
    });
  }
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 1px solid #e0e0e0;
}
</style>