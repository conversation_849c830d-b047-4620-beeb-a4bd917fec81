<template>
  <div class="row">
    <div class="col-12">
      <q-card class="reports q-mb-md" id="reports">
        <q-card-section class="text-h6 hg-underline q-pb-lg">Events</q-card-section>

        <q-card-section>
          <q-tabs
            v-model="activeTab"
            class="text-primary"
            active-color="primary"
            indicator-color="primary"
            align="left"
            narrow-indicator
          >
            <q-tab name="forecast" label="Forecast" />
            <q-tab name="forecastTargets" label="Forecast Targets" />
            <q-tab name="summary" label="Summary" />
          </q-tabs>

          <q-tab-panels v-model="activeTab" animated>
            <q-tab-panel name="forecast">
              <forecast></forecast>
            </q-tab-panel>
            <q-tab-panel name="forecastTargets">
              <forecast-targets></forecast-targets>
            </q-tab-panel>
            <q-tab-panel name="summary">
              <summary-report></summary-report>
            </q-tab-panel>
          </q-tab-panels>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import Forecast from './Forecast.vue';
import ForecastTargets from './ForecastTargets.vue';
import SummaryReport from './SummaryReport.vue';

// Component data
const activeTab = ref('forecast');
</script>

<style scoped>
.hg-underline {
  border-bottom: 1px solid #e0e0e0;
}
</style>