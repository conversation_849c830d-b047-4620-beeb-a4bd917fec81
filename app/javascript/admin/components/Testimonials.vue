<template>
  <div class="col-12">
    <q-card class="q-mb-md">
      <q-card-section class="text-h6 hg-underline q-pb-lg">
        Testimonial
      </q-card-section>
      
      <q-card-section>
        <div class="row justify-end">
          <add-testimonial @update:testimonial="addNewTestimonial"></add-testimonial>
        </div>
        
        <q-table
          class="q-mt-md"
          :rows="testimonials"
          :columns="columns"
          row-key="id"
          flat
          bordered
        >
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="id" :props="props">{{ props.row.id }}</q-td>
              <q-td key="name" :props="props">{{ props.row.name }}</q-td>
              <q-td key="industry" :props="props">{{ props.row.industry }}</q-td>
              <q-td key="edit" :props="props" auto-width>
                <add-testimonial 
                  :testimonial="props.row"
                  @update:testimonial="updateTestimonial"
                ></add-testimonial>
              </q-td>
              <q-td key="delete" :props="props" auto-width>
                <q-btn 
                  :id="'deleteTestimonial_' + props.rowIndex" 
                  color="negative" 
                  size="sm"
                  flat
                  @click="deleteTestimonial(props.row)"
                >
                  <i class="fa fa-trash" aria-hidden="true"></i>
                </q-btn>
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';
import AddTestimonial from './AddTestimonial.vue';

// Initialize Quasar
const $q = useQuasar();

// Component data
const testimonials = ref([]);
const columns = [
  { name: 'id', label: 'Testimonial ID', field: 'id', align: 'left' },
  { name: 'name', label: 'Testimonial Author', field: 'name', align: 'left' },
  { name: 'industry', label: 'Testimonial Industry', field: 'industry', align: 'left' },
  { name: 'edit', label: 'Edit', field: 'edit', align: 'center' },
  { name: 'delete', label: 'Delete', field: 'delete', align: 'center' }
];

// Methods
const addNewTestimonial = (newTestimonial) => {
  testimonials.value.push(newTestimonial);
};

const updateTestimonial = (updatedTestimonial) => {
  const index = testimonials.value.findIndex(t => t.id === updatedTestimonial.id);
  if (index !== -1) {
    testimonials.value[index] = updatedTestimonial;
  }
};

const deleteTestimonial = async (testimonial) => {
  try {
    $q.dialog({
      title: 'Are you sure?',
      message: 'This will delete this testimonial!',
      cancel: true,
      persistent: true
    }).onOk(async () => {
      try {
        await axios.delete(`/testimonials/${testimonial.id}`);
        
        $q.notify({
          color: 'positive',
          message: 'Removed testimonial!',
          icon: 'check_circle'
        });
        
        const index = testimonials.value.findIndex(t => t.id === testimonial.id);
        testimonials.value.splice(index, 1);
      } catch (error) {
        $q.notify({
          color: 'negative',
          message: 'Testimonial not removed',
          icon: 'error'
        });
      }
    });
  } catch (error) {
    console.error('Error in deleteTestimonial:', error);
  }
};

// Fetch testimonials on mounted
onMounted(async () => {
  try {
    const response = await axios.get("/testimonials/get_testimonials");
    testimonials.value = response.data;
  } catch (error) {
    console.error('Error fetching testimonials:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load testimonials',
      icon: 'error'
    });
  }
});
</script>