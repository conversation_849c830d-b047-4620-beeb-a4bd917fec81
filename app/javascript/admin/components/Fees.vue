<template>
  <q-page padding>
    <q-card class="q-mt-md">
      <q-card-section class="bg-primary text-white">
        <div class="text-h6">Fees Chargeable</div>
      </q-card-section>

      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-md-6">
            <q-card>
              <q-card-section class="bg-grey-3">
                <div class="row items-center">
                  <q-icon name="fab fa-cc-stripe" size="32px" class="q-mr-md" />
                  <span class="text-subtitle1">Fees</span>
                </div>
              </q-card-section>

              <q-card-section>
                <p>UK</p>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_card"
                    type="number"
                    label="Debit/Credit"
                    min="0"
                    max="99.99"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-percent" />
                    </template>
                  </q-input>
                </div>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_card_add"
                    type="number"
                    label="Stripe card fees additional"
                    min="0"
                    max="100"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-pound-sign" />
                    </template>
                  </q-input>
                </div>

                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_uk_premium"
                    type="number"
                    label="Stripe UK Premium Fees Card"
                    min="0"
                    max="99.99"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-percent" />
                    </template>
                  </q-input>
                </div>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_uk_premium_add"
                    type="number"
                    label="Stripe UK Premium Fees Additional"
                    min="0"
                    max="100"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-pound-sign" />
                    </template>
                  </q-input>
                </div>
              </q-card-section>

              <q-card-section>
                <p>EU</p>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_eu"
                    type="number"
                    label="Debit/Credit EU"
                    min="0"
                    max="99.99"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-percent" />
                    </template>
                  </q-input>
                </div>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_eu_add"
                    type="number"
                    label="EU Stripe Fees Additional"
                    min="0"
                    max="100"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-pound-sign" />
                    </template>
                  </q-input>
                </div>
              </q-card-section>

              <q-card-section>
                <p>International</p>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_int"
                    type="number"
                    label="International Cards"
                    min="0"
                    max="99.99"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-percent" />
                    </template>
                  </q-input>
                </div>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.stripe_fees_int_add"
                    type="number"
                    label="International Fees Additional"
                    min="0"
                    max="100"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-pound-sign" />
                    </template>
                  </q-input>
                </div>
              </q-card-section>

              <q-card-section>
                <p>BACs</p>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.bacs_fees"
                    type="number"
                    label="BACs Fees"
                    min="0"
                    max="100"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-percent" />
                    </template>
                  </q-input>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-md-6">
            <q-card>
              <q-card-section class="bg-grey-3">
                <div class="row items-center">
                  <q-icon name="fas fa-map-marker-alt" size="32px" class="q-mr-md" />
                  <span class="text-subtitle1">HG Fees</span>
                </div>
              </q-card-section>

              <q-card-section>
                <p>Standard</p>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.hg_fees"
                    type="number"
                    label="HG Fees"
                    min="0"
                    max="99.99"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-percent" />
                    </template>
                  </q-input>
                </div>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.hg_fees_add"
                    type="number"
                    label="HG Fees Additional"
                    min="0"
                    max="100"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-pound-sign" />
                    </template>
                  </q-input>
                </div>
              </q-card-section>

              <q-card-section>
                <p>Charity</p>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.hg_fees_charity"
                    type="number"
                    label="HG Charity Fees"
                    min="0"
                    max="99.99"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-percent" />
                    </template>
                  </q-input>
                </div>
                <div class="q-mb-sm">
                  <q-input
                    v-model="newFees.hg_fees_charity_add"
                    type="number"
                    label="HG Charity Fees Additional"
                    min="0"
                    max="100"
                    outlined
                    dense
                  >
                    <template v-slot:append>
                      <q-icon name="fas fa-pound-sign" />
                    </template>
                  </q-input>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';

// Initialize Quasar
const $q = useQuasar();

// Component data
const fees = ref([]);
const newFees = reactive({});

// Format date helper
const formatDate = (date) => {
  if (!date) return '';
  return dayjs(date).format('DD/MM/YYYY');
};

// Methods
const getFees = async () => {
  try {
    const response = await axios.get("/get_fees");
    fees.value = response.data;
    
    // Copy values to newFees object
    Object.keys(response.data).forEach(key => {
      if (key !== 'id' && key !== 'created_at' && key !== 'updated_at') {
        newFees[key] = response.data[key];
      }
    });
  } catch (error) {
    console.error('Error fetching fees:', error);
    $q.notify({
      color: 'negative',
      message: 'Error fetching fees',
      icon: 'error'
    });
  }
};

const checkNotValidPercent = (feeAmount) => {
  if (feeAmount == undefined || feeAmount < 0.01 || feeAmount > 99.99) {
    return true;
  }
  return false;
};

const confirmFees = async () => {
  try {
    $q.dialog({
      title: 'Are you sure you wish to change the fees charged?',
      message: 'This will set the fees charged going forward!',
      cancel: true,
      persistent: true
    }).onOk(async () => {
      // Validate fee percentages
      if (
        checkNotValidPercent(newFees.hg_fees) ||
        checkNotValidPercent(newFees.hg_fees_charity) ||
        checkNotValidPercent(newFees.stripe_fees_card) ||
        checkNotValidPercent(newFees.stripe_fees_int) ||
        checkNotValidPercent(newFees.stripe_fees_eu) ||
        checkNotValidPercent(newFees.stripe_fees_uk_premium) ||
        checkNotValidPercent(newFees.bacs_fees)
      ) {
        $q.notify({
          color: 'negative',
          message: 'Please enter valid fee amounts for all areas',
          icon: 'error'
        });
        return;
      }
      
      try {
        await axios.post("/fees_update", {
          options: newFees
        });
        
        $q.notify({
          color: 'positive',
          message: 'Fee Saved Successfully',
          icon: 'check_circle'
        });
        
        // Refresh fees data
        await getFees();
      } catch (error) {
        $q.notify({
          color: 'negative',
          message: 'Fees Not Saved',
          icon: 'error'
        });
      }
    });
  } catch (error) {
    console.error('Error in confirmFees:', error);
  }
};

// Lifecycle hooks
onMounted(() => {
  getFees();
});
</script>