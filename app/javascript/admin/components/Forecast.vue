<template>
  <div class="col-12">
    <div class="wrapper">
      <q-card class="q-mb-md">
        <q-card-section>
          <label for="yearTarget" class="q-mb-sm block">Select Target Year</label>
          <q-select
            @update:model-value="chooseYear"
            id="yearTarget"
            name="yearTarget"
            v-model="selectedYear"
            :options="targetYears"
            option-label="description"
            option-value="id"
            map-options
            emit-value
            outlined
            dense
            style="max-width: 300px"
          />
        </q-card-section>
      </q-card>

      <q-card>
        <q-card-section>
          <div class="q-table__container q-table--horizontal-separator">
            <table class="q-table table-forecast">
              <thead>
                <tr class="toprow">
                  <th class="empty"></th>
                  <th class="empty"></th>
                  <th class="empty"></th>
                  <th class="empty"></th>
                  <th colspan="2" class="nowrap text-center">Advanced Payments</th>
                  <th colspan="6" class="text-center">Fees</th>
                  <th class="empty"></th>
                </tr>
                <tr class="mainrow">
                  <th>Month / Event Name</th>
                  <th>Org Name</th>
                  <th>Target Revenue</th>
                  <th>Free Event - Per Booking Cost</th>
                  <th>Client Managed</th>
                  <th>HG Managed</th>
                  <th>From Debit/CC Payments</th>
                  <th>Setup</th>
                  <th>Event Mgt</th>
                  <th>Badging</th>
                  <th>Equipment</th>
                  <th>Total</th>
                  <th>Percentage To Target</th>
                </tr>
              </thead>
              <tbody>
                <tr 
                  v-for="forecast in forecastActual" 
                  :key="forecast.month + (forecast.event_id || '')" 
                  :class="{ 'text-weight-bold': forecast.summary_row }"
                >
                  <td class="text-capitalize" v-if="!forecast.summary_row">
                    {{forecast.month}} -
                    <a :href="'/dashboard/' + forecast.event_id">{{forecast.event_name}}</a>
                  </td>
                  <td v-if="!forecast.summary_row">{{forecast.org_name}}</td>
                  <td 
                    v-if="forecast.summary_row" 
                    @click="toggleMonth(forecast)" 
                    class="text-capitalize text-primary cursor-pointer text-underline"
                  >
                    {{forecast.month}}
                  </td>
                  <td v-if="forecast.summary_row"></td>
                  <td>
                    {{forecast.forecast_target}}
                  </td>
                  <td>
                    {{forecast.hg_fees}}
                  </td>
                  <td>{{forecast.free_event_fee}}</td>
                  <td>{{forecast.advanced_payments_client_managed}}</td>
                  <td>{{forecast.advanced_payments_hg_managed}}</td>
                  <td>{{forecast.setup_fee}}</td>
                  <td>{{forecast.management_fee}}</td>
                  <td>{{forecast.badging_cost}}</td>
                  <td>{{forecast.equipment_cost}}</td>
                  <td>{{forecast.total}}</td>
                  <td v-if="forecast.summary_row">
                    {{calculatePercentage(forecast.forecast_target, forecast.total)}}
                  </td>
                  <td v-if="!forecast.summary_row">
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </q-card-section>

        <q-card-section>
          <q-btn color="primary" icon="download" label="Download CSV" @click="downloadCsv" />
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';

// Initialize Quasar
const $q = useQuasar();

// Component data
const targetData = ref({});
const forecastTargets = ref({});
const targetYears = ref(null);
const selectedYear = ref(null);
const forecastActual = ref(null);

// Methods
const calculatePercentage = (target, total) => {
  if (!target) return "Please add a target for this month.";
  return (total / target * 100).toFixed(2) + " %";
};

const chooseYear = async () => {
  try {
    const response = await axios.get(`/forecasts/?year=${selectedYear.value}.json`);
    
    targetData.value = response.data.forecast_data;
    forecastActual.value = targetData.value.event_fees;
    
    if (response.data.forecast_data.event_fees.length === 0) {
      $q.notify({
        color: 'negative',
        message: 'The targets for this year could not be retrieved',
        icon: 'error'
      });
    }
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'The targets for this year could not be retrieved',
      icon: 'error'
    });
    console.error('Error fetching forecast data:', error);
  }
};

const toggleMonth = (forecast) => {
  forecast.show_row = !forecast.show_row;
  
  // Update all items with the same month
  if (forecastActual.value) {
    forecastActual.value.forEach(forecastMonth => {
      if (forecastMonth.month === forecast.month.toLowerCase()) {
        forecastMonth.show_row = forecast.show_row;
      }
    });
  }
};

const downloadCsv = () => {
  window.location.href = "/forecasts.csv";
};

// Fetch initial forecast data
onMounted(async () => {
  try {
    const response = await axios.get("/forecasts.json");
    
    targetData.value = response.data.forecast_data;
    targetYears.value = targetData.value.target_years;
    selectedYear.value = targetData.value.current_year;
    forecastActual.value = targetData.value.event_fees;
  } catch (error) {
    console.error('Error fetching initial forecast data:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load forecast data',
      icon: 'error'
    });
  }
});
</script>

<style scoped>
.table-forecast {
  width: 100%;
  border-collapse: collapse;
}

.table-forecast th,
.table-forecast td {
  padding: 8px;
  border: 1px solid #ddd;
}

.table-forecast .empty {
  border: none;
  background-color: transparent;
}

.table-forecast .nowrap {
  white-space: nowrap;
}

.text-underline {
  text-decoration: underline;
}

.block {
  display: block;
}
</style>