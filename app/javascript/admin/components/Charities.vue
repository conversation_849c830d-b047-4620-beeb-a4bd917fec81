<template>
  <q-page padding>
    <div class="row">
      <div class="col-12 q-mx-auto">
        <q-card flat bordered class="q-ml-md">
          <q-card-section class="hg-underline q-pb-lg">
            <div class="text-h6">Verify Charities</div>
          </q-card-section>
          <q-card-section>
            <q-table
              :rows="charities"
              :columns="columns"
              row-key="id"
              flat
              bordered
              :pagination="{ rowsPerPage: 0 }"
              class="q-mt-sm"
            >
              <template v-slot:body="props">
                <q-tr :props="props">
                  <q-td key="organisation_name" :props="props">{{ props.row.organisation_name }}</q-td>
                  <q-td key="registration_number" :props="props">{{ props.row.registration_number }}</q-td>
                  <q-td key="full_name" :props="props">{{ props.row.full_name }}</q-td>
                  <q-td key="verify" :props="props">
                    <q-btn 
                      v-if="!props.row.verified" 
                      color="warning" 
                      @click="verify(props.row, true)"
                      label="Verify Charity"
                    />
                    <span v-if="props.row.verified" class="text-positive">Verified</span>
                  </q-td>
                  <q-td key="remove" :props="props">
                    <q-btn 
                      v-if="props.row.verified" 
                      color="negative" 
                      @click="verify(props.row, false)"
                      label="Remove Charity Verification"
                    />
                    <span v-if="!props.row.verified" class="text-negative">Not Verified</span>
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';

// Initialize Quasar
const $q = useQuasar();

// Component data
const charities = ref([]);

// Define table columns
const columns = [
  { name: 'organisation_name', align: 'left', label: 'Organisation Name', field: 'organisation_name' },
  { name: 'registration_number', align: 'left', label: 'Charity Number', field: 'registration_number' },
  { name: 'full_name', align: 'left', label: 'Charity Name', field: 'full_name' },
  { name: 'verify', align: 'left', label: 'Verified', field: 'verified' },
  { name: 'remove', align: 'left', label: 'Remove Validation', field: 'verified' }
];

// Fetch charities on mount
onMounted(async () => {
  try {
    const response = await axios.get("hg_admin/administer_charities");
    charities.value = response.data.charities;
  } catch (error) {
    console.error('Error fetching charities:', error);
    $q.notify({
      message: 'Error fetching charities',
      color: 'negative',
      position: 'top-right',
      timeout: 2000
    });
  }
});

// Methods
const verify = async (charity, verify) => {
  const title = verify 
    ? "Verify Charity Organisation?" 
    : "Cancel Charity Verification?";
    
  const message = verify
    ? "This will permit the organisation to use the charity rate"
    : "This will put the organisation back on the standard fees";
    
  try {
    $q.dialog({
      title: title,
      message: message,
      ok: {
        label: verify ? 'Yes, Verify' : 'Remove Verification',
        color: verify ? 'warning' : 'negative'
      },
      cancel: {
        label: 'Cancel',
        flat: true
      },
      persistent: true
    }).onOk(async () => {
      try {
        await axios.put("/verify_charity", {
          charity_id: charity.id,
          verify: verify
        });
        
        charity.verified = verify;
        
        $q.notify({
          message: verify 
            ? "Charitable Organisation Verified Successfully" 
            : "Charitable Organisation Verification Removed",
          color: 'positive',
          position: 'top-right',
          timeout: 2000
        });
      } catch (error) {
        $q.notify({
          message: "Charity Not Verified",
          color: 'negative',
          position: 'top-right',
          timeout: 2000
        });
      }
    });
  } catch (error) {
    console.error('Dialog error:', error);
  }
};
</script>

<style scoped>
.hg-underline {
  border-bottom: 1px solid #e0e0e0;
}
</style>