<template>
  <q-page padding>
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <div class="row q-col-gutter-md">
          <div class="col">
            <dial
              title-text="Total Events "
              :dial-count="eventsCount"
              :dial-total="eventsCount"
            ></dial>
          </div>
          <div class="col">
            <dial
              title-text="Uncompleted Events "
              :dial-count="uncompletedLength"
              :dial-total="events.length"
            ></dial>
          </div>

          <div class="col">
            <dial
              title-text="Events Created this month "
              :dial-count="createdCount"
              :dial-total="events.length"
            ></dial>
          </div>

          <div class="col">
            <dial
              title-text="Completed Events "
              :dial-count="completedLength"
              :dial-total="events.length"
            ></dial>
          </div>
          <div class="col">
            <dial
              title-text="Live Events "
              :dial-count="liveLength"
              :dial-total="events.length"
            ></dial>
          </div>
        </div>
      </div>

      <div class="col-12">
        <q-card flat bordered>
          <q-card-section class="hg-underline">
            <div class="text-h6">Events</div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-md-2 col-xs-12">
                <q-input
                  outlined
                  v-model="eventFilter"
                  placeholder="Event Name"
                  aria-label="Filter Events"
                />
              </div>
              <div class="col-md-2 col-xs-12">
                <q-select 
                  outlined
                  v-model="organisationFilter" 
                  :options="organisationOptions" 
                  option-label="text" 
                  option-value="value"
                  map-options
                  emit-value
                  label="Organization"
                />
              </div>

              <div class="col-md-2 col-xs-12">
                <q-select 
                  outlined
                  v-model="completeFilter" 
                  :options="completeOptions" 
                  option-label="text" 
                  option-value="value"
                  map-options
                  emit-value
                  label="Completion Status"
                />
              </div>

              <div class="col-md-3 col-xs-12">
                <q-select 
                  outlined
                  v-model="eventStatusFilter" 
                  :options="eventStatusOptions" 
                  option-label="text" 
                  option-value="value"
                  map-options
                  emit-value
                  label="Event Status"
                />
              </div>

              <div class="col-md-3 col-xs-12">
                <q-select 
                  outlined
                  v-model="expiredFilter" 
                  :options="expiredOptions" 
                  option-label="text" 
                  option-value="value"
                  map-options
                  emit-value
                  label="Expiration Status"
                />
              </div>
            </div>

            <div class="row q-col-gutter-md q-mt-md">
              <div class="col-auto">
                <q-btn 
                  color="primary" 
                  outline 
                  @click="filterEvents"
                  label="Filter"
                />
              </div>
              <div class="col-auto">
                <q-btn 
                  color="secondary" 
                  outline 
                  @click="clearFilter"
                  label="Clear"
                />
              </div>
              <div class="col-auto">
                <q-btn
                  color="primary"
                  outline
                >
                  <q-menu>
                    <div class="q-pa-md" style="width: 250px">
                      <div class="text-subtitle2 q-mb-sm">Filter By Event Tags</div>
                      <q-select
                        outlined
                        v-model="tagsSelected"
                        multiple
                        :options="tagsList"
                        option-label="label" 
                        option-value="value"
                        map-options
                        emit-value
                        label="Select Tags"
                        @update:model-value="filterByTags"
                      />
                    </div>
                  </q-menu>
                  <div class="row items-center no-wrap">
                    <q-icon left name="filter_list" />
                    <div>Click to filter by tags</div>
                  </div>
                </q-btn>
              </div>
            </div>

            <q-table
              class="q-mt-md"
              :rows="events"
              :columns="columns"
              row-key="id"
              flat
              bordered
              :pagination="{ rowsPerPage: 0 }"
            >
              <template v-slot:body="props">
                <q-tr :props="props">
                  <q-td auto-width>
                    <q-btn
                      size="sm"
                      flat
                      round
                      :icon="dropexpanded[props.rowIndex] ? 'remove' : 'add'"
                      @click="setExpanded(props.rowIndex, !dropexpanded[props.rowIndex])"
                    />
                  </q-td>
                  <q-td key="id" :props="props">{{ props.row.id }}</q-td>
                  <q-td key="title" :props="props" style="word-break: break-word">{{ props.row.title }}</q-td>
                  <q-td key="organisation" :props="props">
                    {{ props.row.organisation_name }} (#{{ props.row.organisation_id }})
                  </q-td>
                  <q-td key="date" :props="props">
                    {{ formatDate(props.row.datetimefrom) }}
                  </q-td>
                  <q-td key="complete" :props="props">
                    <div
                      v-if="props.row.complete"
                      class="text-positive text-weight-bold"
                    >
                      Yes
                    </div>
                    <div
                      v-else
                      class="text-negative text-weight-bold"
                    >
                      No
                    </div>
                  </q-td>
                  <q-td key="status" :props="props">
                    <div
                      v-if="props.row.live"
                      class="text-positive text-weight-bold"
                    >
                      Live
                    </div>
                    <div
                      v-else
                      class="text-negative text-weight-bold"
                    >
                      Inactive
                    </div>
                  </q-td>
                  <q-td key="expired" :props="props">
                    <div
                      v-if="expired(props.row)"
                      class="text-negative text-weight-bold"
                    >
                      Expired
                    </div>
                    <div v-else class="text-positive">
                      No
                    </div>
                  </q-td>
                  <q-td key="created" :props="props">
                    {{ formatDate(props.row.event_created_date) }}
                  </q-td>
                  <q-td key="actions" :props="props">
                    <q-btn-dropdown color="primary" label="Actions" size="sm">
                      <q-list>
                        <q-item clickable v-close-popup @click="navigateTo(`/dashboard/${props.row.id}`)">
                          <q-item-section>View</q-item-section>
                        </q-item>
                        <q-item clickable v-close-popup @click="navigateTo(`/events/${props.row.id}/edit`)">
                          <q-item-section>Edit</q-item-section>
                        </q-item>
                        <template v-if="expired(props.row)">
                          <q-separator />
                          <q-item clickable v-close-popup @click="navigateTo(`stripe_breakdown/${props.row.id}`)">
                            <q-item-section>Stripe Revenue Breakdown</q-item-section>
                          </q-item>
                          <q-item clickable v-close-popup @click="navigateTo(`/stripe_tax_summary/${props.row.id}`)">
                            <q-item-section>Stripe Tax Summary</q-item-section>
                          </q-item>
                        </template>
                      </q-list>
                    </q-btn-dropdown>
                  </q-td>
                </q-tr>
                <q-tr v-show="dropexpanded[props.rowIndex]">
                  <q-td colspan="3">
                    <strong>Organiser Name: </strong>
                    {{ props.row.organiser }}
                  </q-td>
                  <q-td colspan="3">
                    <strong>Organiser Email: </strong>
                    {{ props.row.organiser_email }}
                  </q-td>
                  <q-td
                    v-if="
                      props.row.stage !== null &&
                      props.row.stage !== 'Complete'
                    "
                    colspan="4"
                  >
                    <strong>Completion Stage: </strong>
                    {{ props.row.stage }}
                  </q-td>
                </q-tr>
              </template>
            </q-table>

            <div class="q-pa-lg flex flex-center">
              <q-pagination
                v-model="selectedPage"
                :max="Math.ceil(eventsCount / 50)"
                :max-pages="6"
                boundary-links
                @update:model-value="pageChanged"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';
import Dial from '@/main/dial.vue';
import dayjs from 'dayjs';

const $q = useQuasar();

// Define table columns
const columns = [
  { name: 'expand', align: 'left', label: '', field: 'expand' },
  { name: 'id', align: 'left', label: 'Event ID', field: 'id', sortable: true },
  { name: 'title', align: 'left', label: 'Event Name', field: 'title', sortable: true },
  { name: 'organisation', align: 'left', label: 'Organisation', field: 'organisation_name', sortable: true },
  { name: 'date', align: 'left', label: 'Date/Time', field: 'datetimefrom', sortable: true },
  { name: 'complete', align: 'left', label: 'Complete', field: 'complete', sortable: true },
  { name: 'status', align: 'left', label: 'Status', field: 'live', sortable: true },
  { name: 'expired', align: 'left', label: 'Expired', field: row => expired(row), sortable: true },
  { name: 'created', align: 'left', label: 'Created', field: 'event_created_date', sortable: true },
  { name: 'actions', align: 'left', label: 'Actions', field: 'actions' }
];

// Component data
const events = ref([]);
const dropexpanded = ref([]);
const createdCount = ref(0);
const eventsCount = ref(0);
const uncompletedLength = ref(0);
const completedLength = ref(0);
const liveLength = ref(0);
const eventFilter = ref(null);
const selectedPage = ref(1);
const tagsSelected = ref([]);
const tagsList = ref([]);
const tags = ref([]);
const eventStatusFilter = ref(1);
const eventStatusOptions = [
  { text: 'Select Status', value: 0 },
  { text: 'Live', value: 1 },
  { text: 'Inactive', value: 2 },
];

const organisationFilter = ref(0);
const organisationOptions = ref([{ text: 'All Organisations', value: 0 }]);
const completeFilter = ref(1);
const completeOptions = [
  { text: 'Select Complete', value: 0 },
  { text: 'Complete', value: 1 },
  { text: 'Incomplete', value: 2 },
];
const expiredFilter = ref(2);
const expiredOptions = [
  { text: 'Select Expired', value: 0 },
  { text: 'Expired', value: 1 },
  { text: 'Not Expired', value: 2 },
];

// Format date with dayjs instead of dayjs
const formatDate = (date) => {
  if (!date) return '';
  return dayjs(date).format('DD/MM/YYYY HH:mm');
};

// Methods
const getOrgData = async () => {
  try {
    const response = await axios.get('/hg_admin/get_org_details.json');
    response.data.org_filter.forEach((org) => {
      organisationOptions.value.push({text: org.name, value: org.id});
    });
  } catch (error) {
    console.log(error);
  }
  
  pageChanged();
};

const expired = (event) => {
  return dayjs(event.datetimeto).isBefore(dayjs());
};

const filterByTags = () => {
  pageChanged();
};

const toggleDate = async (show, event_id) => {
  try {
    await axios.post('/hg_admin/show_date_toggle?id=' + event_id);
    $q.notify({
      message: 'Date preferences changed',
      color: 'positive',
      position: 'top-right',
      timeout: 2000
    });
  } catch (error) {
    console.log(error);
    $q.notify({
      message: 'Error changing date preferences',
      color: 'negative',
      position: 'top-right',
      timeout: 2000
    });
  }
};

const setExpanded = (indx, value) => {
  for (let i = 0; i < dropexpanded.value.length; i++) {
    if (indx === i) {
      dropexpanded.value[i] = value;
    } else {
      if (value) dropexpanded.value[i] = !value;
    }
  }
};

const pageChanged = async (page = null) => {
  if (page) {
    selectedPage.value = page;
  }

  try {
    const response = await axios.get('/get_events_for_admin', {
      params: {
        page: selectedPage.value,
        search: eventFilter.value,
        status: eventStatusFilter.value,
        tags: tagsSelected.value,
        org: organisationFilter.value,
        complete: completeFilter.value,
        expired: expiredFilter.value,
      },
    });
    
    events.value = response.data.events;
    dropexpanded.value = Array(events.value.length).fill(false);
    
    events.value.forEach((event) => {
      if (event.tags) {
        event.tags.forEach((tag) => {
          if (!tags.value.includes(tag)) {
            tags.value.push(tag);
            tagsList.value.push({
              value: tag,
              label: tag,
            });
          }
        });
      }
    });

    createdCount.value = response.data.created_count;
    eventsCount.value = response.data.events_count;
    uncompletedLength.value = response.data.uncompleted_count;
    completedLength.value = response.data.completed_count;
    liveLength.value = response.data.live_count;
  } catch (error) {
    console.error('Error fetching events:', error);
    $q.notify({
      message: 'Error fetching events',
      color: 'negative',
      position: 'top-right',
      timeout: 2000
    });
  }
};

const filterEvents = () => {
  pageChanged();
};

const clearFilter = () => {
  selectedPage.value = 1;
  eventFilter.value = null;
  eventStatusFilter.value = 0;
  tagsSelected.value = [];
  organisationFilter.value = 0;
  completeFilter.value = 0;
  expiredFilter.value = 0;
  pageChanged();
};

const navigateTo = (url) => {
  window.location.href = url;
};

// Lifecycle hooks
onMounted(() => {
  getOrgData();
});
</script>

<style scoped>
.hg-underline {
  border-bottom: 1px solid #e0e0e0;
}
</style>