<template>
  <div>
    <q-btn flat size="sm" @click="showModal">
      <i v-if="props.testimonial?.id" class="fa fa-wrench" aria-hidden="true"></i>
      <i v-else class="fa fa-plus" aria-hidden="true"></i>
    </q-btn>

    <q-dialog v-model="modalVisible" ref="dialogRef" @hide="onDialogHide">
      <q-card style="width: 500px">
        <q-card-section class="text-h6">
          Manage Testimonials
        </q-card-section>

        <q-card-section>
          <q-form @submit="submit" class="q-gutter-md">
            <q-input 
              v-model="testim.name" 
              label="Name"
              :error="!!errors.name" 
              :error-message="errors.name"
              outlined
              required
            />

            <q-input 
              v-model="testim.testimonial_text" 
              label="Testimonial Text"
              :error="!!errors.text" 
              :error-message="errors.text"
              outlined
              required
              type="textarea"
            />

            <q-input 
              v-model="testim.industry" 
              label="Industry"
              :error="!!errors.industry" 
              :error-message="errors.industry"
              outlined
              required
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn color="primary" label="Save Testimonial" @click="submit" />
          <q-btn flat label="Close" @click="hideModal" class="q-ml-sm" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, defineEmits, defineProps } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';

// Initialize Quasar notification system
const $q = useQuasar();

// Props and emits
const props = defineProps({
  testimonial: Object
});

const emit = defineEmits(['update:testimonial']);

// Component data
const modalVisible = ref(false);
const dialogRef = ref(null);
const testim = reactive({
  id: null,
  name: null,
  testimonial_text: null,
  industry: null
});
const errors = reactive({});

// Methods
const showModal = () => {
  modalVisible.value = true;
};

const hideModal = () => {
  modalVisible.value = false;
};

const onDialogHide = () => {
  // Reset form on dialog hide
  if (!testim.id) {
    Object.assign(testim, {
      id: null,
      name: null,
      testimonial_text: null,
      industry: null
    });
  }
};

const validate = () => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key]);
  let isValid = true;
  
  if (!testim.name) {
    errors.name = 'The name field is required';
    isValid = false;
  }
  
  if (!testim.testimonial_text) {
    errors.text = 'The testimonial text is required';
    isValid = false;
  }
  
  if (!testim.industry) {
    errors.industry = 'The industry field is required';
    isValid = false;
  }
  
  return isValid;
};

const submit = async () => {
  if (!validate()) {
    $q.notify({
      color: 'negative',
      message: 'Please fix the validation issues',
      icon: 'warning'
    });
    return;
  }
  
  await saveTestimonials();
};

const saveTestimonials = async () => {
  try {
    let response;
    
    if (testim.id) {
      // Update existing testimonial
      response = await axios.put(`/testimonials/${testim.id}`, {
        testimonial: testim
      });
      
      // Update the parent component
      emit('update:testimonial', response.data);
      
      $q.notify({
        color: 'positive',
        message: 'The testimonial has been updated!',
        icon: 'check_circle'
      });
    } else {
      // Create new testimonial
      response = await axios.post("/testimonials", {
        testimonial: testim
      });
      
      // Emit event so parent component can add it to the list
      emit('update:testimonial', response.data);
      
      $q.notify({
        color: 'positive',
        message: 'The testimonial has been created!',
        icon: 'check_circle'
      });
    }
    
    hideModal();
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: testim.id ? 
        'The testimonial could not be updated!' : 
        'The testimonial could not be saved!',
      icon: 'error'
    });
  }
};

// Initialize data when component is created
onMounted(() => {
  if (props.testimonial) {
    Object.assign(testim, props.testimonial);
  }
});
</script>