<template>
  <div class="wrapper">
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="row">
              <div class="col-12">
                <div class="group">
                  <date-filter @dates-changed="onDatesChanged"></date-filter>
                </div>
                <div class="q-mt-md">
                  <div class="row q-col-gutter-sm items-center">
                    <div class="col-auto">
                      <q-select
                        v-model="organisationFilter"
                        :options="organisationOptions"
                        option-label="text"
                        option-value="value"
                        map-options
                        emit-value
                        outlined
                        dense
                        placeholder="All Organisations"
                      />
                    </div>
                    <div class="col-auto">
                      <q-select
                        v-model="eventStatusFilter"
                        :options="eventStatusOptions"
                        option-label="text"
                        option-value="value"
                        map-options
                        emit-value
                        outlined
                        dense
                        placeholder="All"
                      />
                    </div>
                    <div class="col-auto">
                      <autocomplete 
                        label="All Events" 
                        :isAsync="false"
                        :items="eventSearch" 
                        :inLine="true"
                        @typeahead="onTypeahead"
                        @auto-selected="onAutoSelected"
                      ></autocomplete>
                    </div>
                    <div class="col-auto">
                      <q-btn color="primary" flat @click="loadData" label="Filter" />
                    </div>
                    <div class="col-auto">
                      <q-btn flat @click="clearFilter" label="Clear" />
                    </div>
                    <div class="col-auto">
                      <q-btn color="primary" @click="exportReport" label="Export Report" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <q-table
              id="summary_table"
              :rows="report"
              :columns="columns"
              row-key="client_name"
              flat
              bordered
              :pagination="{ rowsPerPage: 0 }"
              class="q-mt-md"
            >
              <template v-slot:body-cell-forecast_revenue="props">
                <q-td :props="props">
                  {{ props.value }}
                  <p class="q-ma-none">
                    <small>If all tickets booked at full price</small>
                  </p>
                </q-td>
              </template>

              <template v-slot:bottom-row>
                <q-tr v-if="lastPage">
                  <q-td>Grand Total</q-td>
                  <q-td>{{ grandTotals.forecast }}</q-td>
                  <q-td>{{ grandTotals.profit }}</q-td>
                  <q-td>{{ grandTotals.stripe }}</q-td>
                  <q-td>{{ grandTotals.hg_fees }}</q-td>
                  <q-td>{{ grandTotals.total_fees }}</q-td>
                  <q-td>{{ grandTotals.revenue }}</q-td>
                  <q-td>{{ grandTotals.tickets_sold }}</q-td>
                </q-tr>
              </template>
            </q-table>
            
            <div class="row justify-center q-mt-md">
              <q-pagination
                v-model="selectedPage"
                :max="Math.ceil(totalItems / 100)"
                :max-pages="6"
                direction-links
                @update:model-value="loadData"
              />
            </div>
            
            <p class="q-mt-md">Last updated at {{ lastUpdatedAt }}</p>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import DateFilter from '@/common/date-filters.vue';
import Autocomplete from '@/common/autocomplete.vue';
import { useEventBus } from '@/events/event-bus';

// Set up dayjs plugins
dayjs.extend(quarterOfYear);

// Component data
const eventBus = useEventBus();
const totalItems = ref(0);
const selectedPage = ref(1);
const grandTotals = ref({});
const report = ref({});
const eventStatusFilter = ref(0);
const eventStatusOptions = [
  { text: 'All', value: 0 },
  { text: 'Live', value: 1 },
  { text: 'Expired', value: 2 },
];
const dateFromFilter = ref(null);
const dateToFilter = ref(null);
const organisationFilter = ref('0');
const organisationOptions = ref([{ text: 'All Organisations', value: '0' }]);
const lastUpdatedAt = ref(null);
const eventSearch = ref([]);
const eventSelected = ref(null);

// Define table columns
const columns = [
  { name: 'client_name', align: 'left', label: 'Client', field: 'client_name' },
  { name: 'forecast_revenue', align: 'left', label: 'Forecasted Ticket Revenue', field: 'forecast' },
  { name: 'profit', align: 'left', label: 'Net Revenue', field: 'profit' },
  { name: 'stripe', align: 'left', label: 'Stripe Fees', field: 'stripe' },
  { name: 'hg_fees', align: 'left', label: 'Application Fees', field: 'hg_fees' },
  { name: 'total_fees', align: 'left', label: 'Total Fees', field: 'total_fees' },
  { name: 'revenue', align: 'left', label: 'Gross Revenue', field: 'revenue' },
  { name: 'tickets_sold', align: 'left', label: 'Tickets Sold', field: 'tickets_sold' }
];

// Computed properties
const lastPage = computed(() => {
  return Math.ceil(totalItems.value / 100) === selectedPage.value;
});

// Methods
const loadData = async (page = null) => {
  if (page) {
    selectedPage.value = page;
  }
  
  try {
    const response = await axios.get(
      `/hg_admin/reports.json?page=${selectedPage.value}&eventStatusFilter=${eventStatusFilter.value}&dateFromFilter=${dateFromFilter.value}&dateToFilter=${dateToFilter.value}&org_id=${organisationFilter.value}&eventFilter=${eventSelected.value || ''}`
    );
    
    report.value = response.data.report;
    totalItems.value = response.data.no_of_rows;
    grandTotals.value = response.data.grand_totals;
    lastUpdatedAt.value = response.data.last_updated_at;
  } catch (error) {
    console.error('Error loading report data:', error);
  }
};

const clearFilter = () => {
  eventStatusFilter.value = 0;
  setDefaultDates();
  organisationFilter.value = '0';
  eventSelected.value = null;
  eventSearch.value = [];
  eventBus.emit('clearAutocomplete');
  loadData();
};

const setDefaultDates = () => {
  const quarter = dayjs().subtract(4 - dayjs().quarter(), 'quarter');
  dateFromFilter.value = quarter.startOf('quarter').format('YYYY-MM-DD');
  dateToFilter.value = quarter.endOf('quarter').format('YYYY-MM-DD');
  eventBus.emit('resetDateFilter');
};

const exportReport = () => {
  window.location.href = `/hg_admin/reports.csv?eventStatusFilter=${eventStatusFilter.value}&org_id=${organisationFilter.value}&dateFromFilter=${dateFromFilter.value}&dateToFilter=${dateToFilter.value}&eventFilter=${eventSelected.value || ''}`;
};

const onDatesChanged = (from, to) => {
  dateFromFilter.value = from;
  dateToFilter.value = to;
};

const onTypeahead = async (search) => {
  eventSelected.value = search;
  
  try {
    const response = await axios.get(
      `/events/typeahead_events.json?query=${search}&referrer=admin`
    );
    
    eventSearch.value = [];
    response.data.forEach((ev) => {
      eventSearch.value.push(ev.title);
    });
  } catch (error) {
    console.error('Error fetching event typeahead data:', error);
  }
};

const onAutoSelected = (result) => {
  eventSelected.value = result;
};

// Setup
onMounted(async () => {
  setDefaultDates();
  
  try {
    const response = await axios.get('/hg_admin/get_org_details.json');
    response.data.org_filter.forEach((org) => {
      organisationOptions.value.push({
        text: org.name,
        value: org.id,
      });
    });
  } catch (error) {
    console.error('Error fetching organisation data:', error);
  }
  
  loadData();
});
</script>