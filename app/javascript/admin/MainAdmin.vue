<template>
  <q-layout view="hHh LpR fFf">
    <!-- Quasar Drawer for sidebar -->
    <q-drawer
      v-model="leftDrawerOpen"
      bordered
      show-if-above
      :width="300"
      :breakpoint="700"
      class="bg-grey-9"
    >
      <!-- sidebar menu start-->
      <q-list class="sidebar-menu">
        <div class="q-mb-md q-px-md q-py-sm" style="border-bottom: 1px solid white">
          <h5 class="q-my-none text-center text-white">
            <strong>{{ currentUser }}</strong>
          </h5>
        </div>

        <notify-link></notify-link>

        <q-item clickable to="/admin/events" class="q-mt-md">
          <q-item-section avatar>
            <q-icon name="settings" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">HG Admin</q-item-label>
          </q-item-section>
        </q-item>

        <q-item clickable to="/admin/reports">
          <q-item-section avatar>
            <q-icon name="groups" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">View Reports</q-item-label>
          </q-item-section>
        </q-item>

        <q-item clickable to="/admin/testimonials">
          <q-item-section avatar>
            <q-icon name="groups" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">Manage Testimonials</q-item-label>
          </q-item-section>
        </q-item>

        <q-item clickable to="/admin/fees">
          <q-item-section avatar>
            <q-icon name="payments" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">Edit Fees</q-item-label>
          </q-item-section>
        </q-item>

        <q-item clickable to="/admin/fee-overrides">
          <q-item-section avatar>
            <q-icon name="add_circle" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">Fee Overrides</q-item-label>
          </q-item-section>
        </q-item>

        <q-item clickable to="/admin/audits">
          <q-item-section avatar>
            <q-icon name="insights" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">Audits</q-item-label>
          </q-item-section>
        </q-item>

        <q-item clickable to="/admin/promo-codes">
          <q-item-section avatar>
            <q-icon name="local_offer" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">Promo Codes</q-item-label>
          </q-item-section>
        </q-item>

        <q-item clickable to="/admin/charity-validation">
          <q-item-section avatar>
            <q-icon name="check_box" color="white" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-white">Administer Charities</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
      <!-- sidebar menu end-->
    </q-drawer>

    <q-page-container class="q-pa-md">
      <router-view></router-view>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import NotifyLink from '@/common/notifications-link.vue';

// Component data
const currentUser = ref(window.currentUser || '');
const leftDrawerOpen = ref(true);
</script>

<style scoped>
.sidebar-menu :deep(.q-item--active) {
  background-color: rgba(255, 255, 255, 0.2);
}

.sidebar-menu :deep(.q-item:hover) {
  background-color: rgba(255, 255, 255, 0.1);
}
</style>