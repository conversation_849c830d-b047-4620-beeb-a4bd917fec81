import { createRouter, createWebHashHistory } from 'vue-router';

// Import admin components
// These will need to be migrated to Vue 3 syntax as well
const AdminEvents = () => import('./components/AdminEvents.vue');
const Reports = () => import('./components/Reports.vue');
const Testimonials = () => import('./components/Testimonials.vue');
const Fees = () => import('./components/Fees.vue');
const FeeOverrides = () => import('./components/FeeOverrides.vue');
const Audit = () => import('./components/Audit.vue');
const PromoCodes = () => import('./components/PromoCodes.vue');
const Charities = () => import('./components/Charities.vue');

const routes = [
  {
    path: '/',
    component: AdminEvents,
    name: 'admin-events'
  },
  {
    path: '/reports',
    component: Reports,
    name: 'reports'
  },
  {
    path: '/testimonials',
    component: Testimonials,
    name: 'testimonials'
  },
  {
    path: '/fees',
    component: Fees,
    name: 'fees'
  },
  {
    path: '/fee-overrides',
    component: FeeOverrides,
    name: 'fee-overrides'
  },
  {
    path: '/audits',
    component: Audit,
    name: 'audits'
  },
  {
    path: '/promo-codes',
    component: PromoCodes,
    name: 'promo-codes'
  },
  {
    path: '/charity-validation',
    component: Charities,
    name: 'charity-validation'
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

export default router;