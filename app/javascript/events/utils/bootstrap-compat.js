// Import Bootstrap 5 CSS for styling compatibility
import 'bootstrap/dist/css/bootstrap.min.css';

// This file can be imported in components that require bootstrap styling
// without needing to use bootstrap-vue components

export const bootstrapUtilityClasses = {
  // Helper function to convert Bootstrap classes to Element Plus equivalents
  convertClasses(bootstrapClass) {
    // Map common bootstrap classes to Element Plus
    const classMap = {
      // Size classes
      'sm': 'small',
      'lg': 'large',
      
      // Colors/Variants
      'primary': 'primary',
      'success': 'success',
      'warning': 'warning',
      'danger': 'error',
      'info': 'info',
      
      // Add more mappings as needed
    };
    
    return classMap[bootstrapClass] || bootstrapClass;
  }
};