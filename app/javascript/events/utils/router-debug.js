/**
 * Router debug utility for troubleshooting ticket loading issues
 * 
 * This file provides utilities to track and debug router navigation 
 * issues, particularly focused on the ticket creation flow
 */

// Small utility to log router navigations with timestamps
export const logRouterNavigation = (from, to) => {
  const timestamp = new Date().toISOString()
  const fromRoute = from?.name || 'unknown'
  const toRoute = to?.name || 'unknown'
  const fromParams = JSON.stringify(from?.params || {})
  const toParams = JSON.stringify(to?.params || {})

  console.group(`%cRouter Navigation at ${timestamp}`, 'color: blue; font-weight: bold;')
  console.log(`From: ${fromRoute} (params: ${fromParams})`)
  console.log(`To: ${toRoute} (params: ${toParams})`)
  console.groupEnd()

  // If navigating to ticket-creation, add more debugging info
  if (to?.name === 'ticket-creation') {
    console.group('%cTicket Creation Debug', 'color: green; font-weight: bold;')
    console.log('Force reload flag:', localStorage.getItem('hg-force-ticket-reload'))
    console.log('Stored event ID:', localStorage.getItem('hg-current-event-id'))
    console.log('Target event ID:', to.params.eventId)
    
    // If the IDs don't match, that's a red flag
    if (localStorage.getItem('hg-current-event-id') !== to.params.eventId) {
      console.warn('%cEvent ID mismatch between route and localStorage!', 'color: red;')
    }
    
    // Log timestamp of last data load
    const lastLoaded = localStorage.getItem('hg-event-last-loaded')
    console.log('Last data load:', lastLoaded ? new Date(parseInt(lastLoaded)).toISOString() : 'never')
    
    console.groupEnd()
  }

  return { 
    fromRoute, 
    toRoute, 
    timestamp,
    hasEventIdChanged: from?.params?.eventId !== to?.params?.eventId
  }
}

// Track ticket loading API calls
export const logTicketApiCall = (eventId, action, data = null) => {
  const timestamp = new Date().toISOString()
  console.group(`%cTicket API Call: ${action} at ${timestamp}`, 'color: purple; font-weight: bold;')
  console.log(`Event ID: ${eventId}`)
  if (data) {
    console.log('Response data:', data)
  }
  console.groupEnd()
}

// Create history of navigation events for troubleshooting
const navigationHistory = []
const MAX_HISTORY_LENGTH = 10

export const addToNavigationHistory = (from, to) => {
  const entry = {
    timestamp: Date.now(),
    fromRoute: from?.name,
    toRoute: to?.name,
    fromParams: from?.params,
    toParams: to?.params,
    eventIdChanged: from?.params?.eventId !== to?.params?.eventId
  }
  
  navigationHistory.unshift(entry)
  
  // Limit history length
  if (navigationHistory.length > MAX_HISTORY_LENGTH) {
    navigationHistory.pop()
  }
  
  return navigationHistory
}

export const getNavigationHistory = () => {
  return navigationHistory
}

/**
 * Initialize router debugging by attaching our debug listeners 
 * @param {object} router - Vue Router instance
 */
export const initRouterDebug = (router) => {
  if (!router) {
    console.warn('No router provided to initRouterDebug')
    return
  }
  
  console.log('%c[Router Debug] Initializing router debugging', 'color: #8a2be2')
  
  // Add afterEach hook to track all navigation
  router.afterEach((to, from) => {
    logRouterNavigation(from, to)
    addToNavigationHistory(from, to)
    
    // Log the current URL hash for debugging
    console.log(`[Router Debug] URL hash: ${window.location.hash}`)
  })
  
  return true
}
