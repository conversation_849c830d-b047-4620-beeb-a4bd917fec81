// Debug utility for HGEvents
// This function can be used in the browser console to test the ticket persistence
window.testTicketPersistence = function() {
  console.group('Ticket Persistence Test');
  
  // Get current event ID
  const currentEventId = localStorage.getItem('hg-current-event-id');
  console.log('Current event ID:', currentEventId);
  
  // Check all relevant localStorage items
  console.log('localStorage items:');
  const relevantKeys = [
    'hg-current-event-id',
    'hg-event-last-loaded',
    'hg-tickets-last-loaded',
    'hg-force-ticket-reload',
    'hg-last-active-time',
    'hg-drawer-mini-state'
  ];
  
  relevantKeys.forEach(key => {
    console.log(`  ${key}:`, localStorage.getItem(key));
  });
  
  // Check if Pinia store is available
  console.log('Checking Pinia store...');
  if (window.__pinia) {
    const storeKeys = Object.keys(window.__pinia);
    console.log('Store keys:', storeKeys);
    
    if (window.__pinia.event) {
      const eventStore = window.__pinia.event;
      console.log('Event store data:', eventStore);
      console.log('Event ID in store:', eventStore.event?.id);
      console.log('Tickets count:', eventStore.event?.tickets?.length || 0);
      console.log('Ticket groups count:', eventStore.event?.ticket_groups?.length || 0);
    } else {
      console.warn('Event store not found in Pinia');
    }
  } else {
    console.warn('Pinia store not available');
  }
  
  // Parse the current URL
  const urlPath = window.location.hash;
  console.log('Current URL hash:', urlPath);
  
  const ticketsRegex = /\/tickets\/(\d+)/;
  const match = urlPath.match(ticketsRegex);
  if (match && match[1]) {
    console.log('Event ID from URL:', match[1]);
    if (match[1] !== currentEventId) {
      console.warn('URL event ID does not match localStorage event ID!');
    }
  } else {
    console.log('No event ID found in URL');
  }
  
  console.groupEnd();
  
  return 'Ticket persistence test completed. Check the console for results.';
};

// This function can be used to manually force a ticket reload
window.forceTicketReload = function() {
  localStorage.setItem('hg-force-ticket-reload', 'true');
  return 'Force reload flag set. Refresh the page to reload tickets.';
};

// This function can be used to clear ticket data from localStorage
window.clearTicketData = function() {
  const relevantKeys = [
    'hg-current-event-id',
    'hg-event-last-loaded',
    'hg-tickets-last-loaded',
    'hg-force-ticket-reload',
    'hg-last-active-time'
  ];
  
  relevantKeys.forEach(key => {
    localStorage.removeItem(key);
  });
  
  return 'Ticket data cleared from localStorage. Refresh the page to reload.';
};

console.log('HGEvents debug utilities loaded. Available functions: testTicketPersistence(), forceTicketReload(), clearTicketData()');
