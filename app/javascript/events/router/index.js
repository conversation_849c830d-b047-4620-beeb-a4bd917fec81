import { createRouter, createWebHashHistory } from 'vue-router'
import { reactive, readonly } from 'vue'
import EventsMain from '../components/EventsMain.vue'
import Event from '../components/Event.vue'
import EventDetails from '../components/details/EventDetails.vue'
import Tickets from '../components/tickets/TicketsCreation.vue'
import RegQuestions from '../components/additional/RegQuestions.vue'
import Terms from '../components/terms/Terms.vue'
import EventPreview from '../components/preview/EventPreview.vue'
import EventWizard from '../views/EventWizard.vue'
import CustomiseEmails from '@/events/components/customise_emails/Start.vue'
import CustomiseInvite from '@/events/components/customise_emails/CustomiseInvite.vue'
import CustomiseConfirmation from '@/events/components/customise_emails/CustomiseConfirmation.vue'
import EmailPreview from '@/events/components/customise_emails/EmailPreview.vue'
import UploadContacts from '@/events/components/contacts/UploadContacts.vue'
import { ticketNavigationGuard } from '@/shared/router/ticketRoutesHandler.js'
import { initRouterDebug } from '@/events/utils/router-debug.js'
// import { checkAuthentication, startSessionKeepAlive } from '../utils/authHelper'
// import Payments from '../components/payments/Payments.vue'
// import PaymentOptions from '../components/payments/PaymentOptions.vue'
// import GoLive from '../components/payments/GoLive.vue'
// import Promote from '../components/promote/Promote.vue'
// import PromoteShare from '../components/promote/PromoteShare.vue'
// import SendInvites from '../components/promote/SendInvites.vue'

// Create reactive router state with Context7 pattern
const state = reactive({
  currentRoute: null,
  previousRoute: null,
  isNavigating: false
})

// Actions for router state management
const actions = {
  setCurrentRoute(route) {
    state.previousRoute = state.currentRoute
    state.currentRoute = route
  },
  setNavigating(value) {
    state.isNavigating = value
  }
}

// Define routes
const routes = [
  // {
  //   path: '/event_details/',
  //   name: 'event-details-controller',
  //   component: EventDetails
  // },
  {
    path: '/',
    name: 'create-event',
    component: EventDetails,
  },
  {
    path: '/:id',
    name: 'edit-event',
    component: EventDetails,
    props: true
  },
  // {
  //   path: '/event/:id/wizard',
  //   name: 'event-wizard',
  //   component: EventWizard,
  // },
  {
    path: '/tickets/:eventId?',
    name: 'ticket-creation',
    component: Tickets,
    props: true
  },
  {
    path: '/reg-questions/:eventId?',
    name: 'reg-questions',
    component: RegQuestions,
    props: true
  },
  {
    path: '/terms/:eventId?',
    props: true,
    name: 'terms',
    component: Terms
  },
  {
    path: '/event-attendees/:eventId?',
    name: 'event-attendees',
    component: UploadContacts,
    props: true
  },

  // {
  //   path: '',
  //   component: Event,
  //   name: 'event',
  //   redirect: { name: 'event-details' },
  //   children: [
  //     {
  //       path: 'details',
  //       name: 'event-details',
  //       component: EventDetails
  //     },
  //     {
  //       path: 'tickets',
  //       name: 'ticket-creation',
  //       component: Tickets
  //     },
  //     {
  //       path: 'additional',
  //       name: 'additional',
  //       component: RegQuestions
  //     },
  //     {
  //       path: 'terms',
  //       name: 'terms',
  //       component: Terms
  //     },
  //     {
  //       path: 'preview',
  //       name: 'preview',
  //       component: EventPreview
  //     }
  //   ]
  // },
  {
    path: '/customise-emails/:eventId?',
    props: true,
    component: CustomiseEmails,
    name: 'customise-emails',
    redirect: { name: 'upload-contacts' },
    children: [
      {
        path: '',
        name: 'upload-contacts',
        component: CustomiseInvite,
        props: true
      },
      {
        path: 'customise-invite',
        name: 'customise-invite',
        component: CustomiseInvite,
        props: true
      },
      {
        path: 'customise-confirmation',
        name: 'customise-confirmation',
        component: CustomiseConfirmation,
        props: true
      },
      {
        path: 'email-preview',
        name: 'email-preview',
        component: EmailPreview,
        props: true
      }
    ]
  },
  // {
  //   path: 'payments',
  //   component: Payments,
  //   name: 'payments',
  //   redirect: { name: 'payment-options' },
  //   children: [
  //     {
  //       path: '',
  //       name: 'payment-options',
  //       component: PaymentOptions
  //     },
  //     {
  //       path: 'go-live',
  //       name: 'go-live',
  //       component: GoLive
  //     }
  //   ]
  // },
  // {
  //   path: 'promote',
  //   component: Promote,
  //   name: 'promote',
  //   redirect: { name: 'promote-share' },
  //   children: [
  //     {
  //       path: '',
  //       name: 'promote-share',
  //       component: PromoteShare
  //     },
  //     {
  //       path: 'send-invites',
  //       name: 'send-invites',
  //       component: SendInvites
  //     }
  //   ]
  // }
]

// Create router instance with hash mode for better Rails compatibility
export const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// Initialize router debugging
initRouterDebug(router);

// Setup navigation guards to track route changes
router.beforeEach(ticketNavigationGuard);

// router.beforeEach(async (to, from, next) => {
//   actions.setNavigating(true)
//   console.log(`Router navigation: ${from.name} -> ${to.name}`, { 
//     from: { 
//       name: from.name, 
//       params: from.params,
//       path: from.path 
//     }, 
//     to: { 
//       name: to.name, 
//       params: to.params,
//       path: to.path 
//     } 
//   })

// Try to check authentication state
// try {
//   // Check authentication status
//   const isAuthenticated = await checkAuthentication()
//   console.log(`Authentication check: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`)

//   if (!isAuthenticated) {
//     console.warn('Authentication check failed, redirecting to login')
//     window.location.href = '/users/sign_in'
//     return
//   }

//   // If we're authenticated, start the session keep-alive
//   startSessionKeepAlive()

//   // Handle navigation to tickets page
//   if (to.name === 'ticket-creation') {
//     // If eventId param exists, use it
//     const eventId = to.params.eventId

//     if (eventId) {
//       console.log(`Navigating to tickets with event ID: ${eventId}`)
//     } else {
//       console.log('No event ID in route params for ticket-creation')
//     }
//   }

//   next()
// } catch (error) {
//   console.error('Error during navigation auth check:', error)
//   next() // Still allow navigation in case of error
// }
// })

router.afterEach((to) => {
  actions.setCurrentRoute(to)
  actions.setNavigating(false)
  console.log(`Navigation complete: ${to.name}`, { params: to.params })
})

// Create Context7 router store
export const routerStore = {
  state: readonly(state),
  ...actions,
  getCurrentRouteName: () => state.currentRoute?.name,
  getPreviousRouteName: () => state.previousRoute?.name,
  isActiveRoute: (name) => state.currentRoute?.name === name,
  isChildOfRoute: (parentName) => {
    return state.currentRoute?.matched.some(record => record.name === parentName)
  }
}

export { routes }
