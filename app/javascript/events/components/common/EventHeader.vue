<template>
  <div class="event-header q-my-md">
    <q-card flat bordered class="event-header-card">
      <q-card-section>
        <div class="row justify-between items-center">
          <div class="col-9">
            <div class="text-h6 text-weight-bold event-title" :style="titleStyle">
              {{ event?.title || 'New Event' }}
            </div>
            <div class="text-caption q-mt-xs text-grey-8">
              <span v-if="event?.id" class="event-id">
                <q-icon name="tag" size="xs" class="q-mr-xs" />
                Event ID: {{ event.id }}
              </span>
            </div>
          </div>
          <div class="col-3 text-right">
            <q-chip v-if="event?.id" color="secondary" text-color="white" dense>
              <q-icon name="calendar_today" left size="xs" />
              {{ formatEventDate }}
            </q-chip>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useEventStore } from '@/stores/event'
import { date } from 'quasar'

const eventStore = useEventStore()
const event = computed(() => eventStore.getEvent)

// Format the event date for display
const formatEventDate = computed(() => {
  if (!event.value || !event.value.datetimefrom) return ''
  
  const fromDate = new Date(event.value.datetimefrom)
  return date.formatDate(fromDate, 'D MMM YYYY')
})

// Use event color for title if available
const titleStyle = computed(() => {
  return event.value && event.value.phcolour 
    ? { color: event.value.phcolour } 
    : {}
})
</script>

<style scoped>
.event-header-card {
  background-color: #f8f8f8;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.event-title {
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
