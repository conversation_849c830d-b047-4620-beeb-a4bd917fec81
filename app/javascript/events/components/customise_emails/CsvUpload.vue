<template>
  <div class="csv-upload">
    <q-card>
      <q-card-section class="text-h5">
        Upload Contacts from CSV
      </q-card-section>
      
      <q-card-section>
        <div class="upload-container">
          <div 
            class="upload-area" 
            @dragover.prevent 
            @drop.prevent="handleDrop"
            :class="{ 'upload-area-active': isDragging }"
            @dragenter="isDragging = true"
            @dragleave="isDragging = false"
          >
            <input
              type="file"
              ref="fileInput"
              accept=".csv"
              @change="handleFileSelect"
              style="display: none"
            />
            <div class="upload-content" @click="triggerFileInput">
              <q-icon name="cloud_upload" size="4rem" color="primary" class="q-mb-md" />
              <p>Drag and drop your CSV file here or click to browse</p>
              <div class="text-grey-7 text-caption">Supported format: CSV</div>
            </div>
          </div>

          <div v-if="selectedFile" class="selected-file q-mt-md">
            <div class="file-info">
              <q-icon name="description" color="primary" size="md" />
              <span>{{ selectedFile.name }}</span>
              <q-space />
              <q-btn
                color="negative"
                flat
                round
                icon="close"
                size="sm"
                @click="removeFile"
              />
            </div>
          </div>

          <div class="q-mt-md">
            <q-btn
              color="primary"
              @click="uploadFile"
              :disable="!selectedFile || uploading"
              :loading="uploading"
              label="Upload File"
            />
          </div>
        </div>

        <q-banner v-if="uploadError" class="bg-negative text-white q-mt-md" rounded>
          {{ uploadError }}
        </q-banner>

        <q-banner v-if="uploadSuccess" class="bg-positive text-white q-mt-md" rounded>
          File uploaded successfully! {{ totalContacts }} contacts imported.
        </q-banner>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()

// State
const fileInput = ref(null)
const selectedFile = ref(null)
const uploading = ref(false)
const uploadError = ref('')
const uploadSuccess = ref(false)
const totalContacts = ref(0)
const isDragging = ref(false)

// Methods
const triggerFileInput = () => {
  fileInput.value.click()
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    validateAndSetFile(file)
  }
}

const handleDrop = (event) => {
  isDragging.value = false
  const file = event.dataTransfer.files[0]
  if (file) {
    validateAndSetFile(file)
  }
}

const validateAndSetFile = (file) => {
  if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
    $q.notify({
      color: 'negative',
      message: 'Please upload a CSV file',
      icon: 'error'
    })
    return
  }
  selectedFile.value = file
  uploadError.value = ''
  uploadSuccess.value = false
}

const removeFile = () => {
  selectedFile.value = null
  fileInput.value.value = ''
  uploadError.value = ''
  uploadSuccess.value = false
}

const uploadFile = async () => {
  if (!selectedFile.value) return

  uploading.value = true
  uploadError.value = ''
  uploadSuccess.value = false

  const formData = new FormData()
  formData.append('file', selectedFile.value)

  try {
    const response = await fetch('/upload_contacts', {
      method: 'POST',
      body: formData
    })

    if (response.ok) {
      const data = await response.json()
      totalContacts.value = data.total_contacts
      uploadSuccess.value = true
      $q.notify({
        color: 'positive',
        message: 'Contacts imported successfully',
        icon: 'check'
      })
    } else {
      throw new Error('Failed to upload file')
    }
  } catch (error) {
    uploadError.value = 'Failed to upload file. Please try again.'
    $q.notify({
      color: 'negative',
      message: 'Failed to upload file',
      icon: 'error'
    })
  } finally {
    uploading.value = false
  }
}
</script>

<style scoped>
.upload-container {
  max-width: 600px;
  margin: 0 auto;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover, .upload-area-active {
  border-color: var(--q-primary);
}

.upload-content {
  color: #666;
}

.selected-file {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>