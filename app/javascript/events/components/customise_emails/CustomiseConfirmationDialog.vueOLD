<template>
<div>
  <!--// TODO component for use in both emails -->
  <b-btn v-b-modal="'customiseConfEmailModal'">Click to customise email</b-btn>

  <b-modal id="customiseConfEmailModal" ref="confModal" size="lg" @show="setData" @ok="handleOk" @cancel="onReset" title="Customise Your Email">
    <div class="card card-body">

      <b-form @submit.stop.prevent="saveEmailTemplate" @reset="onReset">
        <b-form-group label="Greeting:" label-for="greeting" description="e.g. Hello or Hi etc.">
          <b-form-input v-model="confirmation_email_template.welcome_text" name="greeting" v-validate="'required'" :class="{'quill-invalid': errors.has('greeting')}">
          </b-form-input>
          <div class="text-danger" v-show="errors.has('greeting')">
            <small>{{ errors.first('greeting')}}</small>
          </div>
        </b-form-group>

        <b-form-group label-for="forenameOnly">
        <b-form-checkbox id="forenameOnly" v-model="confirmation_email_template.show_forename_only">
            Click to show only the forename in the greeting
        </b-form-checkbox>
        </b-form-group>

        <b-form-group label="Edit email details" label-for="emailDetails">
          <quill-editor name="emailDetails" data-vv-as="email details" v-validate="'required'" :class="{'quill-invalid': errors.has('emailDetails')}" v-model="confirmation_email_template.details" ref="myQuillEditor" :options="editorOptions">
          </quill-editor>
          <div class="text-danger" v-show="errors.has('emailDetails')">
            <small>{{ errors.first('emailDetails')}}</small>
          </div>

          <b-button @click="resetDetails()" size="sm">Reset Confirmation Details To Match Event Details</b-button>
        </b-form-group>

        <div class="text-danger" v-show="errors.has('description')">
          <small>{{ errors.first('description')}}</small>
        </div>

        <b-form-group label="Tickets Title Text" label-for="rsvpText" id="rsvpText">
          <b-form-input id="rsvpText" data-vv-as="tickets table title" name="rsvpText" v-validate="'required'" v-model="confirmation_email_template.title2">
          </b-form-input>
          <div class="text-danger" v-show="errors.has('rsvpText')">
            <small>{{ errors.first('rsvpText')}}</small>
          </div>
        </b-form-group>

         <b-form-group label=" Change thank you message" label-for="thanksMessage" id="thanksMessage">
          <b-form-input id="rsvpText" data-vv-as="thank you message" name="thanksMessage" v-validate="'required'" v-model="confirmation_email_template.text_area_3">
          </b-form-input>
          <div class="text-danger" v-show="errors.has('thanksMessage')">
            <small>{{ errors.first('thanksMessage')}}</small>
          </div>
        </b-form-group>

        <colour-picker-email v-if="event && event.image1" :event='event'></colour-picker-email>

      </b-form>
    </div>

  </b-modal>

</div>
</template>

<script>
import colourPickerEmail from "./../../common/colour-picker-email.vue";

import dayjs from "dayjs";

export default {
  props: ["event"],

  data() {
    return {
      confirmation_email_template: null,
      editorOptions: {}
    };
  },

  created() {
    this.setData();
  },

  methods: {
    setData() {
      this.confirmation_email_template = null;

      if (this.event.confirmation_email_template) {
        this.confirmation_email_template = JSON.parse(
          JSON.stringify(this.event.confirmation_email_template)
        );
      }

      if (!this.confirmation_email_template) {
        this.confirmation_email_template = {
          welcome_text: "Hi",
          show_forename_only: false,
          text_area_1: "The event will take place at " + this.buildAddress(),
          text_area_2:
            "Please see below your ticket details. If they are incorrect, please contact the organiser.",
          text_area_3:
            "Thank you again for your interest in our event. We look forward to seeing you there!",
          title1: " Thank you for booking your place at " + this.event.title,
          title2: "Tickets Selected",
          email_type: "confirmation"
        };
      }
    },

    resetDetails() {
      this.confirmation_email_template.details = this.event.details;
      this.$forceUpdate();
    },

    onReset() {
      this.setData();
    },

    handleOk(evt) {
      var self = this;
      // Prevent modal from closing
      evt.preventDefault();

      this.$validator
        .validateAll()
        .then(result => {
          if (!result) {
            self.$message.error("Please fix the validation issues.");
            return;
          }
          self.saveEmailTemplate();
        })
        .catch(error => {
          self.$message.error("Error while validating tickets.");
        });
    },

    saveEmailTemplate() {
      var self = this;
      var eventDetails = Object.assign({}, this.event);

      this.eventPostable = {
        event: eventDetails
      };

      var event = this.eventPostable.event;
      // TODO use generic step generator to call server
      event.step = 2.2;
      event.email_templates_attributes = [];

      event.email_templates_attributes.push({
        id: this.confirmation_email_template.id,
        show_forename_only: this.confirmation_email_template.show_forename_only,
        welcome_text: this.confirmation_email_template.welcome_text,
        text_area_1: this.confirmation_email_template.text_area_1,
        text_area_2: this.confirmation_email_template.text_area_2,
        text_area_3: this.confirmation_email_template.text_area_3,
        title1: this.confirmation_email_template.title1,
        title2: this.confirmation_email_template.title2,
        details: this.confirmation_email_template.details,
        email_type: "confirmation"
      });

      this.$http
        .put(
          "/events/" + self.event.id + "/update_email_templates.json",
          self.eventPostable
        )
        .then(
          function() {
            self.$message.success("Confirmation Template Saved");
            self.$emit("reloadIframe");

            // Allows this to be persisted between pages
            self.event.confirmation_email_template =
              event.email_templates_attributes[0]; //Zero because it gets stuck in an array

            self.$store.commit("setEvent", self.event);
            self.$refs.confModal.hide();
            self.$notify.success({
              title: "Email Updated",
              message: "Your confirmation email details have been updated!"
            });
          },
          function(response) {
            self.$swal(
              "Confirmation Email Template Not Updated: " +
                JSON.stringify({
                  data: response.data
                })
            );
          }
        )
        .catch(e => {
          console.log(e);
        });
    },

    // TODO duplicated from the mixin, may want to remove from
    buildAddress() {
      var address = this.event.location + ", " + this.event.address1 + ", ";
      if (this.event.address2) {
        address += this.event.address2 + ", ";
      }
      address += this.event.city + ", ";
      if (this.event.county) {
        address += this.event.county + ", ";
      }
      address += this.event.postcode;

      return address;
    }
  },

  components: {
    colourPickerEmail: colourPickerEmail
  }
};
</script>
