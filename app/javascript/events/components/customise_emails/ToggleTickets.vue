<template>
  <div class="q-pa-sm">
    <div class="row items-center q-gutter-sm">
      <q-toggle
        v-model="event.print_tickets"
        :label="event.print_tickets ? 'Printable Tickets' : 'No Printable Tickets'"
        @update:model-value="togglePrintTickets"
        color="primary"
      />
      
      <q-btn
        flat
        round
        dense
        icon="info"
        color="grey-6"
        size="sm"
      >
        <q-tooltip class="bg-primary text-white text-body2" anchor="top middle" self="bottom middle">
          Select this if you wish to produce a pdf document consisting of printable tickets for your event.
        </q-tooltip>
      </q-btn>
    </div>
  </div>
</template>

<script setup>
import { useQuasar } from 'quasar'

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
})

const $q = useQuasar()

const togglePrintTickets = async (value) => {
  try {
    const response = await fetch(`/events/${props.event.id}/toggle_print_tickets.json`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        toggle: value
      })
    })

    if (!response.ok) {
      throw new Error('Failed to update preferences')
    }

    $q.notify({
      type: 'positive',
      message: 'Preferences Updated'
    })
  } catch (error) {
    console.error('Error updating print tickets preference:', error)
    $q.notify({
      type: 'negative',
      message: 'Could Not Update.'
    })
  }
}
</script>
