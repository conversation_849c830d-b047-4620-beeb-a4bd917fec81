<template>
  <div>
    <div class="col-12 mx-auto">
      <div v-if="event" class="card mx-auto">
        <div class="card-header hg-underline">
          Customise Confirmation Email

        </div>

        <div class="card-body">
          <div class="row">
            <div class="col-4">
              <el-upload v-if="advanced_user" :action="'/document_upload/upload_file?email_type=confirmation&event_id=' + event.id" :on-remove="handleRemove" :headers="headers" multiple :limit="5" :on-exceed="handleExceed" :on-success="handleSuccess" :file-list="attachmentsList">
                <el-button size="small" type="primary">Click to Upload Attachments</el-button>
                <div slot="tip" class="el-upload__tip">e.g. Documents for Your Event</div>
              </el-upload>
            </div>

            <toggle-tickets :event="event"></toggle-tickets>

            <div class="col-4">
              <customise-conf-emails @reloadIframe="reloadIframe" class="float-right" :event="event"></customise-conf-emails>
            </div>
          </div>
        </div>

      </div>

      <b-card title="Confirmation Email Preview">
        <div v-if="url" style="min-height: 800px" class="embed-responsive embed-responsive-16by9">
          <iframe @load="load" id="confIframe" ref="confIframe" class="embed-responsive-item" :src="url" allowfullscreen></iframe>
        </div>
      </b-card>

      <div class="card hg-topline">
        <div class="card-body" align="center">
          <div class="col-8 mx-auto">
            <button class="btn btn-primary btn-lg" @click="goToEmailPreview()" type="button">View Email Previews</button>
          </div>
        </div>
      </div>

    </div>

  </div>

</template>

<script>
import templateMixins from "./email_template_mixins";
import customiseConfirmationDialog from "./CustomiseConfirmationDialog.vue";
import toggleTickets from "./ToggleTickets.vue";

import dayjs from "dayjs";

export default {
  mixins: [templateMixins],

  components: {
    customiseConfEmails: customiseConfirmationDialog,
    toggleTickets: toggleTickets
  },

  data() {
    return {
      event: this.$store.getters.getEvent,
      image_bucket: window.imageBucket,
      app_image_bucket: appImageBucket,
      advanced_user: this.$store.getters.getAdvancedUser,
      emailType: "confirmation",
      headers: {
        "X-CSRF-Token": document
          .getElementsByName("csrf-token")[0]
          .getAttribute("content")
      },
      url: null
    };
  },

  created() {
    this.url = "/email_preview/" + this.event.id + "/show_confirmation";
  },

  methods: {
    load() {
      $("#confIframe")
        .contents()
        .find(".body")
        .css("pointer-events", "none");
    },

    goToEmailPreview() {
      // TODO update to step
      this.$router.push({
        name: "email-preview"
      });
    },

    reloadIframe() {
      this.url =
        "/email_preview/" +
        this.event.id +
        "/show_confirmation?rand" +
        Math.random;
      this.$refs.confIframe.src = this.url;
    }
  }
};
</script>
