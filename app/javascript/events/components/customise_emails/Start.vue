<template>
  <div class="customise-emails">
    <q-card class="q-mb-md">
      <q-card-section class="text-h5"> Customise Emails </q-card-section>

      <q-card-section class="q-pa-none">
        <q-tabs
          v-model="activeTab"
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="justify"
          narrow-indicator
        >
          <q-tab
            name="customise-invite"
            label="Customise Invite"
            class="tab-item"
            @click="nav('customise-invite')"
          >
            <q-icon
              v-if="stepDone >= 1"
              name="check_circle"
              color="positive"
              size="xs"
              class="q-ml-xs"
            />
          </q-tab>

          <q-tab
            name="customise-confirmation"
            label="Customise Confirmation"
            class="tab-item"
            @click="nav('customise-confirmation')"
          >
            <q-icon
              v-if="stepDone >= 2"
              name="check_circle"
              color="positive"
              size="xs"
              class="q-ml-xs"
            />
          </q-tab>

          <q-tab
            name="email-preview"
            label="Send Preview Emails"
            class="tab-item"
            @click="nav('email-preview')"
          >
            <q-icon
              v-if="stepDone >= 3"
              name="check_circle"
              color="positive"
              size="xs"
              class="q-ml-xs"
            />
          </q-tab>
        </q-tabs>
      </q-card-section>
    </q-card>

    <div class="q-pa-md">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";

// Props
const props = defineProps({
  eventId: {
    type: [String, Number],
    default: null,
  },
});

const route = useRoute();
const router = useRouter();
const $q = useQuasar();
const eventStore = useEventStore();

const activeTab = ref("customise-invite");
const stepDone = ref(0);

// Keep active tab in sync with route
watch(
  () => route.name,
  (name) => {
    if (name === "customise-invite") {
      activeTab.value = "customise-invite";
      stepDone.value = Math.max(stepDone.value, 1);
    } else if (name === "customise-confirmation") {
      activeTab.value = "customise-confirmation";
      stepDone.value = Math.max(stepDone.value, 2);
    } else if (name === "email-preview") {
      activeTab.value = "email-preview";
      stepDone.value = Math.max(stepDone.value, 3);
    }
  },
  { immediate: true }
);

// Error handling for missing event context
const handleMissingEventContext = () => {
  $q.notify({
    type: "negative",
    message: "Event context is required for email customization",
    caption: "Please select an event first",
    position: "top",
    timeout: 4000,
    actions: [
      {
        label: "Go to Events",
        color: "white",
        handler: () => {
          router.push({ name: "dash-main" });
        },
      },
    ],
  });
};

// Validate event context before navigation
const validateEventContext = () => {
  if (!props.eventId) {
    handleMissingEventContext();
    return false;
  }

  const event = eventStore.getSafeEvent;
  if (!event || !event.id) {
    $q.notify({
      type: "warning",
      message: "Event data is loading...",
      caption: "Please wait while we load the event details",
      position: "top",
      timeout: 2000,
      spinner: true,
    });
    return false;
  }

  return true;
};

const nav = (pathTo) => {
  if (!validateEventContext()) return;

  router.push({
    name: pathTo,
    params: props.eventId ? { eventId: props.eventId } : {},
  });
};

onMounted(async () => {
  if (props.eventId) {
    try {
      await eventStore.ensureEventLoaded(props.eventId);
    } catch (error) {
      console.error("Failed to load event in Start component:", error);
      $q.notify({
        type: "negative",
        message: "Failed to load event details",
        caption:
          "Please try refreshing the page or selecting a different event",
        position: "top",
        timeout: 5000,
        actions: [
          {
            label: "Go Back",
            color: "white",
            handler: () => {
              router.push({ name: "dash-main" });
            },
          },
        ],
      });
    }
  } else {
    handleMissingEventContext();
  }
});
</script>

<style lang="scss" scoped>
.customise-emails {
  .q-tabs {
    border-radius: 4px 4px 0 0;

    :deep(.q-tab) {
      cursor: pointer;
      font-weight: 500;

      &.q-tab--active {
        font-weight: 600;
      }
    }

    :deep(.q-tab__content) {
      min-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      gap: 4px;
    }

    :deep(.q-tab__indicator) {
      height: 3px;
      border-radius: 2px;
    }
  }

  .tab-item {
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}
</style>
