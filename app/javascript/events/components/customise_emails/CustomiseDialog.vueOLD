<template>
<div>
  <!--// TODO component for use in both emails -->
  <b-btn v-b-modal="'customiseInviteEmailModal'">Click to customise email</b-btn>

  <b-modal id="customiseInviteEmailModal" ref="inviteModal" size="lg" @show="setData" @ok="handleOk" @cancel="onReset" title="Customise Your Email">
    <div class="card card-body">

      <b-form @submit.stop.prevent="saveInviteEmailTemplate" @reset="onReset">
        <b-form-group label="Greeting:" label-for="greeting" description="e.g. Hello or Hi etc.">
          <b-form-input v-model="invite_email_template.welcome_text" name="greeting" v-validate="'required'" :class="{'quill-invalid': errors.has('greeting')}">
          </b-form-input>
          <div class="text-danger" v-show="errors.has('greeting')">
            <small>{{ errors.first('greeting')}}</small>
          </div>
        </b-form-group>

        <b-form-group label-for="forenameOnly">
          <b-form-checkbox id="forenameOnly" v-model="invite_email_template.show_forename_only">
            Click to show only the forename in the greeting
          </b-form-checkbox>
        </b-form-group>

        <b-form-group label="Edit email details" label-for="emailDetails">
          <quill-editor name="emailDetails" data-vv-as="email details" v-validate="'required'" :class="{'quill-invalid': errors.has('emailDetails')}" v-model="invite_email_template.details" ref="myQuillEditor" :options="editorOptions">
          </quill-editor>
          <div class="text-danger" v-show="errors.has('emailDetails')">
            <small>{{ errors.first('emailDetails')}}</small>
          </div>

          <b-button @click="resetInvitationDetails()" size="sm">Reset Invite Details To Match Event Details</b-button>
        </b-form-group>

        <div class="text-danger" v-show="errors.has('description')">
          <small>{{ errors.first('description')}}</small>
        </div>

        <b-form-group label="RSVP text" label-for="rsvpText" id="rsvpText">
          <b-form-input id="rsvpText" data-vv-as="rsvp text" name="rsvpText" v-validate="'required'" v-model="invite_email_template.title2">
          </b-form-input>
          <small style="color: red">Please check your RSVP date is correct!</small>
          <div class="text-danger" v-show="errors.has('rsvpText')">
            <small>{{ errors.first('rsvpText')}}</small>
          </div>
        </b-form-group>

        <colour-picker-email v-if="event && event.image1" :event='event'></colour-picker-email>

      </b-form>
    </div>

  </b-modal>

</div>
</template>

<script>
import colourPickerEmail from "./../../common/colour-picker-email.vue";

import dayjs from 'dayjs'

export default {
  props: ['event'],

  data() {
    return {
      invite_email_template: null,
      editorOptions: {},
    }
  },

  created() {
    this.setData()
  },

  methods: {

    setData(){
      this.invite_email_template = null

      if (this.event.invite_email_template) {
        this.invite_email_template = JSON.parse(JSON.stringify(this.event.invite_email_template))
      }

      if (!this.invite_email_template) {

        this.invite_email_template = {
          welcome_text: "Hi",
          show_forename_only: false,
          title1: "You are invited to " + this.event.title,
          text_area_1: "The event will take place at " + this.buildAddress(),
          title2: "Please RSVP by clicking the button below before " + dayjs(this.event.datetimefrom).format('MMMM DD, YYYY'),
          text_area_3: "If you have an enquiry about this event, please contact the event organiser " + this.event.organiser,
          text_area_2: "" + this.event.summary,
          ticket_options_title: "Event Details",
          details: this.event.details,
          email_type: "invite"
        };
      }
    },

    resetInvitationDetails() {
      this.invite_email_template.details = this.event.details
    },

    onReset() {
      this.setData()
    },

    handleOk (evt) {
      var self = this
      // Prevent modal from closing
      evt.preventDefault()

      this.$validator
        .validateAll()
        .then(result => {
          if (!result) {
            self.$message.error("Please fix the validation issues.");
            return;
          }
          self.saveInviteEmailTemplate()
        })
        .catch(error => {
          self.$message.error("Error while validating tickets.");
        });
    },

    saveInviteEmailTemplate() {
      var self = this;
      var eventDetails = Object.assign({}, this.event);

      this.eventPostable = {
        event: eventDetails
      };

      var event = this.eventPostable.event;
      // TODO use generic step generator to call server
      event.step = 2.1;
      event.email_templates_attributes = [];

      event.email_templates_attributes.push({
        id: this.invite_email_template.id,
        show_forename_only: this.invite_email_template.show_forename_only,
        welcome_text: this.invite_email_template.welcome_text,
        text_area_1: this.invite_email_template.text_area_1,
        text_area_2: this.invite_email_template.text_area_2,
        text_area_3: this.invite_email_template.text_area_3,
        title1: this.invite_email_template.title1,
        title2: this.invite_email_template.title2,
        ticket_options_title: this.invite_email_template.ticket_options_title,
        details: this.invite_email_template.details,
        email_type: "invite"
      });

      //TODO add to Store
      // this.event.email_templates = $scope.email_templates;

      this.$http
        .put("/events/" + self.event.id + "/update_email_templates.json", self.eventPostable)
        .then((resp) => {
            self.$message.success("Invite Template Saved");
            self.$emit('reloadIframe')

            // Allows this to be persisted between pages
            self.event.invite_email_template = event.email_templates_attributes[0]
            self.$store.commit('setEvent', self.event);
            self.$refs.inviteModal.hide()
            self.$notify.success({title: "Email Updated", message: "Your invite email details have been updated!"})
          })
          .catch((err) => {
            self.$swal("Invite Email Template Not Updated");
          });
    },

    // TODO duplicated from the mixin, may want to remove from
    buildAddress() {
      var address = this.event.location + ', ' + this.event.address1 + ', ';
      if (this.event.address2) {
        address += this.event.address2 + ', ';
      }
      address += this.event.city + ', ';
      if (this.event.county) {
        address += this.event.county + ', ';
      }
      address += this.event.postcode;

      return address;
    }
  },

  components: {
    colourPickerEmail: colourPickerEmail
  },
}
</script>
