<template>
  <div class="customise-confirmation">
    <div class="row q-col-gutter-lg">
      <!-- Left Column: Customization Form -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section class="text-h6">
            Customise Your Email
          </q-card-section>

          <q-card-section>
            <div
              v-if="!confirmation_email_template"
              class="text-center q-pa-lg"
            >
              <q-spinner size="lg" />
              <div class="q-mt-md">Loading email template...</div>
            </div>

            <q-form
              v-else
              ref="emailForm"
              @submit="handleSubmit"
              @reset="onReset"
              class="q-gutter-md"
            >
              <!-- Greeting -->
              <div class="field-group">
                <label class="text-subtitle2">Greeting:</label>
                <q-input
                  v-model="confirmation_email_template.welcome_text"
                  name="greeting"
                  hint="e.g. Hello or Hi etc."
                  :rules="[(val) => !!val || 'Greeting is required']"
                  bottom-slots
                  outlined
                >
                  <template v-slot:error> Greeting is required </template>
                </q-input>
              </div>

              <!-- Forename Only -->
              <div class="field-group">
                <q-checkbox
                  v-model="confirmation_email_template.show_forename_only"
                  label="Click to show only the forename in the greeting"
                />
              </div>

              <!-- Email Details Editor -->
              <div class="field-group">
                <label class="text-subtitle2 q-mb-sm">Edit email details</label>
                <q-editor
                  v-model="confirmation_email_template.details"
                  :rules="[(val) => !!val || 'Email details are required']"
                  min-height="200px"
                  class="q-mb-sm"
                />

                <q-btn
                  flat
                  color="primary"
                  size="sm"
                  label="Reset Confirmation Details To Match Event Details"
                  @click="resetDetails"
                />
              </div>

              <!-- Tickets Title Text -->
              <div class="field-group">
                <label class="text-subtitle2">Tickets Title Text</label>
                <q-input
                  v-model="confirmation_email_template.title2"
                  name="ticketsTitle"
                  :rules="[(val) => !!val || 'Tickets title is required']"
                  outlined
                  bottom-slots
                >
                  <template v-slot:error> Tickets title is required </template>
                </q-input>
              </div>

              <!-- Thank You Message -->
              <div class="field-group">
                <label class="text-subtitle2">Change thank you message</label>
                <q-input
                  v-model="confirmation_email_template.text_area_3"
                  name="thanksMessage"
                  :rules="[(val) => !!val || 'Thank you message is required']"
                  outlined
                  bottom-slots
                >
                  <template v-slot:error>
                    Thank you message is required
                  </template>
                </q-input>
              </div>

              <!-- Form Actions -->
              <div class="row justify-end q-gutter-sm q-mt-md">
                <q-btn flat label="Cancel" @click="onReset" />
                <q-btn
                  type="submit"
                  color="primary"
                  label="Save"
                  :loading="submitting"
                />
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </div>

      <!-- Right Column: Email Preview -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section class="text-h6">
            Confirmation Email Preview
          </q-card-section>

          <q-card-section>
            <div class="email-preview-container">
              <div class="email-mock-browser">
                <div class="browser-header">
                  <div class="browser-controls">
                    <span class="control control-close"></span>
                    <span class="control control-minimize"></span>
                    <span class="control control-maximize"></span>
                  </div>
                  <div class="url-bar">Email Preview</div>
                </div>

                <div class="email-content">
                  <div class="email-header">
                    <h2 class="confirmation-title">Booking Confirmed</h2>
                    <p class="event-title">
                      {{ event?.title || "Event Title" }}
                    </p>
                    <p class="organizer">
                      By {{ event?.organizer_name || "Event Organizer" }}
                    </p>
                  </div>

                  <div class="orange-divider"></div>

                  <div class="confirmation-message">
                    <div class="success-icon">
                      <q-icon
                        name="check_circle"
                        size="48px"
                        color="positive"
                      />
                    </div>
                    <p class="confirmation-text">
                      {{
                        confirmation_email_template?.text_area_3 ||
                        "Thank you for your booking! Your place has been confirmed."
                      }}
                    </p>
                  </div>

                  <div class="event-details">
                    <h3 class="details-heading">
                      {{
                        confirmation_email_template?.title2 || "Event Details"
                      }}
                    </h3>

                    <div class="detail-row">
                      <q-icon name="place" />
                      <span>{{ buildAddress() }}</span>
                    </div>

                    <div class="detail-row">
                      <q-icon name="event" />
                      <span>{{ formatEventDate() }}</span>
                    </div>

                    <div class="detail-row">
                      <q-icon name="access_time" />
                      <span>{{ formatEventTime() }}</span>
                    </div>
                  </div>

                  <div class="orange-divider"></div>

                  <div class="email-body">
                    <p class="greeting">
                      {{ confirmation_email_template?.welcome_text || "Hi" }}
                      {{
                        confirmation_email_template?.show_forename_only
                          ? "John"
                          : "John Smith"
                      }},
                    </p>

                    <div
                      class="email-details"
                      v-if="confirmation_email_template?.details"
                      v-html="confirmation_email_template.details"
                    ></div>
                    <div v-else>
                      <p>
                        We're excited to see you at
                        {{ event?.title || "the event" }}!
                      </p>
                      <p>
                        Please save this email for your records. You may need to
                        show this confirmation at the event.
                      </p>
                    </div>

                    <div class="booking-reference">
                      <strong>Booking Reference: </strong>EVT-{{
                        Math.random().toString(36).substr(2, 9).toUpperCase()
                      }}
                    </div>

                    <div class="action-buttons">
                      <q-btn
                        class="calendar-btn"
                        color="primary"
                        label="Add to Calendar"
                      />
                      <q-btn
                        class="directions-btn"
                        outline
                        color="primary"
                        label="Get Directions"
                      />
                    </div>

                    <div class="contact-info">
                      <h4>Need Help?</h4>
                      <p>
                        If you have any questions about this event, please
                        contact the organizer:
                      </p>
                      <p>
                        <strong>Email:</strong>
                        <a href="#" class="text-primary">{{
                          event?.organizer_email || "<EMAIL>"
                        }}</a>
                      </p>
                      <p>
                        <strong>Phone:</strong>
                        {{ event?.organizer_phone || "Contact via email" }}
                      </p>
                    </div>

                    <div class="cancellation-policy">
                      <h4>Cancellation Policy</h4>
                      <p>
                        If you need to cancel your booking, please contact us at
                        least 24 hours before the event.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import dayjs from "dayjs";
import axios from "axios";

const props = defineProps({
  eventId: {
    type: [String, Number],
    required: true,
  },
});

const router = useRouter();
const $q = useQuasar();
const eventStore = useEventStore();
const submitting = ref(false);
const emailForm = ref(null);

const event = computed(() => eventStore.getEvent);
const confirmation_email_template = ref(null);

const setData = () => {
  confirmation_email_template.value = null;

  if (event.value?.confirmation_email_template) {
    confirmation_email_template.value = JSON.parse(
      JSON.stringify(event.value.confirmation_email_template)
    );
  }

  if (!confirmation_email_template.value) {
    confirmation_email_template.value = {
      welcome_text: "Hi",
      show_forename_only: false,
      title1: `Thank you for booking ${event.value.title}`,
      text_area_1: `The event will take place at ${event.value.location || ""}`,
      title2: "Your Tickets",
      text_area_3:
        "Thank you for booking. We look forward to seeing you at the event!",
      text_area_2: event.value.summary || "",
      ticket_options_title: "Event Details",
      details: event.value.details,
      email_type: "confirmation",
    };
  }
};

// Preview methods
const formatEventDate = () => {
  if (!event.value?.datetimefrom) return "TBC";

  try {
    const date = new Date(event.value.datetimefrom);
    return date.toLocaleDateString("en-GB", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (e) {
    return "TBC";
  }
};

const formatEventTime = () => {
  if (!event.value?.datetimefrom) return "TBC";

  try {
    const fromDate = new Date(event.value.datetimefrom);
    let timeString = fromDate.toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
    });

    if (event.value.datetimeto) {
      const toDate = new Date(event.value.datetimeto);
      timeString +=
        " - " +
        toDate.toLocaleTimeString("en-GB", {
          hour: "2-digit",
          minute: "2-digit",
        });
    }

    return timeString;
  } catch (e) {
    return "TBC";
  }
};

const buildAddress = () => {
  if (!event.value?.event_address) return "Event Location TBC";

  const address = event.value.event_address;
  const parts = [];

  if (address.address1) parts.push(address.address1);
  if (address.address2) parts.push(address.address2);
  if (address.city) parts.push(address.city);
  if (address.postcode) parts.push(address.postcode);

  return parts.length > 0 ? parts.join(", ") : "Event Location TBC";
};

const resetDetails = () => {
  confirmation_email_template.value.details = event.value.details;
};

const onReset = () => {
  setData();
};

const handleSubmit = async () => {
  // Validate form
  const formValid = await emailForm.value?.validate();
  if (!formValid) {
    $q.notify({
      type: "negative",
      message: "Please fix the validation issues",
    });
    return;
  }

  try {
    submitting.value = true;
    const eventDetails = { ...event.value };

    const eventPostable = {
      event: {
        ...eventDetails,
        step: 2.2,
        email_templates_attributes: [
          {
            id: confirmation_email_template.value.id,
            show_forename_only:
              confirmation_email_template.value.show_forename_only,
            welcome_text: confirmation_email_template.value.welcome_text,
            text_area_1: confirmation_email_template.value.text_area_1,
            text_area_2: confirmation_email_template.value.text_area_2,
            text_area_3: confirmation_email_template.value.text_area_3,
            title1: confirmation_email_template.value.title1,
            title2: confirmation_email_template.value.title2,
            ticket_options_title:
              confirmation_email_template.value.ticket_options_title,
            details: confirmation_email_template.value.details,
            email_type: "confirmation",
          },
        ],
      },
    };

    await axios.put(
      `/events/${props.eventId}/update_email_templates.json`,
      eventPostable
    );

    // Update store
    event.value.confirmation_email_template =
      eventPostable.event.email_templates_attributes[0];
    eventStore.setEvent(event.value);

    $q.notify({
      type: "positive",
      message: "Email Updated",
      caption: "Your confirmation email details have been updated!",
    });

    router.push({
      name: "email-preview",
      params: { eventId: props.eventId },
    });
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Failed to update confirmation email template",
    });
  } finally {
    submitting.value = false;
  }
};

onMounted(async () => {
  if (!props.eventId) {
    $q.notify({
      type: "negative",
      message: "Event ID is required",
    });
    return;
  }

  try {
    await eventStore.ensureEventLoaded(props.eventId);
    setData();
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Failed to load event details",
    });
  }
});
</script>

<style lang="scss" scoped>
.customise-confirmation {
  width: 100%;

  .field-group {
    margin-bottom: 1.5rem;
  }

  .q-editor {
    border: 1px solid rgba(0, 0, 0, 0.12);
  }
}

.email-preview-container {
  max-width: 100%;
  margin: 0;
}

.email-mock-browser {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.browser-header {
  background: #f5f5f5;
  padding: 12px 16px;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  gap: 12px;
}

.browser-controls {
  display: flex;
  gap: 6px;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control-close {
  background: #ff5f57;
}

.control-minimize {
  background: #ffbd2e;
}

.control-maximize {
  background: #28ca42;
}

.url-bar {
  flex: 1;
  background: #fff;
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
  color: #666;
}

.email-content {
  padding: 24px;
  line-height: 1.6;
}

.email-header {
  text-align: center;
  margin-bottom: 24px;
}

.confirmation-title {
  font-size: 32px;
  font-weight: bold;
  color: #28a745;
  margin: 8px 0;
}

.event-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 12px 0 8px 0;
}

.organizer {
  font-size: 16px;
  color: #666;
  margin-top: 8px;
}

.orange-divider {
  height: 3px;
  background: linear-gradient(to right, #ff6b35, #f7931e);
  margin: 24px 0;
  border-radius: 2px;
}

.confirmation-message {
  text-align: center;
  margin: 32px 0;
}

.success-icon {
  margin-bottom: 16px;
}

.confirmation-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.event-details {
  margin: 32px 0;
}

.details-heading {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 16px;
  color: #333;
}

.detail-row .q-icon {
  color: #ff6b35;
  font-size: 20px;
}

.email-body {
  margin-top: 24px;
}

.greeting {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
}

.email-body p {
  margin: 16px 0;
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.email-details {
  margin: 16px 0;
  font-size: 16px;
  line-height: 1.6;
}

.booking-reference {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin: 24px 0;
  border-left: 4px solid #ff6b35;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin: 32px 0;
  justify-content: center;
}

.calendar-btn {
  padding: 12px 24px;
  font-weight: 600;
  text-transform: none;
  border-radius: 6px;
}

.directions-btn {
  padding: 12px 24px;
  text-transform: none;
  border-radius: 6px;
}

.contact-info,
.cancellation-policy {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #eee;
  font-size: 14px;
  color: #666;
}

.contact-info h4,
.cancellation-policy h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.contact-info p,
.cancellation-policy p {
  margin-bottom: 8px;
  font-size: 14px;
}

.contact-info strong {
  color: #333;
}

.text-primary {
  color: #ff6b35 !important;
  text-decoration: none;
}

.text-primary:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .calendar-btn,
  .directions-btn {
    width: 100%;
    max-width: 200px;
  }

  .confirmation-title {
    font-size: 28px;
  }

  .event-title {
    font-size: 20px;
  }
}
</style>
