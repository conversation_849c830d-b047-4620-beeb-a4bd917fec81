<template>
  <q-table
    flat
    bordered
    :rows="ticketsAll"
    :columns="columns"
    :pagination="{ rowsPerPage: 0 }"
    hide-bottom
  >
    <template v-slot:header="props">
      <q-tr :props="props">
        <q-th>Details</q-th>
        <q-th v-if="hasPaidTickets">Cost</q-th>
        <q-th>Quantity</q-th>
      </q-tr>
    </template>

    <template v-slot:body="props">
      <q-tr :props="props">
        <q-td>{{ props.row.details }}</q-td>
        <q-td v-if="hasPaidTickets"
          >£{{ formatCurrency(props.row.cost_b) }}</q-td
        >
        <q-td>1</q-td>
      </q-tr>
    </template>

    <template v-slot:bottom-row>
      <q-tr v-if="hasPaidTickets">
        <q-td>
          <strong
            >Total Cost ({{ event.vat_exclusive ? "exc" : "inc" }} VAT)</strong
          >
        </q-td>
        <q-td>£{{ formatCurrency(getTotalWithoutVat()) }}</q-td>
        <q-td></q-td>
      </q-tr>

      <q-tr v-if="event.vat_exclusive">
        <q-td>Vat Amount</q-td>
        <q-td>£{{ formatCurrency(getTotalVAT()) }}</q-td>
        <q-td></q-td>
      </q-tr>

      <q-tr v-if="event.fees_pass_on">
        <q-td>Total Fees Amount</q-td>
        <q-td>£{{ formatCurrency(calcExampleFees()) }} (example)</q-td>
        <q-td></q-td>
      </q-tr>

      <q-tr v-if="hasPaidTickets">
        <q-td>
          <strong>Overall Total</strong>
        </q-td>
        <q-td>£{{ formatCurrency(getTotal(event.vat_exclusive)) }}</q-td>
        <q-td></q-td>
      </q-tr>

      <q-tr>
        <q-td>
          <strong>Paid by Card</strong>
        </q-td>
        <q-td></q-td>
        <q-td></q-td>
      </q-tr>
    </template>
  </q-table>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useEventStore } from "@/stores/event";

const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const eventStore = useEventStore();
const ticketsAll = ref([]);
const hasPaidTickets = computed(() => eventStore.getChargeable);

const columns = [
  { name: "details", label: "Details", field: "details" },
  {
    name: "cost",
    label: "Cost",
    field: "cost_b",
    format: (val) => `£${formatCurrency(val)}`,
  },
  { name: "quantity", label: "Quantity", field: () => 1 },
];

onMounted(() => {
  // Flatten ticket structure into a single array
  props.event.tickets?.forEach((ticket) => {
    ticketsAll.value.push(ticket);
    ticket.child_tickets?.forEach((childTicket) => {
      ticketsAll.value.push(childTicket);
    });
  });

  props.event.ticket_groups?.forEach((tg) => {
    tg.packages?.forEach((ticket) => {
      ticketsAll.value.push(ticket);
      ticket.child_tickets?.forEach((childTicket) => {
        ticketsAll.value.push(childTicket);
      });
    });
  });
});

const formatCurrency = (value) => {
  return parseFloat(value).toFixed(2);
};

const getTotalWithoutVat = () => {
  return ticketsAll.value.reduce((total, ticket) => {
    return total + parseFloat(ticket.cost_b || 0);
  }, 0);
};

const calcExampleFees = () => {
  const total = getTotalWithoutVat();
  return total * 0.025 + 0.4;
};

const getTotalVAT = () => {
  const vatRate = 20;
  return ticketsAll.value.reduce((total, ticket) => {
    const rate = ticket.vat_rate?.rate || vatRate;
    return total + (parseFloat(ticket.cost_b || 0) * rate) / 100;
  }, 0);
};

const getTotal = (vatStatus) => {
  let total = ticketsAll.value.reduce((acc, ticket) => {
    if (vatStatus) {
      const vatAmount =
        (parseFloat(ticket.cost_b || 0) * (ticket.vat_rate?.rate || 20)) / 100;
      return acc + parseFloat(ticket.cost_b || 0) + vatAmount;
    }
    return acc + parseFloat(ticket.cost_b || 0);
  }, 0);

  if (props.event.fees_pass_on) {
    total += parseFloat(calcExampleFees().toFixed(2));
  }

  return total;
};
</script>
