<template>
  <div v-if="event" class="q-pa-md">
    <div class="row q-gutter-md">
      <div class="col-12">
        <h4 class="text-h5 q-mb-md">Register your delegates here</h4>
      </div>

      <div class="col-12">
        <q-banner
          v-for="(alert, index) in alerts"
          :key="index"
          :class="`text-${
            alert.type === 'error'
              ? 'negative'
              : alert.type === 'warning'
              ? 'warning'
              : 'positive'
          }`"
          inline-actions
          rounded
          class="q-mb-sm"
        >
          <template v-slot:avatar>
            <q-icon
              :name="
                alert.type === 'error'
                  ? 'error'
                  : alert.type === 'warning'
                  ? 'warning'
                  : 'check_circle'
              "
            />
          </template>
          {{ alert.msg }}
          <template v-slot:action>
            <q-btn flat round dense icon="close" @click="closeAlert(index)" />
          </template>
        </q-banner>

        <div class="q-mb-md">
          <q-btn
            color="primary"
            @click="showCsvUpload = !showCsvUpload"
            :icon="showCsvUpload ? 'expand_less' : 'expand_more'"
            class="q-mr-sm"
          >
            Add New Contacts
          </q-btn>

          <q-btn
            color="primary"
            @click="showOrgContacts = !showOrgContacts"
            :icon="showOrgContacts ? 'expand_less' : 'expand_more'"
          >
            Import Org Contacts
          </q-btn>
        </div>
      </div>

      <div class="col-12" v-if="showCsvUpload">
        <q-card>
          <q-card-section>
            <csv-uploader :event="event"></csv-uploader>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12" v-if="showOrgContacts">
        <q-card>
          <q-card-section>
            <import-org-contacts :event="event"></import-org-contacts>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12" v-if="emailInvites.length > 0">
        <q-card>
          <q-card-section>
            <q-table
              :rows="emailInvites"
              :columns="columns"
              row-key="id"
              :pagination="pagination"
              @request="onRequest"
              class="full-width"
            >
              <template v-slot:body-cell-opted_out="props">
                <q-td :props="props">
                  {{ props.value ? "Yes" : "No" }}
                </q-td>
              </template>
              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <q-btn
                    color="negative"
                    icon="close"
                    size="sm"
                    round
                    @click="removeInvite(props.row)"
                  />
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <div class="q-mt-lg">
      <q-card class="text-center">
        <q-card-section>
          <q-btn
            color="primary"
            size="lg"
            @click="saveInviteContacts"
            class="q-px-xl"
          >
            Save Contacts
          </q-btn>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useEventStore } from "@/stores/event";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import csvUpload from "./CsvUpload.vue";
import importOrgContacts from "./ImportOrgContacts.vue";
import { eventBus } from "../event-bus";

const props = defineProps({
  email: {
    type: String,
    default: "",
  },
});

const eventStore = useEventStore();
const router = useRouter();
const $q = useQuasar();

// Reactive data
const showCsvUpload = ref(false);
const showOrgContacts = ref(false);
const emailInvites = ref([]);
const alerts = ref([]);
const currentPage = ref(1);
const totalItems = ref(null);
const submitted = ref(false);

const emailInvite = ref({
  id: "",
  forename: "",
  surname: "",
  email: "",
});

// Computed properties
const event = computed(() => eventStore.getEvent);
const advancedUser = computed(() => eventStore.advancedUser);

const regExEmail =
  /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/i;

// Table configuration
const columns = [
  {
    name: "forename",
    required: true,
    label: "First Name",
    align: "left",
    field: "forename",
    sortable: true,
  },
  {
    name: "surname",
    required: true,
    label: "Last Name",
    align: "left",
    field: "surname",
    sortable: true,
  },
  {
    name: "email",
    required: true,
    label: "Email",
    align: "left",
    field: "email",
    sortable: true,
  },
  {
    name: "opted_out",
    required: true,
    label: "Opted Out",
    align: "center",
    field: "opted_out",
    sortable: true,
  },
  {
    name: "actions",
    required: true,
    label: "Delete",
    align: "center",
    field: "actions",
  },
];

const pagination = ref({
  sortBy: null,
  descending: false,
  page: 1,
  rowsPerPage: 100,
  rowsNumber: 0,
});

// Methods
const getUsers = async (pageNumber) => {
  const pageToGoTo = pageNumber || currentPage.value;

  try {
    const response = await fetch(
      `/registered_users/${event.value.id}.json?page=${pageToGoTo}`
    );
    const data = await response.json();

    console.log(`upload contacts component users: ${data.unconfirmed_users}`);
    emailInvites.value = data.unconfirmed_users;
    totalItems.value = data.total_count;
    pagination.value.rowsNumber = data.total_count;
  } catch (error) {
    console.error("Error fetching users:", error);
  }
};

const onRequest = (props) => {
  const { page, rowsPerPage } = props.pagination;
  currentPage.value = page;
  getUsers(page);
};

const removeInvite = (email) => {
  $q.dialog({
    title: "Confirm Delete",
    message: "Are you sure you want to delete this invite?",
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const response = await fetch(`/registered_users/${email.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": document
            .querySelector('meta[name="csrf-token"]')
            ?.getAttribute("content"),
        },
      });

      if (response.ok) {
        $q.notify({
          type: "positive",
          message: "Removed email details",
          position: "top",
        });
        getUsers();
      } else {
        const errorData = await response.json();
        throw new Error(
          errorData.errors?.toString() || "Failed to remove email"
        );
      }
    } catch (error) {
      $q.notify({
        type: "negative",
        message: `Email not removed: ${error.message}`,
        position: "top",
      });
    }
  });
};

const saveInvites = async () => {
  submitted.value = true;

  if (!regExEmail.test(emailInvite.value.email)) {
    submitted.value = false;
    return;
  }

  const emailDetails = {
    forename: emailInvite.value.forename,
    surname: emailInvite.value.surname,
    email: emailInvite.value.email,
    event_booking_attributes: {
      event_id: event.value.id,
    },
  };

  const regUser = {
    registered_user: emailDetails,
  };

  try {
    const response = await fetch("/registered_users.json", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": document
          .querySelector('meta[name="csrf-token"]')
          ?.getAttribute("content"),
      },
      body: JSON.stringify(regUser),
    });

    if (response.ok) {
      const data = await response.json();
      emailDetails.id = data.user.id;
      emailInvites.value.push(emailDetails);

      emailInvite.value = {
        id: "",
        forename: "",
        surname: "",
        email: "",
      };

      $q.notify({
        type: "positive",
        message: "Saved Email Details",
        position: "top",
      });

      getUsers();
    } else {
      const errorData = await response.json();
      throw new Error(errorData.errors?.toString() || "Failed to save email");
    }
  } catch (error) {
    $q.notify({
      type: "negative",
      message: `Email Not Saved: ${error.message}`,
      position: "top",
    });
  }

  submitted.value = false;
};

const closeAlert = (index) => {
  alerts.value.splice(index, 1);
};

const saveInviteContacts = () => {
  router.push({
    name: "customise-confirmation",
  });
};

// Event bus listeners
let usersUpdatedListener;

onMounted(() => {
  usersUpdatedListener = () => {
    getUsers();
  };
  eventBus.on("usersUpdated", usersUpdatedListener);
  getUsers();
});

onUnmounted(() => {
  if (usersUpdatedListener) {
    eventBus.off("usersUpdated", usersUpdatedListener);
  }
});
</script>

<style>
.page-link {
  color: #ff9500 !important;
}
.page-item.active .page-link {
  z-index: 1;
  color: #fff;
  background-color: #ff9500 !important;
  border-color: #ff9500 !important;
}
</style>
