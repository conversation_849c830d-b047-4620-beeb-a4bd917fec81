<template>
  <div class="q-pa-md">
    <q-card>
      <q-card-section>
        <div class="text-h6 q-mb-md">
          Import contacts from organisation contact list
        </div>

        <q-form @submit="importContacts" class="q-gutter-md">
          <div class="row q-gutter-md">
            <div class="col-md-3 col-sm-6 col-xs-12">
              <q-select
                v-model="eventTitle"
                :options="eventOptions"
                label="Select Event"
                emit-value
                map-options
                outlined
              />
            </div>

            <div class="col-md-3 col-sm-6 col-xs-12">
              <q-select
                v-model="eventStatus"
                :options="eventStatusOptions"
                label="Event Status"
                emit-value
                map-options
                outlined
              />
            </div>

            <div class="col-md-3 col-sm-6 col-xs-12">
              <q-select
                v-model="tagsSelected"
                :options="tagsList"
                label="Select Contact Tags"
                multiple
                use-chips
                outlined
              />
            </div>
          </div>

          <div class="row q-gutter-md">
            <div class="col-md-3 col-sm-6 col-xs-12">
              <q-input
                v-model="dateFrom"
                label="Events From"
                type="date"
                outlined
              />
            </div>

            <div class="col-md-3 col-sm-6 col-xs-12">
              <q-input
                v-model="dateTo"
                label="Events To"
                type="date"
                outlined
              />
            </div>
          </div>

          <div class="row">
            <div class="col">
              <q-btn
                v-if="!submitted"
                type="submit"
                color="primary"
                label="Import"
              />
              <q-btn
                v-else
                color="grey"
                disable
                icon="sync"
                label="Please Wait"
              >
                <q-tooltip>Processing import...</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";
import { eventBus } from "../event-bus";
import _ from "lodash";

const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const eventStore = useEventStore();
const $q = useQuasar();

// Reactive data
const eventTitle = ref(0);
const eventOptions = ref([{ label: "Select Event", value: 0 }]);
const tags = ref([]);
const tagsList = ref([]);
const tagsSelected = ref([]);
const eventStatus = ref(0);
const eventStatusOptions = ref([
  { label: "All Contacts", value: 0 },
  { label: "Attended Event", value: 1 },
  { label: "Booked on Event", value: 2 },
  { label: "Cancelled Booking", value: 3 },
]);
const dateFrom = ref(null);
const dateTo = ref(null);
const submitted = ref(false);
const polling = ref(null);

// Computed properties
const queueId = computed({
  get() {
    return eventStore.getImportContactJobID;
  },
  set(value) {
    eventStore.setImportContactJobID(value);
  },
});

// Methods
const clearFields = () => {
  eventTitle.value = 0;
  tagsSelected.value = [];
  eventStatus.value = 0;
  dateFrom.value = null;
  dateTo.value = null;
};

const importContacts = _.debounce(async () => {
  submitted.value = true;
  eventBus.emit("uploadingContacts", true);

  const searchAtts = {
    eventId: props.event.id,
    eventTitle: eventTitle.value,
    contactTag: tagsSelected.value,
    eventStatus: eventStatus.value,
    dateTo: dateTo.value,
    dateFrom: dateFrom.value,
  };

  try {
    const response = await fetch("/org_user_lists/import_contacts.json", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": document
          .querySelector('meta[name="csrf-token"]')
          ?.getAttribute("content"),
      },
      body: JSON.stringify(searchAtts),
    });

    if (response.ok) {
      const data = await response.json();
      console.log(data);
      queueId.value = data.queue;
      pollQueue();
    } else {
      throw new Error("Failed to import contacts");
    }
  } catch (error) {
    console.error(error);
    $q.notify({
      type: "negative",
      message: "There was a problem creating new invites",
      position: "top",
    });
    submitted.value = false;
    eventBus.emit("uploadingContacts", false);
  }
}, 300);

const pollQueue = () => {
  polling.value = setInterval(async () => {
    try {
      const response = await fetch(
        `/org_user_lists/poll_imports.json?job_id=${queueId.value}`
      );

      if (response.ok) {
        const data = await response.json();
        console.log(data);
        const jobFinished = data.success;

        if (jobFinished) {
          stopPolling();
          loadNewContacts(data.no_added);
        }
      } else {
        throw new Error("Failed to poll import status");
      }
    } catch (error) {
      console.error(error);
      $q.notify({
        type: "negative",
        message: "There was a problem creating new invites",
        position: "top",
      });
      stopPolling();
      submitted.value = false;
      queueId.value = null;
      eventBus.emit("uploadingContacts", false);
    }
  }, 10000);
};

const loadNewContacts = (no_contacts_added) => {
  submitted.value = false;
  queueId.value = null;
  eventBus.emit("uploadingContacts", false);
  eventBus.emit("usersUpdated");
  clearFields();

  if (no_contacts_added > 0) {
    $q.notify({
      type: "positive",
      message: "Created invites for organisation contacts",
      position: "top",
    });
  } else {
    $q.notify({
      type: "positive",
      message: "No new contacts were found with these search criteria",
      position: "top",
    });
  }
};

const stopPolling = () => {
  if (polling.value) {
    clearInterval(polling.value);
    polling.value = null;
  }
};

// Lifecycle
onMounted(async () => {
  // Load filter options
  try {
    const response = await fetch(
      `/org_user_lists/get_filter_options.json?eventId=${props.event.id}`
    );

    if (response.ok) {
      const data = await response.json();

      data.event_filter.forEach((ev) => {
        eventOptions.value.push({ label: ev.title, value: ev.id });
      });

      data.tags.forEach((tag) => {
        tags.value.push(tag);
        tagsList.value.push({ label: tag, value: tag });
      });
    }
  } catch (error) {
    console.error("Error loading filter options:", error);
  }

  // Check if there's an existing queue job
  if (queueId.value) {
    submitted.value = true;
    eventBus.emit("uploadingContacts", true);
    setTimeout(() => {
      pollQueue();
    }, 5000);
  }
});

onUnmounted(() => {
  stopPolling();
});
</script>

<style scoped>
.q-card {
  border-top: 4px solid #ff9500;
}
</style>
