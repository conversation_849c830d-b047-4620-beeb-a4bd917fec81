export default {
  computed: {
    borderColour() {
      var color = "#fb9c40";
      if (this.event.ehcolour) {
        color = this.event.ehcolour;
      }
      return {
        borderTop: "4px solid " + color
      };
    },

    datesEqual() {
      return dayjs(this.event.datetimefrom).isSame(
        this.event.datetimeto,
        "day"
      );
    },

    dateFrom() {
      return dayjs(this.event.datetimefrom).format("dd/MM/YYYY");
    },

    logoFileURL() {
      if (this.event.image1) {
        return (
          "https://s3-eu-west-1.amazonaws.com/" +
          this.image_bucket +
          "/" +
          this.event.id +
          "/" +
          this.event.image1 +
          "?" +
          Math.random()
        );
      } else {
        return "";
      }
    },

    imageFileURL() {
      if (this.event.image2) {
        return (
          "https://s3-eu-west-1.amazonaws.com/" +
          this.image_bucket +
          "/" +
          this.event.id +
          "/" +
          this.event.image2 +
          "?" +
          Math.random()
        );
      } else {
        return "";
      }
    },

    attachmentsList() {
      var self = this;
      var attachmentsTempList = [];
      var attachmentsFiltered = this.event.file_uploads.filter(
        uploads => uploads.email_type == self.emailType
      );
      attachmentsFiltered.forEach(function(attachment) {
        attachmentsTempList.push({
          name: attachment.name,
          id: attachment.id
        });
      });
      return attachmentsTempList;
    }
  },

  methods: {
    handleExceed(files, fileList) {
      this.$message.warning("You have reached the limit of 5 Attachments");
    },

    handleSuccess(resp, file) {
      file.id = resp.id;
    },

    handleRemove(file, fileList) {
      var self = this;
      this.$http
        .post("/document_upload/remove_file", {
          eventid: self.event.id,
          email_type: self.emailType,
          filename: file.name,
          file_id: file.id
        })
        .then(function successCallback() {
          self.$message.success("Attachment Removed");
        });
    },

    // TODO remove once confirmation emails done
    buildAddress() {
      var address = this.event.location + ", " + this.event.address1 + ", ";
      if (this.event.address2) {
        address += this.event.address2 + ", ";
      }
      address += this.event.city + ", ";
      if (this.event.county) {
        address += this.event.county + ", ";
      }
      address += this.event.postcode;

      return address;
    }
  }
};
