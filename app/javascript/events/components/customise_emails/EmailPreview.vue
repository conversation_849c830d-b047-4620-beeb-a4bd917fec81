<template>
  <div class="q-pa-md">
    <q-card class="q-mb-md">
      <q-card-section class="bg-primary text-white">
        <div class="text-h6">Preview - send sample emails here</div>
      </q-card-section>

      <q-card-section>
        <div class="q-mb-md">
          <p>Send a sample email to the email address below.</p>
          <p class="text-negative">
            *Please note that figures on the emails are only for demonstration
            purposes only*
          </p>
        </div>

        <div class="q-gutter-y-md" style="max-width: 400px">
          <q-form
            ref="emailForm"
            @submit.prevent="validateDetails"
            class="q-gutter-y-md"
          >
            <q-input
              v-model="emailPreview.send_to"
              label="Email address"
              :rules="[
                (val) => !!val || 'Email is required',
                (val) =>
                  emailRegex.test(val) || 'Please enter a valid email address',
              ]"
              filled
            />

            <q-checkbox
              v-model="emailPreview.invite"
              label="Invite Email"
              class="q-mb-sm"
            />

            <q-checkbox
              v-model="emailPreview.confirmation"
              label="Confirmation Email"
              class="q-mb-sm"
            />

            <q-btn
              label="Send Emails"
              type="submit"
              color="secondary"
              class="q-mt-md"
            />
          </q-form>
        </div>
      </q-card-section>
    </q-card>

    <q-card>
      <q-card-section class="text-center">
        <q-btn
          label="Setup payment options"
          color="primary"
          size="lg"
          class="q-mb-md"
          @click="makeLive"
        />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useEventStore } from "@/stores/event";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";

const props = defineProps({
  eventId: {
    type: [String, Number],
    required: true,
  },
});

const $q = useQuasar();
const eventStore = useEventStore();
const router = useRouter();
const emailForm = ref(null);

// const event = computed(() => eventStore.getEvent)
const emailPreview = ref({
  send_to: "",
  invite: false,
  confirmation: false,
  payment_type: 0,
});

const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

// const eventPostable = ref({
//   event: {
//     id: props.eventId,
//   }
// })

// const setStep = (stepNo) => {
//   event.value.step = Math.max(stepNo, event.value.step)
// }

const makeLive = () => {
  // setStep(2.4)
  router.push({ name: "payment-options" });
};

const validateDetails = async () => {
  try {
    const success = await emailForm.value.validate();
    if (!success) return;

    if (!emailPreview.value.invite && !emailPreview.value.confirmation) {
      $q.notify({
        type: "negative",
        message: "Please choose an email to send",
      });
      return;
    }

    await sendPreviews();
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Error while validating form",
    });
  }
};

const sendPreviews = async () => {
  try {
    await fetch(`/events/${props.eventId}/send_sample_emails.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(emailPreview.value),
    });

    $q.notify({
      type: "positive",
      message: "Selected Sample Emails Have Been Sent!",
      color: "secondary",
    });

    // setStep(2.4)
    // await fetch(`/events/${event.value.id}/update_step.json`, {
    //   method: 'PUT',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify(eventPostable.value)
    // })
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Sample email could not be sent",
    });
  }
};

onMounted(async () => {
  if (!props.eventId) {
    $q.notify({
      type: "negative",
      message: "Event ID is required for this page.",
      caption: "Please select an event first.",
      position: "top",
      timeout: 4000,
      actions: [
        {
          label: "Go to Events",
          color: "white",
          handler: () => {
            router.push({ name: "dash-main" });
          },
        },
      ],
    });
    return;
  }

  try {
    // Ensure event is loaded
    const success = await eventStore.ensureEventLoaded(props.eventId);
    if (!success) {
      throw new Error("Failed to load event data");
    }
    // Update eventPostable.event.id if it wasn't set correctly initially
    // because props might not be available when eventPostable is first defined.
    // if (eventPostable.value.event.id !== props.eventId) {
    //     eventPostable.value.event.id = props.eventId;
    // }
  } catch (error) {
    console.error("Failed to load event in EmailPreview component:", error);
    $q.notify({
      type: "negative",
      message: "Failed to load event details.",
      caption: "Please try refreshing the page or selecting a different event.",
      position: "top",
      timeout: 5000,
      actions: [
        {
          label: "Go Back",
          color: "white",
          handler: () => {
            router.push({ name: "dash-main" });
          },
        },
      ],
    });
  }
});
</script>
