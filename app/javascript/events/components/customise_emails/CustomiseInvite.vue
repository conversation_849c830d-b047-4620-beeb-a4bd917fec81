<template>
  <div class="customise-invite">
    <div class="row q-col-gutter-lg">
      <!-- Left Column: Customization Form -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section class="text-h6">
            Customise Your Email
          </q-card-section>

          <q-card-section>
            <div v-if="!invite_email_template" class="text-center q-pa-lg">
              <q-spinner size="lg" />
              <div class="q-mt-md">Loading email template...</div>
            </div>

            <q-form
              v-else
              ref="emailForm"
              @submit="handleSubmit"
              @reset="onReset"
              class="q-gutter-md"
            >
              <!-- Greeting -->
              <div class="field-group">
                <label class="text-subtitle2">Greeting:</label>
                <q-input
                  v-model="invite_email_template.welcome_text"
                  name="greeting"
                  hint="e.g. Hello or Hi etc."
                  :rules="[(val) => !!val || 'Greeting is required']"
                  bottom-slots
                  outlined
                >
                  <template v-slot:error> Greeting is required </template>
                </q-input>
              </div>

              <!-- Forename Only -->
              <div class="field-group">
                <q-checkbox
                  v-model="invite_email_template.show_forename_only"
                  label="Click to show only the forename in the greeting"
                />
              </div>

              <!-- Email Details Editor -->
              <div class="field-group">
                <label class="text-subtitle2 q-mb-sm">Edit email details</label>
                <q-editor
                  v-model="invite_email_template.details"
                  :rules="[(val) => !!val || 'Email details are required']"
                  min-height="200px"
                  class="q-mb-sm"
                />

                <q-btn
                  flat
                  color="primary"
                  size="sm"
                  label="Reset Invite Details To Match Event Details"
                  @click="resetInvitationDetails"
                />
              </div>

              <!-- RSVP Text -->
              <div class="field-group">
                <label class="text-subtitle2">RSVP text</label>
                <q-input
                  v-model="invite_email_template.title2"
                  name="rsvpText"
                  :rules="[(val) => !!val || 'RSVP text is required']"
                  outlined
                  bottom-slots
                >
                  <template v-slot:hint>
                    <span class="text-negative"
                      >Please check your RSVP date is correct!</span
                    >
                  </template>
                  <template v-slot:error> RSVP text is required </template>
                </q-input>
              </div>

              <!-- Color Picker -->
              <colour-picker-email v-if="event?.image1" :event="event" />

              <!-- Form Actions -->
              <div class="row justify-end q-gutter-sm q-mt-md">
                <q-btn flat label="Cancel" @click="onReset" />
                <q-btn
                  type="submit"
                  color="primary"
                  label="Save"
                  :loading="submitting"
                />
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </div>

      <!-- Right Column: Email Preview -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section class="text-h6">
            Invite Email Preview
          </q-card-section>

          <q-card-section>
            <div v-if="!invite_email_template" class="text-center q-pa-lg">
              <q-spinner size="md" />
              <div class="q-mt-sm">Loading preview...</div>
            </div>

            <div v-else class="email-preview-container">
              <div class="email-mock-browser">
                <div class="browser-header">
                  <div class="browser-controls">
                    <span class="control control-close"></span>
                    <span class="control control-minimize"></span>
                    <span class="control control-maximize"></span>
                  </div>
                  <div class="url-bar">Email Preview</div>
                </div>

                <div class="email-content">
                  <div class="email-header">
                    <p class="invitation-line">You are invited to</p>
                    <h2 class="event-title">
                      {{ event?.title || "Event Title" }}
                    </h2>
                    <p class="organizer">
                      By {{ event?.organiser || "Event Organizer" }}
                    </p>
                  </div>

                  <div class="orange-divider"></div>

                  <div class="event-details">
                    <div class="detail-row">
                      <q-icon name="place" />
                      <span>{{ buildAddress() || "Event Location" }}</span>
                    </div>

                    <div class="detail-row">
                      <q-icon name="event" />
                      <span>{{ formatEventDate() }}</span>
                    </div>

                    <div class="detail-row">
                      <q-icon name="access_time" />
                      <span>{{ formatEventTime() }}</span>
                    </div>
                  </div>

                  <div class="orange-divider"></div>

                  <div class="email-body">
                    <p class="greeting">
                      {{ invite_email_template.welcome_text || "Hi" }}
                      {{
                        invite_email_template.show_forename_only
                          ? "John"
                          : "John Smith"
                      }}
                    </p>

                    <div
                      class="custom-message"
                      v-html="
                        invite_email_template.details ||
                        'Event details will appear here...'
                      "
                    ></div>

                    <p class="rsvp-text">
                      {{
                        invite_email_template.title2 ||
                        "Please RSVP by clicking the button below"
                      }}
                    </p>

                    <div class="action-buttons">
                      <q-btn class="book-btn" color="grey-8" label="Book Now" />
                      <q-btn
                        class="decline-btn"
                        color="grey-6"
                        outline
                        label="Decline Event"
                      />
                    </div>

                    <div class="opt-out">
                      <a href="#" class="text-primary"
                        >Decline and Opt Out of Further Communication</a
                      >
                    </div>

                    <div class="contact-info">
                      <p>
                        If you have an enquiry about this event, please contact
                        the event organiser
                      </p>
                      <p><strong>Contact Organiser:</strong></p>
                      <p>
                        Email:
                        <a href="#" class="text-primary">{{
                          event?.organiser || "<EMAIL>"
                        }}</a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import dayjs from "dayjs";
// import colourPickerEmail from "@/events/components/common/colour-picker-email.vue"
import axios from "axios";

const props = defineProps({
  eventId: {
    type: [String, Number],
    required: true,
  },
});

const router = useRouter();
const $q = useQuasar();
const eventStore = useEventStore();
const submitting = ref(false);
const emailForm = ref(null);

const event = computed(() => eventStore.getEvent);
const invite_email_template = ref(null);

const setData = () => {
  invite_email_template.value = null;

  if (event.value?.invite_email_template) {
    invite_email_template.value = JSON.parse(
      JSON.stringify(event.value.invite_email_template)
    );
  }

  if (!invite_email_template.value) {
    invite_email_template.value = {
      welcome_text: "Hi",
      show_forename_only: false,
      title1: `You are invited to ${event.value.title}`,
      text_area_1: `The event will take place at ${buildAddress()}`,
      title2: `Please RSVP by clicking the button below before ${dayjs(
        event.value.datetimefrom
      ).format("MMMM DD, YYYY")}`,
      text_area_3: `If you have an enquiry about this event, please contact the event organiser ${event.value.organiser}`,
      text_area_2: event.value.summary || "",
      ticket_options_title: "Event Details",
      details: event.value.details,
      email_type: "invite",
    };
  }
};

const resetInvitationDetails = () => {
  invite_email_template.value.details = event.value.details;
};

const onReset = () => {
  setData();
};

const buildAddress = () => {
  const evt = event.value;
  if (!evt) return "";

  let address = `${evt.location}, ${evt.address1}, `;
  if (evt.address2) {
    address += `${evt.address2}, `;
  }
  address += `${evt.city}, `;
  if (evt.county) {
    address += `${evt.county}, `;
  }
  address += evt.postcode;
  return address;
};

const formatEventDate = () => {
  if (!event.value?.datetimefrom) return "TBC";

  try {
    const date = new Date(event.value.datetimefrom);
    return date.toLocaleDateString("en-GB", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (e) {
    return "TBC";
  }
};

const formatEventTime = () => {
  if (!event.value?.datetimefrom) return "TBC";

  try {
    const fromDate = new Date(event.value.datetimefrom);
    let timeString = fromDate.toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
    });

    if (event.value.datetimeto) {
      const toDate = new Date(event.value.datetimeto);
      timeString +=
        " - " +
        toDate.toLocaleTimeString("en-GB", {
          hour: "2-digit",
          minute: "2-digit",
        });
    }

    return timeString;
  } catch (e) {
    return "TBC";
  }
};

const handleSubmit = async () => {
  // Validate form
  const formValid = await emailForm.value?.validate();
  if (!formValid) {
    $q.notify({
      type: "negative",
      message: "Please fix the validation issues",
    });
    return;
  }

  try {
    submitting.value = true;
    const eventDetails = { ...event.value };

    const eventPostable = {
      event: {
        ...eventDetails,
        step: 2.1,
        email_templates_attributes: [
          {
            id: invite_email_template.value.id,
            show_forename_only: invite_email_template.value.show_forename_only,
            welcome_text: invite_email_template.value.welcome_text,
            text_area_1: invite_email_template.value.text_area_1,
            text_area_2: invite_email_template.value.text_area_2,
            text_area_3: invite_email_template.value.text_area_3,
            title1: invite_email_template.value.title1,
            title2: invite_email_template.value.title2,
            ticket_options_title:
              invite_email_template.value.ticket_options_title,
            details: invite_email_template.value.details,
            email_type: "invite",
          },
        ],
      },
    };

    await axios.put(
      `/events/${props.eventId}/update_email_templates.json`,
      eventPostable
    );

    // Update store
    event.value.invite_email_template =
      eventPostable.event.email_templates_attributes[0];
    eventStore.setEvent(event.value);

    $q.notify({
      type: "positive",
      message: "Email Updated",
      caption: "Your invite email details have been updated!",
    });

    router.push({
      name: "customise-confirmation",
      params: { eventId: props.eventId },
    });
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Failed to update invite email template",
    });
  } finally {
    submitting.value = false;
  }
};

onMounted(async () => {
  if (!props.eventId) {
    $q.notify({
      type: "negative",
      message: "Event ID is required",
    });
    return;
  }

  try {
    await eventStore.ensureEventLoaded(props.eventId);
    setData();
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Failed to load event details",
    });
  }
});
</script>

<style lang="scss" scoped>
.customise-invite {
  width: 100%;

  .field-group {
    margin-bottom: 1.5rem;
  }

  .q-editor {
    border: 1px solid rgba(0, 0, 0, 0.12);
  }
}

.email-preview-container {
  max-width: 100%;
  margin: 0;
}

.email-mock-browser {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.browser-header {
  background: #f5f5f5;
  padding: 12px 16px;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  gap: 12px;
}

.browser-controls {
  display: flex;
  gap: 6px;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control-close {
  background: #ff5f57;
}

.control-minimize {
  background: #ffbd2e;
}

.control-maximize {
  background: #28ca42;
}

.url-bar {
  flex: 1;
  background: #fff;
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
  color: #666;
}

.email-content {
  padding: 24px;
  line-height: 1.6;
}

.email-header {
  text-align: center;
  margin-bottom: 24px;
}

.invitation-line {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.event-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 8px 0;
}

.organizer {
  font-size: 16px;
  color: #666;
  margin-top: 8px;
}

.orange-divider {
  height: 3px;
  background: linear-gradient(to right, #ff6b35, #f7931e);
  margin: 24px 0;
  border-radius: 2px;
}

.event-details {
  margin: 24px 0;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 16px;
  color: #333;
}

.detail-row .q-icon {
  color: #ff6b35;
  font-size: 20px;
}

.email-body {
  margin-top: 24px;
}

.greeting {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
}

.custom-message {
  margin: 16px 0;
  font-size: 16px;
  line-height: 1.6;
}

.rsvp-text {
  margin: 24px 0 32px 0;
  font-size: 16px;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin: 32px 0;
  justify-content: center;
}

.book-btn {
  background: #333 !important;
  color: white !important;
  padding: 12px 24px;
  font-weight: 600;
  text-transform: none;
  border-radius: 6px;
}

.decline-btn {
  padding: 12px 24px;
  text-transform: none;
  border-radius: 6px;
}

.opt-out {
  text-align: center;
  margin: 24px 0;
}

.contact-info {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #eee;
  font-size: 14px;
  color: #666;
}

.contact-info p {
  margin-bottom: 8px;
}

.contact-info strong {
  color: #333;
}

.text-primary {
  color: #ff6b35 !important;
  text-decoration: none;
}

.text-primary:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .book-btn,
  .decline-btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
