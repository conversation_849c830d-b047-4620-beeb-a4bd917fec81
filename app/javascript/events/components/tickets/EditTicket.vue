<template>
  <td v-if="event.live">
    <q-btn
      round
      flat
      dense
      color="secondary"
      icon="edit"
      @click.stop="updateTicketVisible = !updateTicketVisible"
    />
      
    <q-dialog v-model="updateTicketVisible">
      <q-card style="width: 400px">
        <q-card-section class="text-h6">
          Edit Ticket Details
        </q-card-section>
        
        <q-card-section>
          <q-form ref="ticketForm" @submit="updateTicket">
            <q-table
              :rows="[ticketToAmend]"
              :columns="columns"
              hide-pagination
              hide-bottom
              :row-key="row => 'ticket'"
              flat
              bordered
            >
              <template v-slot:body="props">
                <q-tr :props="props">
                  <q-td key="details">
                    <q-input
                      v-model="ticketToAmend.details"
                      dense
                      outlined
                      placeholder="Ticket Name"
                      :rules="[ val => !!val || 'Ticket name is required' ]"
                    />
                  </q-td>
                  <q-td key="ticketNo">
                    <q-input
                      v-model="ticketToAmend.ticket_no"
                      type="number"
                      dense
                      outlined
                      :rules="[
                        val => !!val || 'Ticket number is required',
                        val => val > 0 || 'Must be greater than 0'
                      ]"
                    />
                  </q-td>
                  <q-td key="maxAllowed">
                    <q-input
                      v-model="ticketToAmend.max_allowed"
                      type="number"
                      dense
                      outlined
                      :rules="[
                        val => !!val || 'Max tickets is required',
                        val => val > 0 || 'Must be greater than 0',
                        val => val <= ticketToAmend.ticket_no || 'Cannot exceed total tickets'
                      ]"
                    />
                  </q-td>
                </q-tr>
              </template>
            </q-table>

            <div class="q-mt-md">
              <q-btn
                label="Update Ticket Details"
                color="primary"
                type="submit"
                class="full-width"
              />
            </div>

            <div v-if="errorMessages.length" class="q-mt-sm">
              <q-banner rounded class="bg-negative text-white">
                <template v-for="message in errorMessages" :key="message">
                  <div>{{ message }}</div>
                </template>
              </q-banner>
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </td>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'

const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  ticket: {
    type: Object,
    required: true
  }
})

const updateTicketVisible = ref(false)
const ticketToAmend = reactive(Object.assign({}, props.ticket))
const errorMessages = ref([])
const ticketForm = ref(null)
const $q = useQuasar()

const columns = computed(() => [
  { name: 'details', label: 'Ticket Details', field: 'details', align: 'left' },
  { name: 'ticketNo', label: 'No. Tickets/Groups', field: 'ticket_no', align: 'left' },
  { name: 'maxAllowed', label: 'Max Tickets Per Booking', field: 'max_allowed', align: 'left' }
])

const updateTicket = async () => {
  try {
    await ticketForm.value.validate()
    await updateTicketDetails()
  } catch (error) {
    console.error('Validation error:', error)
  }
}

const updateTicketDetails = async () => {
  errorMessages.value = []
  try {
    await axios.put(`/packages/${props.ticket.id}`, { package: ticketToAmend })
    
    $q.notify({
      type: 'positive',
      message: 'Ticket has been updated!',
      icon: 'check_circle'
    })

    // Update the original ticket
    props.ticket.details = ticketToAmend.details
    props.ticket.ticket_no = ticketToAmend.ticket_no
    props.ticket.max_allowed = ticketToAmend.max_allowed
    props.event.ticket_updated_inline = true
    updateTicketVisible.value = false
  } catch (error) {
    if (error.response?.data?.errors) {
      errorMessages.value = error.response.data.errors[0]
    }
    $q.notify({
      type: 'negative',
      message: 'Ticket Could Not Be Updated!',
      icon: 'error'
    })
  }
}
</script>