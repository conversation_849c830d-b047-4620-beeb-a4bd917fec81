<template>
  <div class="ticket-container q-mb-md" :class="{'child-ticket': ticket.child_ticket}">
    <!-- Mobile-friendly stacking cards layout that replaces q-table -->
    <div class="row q-col-gutter-md">
      <!-- Ticket Icon - Visible on all screen sizes -->
      <div class="col-12 col-sm-auto self-center ticket-icon-container">
        <q-avatar v-if="!ticket.child_ticket" color="primary" text-color="white" icon="local_activity" size="lg" />
        <q-icon v-else name="subdirectory_arrow_right" size="lg" color="grey" class="q-ml-xs" />
      </div>
      
      <!-- Actions Column - Moved to the right side as a sidebar -->
      <div class="col-auto actions-sidebar-container hide-on-xs">
        <TicketRowActions
          :event="event"
          :ticket="ticket"
          :ticket-group="ticketGroup"
          :parent-ticket="parentTicket"
          :index="index"
          :advanced="advanced"
          @add-child-ticket="addChildTicketFromRow"
          @delete-ticket="deleteTicket"
          @delete-child-ticket="deleteChildTicket"
        />
      </div>
      
      <!-- Main Content Container - Responsive stacking on xs screen -->
      <div class="col-12 col-sm">
        <div class="row q-col-gutter-md">
          <!-- Layout with a sidebar for actions on desktop -->
          <div class="col-auto actions-sidebar-container hide-on-xs">
            <TicketRowActions
              :event="event"
              :ticket="ticket"
              :ticket-group="ticketGroup"
              :parent-ticket="parentTicket"
              :index="index"
              :advanced="advanced"
              @add-child-ticket="addChildTicketFromRow"
              @delete-ticket="deleteTicket"
              @delete-child-ticket="deleteChildTicket"
            />
          </div>
          
          <!-- Details Column - Full width on xs, 1/3 width on larger screens -->
          <div class="col-12 col-md-4">
            <div class="details-card q-pa-md">
              <div class="card-header q-mb-md">
                <q-icon name="confirmation_number" color="deep-purple-6" size="sm" class="q-mr-xs" />
                <span class="text-subtitle1 text-weight-medium">Ticket Information</span>
              </div>
              
              <div class="row q-col-gutter-md">
                <div class="col-12">
                  <div class="field-label">Ticket Name</div>
                  <q-input
                    :key="`${index}row_ticket`"
                    v-model="ticket.details"
                    outlined
                    placeholder="Enter ticket name"
                    :name="'tdetails' + index"
                    :readonly="event.live"
                    :rules="ticketRules.details"
                    @blur="validateField('details')"
                    class="details-input"
                    bg-color="white"
                  >
                    <template v-slot:prepend>
                      <q-icon name="confirmation_number" color="deep-purple-6" />
                    </template>
                  </q-input>
                </div>
                
                <!-- Description field integrated within the same card -->
                <div class="col-12 q-mt-sm">
                  <div class="field-label">
                    <q-icon name="description" size="xs" class="q-mr-xs" color="deep-purple-6" />
                    Ticket Description
                  </div>
                  <q-input
                    v-model="ticket.description"
                    type="textarea"
                    outlined
                    autogrow
                    rows="2"
                    :name="`ticket_desc${index}`"
                    :readonly="event.live"
                    placeholder="Enter additional information about this ticket"
                    class="full-width description-textarea"
                    bg-color="white"
                  />
                </div>
                
                <div class="col-12">
                  <div class="ticket-badges q-mt-sm">
                    <div class="row q-gutter-sm">
                      <q-chip v-if="!ticket.child_ticket && ticket.group_amount > 1" size="md" color="primary" text-color="white" icon="people">
                        {{ ticket.group_amount }} attendees per ticket
                      </q-chip>
                      
                      <q-chip v-if="ticket.child_ticket" size="md" color="grey-6" text-color="white" icon="subdirectory_arrow_right">
                        Sub-ticket
                      </q-chip>
                    </div>
                  </div>
                </div>
              </div>
              
              <div v-if="event.ticket_type === 'virtual'" class="row q-col-gutter-md q-mt-md">
                <div class="col-12">
                  <div class="field-label">Virtual Link</div>
                  <q-input
                    v-model="ticket.virtual_link"
                    outlined
                    type="url"
                    placeholder="Virtual event link (https://...)"
                    :name="`link_${index}`"
                    :readonly="event.live"
                    maxlength="150"
                    :rules="ticketRules.virtualLink"
                    @blur="validateField('virtual_link')"
                    class="virtual-link-input"
                    data-cy="virtualLink"
                    bg-color="white"
                  >
                    <template v-slot:prepend>
                      <q-icon name="link" color="primary" />
                    </template>
                    <template v-slot:append v-if="ticket.virtual_link">
                      <q-btn 
                        round 
                        flat 
                        icon="open_in_new" 
                        size="xs"
                        color="primary"
                        type="a"
                        :href="ticket.virtual_link"
                        target="_blank"
                      >
                        <q-tooltip>Open link in new tab</q-tooltip>
                      </q-btn>
                    </template>
                  </q-input>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Inventory Column - Full width on xs, 1/3 width on larger screens -->
          <div class="col-12 col-md-4">
            <div class="inventory-card q-pa-md">
              <div class="card-header q-mb-md">
                <q-icon name="inventory_2" color="secondary" size="sm" class="q-mr-xs" />
                <span class="text-subtitle1 text-weight-medium">Inventory Details</span>
              </div>
              
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-6">
                  <div class="field-label">Total tickets available</div>
                  <q-input
                    :key="`${index}row_ticket_no`"
                    v-model="ticket.ticket_no"
                    outlined
                    type="number"
                    :name="'tixamount' + index"
                    :readonly="event.live"
                    :rules="ticketRules.ticketNo"
                    @blur="validateField('ticket_no'); validateMaxAllowed()"
                    placeholder="100"
                    bg-color="white"
                    class="inventory-input"
                  >
                    <template v-slot:prepend>
                      <q-icon name="inventory_2" color="secondary" />
                    </template>
                  </q-input>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="field-label">Max tickets per order</div>
                  <q-input
                    :key="`${index}row_max`"
                    v-model="ticket.max_allowed"
                    outlined
                    type="number"
                    :name="'ticketMax' + index"
                    :readonly="event.live"
                    :rules="ticketRules.maxAllowed"
                    @blur="validateField('max_allowed')"
                    placeholder="1"
                    bg-color="white"
                    class="inventory-input"
                  >
                    <template v-slot:prepend>
                      <q-icon name="person" color="secondary" />
                    </template>
                  </q-input>
                </div>
                
                <!-- Attendees per Ticket moved here from details panel -->
                <div class="col-12" v-if="!ticket.child_ticket">
                  <div class="field-label q-mt-sm">Attendees per ticket</div>
                  <q-input
                    v-model="ticket.group_amount"
                    type="number"
                    outlined
                    min="1"
                    step="1"
                    @blur="preventZero"
                    :readonly="event.live"
                    :name="`group_amount${index}`"
                    class="inventory-input"
                    :rules="[ val => val >= 1 || 'Group amount must be at least 1' ]"
                  >
                    <template v-slot:prepend>
                      <q-icon name="group" size="xs" color="secondary" />
                    </template>
                  </q-input>
                  <!-- <div class="text-caption text-grey q-mt-xs">
                    For group tickets (default: 1)
                  </div> -->
                </div>
              </div>
            </div>
          </div>
          
          <!-- Pricing Column - Full width on xs, 1/3 width on larger screens -->
          <div class="col-12 col-md-4">
            <div class="pricing-card q-pa-md">
              <div class="card-header q-mb-md">
                <q-icon name="payments" color="primary" size="sm" class="q-mr-xs" />
                <span class="text-subtitle1 text-weight-medium">Pricing Options</span>
              </div>
              
              <div class="row q-col-gutter-md">
                <div class="col-12" :class="{'col-sm-6': event.datetime_eb}">
                  <div class="field-label">Regular price</div>
                  <q-input
                    :key="`${index}row_full`"
                    v-model="ticket.cost_b"
                    @blur="setDefaultForEB(); validateField('cost_b')"
                    outlined
                    type="number"
                    step="0.01"
                    :name="'costB' + index"
                    :readonly="event.live || event.ticket_payment_options == 'free'"
                    :rules="ticketRules.costB"
                    placeholder="0.00"
                    bg-color="white"
                    class="pricing-input"
                  >
                    <template v-slot:prepend>
                      <q-icon name="currency_pound" color="primary" />
                    </template>
                  </q-input>
                </div>
                <div class="col-12 col-sm-6" v-if="event.datetime_eb">
                  <div class="field-label">Early bird price</div>
                  <q-input
                    :key="`${index}row_disc`"
                    v-model="ticket.cost_a"
                    outlined
                    type="number"
                    step="0.01"
                    :disable="!event.datetime_eb"
                    :name="'costA' + index"
                    :readonly="event.live"
                    :rules="ticketRules.costA"
                    @blur="validateField('cost_a')"
                    placeholder="0.00"
                    bg-color="white"
                    class="pricing-input"
                  >
                    <template v-slot:prepend>
                      <q-icon name="savings" color="primary" />
                    </template>
                  </q-input>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Actions Column - Fixed at bottom on mobile, right side on desktop -->
          <div class="col-12 actions-container">
            <TicketRowActions
              :event="event"
              :ticket="ticket"
              :ticket-group="ticketGroup"
              :parent-ticket="parentTicket"
              :index="index"
              :advanced="advanced"
              @add-child-ticket="addChildTicketFromRow"
              @delete-ticket="deleteTicket"
              @delete-child-ticket="deleteChildTicket"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, inject, reactive } from 'vue'
import { useEventBus } from '@vueuse/core'
import EditTicket from './EditTicket.vue'
import AdvancedTicketOptions from './AdvancedTicketOptions.vue'
import VirtualTicketOptions from './VirtualTicketOptions.vue'
import TicketRowActions from './TicketRowActions.vue'


const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  ticket: {
    type: Object,
    required: true
  },
  ticketGroup: {
    type: Object,
    default: null
  },
  parentTicket: {
    type: Object,
    default: null
  },
  index: {
    type: String,
    required: true
  },
  advanced: {
    type: Boolean,
    default: false
  },
  freeAllowed: {
    type: Boolean,
    default: false
  }
})

// Access validation context from parent 
const ticketValidation = inject('ticketValidation')

const emit = defineEmits(['addChildTicket', 'deleteTicket', 'deleteChildTicket'])

const { emit: emitEvent, on: onEvent } = useEventBus('ticket')

// Ensure that properties exist
if (!props.ticket.cost_a) props.ticket.cost_a = 0
if (!props.ticket.cost_b) props.ticket.cost_b = 0
if (!props.ticket.ticket_no) props.ticket.ticket_no = 1
if (!props.ticket.max_allowed) props.ticket.max_allowed = 1

const dateTimeEBRRequired = computed(() => props.event.date_eb ? "required" : "")

const ebMin = computed(() => props.event.dateEB ? 0.1 : 0)

const childIndent = computed(() => props.ticket.child_ticket ? { paddingLeft: "15px" } : {})

const minPrice = computed(() => props.freeAllowed ? 0.0 : 0.1)

// State for expandable details panel - always visible now
const showDetails = ref(true);

// No longer needed as details are always shown
const hasAdditionalDetails = computed(() => {
  return (
    (props.ticket.description && props.ticket.description.trim().length > 0) ||
    (!props.ticket.child_ticket && props.ticket.group_amount > 1)
  )
})

// No longer needed, but kept for compatibility
const toggleDetails = () => {
  // No longer toggles, details are always shown
  showDetails.value = true;
}

// Define ticket validation rules
const ticketRules = reactive({
  details: [
    val => !!val || 'Ticket name is required'
  ],
  ticketNo: [
    val => !!val || 'Ticket number is required',
    val => val > 0 || 'Ticket number must be greater than 0'
  ],
  maxAllowed: [
    val => !!val || 'Max tickets is required',
    val => val > 0 || 'Max tickets must be greater than 0',
    val => val <= props.ticket.ticket_no || 'Max tickets cannot exceed total tickets'
  ],
  costB: [
    val => !!val || 'Full price is required',
    val => val >= minPrice.value || `Price must be at least £${minPrice.value}`
  ],
  costA: [
    val => !props.event.datetime_eb || !!val || 'Discount price is required',
    val => !props.event.datetime_eb || val >= minPrice.value || `Price must be at least £${minPrice.value}`,
    val => !props.event.datetime_eb || val <= props.ticket.cost_b || 'Discount price cannot exceed full price'
  ],
  virtualLink: [
    val => !!val || 'Link is required',
    val => /^https?:\/\/.*/.test(val) || 'Must be a valid URL'
  ]
})

// For displaying pricing info in a user-friendly format
const formattedPrice = computed(() => {
  const regularPrice = parseFloat(props.ticket.cost_b || 0).toFixed(2)
  
  if (props.event.datetime_eb && parseFloat(props.ticket.cost_a || 0) > 0) {
    const earlyBirdPrice = parseFloat(props.ticket.cost_a || 0).toFixed(2)
    if (earlyBirdPrice !== regularPrice) {
      return `£${earlyBirdPrice} early bird / £${regularPrice} regular`
    }
  }
  
  return regularPrice > 0 ? `£${regularPrice}` : 'Free'
})

// Method to validate a field and report to parent
const validateField = (field) => {
  if (ticketValidation) {
    // Use the correct rules based on the field
    const rulesForField = ticketRules[field] || []
    return ticketValidation.validateField(props.ticket, field, rulesForField)
  }
  return true
}

// Special validation for max_allowed that depends on ticket_no
const validateMaxAllowed = () => {
  if (ticketValidation) {
    return ticketValidation.validateField(props.ticket, 'max_allowed', ticketRules.maxAllowed)
  }
  return true
}

// Prevent group_amount from being set to zero or negative
const preventZero = () => {
  if (!props.ticket.group_amount || props.ticket.group_amount < 1) {
    props.ticket.group_amount = 1
  }
}

onMounted(() => {
  onEvent('clearEarlyBird', () => {
    props.ticket.cost_a = props.ticket.cost_b
  })
  
  // Details are always visible now
  showDetails.value = true
  
  // Validate initial ticket state
  if (ticketValidation) {
    // Initial validation of required fields
    validateField('details')
    validateField('ticket_no')
    validateField('max_allowed')
    validateField('cost_b')
    if (props.event.datetime_eb) {
      validateField('cost_a')
    }
    if (props.event.ticket_type === 'virtual') {
      validateField('virtual_link')
    }
  }
})

const fireRecalc = (index) => {
  if (index == "0A") {
    emitEvent('reCalc')
  }
}

const setDefaultForEB = () => {
  if (+props.ticket.cost_b > 0) {
    if (+props.ticket.cost_a == 0 || +props.ticket.cost_b < +props.ticket.cost_a) {
      props.ticket.cost_a = props.ticket.cost_b
    }
  }
}

const addChildTicketFromRow = (ticket) => {
  // Emit the event directly to the parent component
  emit('addChildTicket', ticket)
}

const deleteTicket = (ticket, ticketGroup) => {
  emit('deleteTicket', ticket, ticketGroup)
}

const deleteChildTicket = (childTicket) => {
  const index = props.parentTicket.child_tickets.indexOf(childTicket)
  props.parentTicket.child_tickets.splice(index, 1)
  props.event.tickets.push({})
  props.event.tickets.pop()
}
</script>

<style lang="scss" scoped>
/* Common styles */
.q-input {
  min-width: 100px;
}

/* Main ticket container styling */
.ticket-container {
  position: relative;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: rgba(0, 0, 0, 0.01);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.09);
    transform: translateY(-2px);
  }
  
  &.child-ticket {
    background-color: rgba(0, 0, 0, 0.03);
    border-left: 4px solid var(--q-primary, #1976d2);
    margin-left: 16px;
    padding-left: 8px;
  }
}

.ticket-icon-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 16px;
}

/* Input field styling */
.details-input {
  .q-field__native {
    font-weight: 500;
    font-size: 1.05rem;
  }
}

/* Description field styling */
.description-textarea {
  background-color: rgba(103, 58, 183, 0.03);
  border-left: 3px solid rgba(103, 58, 183, 0.3);
}

/* Card styling for content sections */
.inventory-card, 
.pricing-card,
.details-card {
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  margin-bottom: 16px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  @media (max-width: 599px) { /* xs breakpoint in Quasar */
    margin-bottom: 8px;
  }
}

.details-card {
  border-left: 4px solid rgba(103, 58, 183, 0.8);
}

.inventory-card {
  border-left: 4px solid rgba(25, 118, 210, 0.8);
}

.pricing-card {
  border-left: 4px solid rgba(76, 175, 80, 0.8);
}

.card-header {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

/* Field labels */
.field-label {
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
  font-weight: 500;
}

/* Input styling */
.inventory-input, 
.pricing-input {
  width: 100%;
}

/* Actions container positioning */
.actions-container {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0;
  
  @media (max-width: 599px) { /* xs breakpoint in Quasar */
    justify-content: center;
    border-top: 1px dashed rgba(0, 0, 0, 0.1);
    margin-top: 8px;
    padding-top: 16px;
  }
}

/* Actions sidebar container */
.actions-sidebar-container {
  display: none;
  
  @media (min-width: 600px) { /* sm and above breakpoint */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 16px 0;
    order: 3; /* Move to the end of the row */
  }
}

/* Hide actions sidebar on mobile */
.hide-on-xs {
  @media (max-width: 599px) { /* xs breakpoint in Quasar */
    display: none !important;
  }
}

/* Details panel styling */
.details-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 767px) { /* md breakpoint and below in Quasar */
  .inventory-card, 
  .pricing-card,
  .details-card {
    margin-bottom: 12px;
  }
}

@media (max-width: 599px) { /* xs breakpoint in Quasar */
  .ticket-container {
    padding: 12px 8px;
    
    &.child-ticket {
      margin-left: 8px;
    }
  }
  
  .card-header {
    padding-bottom: 6px;
    margin-bottom: 8px;
  }
  
  .field-label {
    margin-bottom: 2px;
  }
  
  .q-input {
    margin-bottom: 4px;
  }
}

/* Improve placeholder visibility */
:deep(.q-field__native::placeholder) {
  opacity: 0.5;
}

/* Input validation styling */
:deep(.q-field__bottom) {
  padding-top: 4px;
  font-size: 11px;
  
  @media (max-width: 599px) {
    padding-top: 2px;
  }
}

:deep(.q-field--error) {
  .q-field__bottom {
    color: var(--q-negative);
  }
}
</style>