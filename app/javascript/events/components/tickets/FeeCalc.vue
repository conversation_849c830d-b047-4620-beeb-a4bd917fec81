<template>
  <q-card>
    <q-card-section>
      <div class="flex flex-wrap">
        <div class="w-full">
          <h4>Use the calculator below to show how much you will be charged in fees.</h4>
          <h5>Please note these figures are representative only.</h5>
        </div>

        <div class="w-1/3">
          <label for="ticketPrice">Ticket Price</label>
          <q-input
            v-model.number="ticketCost"
            type="number"
            :min="0.01"
            class="mb-2"
            dense
            outlined
          />
        </div>

        <div class="w-full">
          <q-table 
            :rows="tableData" 
            :columns="columns"
            row-key="region"
            flat
            bordered
          >
            <template v-slot:body-cell-passOnFees="props">
              <q-td :props="props">
                <strong>{{ props.value }}</strong>
              </q-td>
            </template>
            <template v-slot:header-cell-passOnFees="props">
              <q-th :props="props">
                <div class="flex items-center">
                  Pass On Fees
                  <q-icon
                    name="info"
                    class="q-ml-sm cursor-pointer"
                  >
                    <q-tooltip>
                      If you want to receive the ticket price in full, charge this for the ticket instead
                    </q-tooltip>
                  </q-icon>
                </div>
              </q-th>
            </template>
          </q-table>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
})

const $q = useQuasar()
const fees = ref({})
const ticketCost = ref(10.00)

const columns = [
  { name: 'region', align: 'left', label: 'Regional Fees', field: 'region' },
  { name: 'feesAmount', align: 'left', label: 'Fees Amount', field: 'feesAmount' },
  { name: 'delegatePay', align: 'left', label: 'Delegate Will Pay', field: 'delegatePay' },
  { name: 'youReceive', align: 'left', label: 'You Will Receive', field: 'youReceive' },
  { name: 'passOnFees', align: 'left', label: 'Pass On Fees', field: 'passOnFees' },
]

const charityRateEnabled = computed(() => {
  return props.event.charity_rate_enabled
})

const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP',
    minimumFractionDigits: 2
  }).format(value)
}

const calculateTicketPrices = (percent_fees, flat_fees) => {
  return ticketCost.value - (((ticketCost.value / 100) * parseFloat(percent_fees)) + parseFloat(flat_fees))
}

const calculatePassOnFees = (percent_fees, flat_fees) => {
  return (ticketCost.value + parseFloat(flat_fees)) / (1 - (parseFloat(percent_fees) / 100))
}

const tableData = computed(() => {
  if (!Object.keys(fees.value).length) return []
  
  return [
    {
      region: 'UK Fees',
      feesAmount: charityRateEnabled.value 
        ? `${fees.value.charity_fees_card}% + ${formatCurrency(fees.value.charity_fees_add)}`
        : `${fees.value.standard_fees_card}% + ${formatCurrency(fees.value.standard_fees_add)}`,
      delegatePay: formatCurrency(ticketCost.value),
      youReceive: formatCurrency(calculateTicketPrices(fees.value.standard_fees_card, fees.value.standard_fees_add)),
      passOnFees: formatCurrency(calculatePassOnFees(fees.value.standard_fees_card, fees.value.standard_fees_add))
    },
    {
      region: 'UK Premium Card Fees',
      feesAmount: charityRateEnabled.value 
        ? `${fees.value.charity_uk_premium_fees_card}% + ${formatCurrency(fees.value.charity_uk_premium_fees_add)}`
        : `${fees.value.uk_premium_fees_card}% + ${formatCurrency(fees.value.uk_premium_fees_add)}`,
      delegatePay: formatCurrency(ticketCost.value),
      youReceive: formatCurrency(calculateTicketPrices(fees.value.uk_premium_fees_card, fees.value.uk_premium_fees_add)),
      passOnFees: formatCurrency(calculatePassOnFees(fees.value.uk_premium_fees_card, fees.value.uk_premium_fees_add))
    },
    {
      region: 'EU Fees',
      feesAmount: charityRateEnabled.value 
        ? `${fees.value.eu_charity_fees_card}% + ${formatCurrency(fees.value.eu_charity_fees_add)}`
        : `${fees.value.eu_standard_fees_card}% + ${formatCurrency(fees.value.eu_standard_fees_add)}`,
      delegatePay: formatCurrency(ticketCost.value),
      youReceive: formatCurrency(calculateTicketPrices(fees.value.eu_standard_fees_card, fees.value.eu_standard_fees_add)),
      passOnFees: formatCurrency(calculatePassOnFees(fees.value.eu_standard_fees_card, fees.value.eu_standard_fees_add))
    },
    {
      region: 'International Fees',
      feesAmount: charityRateEnabled.value 
        ? `${fees.value.int_charity_fees_card}% + ${formatCurrency(fees.value.int_charity_fees_add)}`
        : `${fees.value.int_standard_fees_card}% + ${formatCurrency(fees.value.int_standard_fees_add)}`,
      delegatePay: formatCurrency(ticketCost.value),
      youReceive: formatCurrency(calculateTicketPrices(fees.value.int_standard_fees_card, fees.value.int_standard_fees_add)),
      passOnFees: formatCurrency(calculatePassOnFees(fees.value.int_standard_fees_card, fees.value.int_standard_fees_add))
    },
    {
      region: 'BACs Fees',
      feesAmount: charityRateEnabled.value 
        ? `${fees.value.charity_stripe_bacs_fees}% + ${formatCurrency(fees.value.charity_stripe_bacs_fees_add)}`
        : `${fees.value.stripe_bacs_fees}% + ${formatCurrency(fees.value.stripe_bacs_fees_add)}`,
      delegatePay: formatCurrency(ticketCost.value),
      youReceive: formatCurrency(calculateTicketPrices(fees.value.stripe_bacs_fees, fees.value.stripe_bacs_fees_add)),
      passOnFees: formatCurrency(calculatePassOnFees(fees.value.stripe_bacs_fees, fees.value.stripe_bacs_fees_add))
    }
  ]
})

onMounted(async () => {
  let url = "/active_fees"
  if (props.event) {
    url = url + "?event_id=" + props.event.id
  }

  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Failed to fetch fees')
    }
    fees.value = await response.json()
  } catch (error) {
    console.error('Error fetching fees:', error)
    $q.notify({
      message: 'Error fetching fee information',
      color: 'negative'
    })
  }
})
</script>

<style scoped>
.mb-2 {
  margin-bottom: 0.5rem;
}
</style>
