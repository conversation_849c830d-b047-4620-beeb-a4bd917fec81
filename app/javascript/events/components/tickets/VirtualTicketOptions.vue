<template>
  <div>
    <q-btn
      flat
      dense
      :color="virtual ? 'positive' : 'secondary'"
      :icon="virtual ? 'videocam' : 'link'"
      :label="virtual ? 'Virtual Settings' : 'Add Virtual'"
      @click="visible = true"
      size="sm"
      class="q-px-sm"
      :class="{'bg-green-1': virtual}"
      data-cy="virtualTicketOptions"
    >
      <q-tooltip>{{ virtual ? 'Edit virtual event settings' : 'Add virtual event settings' }}</q-tooltip>
      <q-badge v-if="virtual && hasCredentials" floating color="positive" text-color="white">
        <q-icon name="check" size="xs" />
      </q-badge>
    </q-btn>

    <q-dialog v-model="visible" persistent>
      <q-card style="width: 550px; max-width: 95vw" class="virtual-settings-dialog" data-cy="advancedOptionsTable">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">
            <q-icon name="videocam" class="q-mr-sm" />
            Virtual Event Settings
          </div>
        </q-card-section>
        
        <q-card-section class="q-pt-lg">
          <p class="text-grey-8 q-mb-md">
            <q-icon name="info" color="info" /> 
            Configure the virtual event settings for this ticket. The meeting ID and password will be sent to attendees.
          </p>
          
          <div class="q-mb-lg">
            <q-input
              v-model="meetingId"
              :name="`meeting_id_${index}`"
              :id="`virtual_meeting_id_${index}`"
              label="Meeting ID (optional)"
              outlined
              maxlength="150"
              :readonly="event.live"
              class="q-mb-md"
              hint="For Zoom, Google Meet, or other virtual platforms"
            >
              <template v-slot:prepend>
                <q-icon name="meeting_room" color="secondary" />
              </template>
              <template v-slot:append v-if="meetingId">
                <q-icon name="check_circle" color="positive" />
              </template>
            </q-input>
            
            <q-input
              v-model="meetingPassword"
              :name="`meeting_password_${index}`"
              :id="`virtual_meeting_password_${index}`"
              label="Meeting password (optional)"
              outlined
              maxlength="150"
              :type="showPassword ? 'text' : 'password'"
              :readonly="event.live"
              hint="Password for accessing the virtual event"
            >
              <template v-slot:prepend>
                <q-icon name="lock" color="secondary" />
              </template>
              <template v-slot:append>
                <q-icon
                  :name="showPassword ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="showPassword = !showPassword"
                />
              </template>
            </q-input>
          </div>
          
          <div class="row justify-center q-col-gutter-md">
            <q-btn
              v-if="!event.live"
              color="positive"
              label="Apply Settings"
              icon="check"
              unelevated
              rounded
              @click="changeToVirtual()"
              :disabled="!ticket.virtual_link"
            />
            
            <q-btn
              v-if="!event.live && virtual"
              color="negative"
              label="Remove Virtual Settings"
              icon="delete"
              unelevated
              rounded
              @click="removeVirtualLink()"
            />
          </div>
          
          <q-banner v-if="!ticket.virtual_link" rounded class="bg-yellow-1 text-grey-9 q-mt-sm">
            <template v-slot:avatar>
              <q-icon name="warning" color="warning" />
            </template>
            Please add a virtual link in the ticket details section before saving virtual settings.
          </q-banner>
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat label="Close" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  ticket: {
    type: Object,
    required: true
  },
  index: {
    type: String,
    required: true
  }
})

const visible = ref(false)
const virtual = ref(false)
const meetingId = ref('')
const meetingPassword = ref('')
const showPassword = ref(false)

const hasCredentials = computed(() => {
  return !!(meetingId.value || meetingPassword.value)
})

onMounted(() => {
  if (props.ticket.virtual_link) {
    virtual.value = true
    meetingId.value = props.ticket.meeting_id || ''
    meetingPassword.value = props.ticket.meeting_password || ''
  }
})

const changeToVirtual = () => {
  if (props.ticket.virtual_link && props.ticket.virtual_link.length > 0) {
    props.ticket.meeting_id = meetingId.value
    props.ticket.meeting_password = meetingPassword.value
    virtual.value = true
    visible.value = false
  } else {
    changeToStandard()
  }
}

const removeVirtualLink = () => {
  changeToStandard()
  visible.value = false
}

const changeToStandard = () => {
  virtual.value = false
  meetingId.value = ''
  meetingPassword.value = ''
  props.ticket.meeting_id = ''
  props.ticket.meeting_password = ''
}
</script>

<style lang="scss" scoped>
.virtual-settings-dialog {
  border-radius: 8px;
  overflow: hidden;

  .q-card__section {
    padding: 16px 24px;
  }
}
</style>