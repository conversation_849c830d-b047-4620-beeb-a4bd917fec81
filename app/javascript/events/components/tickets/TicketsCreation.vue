<script setup>
import { ref, computed, onMounted, watch, provide, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useQuasar } from "quasar";
import { date } from "quasar";
// import FeeCalc from './FeeCalc.vue'
import TicketRow from "./TicketRow.vue";
import SavedStatusIndicator from "./SavedStatusIndicator.vue";
import EventProgressDialog from "../shared/EventProgressDialog.vue";
import { useEventStore } from "@/stores/event"; // Import the unified store
import VueDraggable from "vuedraggable";
import { useEventBus } from "@vueuse/core"; // Use vueuse's eventBus instead of custom event bus
import axios from "axios"; // Import axios for HTTP requests
import { useTicketValidation } from "../../composables/useTicketValidation"; // Import the validation composable
// import DebugPanel from '../debug/DebugPanel.vue' // Import the debug panel
// import { requireAuthentication } from '../../utils/authHelper' // Import the auth helper

const $q = useQuasar();
const eventStore = useEventStore(); // Use the Pinia store instead of Vuex
const router = useRouter();
const route = useRoute();

// Get the eventId from route params if available
const props = defineProps({
  eventId: {
    type: [String, Number],
    default: null,
  },
});

// Create event buses
const ticketEventBus = useEventBus("ticket-events");

const freeEvents = ref(window.freeEvents);
const event = computed(() => eventStore.getEvent); // Use the safe getter
const buttonDisabled = ref(false);
const advanced = computed(() => eventStore.advancedUser); // Access advanced user from Pinia store
const packagesSubmitted = ref(false);
const packagesSize = ref(0);
const menuTitle = ref("");
const tickets = ref([]);
const eventPostable = ref({});
const costFull = ref(0.0);
const costDiscount = ref(0.0);
const showTicketOptionsDialog = ref(false);
// const showDebugPanel = ref(false)

// Initialize ticket validation
const {
  validateTicket,
  validateField,
  validationErrors,
  validationState,
  rules,
  resetValidation,
} = useTicketValidation();

// Provide validation context to child components
provide("ticketValidation", {
  validateField,
  validationErrors,
  validationState,
  rules,
});

// Column definitions for the ticket tables
const ticketColumns = [
  {
    name: "icon",
    // label: '',
    field: "icon",
    align: "left",
    sortable: false,
  },
  {
    name: "details",
    // label: 'Ticket Details',
    field: "details",
    align: "left",
    sortable: false,
  },
  {
    name: "inventory",
    // label: 'Inventory',
    field: (row) => {
      if (!row) return "N/A";
      const ticketNo = row.ticket_no !== undefined ? row.ticket_no : 0;
      const maxAllowed = row.max_allowed !== undefined ? row.max_allowed : 1;
      return `${ticketNo} tickets (max ${maxAllowed} per booking)`;
    },
    align: "left",
    sortable: false,
  },
  {
    name: "pricing",
    // label: 'Pricing',
    field: (row) => {
      if (!row) return "N/A";
      const costStr = row.cost_b !== undefined ? row.cost_b : "0";
      const cost = parseFloat(costStr);
      return cost > 0 ? `£${cost.toFixed(2)}` : "Free";
    },
    align: "left",
    sortable: false,
  },
  {
    name: "options",
    // label: 'Options',
    field: "options",
    align: "center",
    sortable: false,
  },
  {
    name: "actions",
    // label: 'Actions',
    field: "actions",
    align: "right",
    sortable: false,
  },
];

// State object following Context7 pattern
const state = ref({
  isLoading: true, // Start with loading true
  isEventLoading: true, // Separate loading state for event
  error: null,
  savedStatus: {
    lastSaved: null,
    ticketsAreSaved: false,
  },
  ticketsLoaded: false,
});

// Computed property to check if event is ready for rendering
const isEventLive = computed(() => event.value.live === true);
const isEventReady = computed(() => {
  return (
    event.value &&
    event.value.id &&
    !state.value.isEventLoading &&
    typeof event.value === "object"
  );
});

// Computed property for overall loading state
const isComponentReady = computed(() => {
  return isEventReady.value && state.value.ticketsLoaded;
});

// Create reactive variable for the progress dialog
const showProgressDialog = ref(false);

const earlyBirdEnabled = ref(false);
const ticketScheduleEnabled = ref(false);

const handlePaymentOptionChange = (option) => {
  if (option === "free") {
    // Set all tickets to free if option is 'free'
    setTicketsToFree();
  }
};

const handleEarlyBirdChange = (enabled) => {
  if (enabled) {
    if (!event.value?.datetime_eb) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      event.value.datetime_eb = date.formatDate(tomorrow, "YYYY-MM-DD");
    }
  } else {
    event.value.datetime_eb = null;
  }
};

const dateOptions = (dateStr) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date.getDateDiff(dateStr, today, "days") >= 0;
};

// Helper method to validate if event data is stale
const isEventDataStale = (eventData) => {
  // Check for required properties to consider event data complete
  if (!eventData) return true;
  if (!eventData.id) return true;
  if (!Array.isArray(eventData.tickets)) return true;

  // If we have no tickets and we're on the tickets page, data might be stale
  if (eventData.tickets.length === 0 && route.name === "ticket-creation") {
    return true;
  }

  // If it's been more than 2 minutes since we last loaded the data, consider it stale
  const lastLoadTime = localStorage.getItem("hg-event-last-loaded");
  if (lastLoadTime) {
    const twoMinutesAgo = Date.now() - 2 * 60 * 1000;
    if (parseInt(lastLoadTime) < twoMinutesAgo) {
      console.log("Event data is older than 2 minutes, reloading");
      return true;
    }
  } else {
    return true;
  }

  return false;
};

// Check server connection and reload event data if needed
const checkServerAndReloadData = async (eventId) => {
  if (!eventId) return false;

  try {
    console.log("Checking server connection...");
    // Use the Rails ping endpoint
    const response = await axios.get("/ping", {
      timeout: 5000, // 5 second timeout
      headers: {
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
        Expires: "0",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    // If server is responding but we're missing data, reload
    if (response.status === 200) {
      console.log("Server connection verified, reloading event data");
      const success = await loadEventData(eventId);
      if (success) {
        console.log("Event data reloaded successfully");
        // Now reload tickets data
        await loadTickets();
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error("Server connection check failed:", error);
    // Show an error notification to the user
    $q.notify({
      type: "negative",
      message:
        "Connection to server failed. Please check your internet connection and try again.",
      position: "top",
      timeout: 5000,
      actions: [
        {
          label: "Retry",
          color: "white",
          handler: () => checkServerAndReloadData(eventId),
        },
      ],
    });
    return false;
  }
};

// Load event details by ID if not already available
const loadEventData = async (eventId) => {
  if (!eventId) return false;

  try {
    console.log(`Loading event data for ID: ${eventId}`);
    state.value.isLoading = true;

    // Use a cache-busting parameter to ensure we get fresh data
    const cacheBuster = new Date().getTime();
    const response = await axios.get(
      `/event_details/${eventId}?_=${cacheBuster}`,
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );

    if (response.data && response.data.event) {
      // Update the store with the event data
      const receivedEvent = response.data.event;
      console.log("Event data loaded successfully", receivedEvent);

      // Temporarily suspend reactivity updates when updating localStorage
      await new Promise((resolve) => setTimeout(resolve, 0)); // Break the reactivity chain

      // Update the store (use setEvent instead of directly modifying event.value to avoid reactivity issues)
      eventStore.setEvent(receivedEvent);

      // Store the current event ID in localStorage for persistence only if changed
      if (
        localStorage.getItem("hg-current-event-id") !==
        receivedEvent.id.toString()
      ) {
        localStorage.setItem(
          "hg-current-event-id",
          receivedEvent.id.toString()
        );
      }

      // Record when we last loaded the data
      localStorage.setItem("hg-event-last-loaded", Date.now().toString());

      // Clear the force reload flag once we've loaded the data
      localStorage.removeItem("hg-force-ticket-reload");

      state.value.isLoading = false;
      return true;
    } else if (response.data && response.data.error) {
      console.error(`API Error: ${response.data.error}`);

      // Check if this is an authentication error
      if (
        response.data.error.includes("Access Denied") ||
        response.data.error.includes("log in")
      ) {
        // Try to handle authentication by reloading the page
        $q.dialog({
          title: "Authentication Error",
          message:
            "Your session may have expired. Would you like to reload the page?",
          ok: {
            color: "primary",
            label: "Reload",
          },
          cancel: {
            label: "Cancel",
          },
          persistent: true,
        }).onOk(() => {
          // Try to force a reauth by going to root
          window.location.href = "/";
        });
      }

      state.value.error = response.data.error;
      $q.notify({
        type: "negative",
        message: response.data.error,
        position: "top",
        timeout: 3000,
      });
      return false;
    }
    return false;
  } catch (error) {
    console.error("Failed to load event data:", error.response || error);
    state.value.error =
      error.response?.data?.error ||
      "Failed to load event data. Please try again.";
    $q.notify({
      type: "negative",
      message: state.value.error,
      position: "top",
      timeout: 5000,
    });
    return false;
  } finally {
    state.value.isLoading = false;
  }
};

// Add method to load tickets from the controller using axios
const loadTickets = async () => {
  // Use all available sources for event ID (store, route params, props)
  const eventId =
    event.value?.id ||
    route.params.eventId ||
    props.eventId ||
    localStorage.getItem("hg-current-event-id");

  if (!eventId) {
    console.warn("Cannot load tickets: No event ID available");
    return;
  }

  state.value.isLoading = true;
  state.value.error = null;

  // Check if the event already has tickets with IDs and if we're not forcing a reload
  if (
    event.value &&
    event.value.tickets &&
    Array.isArray(event.value.tickets) &&
    event.value.tickets.length > 0 &&
    event.value.tickets.some((ticket) => ticket.id) &&
    localStorage.getItem("hg-force-ticket-reload") !== "true"
  ) {
    console.log("Event already has tickets with IDs, using cached data");
    state.value.ticketsLoaded = true;
    state.value.isLoading = false;
    return;
  }

  // Clear the force reload flag if it was set
  localStorage.removeItem("hg-force-ticket-reload");

  try {
    console.log(`Loading tickets for event ID: ${eventId}`);

    // Add a cache-busting parameter to prevent browser/CDN caching
    const cacheBuster = new Date().getTime();
    const response = await axios.get(
      `/event_details/${eventId}/tickets?_=${cacheBuster}`,
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
    console.log("Tickets API response:", response.data);

    // Check for error response
    if (response.data && response.data.error) {
      state.value.error = response.data.error;
      $q.notify({
        type: "negative",
        message: response.data.error,
        position: "top",
        timeout: 3000,
      });

      if (response.data.error.includes("Access Denied")) {
        // Handle authentication issues
        $q.dialog({
          title: "Authentication Error",
          message:
            "Your session may have expired. Would you like to go back to the events page?",
          ok: {
            color: "primary",
            label: "Yes",
          },
          cancel: {
            color: "negative",
            label: "No",
          },
          persistent: true,
        }).onOk(() => {
          window.location.href = "/"; // Redirect to root to trigger reauth
        });
      }
      return;
    }

    // Initialize tickets array if it doesn't exist in the response
    if (!response.data.tickets) {
      response.data.tickets = [];
    }

    // Set tickets from response
    tickets.value = response.data.tickets;

    // Ensure we have a valid event object before setting tickets
    if (!event.value) {
      await loadEventData(eventId);
    }

    // Update the event object with the tickets
    if (event.value) {
      event.value.tickets = response.data.tickets;
      packagesSize.value = response.data.tickets.length;

      // Initialize ticket_groups array if it doesn't exist in the response
      if (!response.data.ticket_groups) {
        response.data.ticket_groups = [];
      }

      event.value.ticket_groups = response.data.ticket_groups;

      // Store updated event in the store to ensure persistence
      eventStore.setEvent(event.value);

      // Set a timestamp for when we last loaded tickets
      localStorage.setItem("hg-tickets-last-loaded", Date.now().toString());
    }

    // Add a default ticket if there are no tickets or ticket groups
    if (
      (!event.value.tickets || event.value.tickets.length === 0) &&
      (!event.value.ticket_groups || event.value.ticket_groups.length === 0)
    ) {
      addPackage(true);
    }

    // Mark tickets as loaded
    state.value.ticketsLoaded = true;
    console.log("Tickets loaded successfully", {
      tickets: event.value?.tickets?.length || 0,
      groups: event.value?.ticket_groups?.length || 0,
    });
  } catch (error) {
    state.value.error =
      error.response?.data?.error || "Failed to load ticket details";
    $q.notify({
      type: "negative",
      message: state.value.error,
      position: "top",
      timeout: 3000,
    });
    console.error("Error loading tickets:", error);
  } finally {
    state.value.isLoading = false;
  }
};

// Manual refresh function for explicit reloading of data
const refreshTicketsData = async () => {
  const eventId =
    event.value?.id ||
    route.params.eventId ||
    props.eventId ||
    localStorage.getItem("hg-current-event-id");
  if (!eventId) return false;

  try {
    state.value.isLoading = true;

    // Force clear any cached tickets data
    state.value.ticketsLoaded = false;

    // Set a flag to force ticket reload even if we have cached tickets
    localStorage.setItem("hg-force-ticket-reload", "true");

    // Load fresh event data
    const success = await loadEventData(eventId);
    if (success) {
      // Then load fresh tickets data
      await loadTickets();

      // Clear the force reload flag
      localStorage.removeItem("hg-force-ticket-reload");

      $q.notify({
        type: "positive",
        message: "Ticket data refreshed successfully",
        position: "top",
        timeout: 2000,
      });

      return true;
    }
    return false;
  } catch (error) {
    console.error("Failed to refresh tickets data:", error);
    $q.notify({
      type: "negative",
      message: "Failed to refresh ticket data",
      position: "top",
      timeout: 3000,
    });
    return false;
  } finally {
    state.value.isLoading = false;
    localStorage.removeItem("hg-force-ticket-reload");
  }
};

// Check for URL hash with ticket ID and ensure it matches the localStorage
const checkUrlHashForTicketId = () => {
  const hash = window.location.hash;
  const ticketsRegex = /\/tickets\/(\d+)/;
  const match = hash.match(ticketsRegex);

  if (match && match[1]) {
    const urlEventId = match[1];
    const storedEventId = localStorage.getItem("hg-current-event-id");

    // If we have a mismatch between URL and localStorage, prioritize the URL
    if (storedEventId !== urlEventId) {
      console.log(
        `URL event ID ${urlEventId} doesn't match stored ID ${storedEventId}, updating storage`
      );
      localStorage.setItem("hg-current-event-id", urlEventId);
      localStorage.setItem("hg-force-ticket-reload", "true");
      return urlEventId;
    }

    return urlEventId;
  }

  // Check sessionStorage backup as a fallback
  const sessionBackupId = sessionStorage.getItem("hg-current-event-id-backup");
  if (sessionBackupId && !localStorage.getItem("hg-current-event-id")) {
    console.log(
      "Restoring event ID from sessionStorage backup:",
      sessionBackupId
    );
    localStorage.setItem("hg-current-event-id", sessionBackupId);
    localStorage.setItem("hg-force-ticket-reload", "true");
    return sessionBackupId;
  }

  return null;
};

// Initialize tickets logic using Context7 pattern - no watcher since event ID is unlikely to change

// Set up handlers for window visibility/focus events
const handleVisibilityChange = () => {
  if (
    document.visibilityState === "visible" &&
    route.name === "ticket-creation"
  ) {
    // Check how long the page has been hidden
    const lastActiveTime = localStorage.getItem("hg-last-active-time");
    const now = Date.now();

    if (lastActiveTime) {
      const timeDiff = now - parseInt(lastActiveTime);
      // Only refresh if page was hidden for more than 5 minutes
      if (timeDiff > 5 * 60 * 1000) {
        console.log(
          "Page was hidden for more than 5 minutes, refreshing tickets data"
        );
        refreshTicketsData();
      }
    }

    // Update last active time
    localStorage.setItem("hg-last-active-time", now.toString());
  }
};

// Add event listener for window unload to ensure event ID is saved to sessionStorage
// This adds an extra layer of persistence for the event ID
window.addEventListener("beforeunload", () => {
  const currentEventId = localStorage.getItem("hg-current-event-id");
  if (currentEventId) {
    // Save to sessionStorage as a backup
    sessionStorage.setItem("hg-current-event-id-backup", currentEventId);
    console.log(
      "Saved event ID to sessionStorage before unload:",
      currentEventId
    );
  }
});

onMounted(async () => {
  // Set up visibility change event listeners
  document.addEventListener("visibilitychange", handleVisibilityChange);
  window.addEventListener("focus", () => {
    if (document.visibilityState === "visible") {
      localStorage.setItem("hg-last-active-time", Date.now().toString());
    }
  });

  // Set initial active time
  localStorage.setItem("hg-last-active-time", Date.now().toString());
});

// Remove event listener when component is unmounted
onUnmounted(() => {
  document.removeEventListener("visibilitychange", handleVisibilityChange);

  // Clear temporary flags to prevent issues on next mount
  localStorage.removeItem("hg-force-ticket-reload");

  // Ensure event data is saved to the store before unmounting
  if (event.value && event.value.id) {
    console.log("Saving event data to store before unmount");
    eventStore.setEvent(event.value);
  }
});

onMounted(async () => {
  // Get event ID from all possible sources, including localStorage
  const storedEventId = localStorage.getItem("hg-current-event-id");
  const eventId =
    checkUrlHashForTicketId() ||
    route.params.eventId ||
    props.eventId ||
    event.value?.id ||
    storedEventId;

  // If we don't have an event ID at all, show error message
  if (!eventId) {
    console.error(
      "No event ID found in store, route params, props, or localStorage"
    );
    $q.dialog({
      title: "No Event Selected",
      message: "Please create or select an event before managing tickets.",
      ok: {
        color: "primary",
        label: "Go to Event Details",
      },
      persistent: true,
    }).onOk(() => {
      // Navigate back to event creation
      router.push({ name: "create-event" });
    });
    return;
  }

  console.log(`Initializing tickets page with event ID: ${eventId}`);

  // Prevent potential circular reactivity by avoiding unnecessary localStorage updates
  // Only update localStorage if the value is different
  if (localStorage.getItem("hg-current-event-id") !== eventId) {
    localStorage.setItem("hg-current-event-id", eventId);
  }

  // Check if we have a full event object in the store already
  const currentEventId = eventStore.getEventId;
  const hasCompleteEventInStore =
    event.value &&
    event.value.id === Number(eventId) &&
    event.value.tickets !== undefined;

  // Determine if we need to reload data
  const forceReload = localStorage.getItem("hg-force-ticket-reload") === "true";
  const isNewEvent = currentEventId !== Number(eventId);
  const needsRefresh =
    isEventDataStale(event.value) || forceReload || isNewEvent;

  // If we don't have a complete event or data is stale, load it
  if (!hasCompleteEventInStore || needsRefresh) {
    console.log("Loading complete event data from API");
    state.value.isEventLoading = true;
    const success = await loadEventData(eventId);

    if (!success) {
      console.error(`Failed to load event data for ID: ${eventId}`);
      state.value.isEventLoading = false;
      state.value.error = "Failed to load event data";
      return;
    }
    state.value.isEventLoading = false;
  } else {
    console.log("Using complete event data from store");
    state.value.isEventLoading = false;
  }

  // Initialize the tickets array if not already done
  if (!event.value.tickets) {
    event.value.tickets = [];
  }

  // Set default ticket payment option if not set
  if (!event.value.ticket_payment_options) {
    event.value.ticket_payment_options = "paid";
  }

  // Early bird is disabled by default (datetime_eb will be null)
  if (event.value.datetime_eb === undefined) {
    event.value.datetime_eb = null;
  }

  // Load tickets data from the tickets controller if not already loaded or if we have saved tickets
  const hasValidTickets =
    event.value.tickets &&
    Array.isArray(event.value.tickets) &&
    event.value.tickets.some((ticket) => ticket && ticket.id);

  const hasValidGroupTickets =
    event.value.ticket_groups &&
    Array.isArray(event.value.ticket_groups) &&
    event.value.ticket_groups.some(
      (group) =>
        group.packages && group.packages.some((ticket) => ticket && ticket.id)
    );

  // Only load tickets if we don't have any saved tickets yet
  // (The eventStore now automatically loads tickets, but we may need fresh data for editing)
  if (!state.value.ticketsLoaded && !hasValidTickets && !hasValidGroupTickets) {
    console.log(
      `Loading tickets for event ID: ${eventId} - no saved tickets found`
    );
    await loadTickets();
  } else if (hasValidTickets || hasValidGroupTickets) {
    console.log(
      `Event ${eventId} already has saved tickets loaded, skipping ticket reload`
    );
    state.value.ticketsLoaded = true;
  } else {
    console.log(`Loading fresh ticket data for editing event ID: ${eventId}`);
    await loadTickets();
  }

  // Listen for ticket options confirmed event
  useEventBus("ticketOptionsConfirmed").on(() => {
    event.value.confirm_ticket_options = true;
  });

  // Log state of tickets for debugging
  console.log("Tickets initialization complete:", {
    eventId: event.value?.id,
    ticketsCount: event.value?.tickets?.length || 0,
    groupsCount: event.value?.ticket_groups?.length || 0,
    ticketsLoaded: state.value.ticketsLoaded,
  });

  // Set overall loading to false once everything is ready
  state.value.isLoading = false;
});

// Watch for URL hash changes to detect events/unified#/tickets/442 format changes
watch(
  () => window.location.hash,
  async (newHash, oldHash) => {
    // Extract event ID from URL hash
    const ticketsRegex = /\/tickets\/(\d+)/;
    const match = newHash.match(ticketsRegex);

    if (match && match[1]) {
      const hashEventId = match[1];
      const currentStoredId = localStorage.getItem("hg-current-event-id");

      // Only reload if the ID from the hash is different from the current stored ID
      if (hashEventId !== currentStoredId) {
        console.log(
          `URL hash changed with new event ID: ${hashEventId}, reloading data`
        );
        localStorage.setItem("hg-current-event-id", hashEventId);
        localStorage.setItem("hg-force-ticket-reload", "true");
        await loadEventData(hashEventId);
        await loadTickets();
      }
    }
  },
  { immediate: true }
);

// Watch for route parameter changes to keep the event ID synchronized
watch(
  () => route.params.eventId,
  (newEventId) => {
    if (newEventId) {
      console.log(`Route eventId changed to: ${newEventId}`);
      localStorage.setItem("hg-current-event-id", newEventId);

      // If we have a new event ID but it's different from the current one, reload the event
      if (event.value?.id !== Number(newEventId)) {
        loadEventData(newEventId).then((success) => {
          if (success) {
            loadTickets();
          }
        });
      }
    }
  }
);

// Watch for the event object changes to ensure it's always saved to the store
/*watch(
  () => event.value,
  (newEvent) => {
    if (newEvent && newEvent.id) {
      eventStore.setEvent(newEvent);
    }
  },
  { deep: true }
);*/

// Handle ticket options update from the dialog
const handleTicketOptionsUpdate = (options) => {
  // Update event with the selected options from the dialog
  event.value.confirm_ticket_options = true;

  // If the user selects 'free' payment option, set all tickets to free
  if (event.value.ticket_payment_options === "free") {
    setTicketsToFree();
  }

  // Notify user that options have been updated
  $q.notify({
    type: "positive",
    message: "Ticket options updated",
    position: "top",
    timeout: 2000,
  });
};

// Methods
const setTicketsToFree = () => {
  event.value.ticket_payment_options = "free";
  tickets.value.forEach((ticket) => {
    ticket.cost_a = "0";
    ticket.cost_b = "0";
    if (ticket.child_tickets) {
      ticket.child_tickets.forEach((childTicket) => {
        childTicket.cost_a = "0";
        childTicket.cost_b = "0";
      });
    }
  });

  if (event.value.ticket_groups) {
    event.value.ticket_groups.forEach((group) => {
      if (group.packages) {
        group.packages.forEach((pack) => {
          pack.cost_a = "0";
          pack.cost_b = "0";
          if (pack.child_tickets) {
            pack.child_tickets.forEach((childTicket) => {
              childTicket.cost_a = "0";
              childTicket.cost_b = "0";
            });
          }
        });
      }
    });
  }
};

const reCalc = () => {
  // Emit event for fee calculator
  ticketEventBus.emit("reCalc");
};

const checkInitTicket = (ticket) => {
  if (ticket.ticket_no) {
    ticket.ticketmin = ticket.ticket_no;
  } else {
    ticket.ticketmin = 1;
  }
};

const combineDateTime = (datePart, timePart) => {
  const timeParts = timePart.split(":");
  if (datePart && timeParts) {
    return new Date(
      Date.UTC(
        datePart.getUTCFullYear(),
        datePart.getMonth(),
        datePart.getDate(),
        timeParts[0],
        timeParts[1]
      )
    );
  }
};

const addPackage = (direct = true, groupTicket = null, groupIndex = null) => {
  console.log("addPackage called with params:", {
    direct,
    groupTicket: groupTicket ? "present" : "null",
    groupIndex,
  });

  let packages;
  let group_id;

  if (direct) {
    // Ensure event.value.tickets is initialized as an array
    if (!event.value.tickets) {
      event.value.tickets = [];
    }
    packages = event.value.tickets;
  } else {
    // Ensure groupTicket.packages is initialized as an array
    if (!groupTicket.packages) {
      groupTicket.packages = [];
    }
    packages = groupTicket.packages;
    group_id = groupTicket.id;
  }

  // Check length only after ensuring packages is initialized
  if (packages.length === 100) {
    $q.dialog({
      title: "Error",
      message: "You have reached the limit of 100 tickets",
      color: "negative",
      persistent: true,
    });
    return;
  }

  // Create a new ticket with safe defaults
  const newTicket = {
    has_options: false,
    details: "",
    virtual_link: "",
    meeting_id: "",
    meeting_password: "",
    start_time: null,
    end_time: null,
    cost_a: 0.0,
    cost_b: 0.0,
    ticket_no: 100, // Default to 100 tickets available
    is_new: true,
    max_allowed: 1,
    group_ticket_id: group_id,
    group_index: groupIndex,
    group_amount: 1,
    package_options_attributes: [],
    child_tickets: [],
  };

  // Add the new ticket to the packages array
  packages.push(newTicket);

  // Force reactivity update if needed
  if (direct && event.value.tickets) {
    event.value.tickets = [...event.value.tickets];
  } else if (groupTicket && groupTicket.packages) {
    groupTicket.packages = [...groupTicket.packages];
  }

  // Log the newly created ticket for debugging
  console.log("Created new ticket:", newTicket);

  packagesSize.value += 1;
};

const addChildTicket = (parentTicket) => {
  // Ensure parentTicket is defined
  if (!parentTicket) {
    console.error("Cannot add child ticket: Parent ticket is undefined");
    $q.notify({
      type: "negative",
      message: "Cannot add child ticket: Parent ticket is undefined",
      position: "top",
    });
    return;
  }

  // Log the parent ticket ID for debugging
  console.log("Adding child ticket to parent with ID:", parentTicket.id);

  // Ensure child_tickets array is initialized
  if (!parentTicket.child_tickets) {
    parentTicket.child_tickets = [];
  }

  const childTicket = {
    has_options: false,
    details: "Addon Ticket",
    virtual_link: "",
    meeting_id: "",
    meeting_password: "",
    start_time: null,
    end_time: null,
    cost_a: 0.0,
    cost_b: 0.0,
    ticket_no: 100, // Default to 100 tickets available
    is_new: true,
    max_allowed: 1,
    child_ticket: true,
    parent_id: parentTicket.id,
    group_amount: 1,
  };

  // Add the child ticket to the parent's child_tickets array
  parentTicket.child_tickets.push(childTicket);

  // Force reactivity update to ensure UI reflects the changes
  if (event.value.tickets) {
    event.value.tickets = [...event.value.tickets];
  }

  // Log the newly created child ticket for debugging
  console.log("Created new child ticket:", childTicket);

  // Force update to trigger reactivity
  event.value.tickets = [...event.value.tickets];
};

const deleteTicket = (ticket, group = null) => {
  if (!ticket) {
    console.warn("Cannot delete undefined ticket");
    return;
  }

  if (group) {
    if (!group.packages || !Array.isArray(group.packages)) {
      console.warn("Group packages is not an array");
      return;
    }
    const index = group.packages.indexOf(ticket);
    if (index !== -1) {
      group.packages.splice(index, 1);
      packagesSize.value -= 1;
    }
  } else {
    if (!event.value.tickets || !Array.isArray(event.value.tickets)) {
      console.warn("Event tickets is not an array");
      return;
    }
    const index = event.value.tickets.indexOf(ticket);
    if (index !== -1) {
      event.value.tickets.splice(index, 1);
      packagesSize.value -= 1;
    }
  }
};

const addGroup = () => {
  const groups = event.value.ticket_groups;
  const group = {
    description: "",
    packages: [],
  };
  addPackage(false, group);
  groups.push(group);
};

const deleteGroup = (groupTicket) => {
  groupTicket.packages.forEach((ticket) => {
    ticket.ticket_group_id = undefined;
    ticket.group_index = undefined;
  });
  const index = event.value.ticket_groups.indexOf(groupTicket);
  event.value.ticket_groups.splice(index, 1);
};

const addSinglePackageToGroup = (groupTicket, groupIndex) => {
  // This is a specialized wrapper around addPackage for ticket groups
  addPackage(false, groupTicket, groupIndex);
};

const showEmpty = (tickets) => {
  // First check if tickets array exists
  if (!tickets || !Array.isArray(tickets)) {
    console.log("Tickets is not an array");
    return true;
  }

  // Check if the array is empty
  if (tickets.length === 0) {
    console.log("Tickets array is empty");
    return true;
  }

  // Check if all tickets are undefined/null (which can happen during initialization)
  const hasAnyValidTicket = tickets.some((ticket) => {
    const isValid = ticket !== undefined && ticket !== null;
    if (!isValid) {
      console.log("Found invalid ticket:", ticket);
    }
    return isValid;
  });

  if (!hasAnyValidTicket) {
    console.log("No valid tickets found");
    return true;
  }

  return false;
};

// Rest of your methods (validateTickets, saveTickets, etc.) remains the same
// Validate tickets before saving
const validateTickets = () => {
  // Reset validation state
  resetValidation();

  // Define validation rules for tickets
  const ticketValidationRules = {
    details: [rules.required],
    ticket_no: [rules.required, rules.positiveNumber],
    max_allowed: [
      rules.required,
      rules.positiveNumber,
      rules.notExceedTicketNo,
    ],
    cost_b: [
      (val) =>
        event.value.ticket_payment_options === "free" ||
        val >= 0 ||
        "Price cannot be negative",
      (val) =>
        !event.value.datetime_eb ||
        event.value.ticket_payment_options === "free" ||
        parseFloat(val) >= 0 ||
        "Regular price must be valid",
    ],
    cost_a: [
      (val) =>
        !event.value.datetime_eb ||
        event.value.ticket_payment_options === "free" ||
        parseFloat(val) >= 0 ||
        "Early bird price must be valid",
      (val) =>
        !event.value.datetime_eb ||
        event.value.ticket_payment_options === "free" ||
        parseFloat(val) <= parseFloat(event.value.cost_b || 0) ||
        "Early bird price cannot exceed regular price",
    ],
    virtual_link: [
      (val) =>
        event.value.ticket_type !== "virtual" ||
        val ||
        "Virtual link is required for virtual tickets",
      (val) => !val || /^https?:\/\/.*/.test(val) || "Must be a valid URL",
    ],
    // Simplified datetime validation for save-time validation
    datetime: [
      (val, ticket) => {
        // The val parameter will be undefined since there's no ticket.datetime field
        // We validate based on the ticket's start_time and end_time properties

        // Check if both start and end datetime are set together (or neither)
        const hasStartDateTime = ticket && ticket.start_time;
        const hasEndDateTime = ticket && ticket.end_time;

        if (hasStartDateTime && !hasEndDateTime) {
          return "End date and time are required when start date and time are set";
        }
        if (hasEndDateTime && !hasStartDateTime) {
          return "Start date and time are required when end date and time are set";
        }

        // Validate against event boundaries if both start and end are set
        if (hasStartDateTime && hasEndDateTime) {
          const ticketStart = new Date(ticket.start_time);
          const ticketEnd = new Date(ticket.end_time);

          if (isNaN(ticketStart.getTime()) || isNaN(ticketEnd.getTime())) {
            return "Invalid date/time format";
          }

          if (ticketEnd <= ticketStart) {
            return "End time must be after start time";
          }

          if (event.value.datetimefrom) {
            const eventStart = new Date(event.value.datetimefrom);
            if (ticketStart < eventStart) {
              return "Ticket start time cannot be before event start time";
            }
          }

          if (event.value.datetimeto) {
            const eventEnd = new Date(event.value.datetimeto);
            if (ticketEnd > eventEnd) {
              return "Ticket end time cannot be after event end time";
            }
          }
        }

        return true;
      },
    ],
  };

  let allTicketsValid = true;

  // Validate main tickets
  if (event.value.tickets && Array.isArray(event.value.tickets)) {
    for (const ticket of event.value.tickets) {
      if (ticket) {
        const isValid = validateTicket(ticket, ticketValidationRules);
        allTicketsValid = allTicketsValid && isValid;

        // Also validate any child tickets
        if (ticket.child_tickets && Array.isArray(ticket.child_tickets)) {
          for (const childTicket of ticket.child_tickets) {
            if (childTicket) {
              const childIsValid = validateTicket(
                childTicket,
                ticketValidationRules
              );
              allTicketsValid = allTicketsValid && childIsValid;
            }
          }
        }
      }
    }
  }

  // Validate group tickets if they exist
  if (event.value.ticket_groups && Array.isArray(event.value.ticket_groups)) {
    for (const group of event.value.ticket_groups) {
      if (group && group.packages && Array.isArray(group.packages)) {
        for (const ticket of group.packages) {
          if (ticket) {
            const isValid = validateTicket(ticket, ticketValidationRules);
            allTicketsValid = allTicketsValid && isValid;
          }
        }
      }
    }
  }

  if (!allTicketsValid) {
    $q.notify({
      type: "negative",
      message: "Please fix validation errors before saving",
      position: "top",
      timeout: 3000,
    });
    return false;
  }

  saveTickets();

  return allTicketsValid;
};

// Check if valid to save tickets - export for EventStepper
const isValidToSaveTickets = computed(() => {
  // Always return true since we'll add a default ticket if none exist
  // This makes the stepper work even when no tickets are manually added
  return !buttonDisabled.value;
});

// Update saveTickets method to use axios
const saveTickets = async () => {
  buttonDisabled.value = true;
  packagesSubmitted.value = true;
  state.value.error = null;
  state.value.savedStatus.ticketsAreSaved = false;
  state.value.isLoading = true;

  // Ensure event.tickets is initialized
  if (!event.value.tickets) {
    event.value.tickets = [];
  }

  // Ensure ticket_groups is initialized
  if (!event.value.ticket_groups) {
    event.value.ticket_groups = [];
  }

  // Count total tickets
  let ticketCount = 0;
  if (event.value.ticket_groups) {
    event.value.ticket_groups.forEach((tg) => {
      ticketCount += tg.packages?.length || 0;
    });
  }
  ticketCount += event.value.tickets?.length || 0;

  if (ticketCount === 0) {
    // Add a default ticket if there are no tickets
    addPackage(true);
    ticketCount = 1;

    // Still show message to user but don't return - continue with the save
    $q.notify({
      type: "info",
      message: "Added a default ticket for you",
      position: "top",
      timeout: 2000,
    });
  }

  event.value.step = 1.2;

  const eventDetails = Object.assign({}, event.value);

  // Create a new clean structure that matches what Rails expects
  const eventData = {
    packages_attributes: [],
    ticket_groups_attributes: [],
  };

  // Set up the eventPostable with the required structure
  eventPostable.value = {
    event: eventData,
  };

  // Validate early bird dates
  if (
    event.value.datetime_eb &&
    date.isAfter(event.value.datetime_eb, event.value.close_date)
  ) {
    $q.dialog({
      title: "Error",
      message: "Your early bird date is after the event close date.",
      color: "negative",
      persistent: true,
    });
    event.value.datetime_eb = null;
    return false;
  }

  if (
    event.value.datetime_eb &&
    date.isAfter(event.value.datetime_eb, event.value.datetimefrom)
  ) {
    $q.dialog({
      title: "Error",
      message: "Your early bird date is after the event start date.",
      color: "negative",
      persistent: true,
    });
    event.value.datetime_eb = null;
    return false;
  }

  // Virtual tickets validation
  if (event.value.ticket_type === "virtual" && !allTicketsVirtual()) {
    $q.dialog({
      title: "Error",
      message: "All tickets for a virtual location require a link.",
      color: "negative",
      persistent: true,
    });
    return false;
  }

  // Make sure tickets array exists before processing
  if (event.value.tickets && Array.isArray(event.value.tickets)) {
    // Process main tickets that are not in a group (direct packages_attributes)
    processTickets(
      event.value.tickets,
      eventPostable.value.event.packages_attributes
    );
  } else {
    event.value.tickets = [];
    console.warn("No tickets array found, creating empty array");
  }

  // Process ticket groups
  if (event.value.ticket_groups && Array.isArray(event.value.ticket_groups)) {
    event.value.ticket_groups.forEach((tg) => {
      // Skip ticket groups that don't have packages
      if (
        !tg.packages ||
        !Array.isArray(tg.packages) ||
        tg.packages.length === 0
      ) {
        return;
      }

      // Create a fresh array for packages in this group
      const groupPackagesAttributes = [];

      // Process tickets within this group
      processTickets(tg.packages, groupPackagesAttributes);

      // Only push ticket groups that actually have processed packages
      if (groupPackagesAttributes.length > 0) {
        // Add the group with its processed packages to ticket_groups_attributes
        eventPostable.value.event.ticket_groups_attributes.push({
          id: tg.id || undefined, // Only send ID if it exists
          description: tg.description || "",
          packages_attributes: groupPackagesAttributes,
        });
      }
    });
  } else {
    event.value.ticket_groups = [];
    console.warn("No ticket_groups array found, creating empty array");
  }

  // Check discount prices
  if (event.value.datetime_eb && costDiscount.value === 0) {
    $q.dialog({
      title: "Error",
      message:
        "Your package discount prices are free, this is currently not allowed.",
      color: "negative",
      persistent: true,
    });
    buttonDisabled.value = false;
    return false;
  }

  try {
    // Sanitize the data structure before sending to make sure we don't have null or undefined values
    sanitizeDataStructure(eventPostable.value);

    // Debug the structure being sent to the server
    console.log(
      "Sending to server:",
      JSON.stringify(eventPostable.value, null, 2)
    );

    // Use PUT request to the update_all endpoint for saving multiple tickets
    const response = await axios.put(
      `/event_details/${event.value.id}/tickets/update_all`,
      eventPostable.value
    );

    // Success handling - axios automatically parses JSON
    const data = response.data;

    // Update saved status
    state.value.savedStatus.ticketsAreSaved = true;
    state.value.savedStatus.lastSaved = new Date();

    $q.notify({
      type: "positive",
      message: "Tickets Saved Successfully",
      position: "top",
      timeout: 2000,
    });

    //get tickets from api and set event with those ones...
    event.value.tickets = data.tickets;
    event.value.ticket_groups = data.ticket_groups;

    eventStore.setEvent(event.value); // Use Pinia action instead of Vuex commit
    eventStore.setChargeable(data.has_paid_tickets); // Use Pinia action

    // Update localStorage timestamps to ensure fresh data
    localStorage.setItem("hg-current-event-id", event.value.id.toString());
    localStorage.setItem("hg-event-last-loaded", Date.now().toString());

    // Show the progress dialog after successful save
    showProgressDialog.value = true;

    // Return true to indicate success - useful for the stepper
    return true;
  } catch (error) {
    console.error("Error saving tickets:", error);

    state.value.savedStatus.ticketsAreSaved = false;
    state.value.error = error.message || "Failed to save tickets";

    let errorMessage = "There was a problem saving your tickets.";

    if (error.response) {
      console.error("Server response error:", error.response.data);

      // Check for specific API error messages
      if (error.response.data && error.response.data.error) {
        errorMessage = error.response.data.error;
      } else if (error.response.status === 422) {
        errorMessage = "The ticket data format was invalid. Please try again.";
      } else if (error.response.status === 500) {
        errorMessage =
          "Server error while saving tickets. Please check the format of your ticket data and try again.";
      }
    } else if (error.request) {
      errorMessage =
        "No response received from the server. Please check your network connection.";
    } else {
      errorMessage = `Error preparing ticket request: ${error.message}`;
    }

    // Show a persistent notification about the error
    $q.notify({
      type: "negative",
      message: "Failed to save tickets",
      caption: errorMessage,
      position: "top",
      timeout: 5000,
      actions: [{ label: "Dismiss", color: "white" }],
    });

    if (event.value.ticket_updated_inline) {
      $q.notify({
        type: "info",
        message:
          "The individual updates to your tickets have already been saved!",
        timeout: 0,
        actions: [{ label: "Dismiss", color: "white" }],
      });
      event.value.ticket_updated_inline = null;
    } else {
      // Show error dialog with details
      $q.dialog({
        title: "Error Saving Tickets",
        message: errorMessage,
        color: "negative",
        persistent: true,
        ok: "OK",
      });
    }

    buttonDisabled.value = false;

    // Return false to indicate failure - useful for the stepper
    return false;
  } finally {
    // Always reset loading state regardless of success or failure
    state.value.isLoading = false;
    buttonDisabled.value = false;
    packagesSubmitted.value = false;
  }
};

const processTickets = (ticketParent, parentAttributes) => {
  // Guard clause for safety
  if (!ticketParent || !Array.isArray(ticketParent)) {
    console.warn(
      "processTickets called with invalid ticketParent:",
      ticketParent
    );
    return;
  }

  ticketParent.forEach((ticket) => {
    if (!ticket.cost_a) {
      ticket.cost_a = 0.0;
    }

    if (!ticket.cost_b) {
      ticket.cost_b = 0.0;
    }

    const vatSelectedId =
      ticket.vat_rate && ticket.vat_rate.id ? parseInt(ticket.vat_rate.id) : 1;

    const child_tickets_attributes = [];
    if (ticket.child_tickets && Array.isArray(ticket.child_tickets)) {
      ticket.child_tickets.forEach((childTicket) => {
        if (!childTicket) {
          console.warn("Skipping undefined/null child ticket");
          return;
        }

        // Convert string values to appropriate types
        const childCostA = parseFloat(childTicket.cost_a || 0).toFixed(2);
        const childCostB = parseFloat(childTicket.cost_b || 0).toFixed(2);
        const childMaxAllowed = parseInt(childTicket.max_allowed || 1);
        const childTicketNo = parseInt(childTicket.ticket_no || 100);
        const childGroupAmount = parseInt(childTicket.group_amount || 1);

        child_tickets_attributes.push({
          id: childTicket.id,
          event_id: event.value.id,
          details: childTicket.details || "Addon Ticket",
          virtual_link: childTicket.virtual_link || "",
          meeting_id: childTicket.meeting_id || "",
          meeting_password: childTicket.meeting_password || "",
          start_time: childTicket.start_time || null,
          end_time: childTicket.end_time || null,
          max_allowed: childMaxAllowed,
          cost_a: childCostA,
          cost_b: childCostB,
          ticket_no: childTicketNo,
          parent_id: ticket.id, // Ensure parent_id is set correctly to this ticket's ID
          is_new: childTicket.is_new !== false,
          package_type: childTicket.package_type || "package",
          vat_rate_id: vatSelectedId,
          group_amount: childGroupAmount,
          ticket_type: childTicket.ticket_type || "single",
          details_for_all_group_members:
            !!childTicket.details_for_all_group_members,
          tickets_for_whole_group: !!childTicket.tickets_for_whole_group,
          child_ticket: true,
        });

        costFull.value += +childTicket.cost_b;
        costDiscount.value += +childTicket.cost_a;
      });
    }

    // Convert string values to appropriate types for parent ticket
    const costA = parseFloat(ticket.cost_a || 0).toFixed(2);
    const costB = parseFloat(ticket.cost_b || 0).toFixed(2);
    const maxAllowed = parseInt(ticket.max_allowed || 1);
    const ticketNo = parseInt(ticket.ticket_no || 100);
    const groupAmount = parseInt(ticket.group_amount || 1);

    // Process ticket options ensuring they have the right structure
    const package_options_attributes = processTicketOptions(ticket);

    parentAttributes.push({
      id: ticket.id,
      event_id: event.value.id,
      details:
        ticket.details ||
        (ticket.child_ticket ? "Addon Ticket" : "General Admission"),
      virtual_link: ticket.virtual_link || "",
      meeting_id: ticket.meeting_id || "",
      meeting_password: ticket.meeting_password || "",
      start_time: ticket.start_time || null,
      end_time: ticket.end_time || null,
      max_allowed: maxAllowed,
      cost_a: costA,
      cost_b: costB,
      ticket_no: ticketNo,
      is_new: ticket.is_new !== false,
      package_type: ticket.package_type || "package",
      vat_rate_id: vatSelectedId,
      group_amount: groupAmount,
      ticket_type: ticket.ticket_type || "single",
      details_for_all_group_members: !!ticket.details_for_all_group_members,
      tickets_for_whole_group: !!ticket.tickets_for_whole_group,
      package_options_attributes: package_options_attributes,
      child_tickets_attributes: child_tickets_attributes,
    });

    costFull.value += +ticket.cost_b;
    costDiscount.value += +ticket.cost_a;
  });
};

const allTicketsVirtual = () => {
  let result = true;
  tickets.value.forEach((ticket) => {
    if (!ticket.virtual_link || ticket.virtual_link === "") {
      result = false;
    }
  });
  return result;
};

const processTicketOptions = (ticket) => {
  // Initialize an empty array if no options exist
  if (!ticket.package_options_attributes && !ticket.package_options) {
    return [];
  }

  // If package_options exists but not package_options_attributes, process package_options
  if (
    !ticket.package_options_attributes &&
    ticket.package_options &&
    Array.isArray(ticket.package_options)
  ) {
    const processedOptions = [];

    ticket.package_options.forEach((po) => {
      if (!po) return;

      const option = {
        id: po.id,
        option_title: po.option_title || "",
        option_required: !!po.option_required,
        package_sub_options_attributes: [],
      };

      // Process sub-options
      if (Array.isArray(po.package_sub_options)) {
        po.package_sub_options.forEach((subOption) => {
          if (!subOption) return;

          option.package_sub_options_attributes.push({
            id: subOption.id,
            option_value: subOption.option_value || "",
            option_cost: parseFloat(subOption.option_cost || 0).toFixed(2),
          });
        });
      } else if (Array.isArray(po.package_sub_options_attributes)) {
        po.package_sub_options_attributes.forEach((subOption) => {
          if (!subOption) return;

          option.package_sub_options_attributes.push({
            id: subOption.id,
            option_value: subOption.option_value || "",
            option_cost: parseFloat(subOption.option_cost || 0).toFixed(2),
          });
        });
      }

      processedOptions.push(option);
    });

    return processedOptions;
  } else if (Array.isArray(ticket.package_options_attributes)) {
    // If package_options_attributes already exists, ensure correct format
    return ticket.package_options_attributes
      .map((po) => {
        if (!po) return null;

        const option = {
          id: po.id,
          option_title: po.option_title || "",
          option_required: !!po.option_required,
          package_sub_options_attributes: [],
        };

        if (Array.isArray(po.package_sub_options_attributes)) {
          po.package_sub_options_attributes.forEach((subOption) => {
            if (!subOption) return;

            option.package_sub_options_attributes.push({
              id: subOption.id,
              option_value: subOption.option_value || "",
              option_cost: parseFloat(subOption.option_cost || 0).toFixed(2),
            });
          });
        }

        return option;
      })
      .filter(Boolean); // Remove any null entries
  }

  return []; // Return empty array as fallback
};

// Helper function to recursively sanitize the data structure
const sanitizeDataStructure = (obj) => {
  if (!obj || typeof obj !== "object") return;

  // For arrays, filter out null/undefined values and sanitize each remaining item
  if (Array.isArray(obj)) {
    for (let i = 0; i < obj.length; i++) {
      if (obj[i] === null || obj[i] === undefined) {
        obj.splice(i, 1);
        i--;
      } else {
        sanitizeDataStructure(obj[i]);
      }
    }
    return;
  }

  // For objects, remove null/undefined properties and sanitize each remaining property
  Object.keys(obj).forEach((key) => {
    if (obj[key] === null || obj[key] === undefined) {
      delete obj[key];
    } else if (typeof obj[key] === "object") {
      sanitizeDataStructure(obj[key]);

      // If it's an empty array or object after sanitizing, remove it
      if (
        (Array.isArray(obj[key]) && obj[key].length === 0) ||
        (typeof obj[key] === "object" && Object.keys(obj[key]).length === 0)
      ) {
        delete obj[key];
      }
    } else if (key === "cost_a" || key === "cost_b" || key === "option_cost") {
      // Ensure numeric values for costs
      obj[key] = parseFloat(obj[key] || 0).toFixed(2);
    }
  });
};

// This will be used by the parent component to check if tickets can be saved
defineExpose({
  isValidToSaveTickets,
  saveTickets,
});
</script>

<template>
  <div class="ticket-creation" id="ticketcreation">
    <!-- Loading State -->
    <div v-if="state.isLoading || !isEventReady" class="loading-container">
      <div class="text-center q-pa-xl">
        <q-spinner color="primary" size="4rem" />
        <div class="text-h6 q-mt-md">Loading Event Data...</div>
        <div class="text-grey-6 q-mt-sm">
          <span v-if="state.isEventLoading">Loading event details</span>
          <span v-else-if="!state.ticketsLoaded">Loading tickets</span>
          <span v-else>Preparing interface</span>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="state.error" class="error-container">
      <q-banner rounded class="bg-negative text-white q-ma-md">
        <template v-slot:avatar>
          <q-icon name="error" color="white" />
        </template>
        {{ state.error }}
        <template v-slot:action>
          <q-btn flat color="white" label="Retry" @click="$router.go(0)" />
        </template>
      </q-banner>
    </div>

    <!-- Main Content - Only render when event is ready -->
    <div v-else-if="isEventReady">
      <div class="row justify-between items-center q-ma-sm">
        <div class="col-auto text-h5">
          <q-icon
            name="local_activity"
            color="primary"
            size="md"
            class="q-mr-sm"
          />
          Ticket Management
        </div>
        <div class="col-auto d-flex items-center">
          <SavedStatusIndicator
            :is-saved="state.savedStatus.ticketsAreSaved"
            :saving="state.isLoading"
            :saved-time="state.savedStatus.lastSaved"
            class="q-mr-md"
          />
          <q-spinner
            v-if="state.isLoading"
            color="primary"
            size="2em"
            class="q-mr-md"
          />
        </div>
      </div>

      <!-- Event Live Status Banner -->
      <div v-if="isEventLive" class="row q-ma-md">
        <q-banner rounded class="bg-negative text-white q-ma-md">
          <template v-slot:avatar>
            <q-icon name="warning" color="white" />
          </template>
          This event is live. You are not allowed to make changes to tickets
          once an event has gone live.
        </q-banner>
      </div>

      <!-- Ticket Options Buttons -->
      <div class="row q-ma-sm q-gutter-sm items-center">
        <div class="col-auto">
          <q-select
            v-model="event.ticket_payment_options"
            :options="[
              { label: 'Free Event', value: 'free' },
              { label: 'Paid Event', value: 'paid' },
              { label: 'Mixed Pricing', value: 'mixed' },
            ]"
            label="Ticket Payment Options"
            filled
            map-options
            @update:model-value="handlePaymentOptionChange"
            style="width: 240px"
            :disable="isEventLive"
          >
            <template v-slot:prepend>
              <q-icon name="payments" color="primary" />
            </template>
          </q-select>
        </div>
        <div class="col-auto">
          <q-checkbox
            v-model="ticketScheduleEnabled"
            label="Set Ticket Date And Time"
            color="primary"
            :disable="isEventLive"
          />
        </div>
        <div class="col-auto">
          <q-checkbox
            v-model="earlyBirdEnabled"
            label="Enable Early Bird Tickets"
            color="primary"
            @update:model-value="handleEarlyBirdChange"
            :disable="isEventLive"
          />
        </div>
        <div class="col-auto" v-if="earlyBirdEnabled">
          <q-input
            v-model="event.datetime_eb"
            label="Early Bird End Date"
            filled
            readonly
          >
            <template v-slot:prepend>
              <q-icon name="event" color="primary" />
            </template>
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date
                    v-model="event.datetime_eb"
                    mask="YYYY-MM-DD"
                    :options="dateOptions"
                    color="primary"
                  />
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
          <div class="text-caption text-grey-8 q-mt-xs">
            Early bird tickets will be available until this date
          </div>
        </div>
      </div>

      <!-- Payment Type Selection moved to the dialog -->

      <!-- Advanced options for attendee details -->
      <q-card class="col-12 q-mb-md attendee-options-card" v-if="advanced">
        <q-card-section class="bg-secondary text-white q-py-sm">
          <div class="text-h6">
            <q-icon name="person" class="q-mr-sm" />
            Attendee Information
          </div>
        </q-card-section>
        <q-card-section class="q-pt-md">
          <div class="row items-center q-col-gutter-md">
            <div class="col-12 col-sm-auto">
              <q-toggle
                v-model="event.show_add_attendees"
                color="primary"
                size="lg"
                label="Require attendee details when booking"
                left-label
              />
            </div>
            <div class="col text-grey-8">
              <q-icon name="info" color="info" class="q-mr-xs" />
              This option needs to be active if you want to add menus to your
              tickets.
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Tickets Section -->
      <q-card class="q-mb-md tickets-card">
        <!-- Tickets listing - only use direct rendering of ticket components -->
        <q-card-section
          v-if="
            event.tickets &&
            event.tickets.length > 0 &&
            !showEmpty(event.tickets)
          "
        >
          <div class="ticket-list">
            <div
              v-for="(ticket, etindex) in event.tickets"
              :key="etindex + 'A'"
              class="ticket-item q-mb-md"
            >
              <ticket-row
                v-if="event"
                @addChildTicket="addChildTicket"
                @deleteTicket="deleteTicket"
                :event="event"
                :ticket="ticket"
                :index="etindex + 'A'"
                :advanced="advanced"
                :freeAllowed="freeEvents"
                :is-saved="state.savedStatus.ticketsAreSaved && ticket.id"
                :showSchedule="ticketScheduleEnabled"
              >
              </ticket-row>

              <div
                v-if="
                  ticket.child_tickets && Array.isArray(ticket.child_tickets)
                "
                class="child-tickets"
              >
                <ticket-row
                  v-if="event"
                  v-for="(childTicket, innerIndex) in ticket.child_tickets"
                  :key="etindex + '_' + innerIndex + '_B'"
                  :event="event"
                  :ticket="childTicket"
                  :parentTicket="ticket"
                  :index="etindex + '_' + innerIndex + '_B'"
                  :freeAllowed="freeEvents"
                  :advanced="advanced"
                  :is-saved="
                    state.savedStatus.ticketsAreSaved && childTicket.id
                  "
                  :showSchedule="ticketScheduleEnabled"
                  class="child-ticket-row"
                >
                </ticket-row>
              </div>
            </div>
          </div>
        </q-card-section>

        <!-- Empty State -->
        <q-card-section v-else class="text-center q-pa-xl">
          <q-icon name="confirmation_number" size="4rem" color="grey-5" />
          <div class="text-h5 q-mt-md">Add Your First Ticket</div>
          <div class="q-mt-sm text-grey-8">
            Define tickets for your event to start selling
          </div>
          <q-btn
            color="primary"
            :disable="isEventLive || buttonDisabled"
            @click="addPackage"
            label="Add Ticket"
            class="q-mt-lg"
            icon="add_circle"
            unelevated
            rounded
            size="lg"
          />
        </q-card-section>
      </q-card>

      <!-- Ticket Groups Section -->
      <q-card
        class="q-mb-md ticket-groups-card"
        v-if="event.ticket_groups && event.ticket_groups.length > 0"
      >
        <q-card-section class="bg-secondary text-white q-py-sm">
          <div class="row justify-between items-center">
            <div class="text-h6">
              <q-icon name="category" class="q-mr-sm" />
              Ticket Groups
            </div>
            <q-btn
              v-if="advanced && event.confirm_ticket_options"
              flat
              round
              color="white"
              icon="add_circle"
              :disable="isEventLive"
              @click="addGroup()"
              label="Add Group"
            >
              <q-tooltip>Create a new ticket group</q-tooltip>
            </q-btn>
          </div>
        </q-card-section>

        <vue-draggable
          v-model="event.ticket_groups"
          handle=".my-handleG"
          item-key="id"
          class="q-pa-sm"
        >
          <template #item="{ element: ticketGroup, index: gIndex }">
            <q-card class="q-mb-sm ticket-group-card">
              <q-card-section class="bg-grey-3 q-py-sm">
                <div class="row items-center">
                  <div class="col-auto">
                    <q-btn flat dense icon="drag_indicator" class="my-handleG">
                      <q-tooltip>Drag to reorder groups</q-tooltip>
                    </q-btn>
                  </div>
                  <div class="col">
                    <q-input
                      v-model="ticketGroup.description"
                      outlined
                      dense
                      placeholder="Group name (e.g. VIP Tickets)"
                      class="group-name-input"
                      bg-color="white"
                    />
                  </div>
                  <div class="col-auto">
                    <q-btn
                      color="negative"
                      dense
                      round
                      :disable="isEventLive"
                      @click="deleteGroup(ticketGroup)"
                      icon="delete"
                      class="q-mr-sm"
                    >
                      <q-tooltip>Delete this ticket group</q-tooltip>
                    </q-btn>
                    <q-btn
                      color="primary"
                      dense
                      :disable="isEventLive || buttonDisabled"
                      @click="addSinglePackageToGroup(ticketGroup, gIndex)"
                      icon="add"
                      label="Add Ticket"
                      unelevated
                    />
                  </div>
                </div>
              </q-card-section>

              <q-card-section class="q-pt-none q-pb-sm">
                <!-- Direct rendering of ticket components for groups -->
                <div
                  class="ticket-group-content q-py-sm"
                  v-if="ticketGroup.packages && ticketGroup.packages.length > 0"
                >
                  <div
                    v-for="(ticket, tgpindex) in ticketGroup.packages"
                    :key="tgpindex + 'tgp'"
                    class="ticket-item q-mb-sm"
                  >
                    <ticket-row
                      v-if="event"
                      :event="event"
                      @addChildTicket="addChildTicket"
                      @deleteTicket="deleteTicket"
                      :ticket="ticket"
                      :ticketGroup="ticketGroup"
                      :index="gIndex + '_' + tgpindex + 'A'"
                      :freeAllowed="freeEvents"
                      :advanced="advanced"
                      :is-saved="state.savedStatus.ticketsAreSaved && ticket.id"
                      :showSchedule="ticketScheduleEnabled"
                    >
                    </ticket-row>

                    <!-- <div v-if="ticket.child_tickets && Array.isArray(ticket.child_tickets)" class="child-tickets"> -->
                    <ticket-row
                      v-if="event"
                      class="child-ticket-row"
                      v-for="(childTicket, cIndex) in ticket.child_tickets"
                      :key="tgpindex + '_' + cIndex + '_B'"
                      :freeAllowed="freeEvents"
                      :parentTicket="ticket"
                      :ticketGroup="ticketGroup"
                      :event="event"
                      :ticket="childTicket"
                      :index="gIndex + '_' + tgpindex + 'B'"
                      :advanced="advanced"
                      :is-saved="
                        state.savedStatus.ticketsAreSaved && childTicket.id
                      "
                      :showSchedule="ticketScheduleEnabled"
                    >
                    </ticket-row>
                    <!-- </div> -->

                    <div class="text-center q-py-md">
                      <q-btn
                        color="secondary"
                        icon="add_circle"
                        :disable="isEventLive || buttonDisabled"
                        @click="addSinglePackageToGroup(ticketGroup, gIndex)"
                        label="Add Ticket to Group"
                        unelevated
                        rounded
                        class="q-py-sm"
                      />
                    </div>
                  </div>
                </div>

                <!-- Empty group state -->
                <div v-else class="text-center q-py-lg">
                  <q-icon name="playlist_add" size="2rem" color="grey-5" />
                  <div class="text-subtitle1 q-mt-sm">
                    No tickets in this group yet
                  </div>
                  <q-btn
                    flat
                    color="primary"
                    :disable="isEventLive || buttonDisabled"
                    @click="addSinglePackageToGroup(ticketGroup, gIndex)"
                    label="Add First Ticket"
                    class="q-mt-sm"
                    icon="add"
                  />
                </div>
              </q-card-section>
            </q-card>
          </template>
        </vue-draggable>
      </q-card>

      <!-- Add More Tickets Action Bar -->
      <div class="row justify-center q-my-md" v-if="!state.isLoading">
        <q-card class="add-more-tickets-card col-12">
          <q-card-section
            class="row items-center justify-center bg-grey-2 q-py-sm"
          >
            <div class="col-12 text-center">
              <q-btn
                color="secondary"
                icon="add_circle"
                :disable="isEventLive || buttonDisabled"
                @click="addPackage()"
                label="Add More Tickets"
                unelevated
                rounded
                class="q-py-xs"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Save Actions -->
      <div class="row justify-center q-mt-md">
        <q-btn
          color="primary"
          size="lg"
          icon="save"
          :loading="state.isLoading"
          :disable="isEventLive || buttonDisabled"
          @click="validateTickets()"
          label="Save Tickets"
          unelevated
          rounded
          class="save-button q-py-sm q-px-xl"
        >
          <template v-if="state.isLoading" #loading>
            <q-spinner-dots color="white" />
            <span class="q-ml-sm">Saving...</span>
          </template>
        </q-btn>
      </div>

      <!-- Error Message Display -->
      <div v-if="state.error" class="row justify-center q-mt-md">
        <q-banner rounded class="bg-negative text-white">
          <template v-slot:avatar>
            <q-icon name="error" color="white" />
          </template>
          {{ state.error }}
          <template v-slot:action>
            <q-btn
              flat
              color="white"
              label="Dismiss"
              @click="state.error = null"
            />
          </template>
        </q-banner>
      </div>

      <!-- Event Progress Dialog -->
      <EventProgressDialog
        v-model="showProgressDialog"
        context="tickets"
        :event-id="event?.id"
      />

      <!-- Debug Panel -->
      <!-- <DebugPanel :is-visible="showDebugPanel" @close="showDebugPanel = false" /> -->
    </div>
    <!-- End of main content conditional -->
  </div>
</template>

<style lang="scss" scoped>
.no-event-container {
  max-width: 600px;
  margin: 3rem auto;
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .q-icon {
    opacity: 0.8;
  }
}

.ticket-creation {
  padding: 0;
  margin: 0 auto;
}

.payment-type-card,
.attendee-options-card,
.tickets-card,
.ticket-groups-card,
.add-more-tickets-card {
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);

  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  }
}

.my-handleG {
  cursor: move;
  cursor: -webkit-grabbing;
}

.child-ticket-row {
  background-color: rgba(0, 0, 0, 0.02);
  // border-left: 3px solid #ccc;
}

.ticket-list {
  padding: 0 8px;
}

.ticket-item {
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 8px;

  &:last-child {
    border-bottom: none;
  }
}

.child-tickets {
  // margin-left: 20px;
  // padding-left: 10px;
  // border-left: 3px solid #ccc;
  margin-top: 8px;
}

.group-name-input {
  .q-field__native {
    font-weight: 500;
  }
}

.save-button {
  transition: transform 0.2s ease;
  margin-bottom: 1rem;

  &:hover:not(:disabled) {
    transform: scale(1.05);
  }
}

.add-more-tickets-card {
  max-width: 500px;
  margin: 0 auto;
}

.refresh-btn {
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: rotate(30deg);
    transition: transform 0.3s ease;
  }
}

/* Styles for the saved status badge */
:deep(.saved-status-container) {
  opacity: 0.9;
  transition: opacity 0.3s ease;
  min-width: 120px;
}

:deep(.saved-status-container:hover) {
  opacity: 1;
}

:deep(.saved-badge) {
  animation: pulse 2s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

/* Loading and Error Containers */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background-color: #fafafa;
  border-radius: 8px;
  margin: 1rem;
}

.error-container {
  margin: 1rem;
}
</style>
