<template>
  <div v-if="event" class="actions-wrapper">
    <!-- Delete Ticket Button - Only visible when event is not live, at the top -->
    <q-btn 
      v-if="!event.live" 
      round 
      color="negative" 
      icon="delete" 
      class="action-btn q-mb-md" 
      @click="removeTicket"
    >
      <q-tooltip>{{ ticket.child_ticket ? 'Remove Sub-ticket' : 'Delete Ticket' }}</q-tooltip>
    </q-btn>
    
    <!-- Add Sub-ticket Button - Only visible when not a child ticket and event is not live, at the bottom -->
    <!-- <q-btn 
      v-if="!ticket.child_ticket && !event.live" 
      round 
      color="primary" 
      icon="add_circle" 
      class="action-btn" 
      @click="addSubTicket"
    >
      <q-tooltip>Add Sub-ticket</q-tooltip>
    </q-btn> -->
    
    <!-- Hidden components that will be triggered by other methods if needed -->
    <!-- <AdvancedTicketOptions ref="advancedOptionsRef" :advanced="advanced" :event-id="event.id" :index="index" :ticket="ticket" /> -->
    <!-- <VirtualTicketOptions v-if="event.ticket_type === 'virtual'" ref="virtualSettingsRef" :event-id="event.id" :ticket="ticket" :index="index" /> -->
  </div>
</template>

<script setup>
import { ref } from 'vue';
import EditTicket from './EditTicket.vue';
import AdvancedTicketOptions from './AdvancedTicketOptions.vue';
import VirtualTicketOptions from './VirtualTicketOptions.vue';

const props = defineProps({
  event: {
    type: Object,
    required: true
  },
  ticket: {
    type: Object,
    required: true
  },
  ticketGroup: {
    type: Object,
    default: null
  },
  parentTicket: {
    type: Object,
    default: null
  },
  index: {
    type: String,
    required: true
  },
  advanced: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['addChildTicket', 'deleteTicket', 'deleteChildTicket']);

// References to child components
const editTicketRef = ref(null);
const advancedOptionsRef = ref(null);
const virtualSettingsRef = ref(null);

// No longer needed as Show/Hide details button is removed
// const toggleDetails = () => {
//   emit('toggleDetails');
// };

// Methods to open dialog components if needed externally
const openEditTicket = () => {
  if (editTicketRef.value) {
    editTicketRef.value.$emit('click');
  }
};

const openAdvancedOptions = () => {
  if (advancedOptionsRef.value) {
    advancedOptionsRef.value.$emit('click');
  }
};

const openVirtualSettings = () => {
  if (virtualSettingsRef.value) {
    virtualSettingsRef.value.$emit('click');
  }
};

const addSubTicket = () => {
  emit('addChildTicket', props.ticket);
};

const removeTicket = () => {
  if (props.ticket.child_ticket) {
    emit('deleteChildTicket', props.ticket);
  } else {
    emit('deleteTicket', props.ticket, props.ticketGroup);
  }
};
</script>

<style lang="scss" scoped>
.actions-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  height: 100%;
  
  /* Different alignment for sidebar vs bottom layout */
  @media (min-width: 600px) { /* sm and above breakpoint in Quasar */
    align-items: center;
    justify-content: flex-start;
    padding-top: 16px;
  }
  
  /* When in a mobile layout (bottom of card), use row layout */
  .visible-xs-only & {
    flex-direction: row;
    justify-content: center;
    
    .action-btn {
      margin-bottom: 0;
      margin-right: 12px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
  
  /* Standard button styling */
  .q-btn {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%);
      transform-origin: 50% 50%;
    }
    
    &:hover {
      box-shadow: 0 3px 7px rgba(0, 0, 0, 0.2);
      transform: translateY(-2px);
      
      &:after {
        animation: ripple 0.6s ease-out;
      }
    }
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.3);
    }
    
    transition: all 0.3s ease;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.8;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}
</style>
