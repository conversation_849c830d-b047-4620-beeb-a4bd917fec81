<template>
  <div class="row mt-2">
    <div class="col-12">
      <q-btn @click="toggleCollapse" size="sm" color="primary">{{
        buttonText
      }}</q-btn>

      <div class="mt-2">
        <q-expansion-item v-model="expanded" header-class="hidden">
          <fee-calc :event="event" :vat_rate="20" />
        </q-expansion-item>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import FeeCalc from "./FeeCalc.vue";
import { useEventStore } from "@/stores/event";

// Props
const props = defineProps({
  eventId: {
    type: [String, Number],
    required: true,
  },
});

const eventStore = useEventStore();
const event = computed(() => eventStore.getEvent);

// Ensure event is loaded when component mounts
onMounted(async () => {
  await eventStore.ensureEventLoaded(props.eventId);
});

// Reactive state
const expanded = ref(false);
const isCollapsed = computed(() => !expanded.value);
const buttonText = computed(() =>
  isCollapsed.value ? "Show Fee Calculator" : "Hide Fee Calculator"
);

// Methods
const toggleCollapse = () => {
  expanded.value = !expanded.value;
};
</script>
