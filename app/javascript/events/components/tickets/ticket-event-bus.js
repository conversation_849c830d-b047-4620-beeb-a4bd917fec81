// Vue 3 compatible event bus implementation
import mitt from 'mitt';
// Import bootstrap compatibility utilities if needed
import { bootstrapUtilityClasses } from '../../utils/bootstrap-compat';

const emitter = mitt();

// Create a composable function to access the event bus throughout the application
export function useTicketEventBus() {
  return {
    // Emit an event with optional data
    emit: (event, data) => emitter.emit(event, data),
    
    // Subscribe to an event
    on: (event, callback) => emitter.on(event, callback),
    
    // Unsubscribe from an event
    off: (event, callback) => emitter.off(event, callback)
  };
}

// Export the event bus instance for direct use if needed
export default {
  emit: emitter.emit,
  on: emitter.on,
  off: emitter.off
};