<template>
  <div v-if="ticket">
    <q-btn
      v-if="!hasMenu"
      size="sm"
      color="secondary"
      @click="showModal"
      class="items-center"
      icon="restaurant_menu"
      label="Create Menu"
    >
      <q-tooltip>Create a menu for dinners etc</q-tooltip>
    </q-btn>

    <q-btn
      v-if="hasMenu"
      round
      color="positive" 
      icon="restaurant_menu" 
      @click="showModal"
    >
      <q-tooltip anchor="top middle" self="bottom middle">
        <div class="text-subtitle1 text-center">Menu</div>
        <div v-for="(pOption, index) in packageOptions" :key="index" class="q-pa-md q-my-sm text-center bg-grey-2 rounded-borders">
          <div class="text-subtitle1 text-weight-bold">{{ pOption.title }}</div>
          <div v-for="(title, idx) in pOption.package_sub_options" :key="idx">
            <div v-if="title.title" class="q-mt-sm text-subtitle2">{{ title.title }}</div>
          </div>
        </div>
      </q-tooltip>
    </q-btn>

    <q-dialog v-model="modalVisible" full-width>
      <q-card>
        <q-card-section class="row items-center">
          <div class="text-h6">Build Your Menu</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pa-md">
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-4">
              <div id="sortable-container">
                <div v-for="(pOption, index) in packageOptions" :title="pOption.title" :key="index" class="q-mb-lg">
                  <q-input 
                    v-model="pOption.title" 
                    label="Section Title"
                    outlined
                  >
                    <template v-slot:append>
                      <q-btn 
                        round 
                        flat 
                        dense 
                        color="negative" 
                        icon="delete" 
                        @click="removeOption(pOption)" 
                      />
                    </template>
                  </q-input>

                  <div 
                    v-for="(item, itemIndex) in pOption.package_sub_options" 
                    :key="itemIndex" 
                    class="q-mt-sm"
                  >
                    <q-input 
                      v-model="item.title" 
                      label="Item Title"
                      outlined
                      dense
                      @keyup.enter="handleEnter(pOption)"
                      ref="itemInput"
                    >
                      <template v-slot:append>
                        <q-btn 
                          round 
                          flat 
                          dense 
                          color="negative" 
                          icon="close" 
                          @click="removeSubOption(pOption, item)" 
                        />
                      </template>
                    </q-input>
                  </div>

                  <div class="q-mt-md">
                    <q-btn 
                      color="primary" 
                      label="Add Item" 
                      icon-right="add" 
                      class="full-width" 
                      @click="addSubOption(null, pOption)"
                    />
                  </div>
                </div>
              </div>
              
              <q-btn 
                color="secondary" 
                label="Add another section" 
                class="q-mt-md q-mb-lg" 
                @click="addTicketOption()"
              />
            </div>
            
            <div class="col-12 col-md-8">
              <div 
                v-for="(pOption2, index) in packageOptions" 
                :key="index" 
                class="text-center"
              >
                <q-card class="q-mb-md">
                  <q-card-section>
                    <div class="text-h6">{{ pOption2.title || '[Please add details]' }}</div>
                    <div v-for="(title, titleIndex) in pOption2.package_sub_options" :key="titleIndex">
                      <div v-if="title.title" class="q-mt-md text-subtitle2">{{ title.title }}</div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="bg-white">
          <q-btn flat label="Cancel" color="secondary" v-close-popup />
          <q-btn flat label="Save" color="primary" @click="saveAndClose" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useQuasar } from 'quasar'

const props = defineProps({
  event: Object,
  ticket: Object,
  modalkey: [String, Number]
})

const emit = defineEmits(['saveOptions'])
const $q = useQuasar()

const modalVisible = ref(false)
const optionsLength = ref(0)
const packageOptions = ref([])
const choiceSubmitted = ref(false)
const hasMenu = ref(false)
const itemInput = ref(null)

const showModal = () => {
  modalVisible.value = true
  onShow()
}

onMounted(() => {
  if (props.ticket.package_options && props.ticket.package_options.length > 0) {
    hasMenu.value = true
  } else {
    hasMenu.value = false
  }
})

const onShow = () => {
  if (props.ticket.package_options) {
    packageOptions.value = JSON.parse(JSON.stringify(props.ticket.package_options))
  }
  if (packageOptions.value.length === 0) {
    addTicketOption()
  }
}

const onCancel = () => {
  packageOptions.value = []
  modalVisible.value = false
}

const addTicketOption = () => {
  const newSubOption = {
    title: null
  }
  const newOption = {
    title: null,
    package_sub_options: [newSubOption]
  }

  if (packageOptions.value.length === 5) {
    $q.notify({
      type: 'negative',
      message: 'Only Five options allowed'
    })
    return false
  }

  packageOptions.value.push(newOption)
}

const handleEnter = (section) => {
  addSubOption(null, section)
}

const addSubOption = (event, option) => {
  let valid = true

  option.package_sub_options.forEach(function(sub) {
    if (sub.title == null) {
      $q.notify({
        type: 'negative',
        message: 'You cannot delete the last item, please delete the enclosing option'
      })
      valid = false
    }
  })

  if (!valid) return

  const subOption = {
    title: null,
    description: null
  }

  option.package_sub_options.push(subOption)
  
  // Focus on the newly added input field
  nextTick(() => {
    if (itemInput.value && itemInput.value.length) {
      const lastInput = itemInput.value[itemInput.value.length - 1]
      if (lastInput) {
        lastInput.focus()
      }
    }
  })
}

const removeOption = (option) => {
  const index = packageOptions.value.indexOf(option)
  packageOptions.value.splice(index, 1)

  if (packageOptions.value.length === 0) {
    props.ticket.hasOptions = false
    addTicketOption()
  }
}

const removeSubOption = (option, subOption) => {
  if (option.package_sub_options.length < 2) {
    $q.notify({
      type: 'negative',
      message: 'You cannot delete the last item, please delete the enclosing option'
    })
    return false
  }

  const index = option.package_sub_options.indexOf(subOption)
  option.package_sub_options.splice(index, 1)
}

const saveAndClose = () => {
  let validOptions = true
  choiceSubmitted.value = true
  let clearedOptions = false

  packageOptions.value.forEach(function(option) {
    if (!option.title) {
      if (packageOptions.value.length === 1) {
        if (!option.title) {
          if (
            option.package_sub_options.length === 1 &&
            !option.package_sub_options[0].title
          ) {
            clearedOptions = true
          } else {
            $q.notify({
              type: 'negative',
              message: 'Please ensure you add an option title'
            })
            validOptions = false
            choiceSubmitted.value = false
          }
        }
      } else {
        $q.notify({
          type: 'negative',
          message: 'Please ensure you add an option title'
        })
        validOptions = false
        choiceSubmitted.value = false
      }
    } else {
      option.package_sub_options.forEach(function(subOpt) {
        if (!subOpt.title) {
          $q.notify({
            type: 'negative',
            message: 'Please ensure you add items'
          })
          validOptions = false
          choiceSubmitted.value = false
        }
      })
    }
  })

  if (!validOptions) return

  if (!clearedOptions) {
    props.ticket.package_options = packageOptions.value

    const packOptions = packageOptions.value.filter(function(option) {
      return option.title
    })

    packOptions.forEach(function(option) {
      option.package_sub_options_attributes = option.package_sub_options
    })

    props.ticket.package_options_attributes = packOptions
    $q.notify({
      type: 'positive',
      message: 'Menu options updated successfully'
    })
    hasMenu.value = true
  } else {
    props.ticket.package_options = null
    props.ticket.package_options_attributes = null
    $q.notify({
      type: 'positive',
      message: 'Menu options removed successfully'
    })
    hasMenu.value = false
  }

  emit("saveOptions", props.ticket)
  modalVisible.value = false
}
</script>
