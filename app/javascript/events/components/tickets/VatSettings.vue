<template>
  <div>
    <q-btn
      color="secondary"
      label="VAT Settings"
      size="sm"
      @click="showModal = true"
    />

    <q-dialog
      v-model="showModal"
      persistent
    >
      <q-card style="width: 550px">
        <q-card-section class="row items-center">
          <div class="text-h6">VAT Settings</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup @click="hideModal" />
        </q-card-section>

        <q-card-section>
          <div class="bg-blue-1 q-pa-md rounded-borders q-mb-md">
            <div class="text-subtitle1 text-weight-medium">
              These VAT Details Are Global And Apply To
              <strong>ALL Events</strong> For Your Business
            </div>
          </div>

          <div class="q-mb-md">
            <div class="text-caption text-weight-medium required">VAT Number:</div>
            <div class="row">
              <q-input
                square
                outlined
                dense
                disable
                class="col-auto"
                value="GB"
                style="width: 50px; border-top-right-radius: 0; border-bottom-right-radius: 0"
              />
              <q-input
                v-model="vatNumber"
                outlined
                dense
                placeholder="VAT Number (no spaces)"
                maxlength="9"
                class="col"
                style="border-top-left-radius: 0; border-bottom-left-radius: 0"
              />
            </div>
          </div>
          
          <div class="q-mb-md">
            <q-input
              v-model="companyName"
              outlined
              dense
              label="Company Name (will appear on invoice)"
              placeholder="Company Name"
            />
          </div>
          
          <div class="q-mb-md">
            <q-input
              v-model="companyNumber"
              outlined
              dense
              label="Company Number"
              placeholder="Company Number"
              maxlength="20"
              class="required"
            />
          </div>
          
          <div class="q-mb-md">
            <q-input
              v-model="companyAddress"
              outlined
              type="textarea"
              label="Company Address"
              rows="4"
            />
          </div>
        </q-card-section>

        <q-card-actions align="right" class="bg-white q-pa-md">
          <q-btn 
            color="primary" 
            icon="save"
            label="Save VAT" 
            @click="saveVat" 
          />
          <q-btn 
            v-if="showDelete" 
            color="negative" 
            icon="delete" 
            label="Remove VAT Details" 
            @click="removeVat(vatNumber)" 
          />
          <q-btn flat label="Cancel" @click="hideModal" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
})

const $q = useQuasar()
const showModal = ref(false)
const vatNumber = ref(null)
const companyNumber = ref(null)
const companyName = ref(null)
const companyAddress = ref(null)
const showDelete = ref(false)

// Using mitt or another event bus implementation for Vue 3
// You'll need to create/import your own event bus
const eventBus = inject('eventBus')

onMounted(() => {
  eventBus.on("noVatSettings", () => {
    if (!vatNumber.value) {
      showModal.value = true
    } else {
      props.event.vat_number = vatNumber.value
    }
  })

  fetchVatSettings()
})

const fetchVatSettings = () => {
  axios.get(`/vat?oid=${props.event.organisation_id}`)
    .then(response => {
      vatNumber.value = response.data.vat_number
      companyNumber.value = response.data.company_number
      companyName.value = response.data.company_name
      companyAddress.value = response.data.company_address
      
      if (vatNumber.value) {
        showDelete.value = true
      }
    })
    .catch(() => {
      $q.notify({
        type: 'negative',
        message: 'Could Not Retrieve VAT Settings',
        position: 'top'
      })
    })
}

const hideModal = () => {
  showModal.value = false
}

const saveVat = () => {
  if (!vatNumber.value || !companyNumber.value) {
    $q.notify({
      type: 'warning',
      message: 'Please add a VAT number and a company number',
      caption: 'Please Fix',
      position: 'top'
    })
    return false
  }

  axios.post('/vat', {
    vat_number: vatNumber.value,
    company_name: companyName.value,
    company_number: companyNumber.value,
    company_address: companyAddress.value
  })
  .then(() => {
    showDelete.value = true
    $q.notify({
      type: 'positive',
      message: 'VAT Number Saved',
      position: 'top'
    })
    props.event.vat_number = vatNumber.value
    showModal.value = false
  })
  .catch(error => {
    $q.notify({
      type: 'negative',
      message: error.response?.data?.errors || 'Error saving VAT settings',
      position: 'top'
    })
  })
}

const removeVat = () => {
  axios.delete(`/vat/${vatNumber.value}.json`)
    .then(() => {
      showDelete.value = false
      vatNumber.value = undefined
      companyNumber.value = undefined
      companyName.value = undefined
      companyAddress.value = undefined
      $q.notify({
        type: 'positive',
        message: 'VAT Number Deleted',
        position: 'top'
      })
    })
    .catch(error => {
      $q.notify({
        type: 'negative',
        message: error.response?.data?.errors || 'Error removing VAT settings',
        position: 'top'
      })
    })
}
</script>

<style scoped>
.required:after {
  content: " *";
  color: red;
}
</style>
