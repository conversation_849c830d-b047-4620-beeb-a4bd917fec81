<template>
  <div>
    <q-btn
      v-show="!ticket.child_ticket"
      flat
      dense
      :color="hasAnySettings ? 'primary' : 'secondary'"
      icon="settings"
      label="Settings"
      size="sm"
      class="q-px-sm"
      @click="dialogVisible = true"
      data-cy="ticketSettingsButton"
    >
      <q-tooltip>Configure ticket settings (Advanced & Timing)</q-tooltip>
      <q-badge v-if="hasAnySettings" floating :color="hasTimes && hasAdvancedSettings ? 'deep-orange' : (hasTimes ? 'positive' : 'accent')" text-color="white">
        <q-icon name="check" size="xs" />
      </q-badge>
    </q-btn>
    
    <q-dialog v-model="dialogVisible" persistent>
      <q-card style="width: 700px; max-width: 95vw" class="settings-dialog" data-cy="ticketSettingsDialog">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">
            <q-icon name="settings" class="q-mr-sm" />
            Ticket Settings
          </div>
        </q-card-section>

        <q-tabs
          v-model="tab"
          dense
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="justify"
          narrow-indicator
        >
          <q-tab name="advanced" label="Advanced Options" icon="tune" />
          <q-tab name="timing" label="Time Settings" icon="schedule" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated>
          <q-tab-panel name="advanced" class="q-pa-md">
            <div class="row q-col-gutter-md">
              <!-- Attendees per Ticket moved to TicketRow component -->
              
              <!-- Sub-ticket button moved to TicketRow.vue -->

              <div v-if="!ticket.child_ticket && !event.live && event.show_add_attendees" class="col-12 col-sm-6">
                <q-card flat bordered class="option-card">
                  <q-card-section class="q-pb-none">
                    <div class="text-subtitle1 text-weight-medium">
                      <q-icon name="restaurant_menu" color="primary" size="sm" class="q-mr-xs" />
                      Menu Builder
                    </div>
                    <div class="text-caption text-grey-7">
                      Add menu options for attendees
                    </div>
                  </q-card-section>
                  
                  <q-card-section class="q-pt-sm">
                    <MenuBuilder :event="props.event" :ticket="ticket" :modalkey="index" />
                  </q-card-section>
                </q-card>
              </div>
              
              <div v-if="ticket.group_amount > 1 && !ticket.child_ticket" class="col-12 col-sm-6">
                <q-card flat bordered class="option-card">
                  <q-card-section class="q-pb-none">
                    <div class="text-subtitle1 text-weight-medium">
                      <q-icon name="group_work" color="primary" size="sm" class="q-mr-xs" />
                      Group Settings
                    </div>
                    <div class="text-caption text-grey-7">
                      Configure how attendee information is collected for groups
                      <q-badge outline color="blue" class="q-ml-sm">Attendees: {{ ticket.group_amount }}</q-badge>
                    </div>
                  </q-card-section>
                  
                  <q-card-section class="q-pt-sm">
                    <q-toggle
                      v-model="ticket.details_for_all_group_members"
                      :label="ticket.details_for_all_group_members ? 'Collect all attendee details' : 'Single contact only'"
                      color="primary"
                      class="q-mb-md"
                    />
                    
                    <q-toggle
                      v-if="ticket.details_for_all_group_members"
                      v-model="ticket.tickets_for_whole_group"
                      :label="ticket.tickets_for_whole_group ? 'Individual tickets' : 'Combined ticket'"
                      color="primary"
                    />
                  </q-card-section>
                </q-card>
              </div>

              <!-- Ticket Description moved to TicketRow.vue -->
            </div>
          </q-tab-panel>

          <q-tab-panel name="timing" class="q-pa-md">
            <p class="text-grey-8 q-mb-md">
              <q-icon name="info" color="info" /> 
              Set specific times for this ticket, which can differ from the main event time.
            </p>
            
            <div class="row q-col-gutter-md">
              <div class="col-12 col-sm-6">
                <q-card flat bordered class="option-card q-mb-md">
                  <q-card-section class="q-pb-none">
                    <div class="text-subtitle1 text-weight-medium">
                      <q-icon name="calendar_month" color="primary" size="sm" class="q-mr-xs" />
                      Date Range
                    </div>
                  </q-card-section>
                  
                  <q-card-section class="q-pt-sm">
                    <div class="q-mb-md">
                      <label for="startDate" class="text-weight-medium q-mb-sm block">Start Date</label>
                      <VueDatePicker
                        v-model="localStartDate"
                        :class="{ 'field-error': timeValidationErrors.startDate }"
                        model-type="format"
                        format="YYYY-MM-DD"
                        input-class-name="date-picker modern-datepicker"
                        :enable-time-picker="false"
                        :min-date="eventStartDate"
                        :max-date="eventEndDate"
                        @blur="markFieldAsTouched('startDate')"
                        @update:model-value="handleStartDateChange"
                        teleport
                      />
                      <div v-if="shouldShowError('startDate') && timeValidationErrors.startDate" class="text-negative text-caption q-mt-xs">
                        <q-icon name="warning" size="xs" /> Invalid start date
                      </div>
                    </div>
                    
                    <div>
                      <label for="endDate" class="text-weight-medium q-mb-sm block">End Date</label>
                      <VueDatePicker
                        v-model="localEndDate"
                        :class="{ 'field-error': timeValidationErrors.endDate }"
                        model-type="format"
                        format="YYYY-MM-DD"
                        input-class-name="date-picker modern-datepicker"
                        :enable-time-picker="false"
                        :disabled="!localStartDate"
                        :min-date="localStartDate"
                        :max-date="eventEndDate"
                        @blur="markFieldAsTouched('endDate')"
                        teleport
                      />
                      <div v-if="shouldShowError('endDate') && timeValidationErrors.endDate" class="text-negative text-caption q-mt-xs">
                        <q-icon name="warning" size="xs" /> Invalid end date
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              
              <div class="col-12 col-sm-6">
                <q-card flat bordered class="option-card q-mb-md">
                  <q-card-section class="q-pb-none">
                    <div class="text-subtitle1 text-weight-medium">
                      <q-icon name="schedule" color="primary" size="sm" class="q-mr-xs" />
                      Time Range
                    </div>
                  </q-card-section>
                  
                  <q-card-section class="q-pt-sm">
                    <div class="q-mb-md">
                      <label for="startTime" class="text-weight-medium q-mb-sm block">Start Time</label>
                      <VueDatePicker
                        v-model="localStartTime"
                        time-picker
                        model-type="format"
                        format="HH:mm"
                        input-class-name="modern-datepicker"
                        :disable-date="true"
                        :is24="true"
                        :min-time="startTimeMinConstraint"
                        :max-time="eventEndTime"
                        @blur="markFieldAsTouched('startTime')"
                        @update:model-value="handleStartTimeChange"
                        teleport
                      />
                    </div>
                    
                    <div>
                      <label for="endTime" class="text-weight-medium q-mb-sm block">End Time</label>
                      <VueDatePicker
                        v-model="localEndTime"
                        time-picker
                        model-type="format"
                        format="HH:mm"
                        input-class-name="modern-datepicker"
                        :disable-date="true"
                        :is24="true"
                        :min-time="endTimeMinConstraint"
                        :max-time="endTimeMaxConstraint"
                        @blur="markFieldAsTouched('endTime')"
                        teleport
                      />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
            
            <div class="row justify-center q-my-md">
              <div class="col-12 text-center" v-if="hasTimes">
                <q-banner class="bg-green-1 text-dark rounded-borders">
                  <template v-slot:avatar>
                    <q-icon name="event_available" color="positive" />
                  </template>
                  <div class="text-weight-medium">Current Time Settings</div>
                  <div>
                    {{ formattedTimeSettings }}
                  </div>
                </q-banner>
              </div>
            </div>
            
            <div class="row justify-center q-mt-lg q-col-gutter-md">
              <div class="col-auto" v-if="!event.live">
                <q-btn 
                  color="primary" 
                  :label="hasTimes ? 'Update Times' : 'Set Times'" 
                  icon="save"
                  unelevated
                  rounded
                  @click="applyTimes"
                />
              </div>
              
              <div class="col-auto" v-if="!event.live && hasTimes">
                <q-btn 
                  color="negative" 
                  label="Remove Times" 
                  icon="delete"
                  outline
                  rounded
                  @click="removeTimes"
                />
              </div>
            </div>
            
            <q-card-section v-if="event.live" class="q-pa-md q-mt-md bg-yellow-2 rounded-borders">
              <div class="row items-center">
                <div class="col-auto q-mr-md">
                  <q-icon name="warning" color="orange" size="md" />
                </div>
                <div class="col">
                  <div class="text-weight-medium">Event is Live</div>
                  <p class="q-ma-none text-caption">
                    Time settings cannot be changed for a live event.
                  </p>
                </div>
              </div>
            </q-card-section>
          </q-tab-panel>
        </q-tab-panels>
        
        <q-card-actions align="right" class="bg-grey-2 q-pa-md">
          <q-btn flat label="Done" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
import MenuBuilder from './MenuBuilder.vue'
import dayjs from 'dayjs'
import VueDatePicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'

const props = defineProps({
  advanced: {
    type: Boolean,
    default: false
  },
  event: {
    type: Object,
    required: true
  },
  index: {
    type: [String, Number],
    required: true
  },
  ticket: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([])

const dialogVisible = ref(false)
const tab = ref('advanced')

const hasAdvancedSettings = computed(() => {
  return (
    // group_amount and description are now handled in TicketRow.vue
    (props.ticket.menus && props.ticket.menus.length > 0) ||
    props.ticket.details_for_all_group_members ||
    props.ticket.tickets_for_whole_group
  )
})

// addChildTicket method removed as functionality has moved to TicketRow.vue

// preventZero function moved to TicketRow.vue

const hasTimes = ref(false)
const localStartDate = ref(null)
const localStartTime = ref(null)
const localEndDate = ref(null)
const localEndTime = ref(null)
const formSubmitted = ref(false)
const touchedFields = reactive({})

const timeValidationErrors = reactive({
  startDate: false,
  endDate: false
})

const formattedTimeSettings = computed(() => {
  const startTimeStr = props.ticket.start_time ? 
    dayjs(props.ticket.start_time).format('ddd, D MMM YYYY [at] HH:mm') : '';
    
  if (!props.ticket.end_time) {
    return startTimeStr;
  }
  
  const endTimeStr = dayjs(props.ticket.end_time).format('ddd, D MMM YYYY [at] HH:mm');
  
  if (dayjs(props.ticket.start_time).isSame(props.ticket.end_time, 'day')) {
    return `${startTimeStr} - ${dayjs(props.ticket.end_time).format('HH:mm')}`;
  }
  
  return `${startTimeStr} to ${endTimeStr}`;
});

// Date constraints for datepickers
const eventStartDate = computed(() => {
  return props.event.datetimefrom ? dayjs(props.event.datetimefrom).format('YYYY-MM-DD') : null;
});

const eventEndDate = computed(() => {
  return props.event.datetimeto ? dayjs(props.event.datetimeto).format('YYYY-MM-DD') : null;
});

const eventStartTime = computed(() => {
  return props.event.datetimefrom ? dayjs(props.event.datetimefrom).format('HH:mm') : null;
});

const eventEndTime = computed(() => {
  return props.event.datetimeto ? dayjs(props.event.datetimeto).format('HH:mm') : null;
});

// Dynamic min/max time constraints for the timepickers
const startTimeMinConstraint = computed(() => {
  if (!localStartDate.value || !eventStartDate.value) return null;
  // If start date is the same as event start date, constrain by event start time
  return localStartDate.value === eventStartDate.value ? eventStartTime.value : null;
});

const endTimeMaxConstraint = computed(() => {
  if (!localEndDate.value || !eventEndDate.value) return null;
  // If end date is the same as event end date, constrain by event end time
  return localEndDate.value === eventEndDate.value ? eventEndTime.value : null;
});

const endTimeMinConstraint = computed(() => {
  // If start and end date are the same, end time must be after start time
  if (localStartDate.value && localEndDate.value && localStartTime.value && 
      localStartDate.value === localEndDate.value) {
    return localStartTime.value;
  }
  return null;
});

// Event handler for when start date/time changes to update end date if needed
const handleStartDateChange = () => {
  if (localStartDate.value && localEndDate.value) {
    // If end date is now before start date, update it to start date
    if (dayjs(localEndDate.value).isBefore(localStartDate.value)) {
      localEndDate.value = localStartDate.value;
    }
  }
  markFieldAsTouched('startDate');
}

const handleStartTimeChange = () => {
  if (localStartDate.value && localEndDate.value && localStartDate.value === localEndDate.value) {
    // If on same day and end time exists but is before start time, update end time
    if (localEndTime.value && localStartTime.value > localEndTime.value) {
      localEndTime.value = localStartTime.value;
    }
  }
  markFieldAsTouched('startTime');
}

const markFieldAsTouched = (fieldName) => {
  touchedFields[fieldName] = true
  validateTimeFields()
}

const shouldShowError = (fieldName) => {
  return (formSubmitted.value || touchedFields[fieldName]) && timeValidationErrors[fieldName]
}

const validateTimeFields = () => {
  timeValidationErrors.startDate = localStartDate.value !== null &&
    (dayjs(localStartDate.value).isBefore(props.event.datetimefrom, 'day') ||
     dayjs(localStartDate.value).isAfter(props.event.datetimeto, 'day'));

  timeValidationErrors.endDate = localEndDate.value !== null &&
    ((localStartDate.value === null && localEndDate.value !== null) || 
     dayjs(localEndDate.value).isBefore(props.event.datetimefrom, 'day') ||
     dayjs(localEndDate.value).isAfter(props.event.datetimeto, 'day') ||
     (localStartDate.value && dayjs(localEndDate.value).isBefore(localStartDate.value, 'day')));
  
  return !timeValidationErrors.startDate && !timeValidationErrors.endDate;
}

const applyTimes = () => {
  formSubmitted.value = true;
  if (validateTimeFields()) {
    props.ticket.start_time = combineDateTime(localStartDate.value, localStartTime.value)
    props.ticket.end_time = combineDateTime(localEndDate.value, localEndTime.value)
    
    if (localStartDate.value && !localEndDate.value && !localEndTime.value) {
        props.ticket.end_time = null;
    } else if (localStartDate.value && localEndDate.value && !localEndTime.value && localStartTime.value) {
       if (dayjs(localStartDate.value).isSame(localEndDate.value, 'day')) {
         props.ticket.end_time = combineDateTime(localEndDate.value, localStartTime.value);
       } else {
         props.ticket.end_time = combineDateTime(localEndDate.value, '00:00');
       }
    }

    updateHasTimes();
  }
}

const removeTimes = () => {
  props.ticket.start_time = null
  props.ticket.end_time = null
  localStartDate.value = null
  localStartTime.value = null
  localEndDate.value = null
  localEndTime.value = null
  updateHasTimes()
  formSubmitted.value = false
  Object.keys(touchedFields).forEach(key => touchedFields[key] = false)
  Object.keys(timeValidationErrors).forEach(key => timeValidationErrors[key] = false)
}

const combineDateTime = (datePart, timePart) => {
  if (!datePart) return null;
  const effectiveTimePart = timePart || '00:00';
  const timeParts = effectiveTimePart.split(':');
  
  let dateObj = dayjs(datePart);

  return dateObj.hours(parseInt(timeParts[0], 10)).minutes(parseInt(timeParts[1], 10)).seconds(0).toDate();
}

const updateHasTimes = () => {
  hasTimes.value = !!props.ticket.start_time;
}

watch(() => props.ticket, (newTicket) => {
  if (newTicket) {
    localStartDate.value = newTicket.start_time ? dayjs(newTicket.start_time).format('YYYY-MM-DD') : null;
    localStartTime.value = newTicket.start_time ? dayjs(newTicket.start_time).format('HH:mm') : null;
    localEndDate.value = newTicket.end_time ? dayjs(newTicket.end_time).format('YYYY-MM-DD') : null;
    localEndTime.value = newTicket.end_time ? dayjs(newTicket.end_time).format('HH:mm') : null;
    updateHasTimes();
  }
}, { immediate: true, deep: true });

const hasAnySettings = computed(() => {
  return hasAdvancedSettings.value || hasTimes.value;
});

onMounted(() => {
  if (props.ticket.start_time) {
    localStartDate.value = dayjs(props.ticket.start_time).format('YYYY-MM-DD')
    localStartTime.value = dayjs(props.ticket.start_time).format('HH:mm')
  }
  if (props.ticket.end_time) {
    localEndDate.value = dayjs(props.ticket.end_time).format('YYYY-MM-DD')
    localEndTime.value = dayjs(props.ticket.end_time).format('HH:mm')
  }
  updateHasTimes();
  formSubmitted.value = false;
  Object.keys(touchedFields).forEach(key => touchedFields[key] = false);
  Object.keys(timeValidationErrors).forEach(key => timeValidationErrors[key] = false);
})
</script>

<style lang="scss" scoped>
.settings-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.option-card {
  height: 100%;
  border-radius: 4px;
  transition: box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  
  &:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  }
  .q-card__section--main {
    flex-grow: 1;
  }
}

.date-picker {
  width: 100%;
  height: 38px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 12px;
  box-sizing: border-box;
}

.field-error .date-picker,
.field-error.date-picker {
  border-color: var(--q-negative) !important;
}

.modern-datepicker {
  font-size: 14px;
  height: 40px;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
  padding: 0 12px;
  box-sizing: border-box;
  
  &:focus {
    border-color: var(--q-primary);
  }
}
.field-error .modern-datepicker {
   border-color: var(--q-negative) !important;
}

.block {
  display: block;
}

.q-tab-panel {
  padding-top: 16px;
}

.q-banner {
  border-radius: 4px;
}
</style>