<template>
  <div class="saved-status-container" :class="{ 'saved': isSaved }">
    <q-badge v-if="isSaved" color="positive" class="saved-badge">
      <q-icon name="check_circle" size="xs" class="q-mr-xs" />
      Saved {{ timeAgo }}
    </q-badge>
    <q-badge v-else color="grey-7" class="unsaved-badge">
      <q-icon name="sync" size="xs" class="q-mr-xs" :class="{ 'rotating': saving }" />
      {{ saving ? 'Saving...' : 'Unsaved changes' }}
    </q-badge>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { date } from 'quasar';

const props = defineProps({
  isSaved: {
    type: Boolean,
    default: false
  },
  saving: {
    type: Boolean,
    default: false
  },
  savedTime: {
    type: Date,
    default: null
  }
});

// Calculate human-readable time ago
const timeAgo = computed(() => {
  if (!props.savedTime) return '';
  
  const now = new Date();
  const diff = now.getTime() - props.savedTime.getTime();
  
  // Less than a minute ago
  if (diff < 60000) {
    return 'just now';
  }
  
  // Less than an hour ago
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  }
  
  // More than an hour ago
  return date.formatDate(props.savedTime, 'h:mm A');
});
</script>

<style scoped>
.saved-status-container {
  display: inline-flex;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  padding: 1px;
  border-radius: 12px;
}

.saved-badge, .unsaved-badge {
  font-size: 12px;
  padding: 4px 10px;
  display: flex;
  align-items: center;
  border-radius: 12px;
  font-weight: 500;
  min-width: 120px;
  justify-content: center;
}

.saved-badge {
  background-color: #21BA45;
  animation: pulse-success 2s ease-in-out;
}

.unsaved-badge {
  background-color: #9E9E9E;
}

.rotating {
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse-success {
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(33, 186, 69, 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(33, 186, 69, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(33, 186, 69, 0); }
}
</style>
