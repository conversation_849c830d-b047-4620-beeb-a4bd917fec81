<template>
  <div class="custom-field-builder">
    <div class="instruction-section q-mb-lg">
      <q-icon name="info" size="sm" color="primary" class="q-mr-sm q-mt-xs" />
      <div>
        <p class="text-body2 q-mb-sm">
          <strong>Text Field:</strong> Add your question and click 'Save Text Field' to create a free-text input.
        </p>
        <p class="text-body2 q-mb-none">
          <strong>Dropdown Field:</strong> Add your question, then use 'Add Dropdown Option' to create selectable choices.
        </p>
      </div>
    </div>
    
    <div class="question-section">
      <q-input
        id="question"
        v-model="question.text"
        @focus="clearQuestionErrors"
        placeholder="Enter your question (e.g., 'What is your dietary requirement?')"
        :error="question_error"
        error-message="Question text cannot be blank!"
        outlined
        dense
        class="q-mb-md"
      />

      <!-- Choose Field Type -->
      <q-option-group
        v-model="question.inputType"
        type="radio"
        :options="[
          { label: 'Text Field', value: 'text' },
          { label: 'Dropdown Field', value: 'dropdown' }
        ]"
        color="primary"
        inline
        class="q-mb-md"
      />

      <!-- New Option Input for Dropdown Fields -->
      <div class="new-option q-mb-md row items-center" v-if="question.inputType === 'dropdown'">
        <q-input
          v-model="newOptionText"
          placeholder="Add new option"
          outlined
          dense
          class="col"
          @keyup.enter="addNewOption"
        />
        <q-btn
          color="primary"
          @click="addNewOption"
          icon="add"
          class="q-ml-sm"
        />
      </div>

      <div v-if="question.options.length > 0" class="options-section">
        <div class="text-subtitle2 q-mb-sm">Dropdown Options</div>
        <draggable
          v-model="question.options"
          handle=".drag-handle"
          item-key="nam"
          @end="onDragEnd"
        >
          <template #item="{ element, index }">
            <div class="q-mb-sm">
              <div class="option-group">
                <q-btn
                  color="primary"
                  round
                  flat
                  icon="drag_indicator"
                  class="drag-handle"
                  size="sm"
                >
                  <q-tooltip>Drag to reorder</q-tooltip>
                </q-btn>
                <q-input
                  v-model="element.nam"
                  @focus="clearOptionErrors"
                  @keypress="handleEnter"
                  :placeholder="`Option ${index + 1}`"
                  class="full-width q-mx-sm"
                  outlined
                  dense
                />
                <q-btn
                  color="negative"
                  round
                  flat
                  icon="delete"
                  @click="removeOption(element)"
                  size="sm"
                >
                  <q-tooltip>Remove option</q-tooltip>
                </q-btn>
              </div>
            </div>
          </template>
        </draggable>
      </div>

      <div
        v-if="options_error || options_short || dupe_options"
        class="q-mb-md"
      >
        <q-banner
          dense
          rounded
          v-if="options_error"
          class="bg-negative text-white q-mb-sm"
        >
          <template v-slot:avatar>
            <q-icon name="warning" />
          </template>
          Please add text to all options!
        </q-banner>

        <q-banner
          dense
          rounded
          v-if="options_short"
          class="bg-negative text-white q-mb-sm"
        >
          <template v-slot:avatar>
            <q-icon name="warning" />
          </template>
          Please add more than one option!
        </q-banner>

        <q-banner
          dense
          rounded
          v-if="dupe_options"
          class="bg-negative text-white q-mb-sm"
        >
          <template v-slot:avatar>
            <q-icon name="warning" />
          </template>
          Some of your options are the same! Please fix.
        </q-banner>
      </div>

      <div class="action-buttons">
        <q-btn
          v-if="question.inputType === 'dropdown'"
          @click="addOption"
          color="info"
          icon="add"
          :label="question.options.length === 0 ? 'Add Dropdown Option' : 'Add Another Option'"
          outline
          class="q-mr-sm"
        />
        
        <q-btn
          color="primary"
          @click="saveQuestion"
          :label="question.options.length > 0 ? 'Save Dropdown Field' : 'Save Text Field'"
          :loading="props.loading"
          :disable="!question.text.trim()"
          unelevated
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { useQuasar } from "quasar";
import draggable from "vuedraggable";

const props = defineProps({
  fieldtype: {
    type: String,
    validator: (value) => value === "booker" || value === "attendee",
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const $q = useQuasar();
const emit = defineEmits(["field-saving"]);

const options_error = ref(false);
const options_short = ref(false);
const question_error = ref(false);
const dupe_options = ref(false);
const questionOptions = ref(false);
const requiredField = ref(true);
const inputOptionsShow = ref(false);
const minFieldLength = ref(0);
const maxFieldLength = ref(0);

const question = reactive({
  text: "",
  inputType: "text",
  options: [],
  saveableOptions: [],
});

const newOptionText = ref("");

const addOption = () => {
  question.options.push({ nam: "", val: "" });
};

const addNewOption = () => {
  const text = newOptionText.value && newOptionText.value.trim();
  if (!text) return;
  question.options.push({ nam: text, val: text });
  newOptionText.value = "";
};

const addQuestionOptions = () => {
  questionOptions.value = true;
};

const removeOption = (option) => {
  const index = question.options.indexOf(option);
  question.options.splice(index, 1);
};

const handleEnter = (keyEvent) => {
  if (keyEvent.which === 13) {
    keyEvent.preventDefault();
    addOption();
  }
};

const saveQuestion = () => {
  if (question.text === "") {
    question_error.value = true;
    return;
  }

  const opts = question.options;
  const optNames = [];
  let valid = true;
  let optsValid = true;
  let noDupes = true;

  if (opts.length > 0) {
    opts.forEach((opt) => {
      if (!opt.nam || opt.nam === "") {
        valid = false;
      } else {
        if (optNames.includes(opt.nam)) {
          noDupes = false;
          return;
        }
        opt.val = opt.nam;
        optNames.push(opt.nam);
        question.saveableOptions.push(opt.nam);
      }
    });
    question.inputType = "select";
  } else {
    question.inputType = "text";
  }

  question.mandatory = requiredField.value;

  if (minFieldLength.value) {
    question.minFieldLength = minFieldLength.value;
  }

  if (maxFieldLength.value) {
    question.maxFieldLength = maxFieldLength.value;
  }

  if (!noDupes) dupe_options.value = true;
  if (!valid) options_error.value = true;

  if (opts.length === 1) {
    optsValid = false;
    options_short.value = true;
  }

  if (valid && optsValid && noDupes) {
    emit("field-saving", question, props.fieldtype);

    requiredField.value = true;
    minFieldLength.value = null;
    maxFieldLength.value = null;

    Object.assign(question, {
      text: "",
      inputType: "text",
      options: [],
      saveableOptions: [],
    });
  }
};

const clearOptionErrors = () => {
  options_error.value = false;
  options_short.value = false;
  dupe_options.value = false;
};

const clearQuestionErrors = () => {
  question_error.value = false;
};

const onDragEnd = (event) => {
  console.log("Drag ended", event);
};
</script>

<style scoped>
.custom-field-builder {
  padding: 1rem;
}

.instruction-section {
  display: flex;
  align-items: flex-start;
  background-color: #f0f8ff;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.question-section {
  background-color: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.new-option {
  background-color: #fafafa;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px dashed #ccc;
}

.options-section {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px dashed #ccc;
}

.option-group {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: white;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.drag-handle {
  cursor: move;
}

.full-width {
  width: 100%;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

@media (max-width: 600px) {
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons .q-btn {
    margin: 0.25rem 0;
  }
}
</style>
