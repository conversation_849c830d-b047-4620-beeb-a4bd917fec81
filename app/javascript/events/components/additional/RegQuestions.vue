<template>
  <div class="reg-questions-container">
    <!-- Loading State -->
    <div v-if="!event" class="text-center q-pa-xl">
      <q-spinner size="xl" color="primary" />
      <div class="q-mt-md text-h6">Loading event data...</div>
    </div>

    <!-- Event Loaded -->
    <div v-else class="max-width-container">
      <!-- Live Event Warning -->
      <q-card v-if="hasBookings || event.live" class="warning-card q-mb-lg">
        <q-card-section
          class="hg-underline text-center"
          :style="underlineOverride"
        >
          <q-icon name="warning" size="md" class="q-mr-sm" />
          <span class="text-h6">This event is live</span>
        </q-card-section>
        <q-card-section class="text-center">
          <p class="text-body1 q-mb-none">
            You are not allowed to alter questions once an event has gone live.
          </p>
        </q-card-section>
      </q-card>

      <!-- Main Content -->
      <div v-else>
        <!-- Header -->
        <div class="text-center q-mb-xl">
          <h1 class="text-h4 q-mb-sm">Registration Questions</h1>
          <p class="text-body1 text-grey-7">
            Configure the fields that will appear on your registration form
          </p>
        </div>

        <!-- Form Fields Configuration -->
        <div class="row q-col-gutter-xl">
          <div class="col-12" v-if="!event.show_add_attendees">
            <ChangeFields :event-id="event.id" fieldtype="booker" />
          </div>
          <div class="col-12 col-lg-6" v-else>
            <ChangeFields :event-id="event.id" fieldtype="booker" />
          </div>
          <div class="col-12 col-lg-6" v-if="event.show_add_attendees">
            <ChangeFields :event-id="event.id" fieldtype="attendee" />
          </div>
        </div>

        <!-- Continue Button -->
        <div class="text-center q-mt-xl q-mb-lg">
          <q-btn
            color="primary"
            size="lg"
            label="Continue to Next Step"
            @click="completePage"
            class="save-button"
            :loading="saving"
            unelevated
          />
        </div>
      </div>
    </div>

    <!-- Event Progress Dialog -->
    <EventProgressDialog
      v-model="showProgressDialog"
      context="reg-questions"
      :event-id="event?.id"
    />
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useQuasar } from "quasar";
import axios from "axios";
import ChangeFields from "./ChangeFields.vue";
import EventProgressDialog from "../shared/EventProgressDialog.vue";
import { useEventStore } from "@/stores/event";

const eventStore = useEventStore();
const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const event = computed(() => eventStore.getEvent);
const saving = ref(false);
const showProgressDialog = ref(false);

const props = defineProps({
  eventId: {
    type: [String, Number],
    default: null,
  },
});

const hasBookings = computed(() => eventStore.getHasBookings);
const advanced = computed(() => eventStore.getAdvancedUser);
const underlineOverride = computed(() => {
  return event.value?.phcolour
    ? { borderBottomColor: event.value.phcolour }
    : {};
});

// Load event data if not already loaded
onMounted(async () => {
  const eventId = props.eventId || route.params.eventId;

  if (eventId && !event.value?.id) {
    console.log(`Loading event data for reg-questions with ID: ${eventId}`);
    const success = await eventStore.loadEvent(eventId);

    if (!success) {
      $q.notify({
        type: "negative",
        message: "Failed to load event data",
        position: "top",
        timeout: 3000,
      });
      // Navigate back to event details if we can't load the event
      router.push({ name: "create-event" });
    }
  }
});

const completePage = async () => {
  saving.value = true;

  try {
    // All individual fields are already saved in real-time by ChangeFields component
    // Just show the progress dialog to continue to the next step
    showProgressDialog.value = true;

    // If dialog is closed without selection, default to terms page
    if (!showProgressDialog.value) {
      router.push({ name: "terms" });
    }
  } catch (error) {
    console.error("Error proceeding to next step:", error);
    $q.notify({
      type: "negative",
      message: "Failed to proceed to next step",
      position: "top",
      timeout: 3000,
    });
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.reg-questions-container {
  min-height: 70vh;
  padding: 2rem 1rem;
}

.max-width-container {
  max-width: none;
  margin: 0;
  width: 100%;
}

.warning-card {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffc107;
}

.warning-card .hg-underline {
  border-bottom-color: #ff9800;
  color: #e65100;
}

.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 500;
}

.hg-topline {
  border-top: 2px solid #f0f0f0;
  margin-top: 20px;
}

.save-button {
  min-width: 200px;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .reg-questions-container {
    padding: 1rem 0.5rem;
  }

  .row.q-col-gutter-xl {
    margin: 0 -0.5rem;
  }

  .row.q-col-gutter-xl > div {
    padding: 0 0.5rem;
  }
}
</style>
