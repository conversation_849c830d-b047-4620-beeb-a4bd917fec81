<template>
  <div class="registration-fields-container">
    <q-card class="q-mb-md">
      <q-card-section class="hg-underline">
        <div class="text-h6">
          {{
            fieldtype === "booker"
              ? "Booker Form Fields"
              : "Attendee Form Fields"
          }}
        </div>
      </q-card-section>

      <q-card-section>
        <p>
          Please configure what fields you would like to appear on the
          registration form.
        </p>

        <!-- Static Fields Control -->
        <q-expansion-item
          header-class="text-primary"
          expand-icon-class="text-primary"
          label="Configure Standard Fields"
          v-model="showCollapseFields"
          class="q-mb-md"
        >
          <q-card>
            <q-card-section>
              <!-- Personal Information Fields -->
              <div class="field-section">
                <div class="text-subtitle2 q-mb-md text-weight-medium">
                  Personal Information
                </div>
                <div class="checkbox-grid">
                  <q-checkbox
                    v-model="staticRegField.company_enabled"
                    label="Company Name"
                    class="checkbox-item"
                  />
                  <q-checkbox
                    v-model="staticRegField.job_enabled"
                    label="Job Title"
                    class="checkbox-item"
                  />
                  <q-checkbox
                    v-model="staticRegField.phone_enabled"
                    label="Phone Number"
                    class="checkbox-item"
                  />
                </div>
              </div>

              <!-- Address Fields -->
              <div class="field-section">
                <div class="text-subtitle2 q-mb-md text-weight-medium">
                  Address Information
                </div>
                <div class="checkbox-grid">
                  <q-checkbox
                    v-model="staticRegField.address1_enabled"
                    label="Address Line 1"
                    class="checkbox-item"
                  />
                  <q-checkbox
                    v-model="staticRegField.address2_enabled"
                    label="Address Line 2"
                    class="checkbox-item"
                  />
                  <q-checkbox
                    v-model="staticRegField.town_enabled"
                    label="Town/City"
                    class="checkbox-item"
                  />
                  <q-checkbox
                    v-model="staticRegField.county_enabled"
                    label="County/State"
                    class="checkbox-item"
                  />
                  <q-checkbox
                    v-model="staticRegField.postcode_enabled"
                    label="Postcode/Zip"
                    class="checkbox-item"
                  />
                  <q-checkbox
                    v-model="staticRegField.country_enabled"
                    label="Country"
                    class="checkbox-item"
                  />
                </div>
              </div>

              <div class="text-center q-mt-lg">
                <q-btn
                  color="primary"
                  @click="saveStaticFields"
                  label="Save Field Configuration"
                  :loading="savingFields"
                  unelevated
                />
              </div>
            </q-card-section>
          </q-card>
        </q-expansion-item>

        <!-- Custom Fields Section -->
        <div class="q-mt-lg">
          <div class="field-section">
            <div class="text-subtitle1 q-mb-md text-weight-medium">
              Custom Fields
            </div>

            <q-card
              v-if="customFields.length > 0"
              flat
              bordered
              class="custom-fields-card"
            >
              <q-list separator>
                <q-item
                  v-for="(field, index) in customFields"
                  :key="field.id || `field-${index}`"
                  class="custom-field-item"
                >
                  <q-item-section>
                    <div class="row items-start q-gutter-md">
                      <div class="col">
                        <q-input
                          v-model="field.title"
                          label="Question Text"
                          outlined
                          dense
                          @update:model-value="markFieldAsChanged(field)"
                          class="q-mb-sm"
                        />
                        <div class="row items-center q-gutter-sm">
                          <q-select
                            v-model="field.question_type"
                            :options="['string', 'select']"
                            label="Type"
                            class="col-4"
                            @update:model-value="markFieldAsChanged(field)"
                          />
                          <q-checkbox
                            v-model="field.mandatory"
                            label="Required"
                            @update:model-value="markFieldAsChanged(field)"
                          />
                        </div>
                        <div
                          v-if="field.question_type === 'select'"
                          class="q-mt-sm"
                        >
                          <div class="text-caption q-mb-sm">Options:</div>
                          <div class="q-gutter-sm q-mb-sm">
                            <q-chip
                              v-for="(option, optIdx) in field.options"
                              :key="optIdx"
                              removable
                              @remove="removeOption(field, optIdx)"
                              >{{ option }}</q-chip
                            >
                          </div>
                          <q-input
                            v-model="newOptionText"
                            label="Add option"
                            dense
                            @keyup.enter="addOption(field)"
                            class="q-mb-sm"
                          >
                            <template #append>
                              <q-btn
                                flat
                                icon="add"
                                @click="addOption(field)"
                              />
                            </template>
                          </q-input>
                        </div>
                        <div class="q-mt-sm">
                          <q-btn
                            color="primary"
                            label="Save"
                            :loading="field._saving"
                            @click="saveCustomField(field)"
                            :disable="!field._changed"
                            class="q-mr-sm"
                            dense
                            unelevated
                          />
                          <q-btn
                            color="negative"
                            label="Remove"
                            :loading="removingField === field"
                            @click="removeRegistrationField(field)"
                            dense
                            unelevated
                          />
                        </div>
                      </div>
                    </div>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card>

            <q-card v-else flat bordered class="empty-state-card">
              <q-card-section class="text-center q-py-lg">
                <q-icon
                  name="quiz"
                  size="3rem"
                  color="grey-5"
                  class="q-mb-md"
                />
                <div class="text-grey-6">No custom fields added yet</div>
                <div class="text-caption text-grey-5 q-mt-sm">
                  Use the form below to add custom questions to your
                  registration form
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- Custom Field Builder -->
        <div class="q-mt-lg">
          <q-card flat bordered class="custom-builder-card">
            <q-card-section>
              <div class="text-subtitle1 q-mb-md text-weight-medium">
                Add New Custom Field
              </div>
              <CustomFieldBuilder
                :fieldtype="fieldtype"
                @field-saving="addRegistrationField"
                :loading="addingField"
              />
            </q-card-section>
          </q-card>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from "vue";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";
import CustomFieldBuilder from "./CustomFieldBuilder.vue";
import axios from "axios";

const eventStore = useEventStore();
const $q = useQuasar();

const props = defineProps({
  eventId: {
    type: [String, Number],
    required: true,
  },
  fieldtype: {
    type: String,
    required: true,
    validator: (value) => ["booker", "attendee"].includes(value),
  },
});

// Get event from store, automatically loading if needed
const event = computed(() => eventStore.getEvent);

const showCollapseFields = ref(false);
const savingFields = ref(false);
const addingField = ref(false);
const removingField = ref(null);
const newOptionText = ref("");

// Static fields configuration - default to enabled for better UX
const staticRegField = ref({
  company_enabled: true,
  job_enabled: true,
  phone_enabled: true,
  address1_enabled: true,
  address2_enabled: true,
  town_enabled: true,
  county_enabled: true,
  postcode_enabled: true,
  country_enabled: true,
  field_type: props.fieldtype,
});

// Custom fields computed property
const customFields = computed(() => {
  const storeEvent = eventStore.getEvent;
  if (!storeEvent?.registration_fields) {
    return [];
  }
  const fieldTypeValue = props.fieldtype;

  // Filter to show only custom fields (not standard fields) for the correct field type
  return storeEvent.registration_fields.filter(
    (field) => field.field_type === fieldTypeValue && !field.field_standard
  );
});

// Initialize component
onMounted(async () => {
  // Ensure event is loaded first
  if (props.eventId) {
    const success = await eventStore.ensureEventLoaded(props.eventId);
    if (!success) {
      console.error("Failed to load event for ChangeFields component");
      $q.notify({
        type: "negative",
        message: "Failed to load event data",
        position: "top",
        timeout: 3000,
      });
      return;
    }
  }

  // Load existing static fields if they exist
  if (event.value?.static_reg_field) {
    const staticField = event.value.static_reg_field;
    if (staticField.field_type === props.fieldtype) {
      Object.assign(staticRegField.value, staticField);
    }
  }
});

// Mark field as changed for save button state
const markFieldAsChanged = (field) => {
  field._changed = true;
};

// Add option to dropdown field
const addOption = (field) => {
  if (!newOptionText.value?.trim()) return;

  if (!field.options) {
    field.options = [];
  }

  field.options.push(newOptionText.value.trim());
  newOptionText.value = "";
  markFieldAsChanged(field);
};

// Remove option from dropdown field
const removeOption = (field, index) => {
  if (field.options && field.options.length > index) {
    field.options.splice(index, 1);
    markFieldAsChanged(field);
  }
};

// Save static fields configuration
const saveStaticFields = async () => {
  if (!event.value) {
    console.error("No event available for saving static fields");
    return false;
  }

  savingFields.value = true;

  try {
    const eventPostable = {
      event: {
        id: event.value.id,
        static_reg_field_attributes: {
          ...staticRegField.value,
          id: event.value.static_reg_field?.id || undefined,
        },
      },
    };

    console.log("Saving static fields:", eventPostable);

    const response = await axios.put(
      `/events/${event.value.id}/add_additional.json`,
      eventPostable
    );

    console.log("Save static fields response:", response.data);

    // Update the event data properly to trigger reactivity
    if (response.data?.event) {
      // Update the store directly with the new event data
      eventStore.setEvent(response.data.event);
    } else {
      // Fallback: force reload if response doesn't include the data
      console.log("Response missing event data, forcing reload");
      eventStore.event = null;
      await eventStore.loadEvent(props.eventId);
    }

    $q.notify({
      type: "positive",
      message: "Field configuration saved successfully",
      position: "top",
      timeout: 2000,
    });

    return true;
  } catch (error) {
    console.error("Error saving static fields:", error);
    $q.notify({
      type: "negative",
      message: "Failed to save field configuration",
      position: "top",
      timeout: 3000,
    });
    return false;
  } finally {
    savingFields.value = false;
  }
};

// Save individual custom field
const saveCustomField = async (field) => {
  if (!event.value || !field._changed) return;

  field._saving = true;

  try {
    const fieldData = {
      title: field.title,
      question_type: field.question_type,
      options: field.options || [],
      mandatory: field.mandatory || false,
      field_type: props.fieldtype,
      order_index: field.order_index || 0,
    };

    const eventPostable = {
      event: {
        id: event.value.id,
        registration_fields_attributes: [
          {
            ...fieldData,
            id: field.id || undefined,
          },
        ],
      },
    };

    console.log("Saving custom field:", eventPostable);

    const response = await axios.put(
      `/events/${event.value.id}/add_additional.json`,
      eventPostable
    );

    // Update only the registration_fields in the store to preserve other event data
    if (response.data?.event?.registration_fields) {
      const currentEvent = eventStore.getEvent;
      const updatedEvent = {
        ...currentEvent,
        registration_fields: response.data.event.registration_fields,
      };
      eventStore.setEvent(updatedEvent);

      // Mark field as no longer changed since it's now saved
      field._changed = false;
    } else {
      // Fallback: force reload if response doesn't include the data
      eventStore.event = null;
      await eventStore.loadEvent(props.eventId);
      field._changed = false;
    }

    $q.notify({
      type: "positive",
      message: "Field saved successfully",
      position: "top",
      timeout: 2000,
    });
  } catch (error) {
    console.error("Error saving custom field:", error);
    $q.notify({
      type: "negative",
      message: "Failed to save field",
      position: "top",
      timeout: 3000,
    });
  } finally {
    field._saving = false;
  }
};

// Add new registration field
const addRegistrationField = async (fieldBuilder, fieldtype) => {
  if (!event.value) {
    console.error("No event available for adding registration field");
    return false;
  }

  addingField.value = true;

  try {
    const fieldData = {
      title: fieldBuilder.text,
      question_type: fieldBuilder.inputType,
      options: fieldBuilder.saveableOptions || [],
      mandatory: false,
      field_type: fieldtype,
      order_index: (customFields.value.length || 0) + 1,
    };

    const eventPostable = {
      event: {
        id: event.value.id,
        registration_fields_attributes: [fieldData],
      },
    };

    console.log("Adding new custom field:", eventPostable);

    const response = await axios.put(
      `/events/${event.value.id}/add_additional.json`,
      eventPostable
    );

    console.log("Add field response:", response.data);

    // Update only the registration_fields in the store to preserve other event data
    if (response.data?.event?.registration_fields) {
      const currentEvent = eventStore.getEvent;
      const updatedEvent = {
        ...currentEvent,
        registration_fields: response.data.event.registration_fields,
      };
      eventStore.setEvent(updatedEvent);
    } else {
      // Fallback: force reload if response doesn't include the data
      console.log("Response missing registration_fields, forcing reload");
      eventStore.event = null;
      await eventStore.loadEvent(props.eventId);
    }

    $q.notify({
      type: "positive",
      message: `Added new ${fieldtype} field: ${fieldBuilder.text}`,
      position: "top",
      timeout: 2000,
    });

    return true;
  } catch (error) {
    console.error("Error adding registration field:", error);
    $q.notify({
      type: "negative",
      message: "Failed to add field",
      position: "top",
      timeout: 3000,
    });
    return false;
  } finally {
    addingField.value = false;
  }
};

// Remove registration field
const removeRegistrationField = async (field) => {
  if (!event.value || !field.id) {
    console.error(
      "No event or field ID available for removing registration field"
    );
    return false;
  }

  removingField.value = field;

  try {
    const eventPostable = {
      event: {
        id: event.value.id,
        registration_fields_attributes: [
          {
            id: field.id,
            _destroy: true,
          },
        ],
      },
    };

    console.log("Removing custom field:", eventPostable);

    const response = await axios.put(
      `/events/${event.value.id}/add_additional.json`,
      eventPostable
    );

    // Update only the registration_fields in the store to preserve other event data
    if (response.data?.event?.registration_fields) {
      const currentEvent = eventStore.getEvent;
      const updatedEvent = {
        ...currentEvent,
        registration_fields: response.data.event.registration_fields,
      };
      eventStore.setEvent(updatedEvent);
    } else {
      // Fallback: force reload if response doesn't include the data
      eventStore.event = null;
      await eventStore.loadEvent(props.eventId);
    }

    $q.notify({
      type: "positive",
      message: `Removed field: ${field.title}`,
      position: "top",
      timeout: 2000,
    });
  } catch (error) {
    console.error("Error removing registration field:", error);
    $q.notify({
      type: "negative",
      message: "Failed to remove field",
      position: "top",
      timeout: 3000,
    });
  } finally {
    removingField.value = null;
  }
};
</script>

<style scoped>
.registration-fields-container {
  width: 100%;
  max-width: none;
  margin: 0;
}

.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 500;
}

.field-section {
  margin-bottom: 1.5rem;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  padding: 0.5rem 0;
}

.checkbox-item {
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.custom-fields-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.custom-field-item {
  padding: 1.5rem;
}

.custom-field-item:hover {
  background-color: #f9f9f9;
}

.empty-state-card {
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
}

.custom-builder-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}
</style>
