<template>
  <q-page class="q-pa-md">
    <q-stepper
      class="q-mb-md q-mt-md"
      v-model="activeStep"
      header-nav
      color="primary"
      active-color="amber-8"
      done-color="amber-8"
      alternative-labels
      flat
    >
      <q-step
        :name="0"
        title="Details"
        icon="fa fa-globe"
        :done="currentStep >= 1.1"
        @click="nav('event-details')"
      />
      <q-step
        :name="1"
        title="Tickets"
        icon="fa fa-ticket"
        :done="currentStep >= 1.2"
        @click="nav('ticket-creation')"
      />
      <q-step
        :name="2"
        title="Edit Booking Questions"
        icon="fa fa-question"
        :done="currentStep >= 1.3"
        @click="nav('additional')"
      />
      <q-step
        :name="3"
        title="Terms"
        icon="fa fa-file-text"
        :done="currentStep >= 1.4"
        @click="nav('terms')"
      />
      <q-step
        :name="4"
        title="Preview"
        icon="fa fa-eye"
        @click="nav('preview')"
      />
    </q-stepper>
    
    <!-- Event header showing event title and ID -->
    <EventHeader />
    
    <div class="row topline">
      <div class="col-12">
        <router-view></router-view>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import { useEventStore } from '@/stores/event'
import { routerStore } from '@/events/router'
import { prepareForTicketNavigation } from '@/shared/router/ticketRoutesHandler'
import EventHeader from './common/EventHeader.vue'

const router = useRouter()
const route = useRoute()
const $q = useQuasar()
const activeStep = ref(0)

// Use the Context7 store directly
const currentStep = computed(() => useEventStore().state.currentStep || 0)

const active = computed(() => {
  // Use routerStore to determine active step
  const routeName = routerStore.getCurrentRouteName ? routerStore.getCurrentRouteName() : route.name

  switch (routeName) {
    case 'create-event': return 0
    case 'ticket-creation': return 1
    case 'additional': return 2
    case 'terms': return 3
    case 'preview': return 4
    default: return 0
  }
})

// Make sure activeStep stays in sync with route
watch(active, (newValue) => {
  activeStep.value = newValue
})

// Initialize step
activeStep.value = active.value

const nav = (pathTo) => {
  const curStep = currentStep.value
  const event = eventStore.getEvent()

  switch (pathTo) {
    case 'create-event':
      router.push({ name: pathTo })
      break
    case 'ticket-creation':
      if (!event || !event.id) {
        $q.dialog({
          title: 'No Event Selected',
          message: 'Please create or select an event before managing tickets.',
          ok: {
            color: 'primary',
            label: 'OK'
          },
          persistent: true
        })
      }
      else if (curStep >= 1.1) {
        // Prepare for ticket navigation to prevent reactivity loops
        console.log(`Event.vue: Navigating to tickets for event ${event.id}`)
        prepareForTicketNavigation(event.id)
        
        // Small delay to ensure flags are set
        setTimeout(() => {
          router.push({ name: pathTo, params: { eventId: event.id } })
        }, 10)
      }
      break
    case 'additional':
      if (curStep >= 1.2) {
        router.push({ name: pathTo })
      }
      break
    case 'terms':
      if (curStep >= 1.2) {
        router.push({ name: pathTo })
      }
      break    
    case 'preview':
      if (curStep >= 1.4) {
        router.push({ name: pathTo })
      }
  }
}
</script>

<style scoped>
.topline {
  margin-top: 20px;
  border-top: 4px solid #ff9500;
}

/* Customize Quasar stepper colors */
:deep(.q-stepper__tab--done) .q-stepper__dot {
  background: #ff9500;
}
</style>