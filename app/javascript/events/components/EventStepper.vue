<template>
  <q-stepper v-model="currentStep" flat color="primary" animated header-nav>
    <q-step
      v-for="step in steps"
      :key="step.name"
      :name="step.name"
      :title="step.title"
      :icon="step.icon"
      :done="isStepDone(step)"
      :header-nav="true"
      @click="goToStep(step)"
    />
  </q-stepper>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import { prepareForTicketNavigation } from "@/shared/router/ticketRoutesHandler";

const steps = [
  { name: "create-event", title: "Details", icon: "info" },
  { name: "ticket-creation", title: "Tickets", icon: "confirmation_number" },
  // { name: 'additional', title: 'Questions', icon: 'help' },
  // { name: 'terms', title: 'Terms', icon: 'gavel' },
  // { name: 'customise-emails', title: 'Emails', icon: 'email' },
  // { name: 'preview', title: 'Preview', icon: 'visibility' }
];

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const eventStore = useEventStore();

// Context7 MCP: Enhanced context checking that allows initial event loading
const hasEventContext = computed(() => {
  const event = eventStore.getSafeEvent;
  const hasStoreEvent = !!(event && event.id);

  // Allow navigation if there's an event ID in the current route (for initial loads)
  const hasRouteEventId = !!(route.params.id && route.params.id !== "new");

  return hasStoreEvent || hasRouteEventId;
});

const currentEventId = computed(() => {
  const event = eventStore.getSafeEvent;
  // Prefer event store, fallback to route params for initial loads
  return event?.id || route.params.id;
});

// Helper to check if we have a definitive event context (not just route params)
const hasDefinitiveEventContext = computed(() => {
  const event = eventStore.getSafeEvent;
  return !!(event && event.id);
});

const currentStep = ref(route.name);

watch(
  () => route.name,
  (newName) => {
    currentStep.value = newName;
  }
);

function isStepDone(step) {
  const idx = steps.findIndex((s) => s.name === step.name);
  const currentIdx = steps.findIndex((s) => s.name === currentStep.value);
  return idx < currentIdx;
}

// Context7 MCP: Enhanced notification with context-aware messaging
const showEventRequiredNotification = () => {
  const hasRouteId = !!(route.params.id && route.params.id !== "new");

  if (hasRouteId) {
    // Event ID exists in route but not loaded yet - likely still loading
    $q.notify({
      type: "info",
      message: "Event is loading...",
      caption: "Please wait while we load the event details",
      position: "top",
      timeout: 2000,
      spinner: true,
    });
  } else {
    // No event context at all
    $q.notify({
      type: "warning",
      message: "Please create or select an event first",
      caption: "Event context is required for this feature",
      position: "top",
      timeout: 3000,
      actions: [
        {
          label: "Create Event",
          color: "white",
          handler: () => {
            router.push({ name: "create-event" });
          },
        },
      ],
    });
  }
};

function goToStep(step) {
  if (step.name !== currentStep.value) {
    // Handle create-event and edit-event navigation (always allowed)
    if (step.name === "create-event" || step.name === "edit-event") {
      if (currentEventId.value && currentEventId.value !== "new") {
        router.push({
          name: "edit-event",
          params: { id: currentEventId.value },
        });
      } else {
        router.push({ name: "create-event" });
      }
      return;
    }

    // Context7 MCP: Enhanced validation for ticket creation
    if (step.name === "ticket-creation") {
      if (!hasDefinitiveEventContext.value) {
        if (route.params.id && route.params.id !== "new") {
          // Event is likely still loading, show loading message
          $q.notify({
            type: "info",
            message: "Please wait for event to finish loading",
            caption: "Event details are still being loaded",
            position: "top",
            timeout: 2000,
            spinner: true,
          });
        } else {
          showEventRequiredNotification();
        }
        return;
      }

      console.log(
        `EventStepper: Navigating to tickets for event ${currentEventId.value}`
      );
      prepareForTicketNavigation(currentEventId.value);

      // Add small delay to ensure flags are set
      setTimeout(() => {
        router.push({
          name: step.name,
          params: { eventId: currentEventId.value },
        });
      }, 10);
      return;
    }

    // For other steps, require definitive event context
    if (!hasDefinitiveEventContext.value) {
      showEventRequiredNotification();
      return;
    }

    // Default navigation with event context
    router.push({ name: step.name });
  }
}
</script>

<style scoped>
.q-stepper {
  background: transparent;
}
</style>
