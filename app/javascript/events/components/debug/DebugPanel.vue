<template>
  <div class="debug-panel" v-if="isVisible">
    <div class="debug-header">
      <h4>Debug Panel</h4>
      <q-btn flat dense icon="close" @click="close" />
    </div>
    <div class="debug-content">
      <div class="debug-section">
        <h5>Event Store</h5>
        <pre>{{ eventData }}</pre>
      </div>
      <div class="debug-section">
        <h5>Route Information</h5>
        <div>Route Name: {{ routeName }}</div>
        <div>Route Params: {{ routeParams }}</div>
      </div>
      <div class="debug-section">
        <h5>Authentication</h5>
        <div v-if="authInfo">
          <div class="auth-status" :class="authInfo.authenticated ? 'authenticated' : 'unauthenticated'">
            Authenticated: {{ authInfo.authenticated }}
          </div>
          <div>User ID: {{ authInfo.user_id }}</div>
          <div>User Email: {{ authInfo.user_email }}</div>
          <div>Session ID: {{ authInfo.session_id }}</div>
          <div>Session Expires: {{ authInfo.session_expires_at }}</div>
          <div class="last-checked">Last checked: {{ authLastChecked }}</div>
        </div>
        <div v-else>
          <q-btn label="Check Auth Status" @click="checkAuthStatus" />
        </div>
      </div>
      <div class="debug-section">
        <h5>Event Loading</h5>
        <div class="row q-col-gutter-sm">
          <div class="col-12">
            <q-input 
              v-model="eventIdToLoad" 
              label="Event ID to Load" 
              type="number" 
              dense
              outlined 
            />
          </div>
          <div class="col-12">
            <q-btn 
              label="Load Event" 
              color="primary" 
              @click="loadEventById" 
              :disable="!eventIdToLoad" 
              class="q-mr-sm" 
            />
            <q-btn 
              label="Navigate to Tickets" 
              color="secondary" 
              @click="goToTickets" 
              :disable="!hasEventInStore" 
            />
          </div>
        </div>
      </div>
      <div class="debug-actions">
        <q-btn label="Reload Page" color="primary" @click="reloadPage" />
        <q-btn label="Go to Events" color="secondary" @click="goToEvents" />
        <q-btn label="Clear Event Store" color="negative" @click="clearEventStore" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { eventStore } from '@/stores/event'
import axios from 'axios'

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

const router = useRouter()
const route = useRoute()
const authInfo = ref(null)
const authLastChecked = ref('-')
const eventIdToLoad = ref('')

// Computed properties
const eventData = computed(() => {
  const event = eventStore.getSafeEvent
  return JSON.stringify(event, null, 2)
})

const hasEventInStore = computed(() => {
  const event = eventStore.getSafeEvent
  return event && event.id
})

const routeName = computed(() => route.name)
const routeParams = computed(() => JSON.stringify(route.params, null, 2))

// Methods
const close = () => {
  emit('close')
}

const checkAuthStatus = async () => {
  try {
    const response = await axios.get('/api/debug/auth_status')
    authInfo.value = response.data
    authLastChecked.value = new Date().toLocaleTimeString()
  } catch (error) {
    authInfo.value = { error: error.message, status: error.response?.status }
    authLastChecked.value = new Date().toLocaleTimeString() + ' (error)'
  }
}

// Load event by ID
const loadEventById = async () => {
  if (!eventIdToLoad.value) return
  
  try {
    const response = await axios.get(`/event_details/${eventIdToLoad.value}`)
    if (response.data && response.data.event) {
      eventStore.setEvent(response.data.event)
      console.log('Event loaded into store:', response.data.event)
    }
  } catch (error) {
    console.error('Failed to load event:', error)
  }
}

// Navigate to tickets page with current event ID
const goToTickets = () => {
  const event = eventStore.getSafeEvent
  if (event && event.id) {
    router.push({
      name: 'ticket-creation',
      params: { eventId: event.id }
    })
  }
}

const reloadPage = () => {
  window.location.reload()
}

const goToEvents = () => {
  window.location.href = '/'
}

const clearEventStore = () => {
  eventStore.setEvent({})
  alert('Event store cleared')
}

// Initialize
onMounted(() => {
  checkAuthStatus()
})
</script>

<style scoped>
.debug-panel {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 0;
  z-index: 9999;
  box-shadow: -2px -2px 10px rgba(0,0,0,0.1);
  padding: 10px;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.debug-header h4 {
  margin: 0;
}

.debug-section {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #eee;
}

.debug-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
}

.debug-section pre {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 3px;
  font-size: 12px;
  margin: 0;
}

.debug-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.auth-status {
  font-weight: bold;
  padding: 3px 6px;
  border-radius: 3px;
  display: inline-block;
  margin-bottom: 5px;
}

.authenticated {
  background-color: #c8e6c9;
  color: #2e7d32;
}

.unauthenticated {
  background-color: #ffcdd2;
  color: #c62828;
}

.last-checked {
  font-size: 12px;
  color: #757575;
  margin-top: 5px;
}

.debug-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1rem;
}

.debug-section pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.debug-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}
</style>
