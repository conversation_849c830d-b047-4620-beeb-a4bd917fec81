<template>
  <div class="upload-contacts">
    <q-card>
      <q-card-section class="text-h5"> Register Delegates </q-card-section>

      <q-card-section>
        <div class="row q-col-gutter-md q-mb-md">
          <div class="col-md-6">
            <q-btn
              color="primary"
              @click="showAddContact = true"
              label="Add New Contact"
            />
            <q-btn
              color="secondary"
              @click="importOrgContacts"
              label="Import Organisation Contacts"
              class="q-ml-sm"
            />
          </div>
        </div>

        <!-- Add Contact Dialog -->
        <q-dialog v-model="showAddContact" persistent>
          <q-card style="min-width: 350px">
            <q-card-section class="row items-center">
              <div class="text-h6">Add New Contact</div>
              <q-space />
              <q-btn icon="close" flat round dense v-close-popup />
            </q-card-section>

            <q-card-section>
              <q-form @submit="saveInvites" ref="emailAddForm">
                <q-input
                  v-model="emailInvite.forename"
                  label="First Name"
                  :rules="[(val) => !!val || 'First name is required']"
                  class="q-mb-md"
                />

                <q-input
                  v-model="emailInvite.surname"
                  label="Last Name"
                  :rules="[(val) => !!val || 'Last name is required']"
                  class="q-mb-md"
                />

                <q-input
                  v-model="emailInvite.email"
                  label="Email"
                  type="email"
                  :rules="[
                    (val) => !!val || 'Email is required',
                    (val) =>
                      emailRegex.test(val) || 'Please enter a valid email',
                  ]"
                  class="q-mb-md"
                />
              </q-form>
            </q-card-section>

            <q-card-actions align="right">
              <q-btn flat label="Cancel" color="negative" v-close-popup />
              <q-btn
                type="submit"
                color="primary"
                label="Save"
                :loading="submitted"
                @click="saveInvites"
              />
            </q-card-actions>
          </q-card>
        </q-dialog>

        <!-- Email Invites Table -->
        <q-table
          :rows="emailInvites"
          :columns="columns"
          row-key="id"
          :pagination="pagination"
          :rows-per-page-options="[10]"
          @request="onRequest"
          :loading="loading"
        >
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="forename" :props="props">{{
                props.row.forename
              }}</q-td>
              <q-td key="surname" :props="props">{{ props.row.surname }}</q-td>
              <q-td key="email" :props="props">{{ props.row.email }}</q-td>
              <q-td key="opted_out" :props="props">{{
                props.row.opted_out ? "Yes" : "No"
              }}</q-td>
              <q-td key="actions" :props="props">
                <q-btn
                  color="negative"
                  icon="delete"
                  flat
                  dense
                  @click="confirmDelete(props.row)"
                />
              </q-td>
            </q-tr>
          </template>

          <template v-slot:no-data>
            <div class="full-width row flex-center text-grey-8 q-gutter-sm">
              <q-icon size="2em" name="sentiment_dissatisfied" />
              <span>No contacts added yet</span>
            </div>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import axios from "axios";

const router = useRouter();
const $q = useQuasar();
const eventStore = useEventStore();

// Props
const props = defineProps({
  eventId: {
    type: [String, Number],
    default: null,
  },
  event: {
    type: Object,
    required: false,
  },
});

// Event data from store
const event = computed(() => eventStore.getEvent);

// State
const showAddContact = ref(false);
const emailInvites = ref([]);
const emailInvite = ref({
  id: "",
  forename: "",
  surname: "",
  email: "",
});
const submitted = ref(false);
const loading = ref(false);
const emailAddForm = ref(null);
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const totalItems = ref(0);

// Table configuration
const columns = [
  {
    name: "forename",
    label: "First Name",
    field: "forename",
    align: "left",
    sortable: true,
  },
  {
    name: "surname",
    label: "Last Name",
    field: "surname",
    align: "left",
    sortable: true,
  },
  {
    name: "email",
    label: "Email",
    field: "email",
    align: "left",
    sortable: true,
  },
  {
    name: "opted_out",
    label: "Opted Out",
    field: "opted_out",
    align: "left",
    sortable: true,
  },
  { name: "actions", label: "Actions", field: "actions", align: "center" },
];

const pagination = ref({
  sortBy: "surname",
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// Computed
const totalPages = computed(() =>
  Math.ceil(totalItems.value / pagination.value.rowsPerPage)
);

// Methods
const getUsers = async (page = 1) => {
  const currentEvent = event.value;
  if (!currentEvent || !currentEvent.id) {
    $q.notify({
      color: "warning",
      message: "Event not loaded yet",
      icon: "warning",
    });
    return;
  }

  loading.value = true;
  try {
    const response = await axios.get(
      `/registered_users/${currentEvent.id}.json?page=${page}`
    );
    emailInvites.value = response.data.unconfirmed_users;
    totalItems.value = response.data.total_count;
    pagination.value.page = page;
    pagination.value.rowsNumber = totalItems.value;
  } catch (error) {
    $q.notify({
      color: "negative",
      message: "Failed to fetch users",
      icon: "error",
    });
  } finally {
    loading.value = false;
  }
};

const onRequest = (props) => {
  const { page } = props.pagination;
  getUsers(page);
};

const confirmDelete = (invite) => {
  $q.dialog({
    title: "Confirm",
    message: "Are you sure you want to delete this invite?",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    removeInvite(invite);
  });
};

const removeInvite = async (invite) => {
  try {
    await axios.delete(`/registered_users/${invite.id}`);
    $q.notify({
      color: "positive",
      message: "Email details removed",
      icon: "check",
    });
    getUsers(pagination.value.page);
  } catch (error) {
    $q.notify({
      color: "negative",
      message: "Failed to remove email",
      icon: "error",
    });
  }
};

const saveInvites = async () => {
  submitted.value = true;

  const currentEvent = event.value;
  if (!currentEvent || !currentEvent.id) {
    $q.notify({
      color: "negative",
      message: "Event not loaded - cannot save invites",
      icon: "error",
    });
    submitted.value = false;
    return;
  }

  // Validate the form
  const emailAddRef = emailAddForm.value;
  if (emailAddRef && !emailAddRef.validate()) {
    submitted.value = false;
    return;
  }

  const emailDetails = {
    forename: emailInvite.value.forename,
    surname: emailInvite.value.surname,
    email: emailInvite.value.email,
    event_booking_attributes: {
      event_id: currentEvent.id,
    },
  };

  try {
    const response = await axios.post("/registered_users.json", {
      registered_user: emailDetails,
    });

    // Reset form
    emailInvite.value = {
      id: "",
      forename: "",
      surname: "",
      email: "",
    };
    showAddContact.value = false;

    $q.notify({
      color: "positive",
      message: "Email details saved",
      icon: "check",
    });
    getUsers(pagination.value.page);
  } catch (error) {
    $q.notify({
      color: "negative",
      message: "Failed to save email details",
      icon: "error",
    });
  } finally {
    submitted.value = false;
  }
};

const importOrgContacts = () => {
  // TODO: Implement organization contacts import
  $q.notify({
    color: "info",
    message: "Organization contacts import feature coming soon",
    icon: "info",
  });
};

const saveInviteContacts = () => {
  const currentEvent = event.value;
  if (!currentEvent || !currentEvent.id) {
    $q.notify({
      color: "warning",
      message: "Event not loaded yet",
      icon: "warning",
    });
    return;
  }

  router.push({
    name: "customise-confirmation",
    params: { eventId: currentEvent.id },
  });
};

// Load event if eventId is provided
const ensureEventLoaded = async () => {
  try {
    const eventId = props.eventId || eventStore.getEventId;

    if (!eventId) {
      $q.notify({
        color: "negative",
        message: "No event ID provided",
        icon: "error",
      });
      return false;
    }

    if (!event.value || event.value.id != eventId) {
      await eventStore.ensureEventLoaded(eventId);
    }

    return true;
  } catch (error) {
    console.error("Failed to load event:", error);
    $q.notify({
      color: "negative",
      message: "Failed to load event details",
      icon: "error",
    });
    return false;
  }
};

// Lifecycle
onMounted(async () => {
  const eventLoaded = await ensureEventLoaded();
  if (eventLoaded) {
    getUsers();
  }
});
</script>

<style scoped>
.upload-contacts {
  width: 100%;
}
</style>
