<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card style="min-width: 400px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ dialogTitle }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section>
        <div class="text-body1 q-mb-md">
          {{ completionMessage }}
        </div>

        <div class="text-body2 text-grey-7 q-mb-lg">
          You can return to this event at any time before it goes live to make
          additional changes.
        </div>

        <div v-if="availableActions.length > 0" class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">
            What would you like to do next?
          </div>

          <q-list bordered separator class="rounded-borders">
            <q-item
              v-for="action in availableActions"
              :key="action.key"
              clickable
              v-ripple
              @click="handleAction(action)"
              class="q-py-sm"
            >
              <q-item-section avatar>
                <q-icon
                  :name="action.icon"
                  :color="action.color || 'primary'"
                />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ action.label }}</q-item-label>
                <q-item-label caption>{{ action.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon name="chevron_right" color="grey-5" />
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn
          flat
          label="Return to Dashboard"
          color="grey-7"
          @click="handleReturnToDashboard"
        />
        <q-btn
          unelevated
          label="Stay on Event"
          color="primary"
          @click="handleStayOnEvent"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useEventStore } from "@/stores/event";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  context: {
    type: String,
    required: true,
    validator: (value) =>
      [
        "tickets",
        "additional-questions",
        "terms",
        "emails",
        "preview",
      ].includes(value),
  },
  eventId: {
    type: [String, Number],
    required: true,
  },
});

const emit = defineEmits(["update:modelValue", "action-selected"]);

const router = useRouter();
const eventStore = useEventStore();

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// Context-specific configurations
const contextConfig = {
  tickets: {
    title: "Tickets Saved Successfully!",
    message: "Your tickets have been saved and are ready for bookings.",
    excludeActions: ["tickets"],
  },
  "additional-questions": {
    title: "Additional Questions Saved!",
    message: "Your additional questions have been configured.",
    excludeActions: ["additional-questions"],
  },
  terms: {
    title: "Terms & Conditions Saved!",
    message: "Your terms and conditions have been saved.",
    excludeActions: ["terms"],
  },
  emails: {
    title: "Email Settings Saved!",
    message: "Your email customizations have been saved.",
    excludeActions: ["emails"],
  },
  preview: {
    title: "Preview Complete!",
    message: "You've reviewed your event settings.",
    excludeActions: ["preview"],
  },
};

// All possible actions
const allActions = [
  {
    key: "tickets",
    label: "Manage Tickets",
    description: "Add, edit, or configure ticket options",
    icon: "local_activity",
    color: "primary",
    route: { name: "ticket-creation", params: { eventId: props.eventId } },
  },
  {
    key: "additional-questions",
    label: "Additional Questions",
    description: "Add custom questions for attendees",
    icon: "help",
    color: "info",
    route: { name: "reg-questions", params: { eventId: props.eventId } },
  },
  {
    key: "terms",
    label: "Terms & Conditions",
    description: "Set up terms and conditions",
    icon: "gavel",
    color: "warning",
    route: { name: "terms", params: { eventId: props.eventId } },
  },
  {
    key: "emails",
    label: "Email Customization",
    description: "Customize confirmation and reminder emails",
    icon: "email",
    color: "positive",
    route: { name: "customise-emails", params: { eventId: props.eventId } },
  },
  {
    key: "preview",
    label: "Preview Event",
    description: "See how your event will look to attendees",
    icon: "visibility",
    color: "purple",
    route: { name: "preview", params: { eventId: props.eventId } },
  },
];

const dialogTitle = computed(() => {
  return contextConfig[props.context]?.title || "Action Complete!";
});

const completionMessage = computed(() => {
  return (
    contextConfig[props.context]?.message || "Your changes have been saved."
  );
});

const availableActions = computed(() => {
  const excludeActions = contextConfig[props.context]?.excludeActions || [];
  return allActions.filter((action) => !excludeActions.includes(action.key));
});

const handleAction = (action) => {
  emit("action-selected", action);
  isOpen.value = false;

  // Navigate to the selected action
  if (action.route) {
    router.push(action.route);
  }
};

const handleReturnToDashboard = () => {
  isOpen.value = false;
  router.push({ name: "dash-main" });
};

const handleStayOnEvent = () => {
  isOpen.value = false;
  // Stay on current page/event
};
</script>

<style lang="scss" scoped>
.q-item {
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}
</style>
