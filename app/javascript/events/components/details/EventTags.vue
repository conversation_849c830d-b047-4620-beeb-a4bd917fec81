<template>
  <StyledCard color="#9c27b0" title="Tag Your Events" icon="local_offer">
    <div class="text-subtitle2 q-mb-md text-grey-7">
      Add Tags to Your Events To Allow Easy Searching and Organisation
    </div>

    <div class="q-mb-md">
      <q-chip
        v-for="tag in localTags"
        :key="tag"
        removable
        color="primary"
        text-color="white"
        @remove="handleClose(tag)"
        class="q-ma-xs"
      >
        {{ tag }}
      </q-chip>
    </div>

    <q-input
      v-if="inputVisible"
      v-model="inputValue"
      ref="saveTagInput"
      dense
      class="input-new-tag"
      @keyup.enter="handleInputConfirm"
      @blur="handleInputConfirm"
    />
    <q-btn
      v-else
      color="primary"
      icon="add"
      label="New Tag"
      @click="showInput"
      :disable="props.event.live"
    />
  </StyledCard>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from "vue";
import { useQuasar } from "quasar";
import StyledCard from "@/common/StyledCard.vue";

const $q = useQuasar();
const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["update:tags"]);

const inputVisible = ref(false);
const inputValue = ref("");
const saveTagInput = ref(null);

// Create a local copy of tags to work with
const localTags = ref([]);

// Initialize local tags
onMounted(() => {
  localTags.value = props.event.tags ? [...props.event.tags] : [];
});

// Watch for changes to event.tags from parent
watch(
  () => props.event.tags,
  (newTags) => {
    if (newTags) {
      localTags.value = [...newTags];
    } else {
      localTags.value = [];
    }
  },
  { deep: true }
);

// Update parent when local tags change
const updateTags = () => {
  emit("update:tags", localTags.value);
};

const handleClose = (tag) => {
  localTags.value = localTags.value.filter((t) => t !== tag);
  updateTags();
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    saveTagInput.value?.focus();
  });
};

const handleInputConfirm = () => {
  if (localTags.value.length > 19) {
    $q.notify({
      type: "warning",
      message: "You can add a maximum of 20 tags!",
      position: "top",
      timeout: 2000,
    });
    return;
  }

  if (inputValue.value) {
    localTags.value.push(inputValue.value.toLowerCase());
    updateTags();
  }
  inputVisible.value = false;
  inputValue.value = "";
};
</script>

<style scoped>
/* .hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 500;
}

.input-new-tag {
  width: 120px;
  display: inline-block;
  margin-top: 8px;
} */
</style>
