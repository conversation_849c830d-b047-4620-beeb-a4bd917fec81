<template>
  <div>
    <div class="row">
      <div class="col-8 q-my-md">
        <div class="text-weight-bold">Do you wish to add sponsors?</div>
        <q-btn-toggle
          v-model="event.add_sponsors"
          toggle-color="primary"
          :options="[
            {label: 'Yes', value: true},
            {label: 'No', value: false}
          ]"
          class="q-mt-sm"
        />
      </div>
    </div>

    <q-card v-if="event.add_sponsors">
      <q-card-section class="hg-underline">
        Sponsors
      </q-card-section>

      <q-card-section>
        <p>This title will appear on the email and delegate view above your images.</p>
        <p id="qa_p2">Please change the title to suit your needs otherwise leave blank and 'Sponsors' will show</p>
        
        <q-input
          v-model="event.sponsor_title"
          label="Sponsor Title"
          placeholder="e.g. In Partnership with..."
          class="q-mb-md"
          style="max-width: 50%"
        />

        <div class="row">
          <div class="col-md-3">
            <h5>Please select where you would like to have the sponsors shown</h5>
            <div class="q-mt-md">
              <q-checkbox
                v-model="event.sponsor_view_delegate"
                label="Booking View"
              />
            </div>

            <div class="q-mt-md">
              <q-checkbox
                v-model="event.sponsor_view_email"
                label="Email View"
              />
            </div>
          </div>

          <div class="col-md-9">
            <q-uploader
              :url="`/sponsor_image_upload/upload_sponsors/${event.id}`"
              :headers="headers"
              accept="image/*"
              multiple
              auto-upload
              flat
              bordered
              @uploaded="handleSuccess"
              @rejected="onRejected"
              :max-files="20"
              style="max-width: 100%"
            >
              <template v-slot:header="scope">
                <div class="row no-wrap items-center q-pa-sm">
                  <div class="col text-center">
                    <div class="q-uploader__title">Upload sponsor images</div>
                    <div class="q-uploader__subtitle">Image files only</div>
                  </div>
                  <q-btn
                    v-if="scope.queuedFiles.length > 0"
                    icon="clear_all"
                    round
                    dense
                    flat
                    @click="scope.removeQueuedFiles"
                  />
                </div>
              </template>
            </q-uploader>

            <!-- Display Existing Sponsors -->
            <div class="q-mt-md">
              <div class="row q-col-gutter-md">
                <div 
                  v-for="sponsor in sponsorsList" 
                  :key="sponsor.id" 
                  class="col-4 col-md-3 col-lg-2"
                >
                  <q-card flat bordered>
                    <q-img
                      :src="sponsor.url"
                      style="height: 120px"
                    >
                      <div class="absolute-top-right">
                        <q-btn
                          round
                          color="negative"
                          icon="delete"
                          size="xs"
                          @click="handleRemove(sponsor)"
                        />
                      </div>
                    </q-img>
                  </q-card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'

const $q = useQuasar()
const props = defineProps({
  event: {
    type: Object,
    required: true
  }
})

const headers = {
  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
}

const imageBucket = window.imageBucket
const appImageBucket = window.appImageBucket
const deletable = ref(true)

const permittedFileTypes = ['image/png', 'image/gif', 'image/jpeg']

const sponsorsList = computed(() => {
  return props.event.sponsors?.map(sponsor => ({
    name: sponsor.sponsor_file,
    id: sponsor.id,
    url: `https://s3-eu-west-1.amazonaws.com/${imageBucket}/sponsors/${props.event.id}/${sponsor.sponsor_file}`
  })) || []
})

const handleRemove = async (file) => {
  if (deletable.value) {
    try {
      await axios.post('/sponsor_image_upload/remove_file', {
        eventid: props.event.id,
        file_id: file.id,
        filename: file.name,
        destroy: true
      })

      $q.notify({
        type: 'positive',
        message: 'Sponsor Removed Successfully'
      })

      const sponsor = props.event.sponsors.find(
        s => s.sponsor_file === file.name && s.id === file.id
      )
      props.event.sponsors.splice(props.event.sponsors.indexOf(sponsor), 1)
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Sponsor Could Not be Removed'
      })
    }
  } else {
    deletable.value = true
  }
}

const handleSuccess = (info) => {
  const response = info.xhr.response ? JSON.parse(info.xhr.response) : {}
  
  if (response.filename && response.fileid) {
    // Ensure sponsors array exists
    if (!props.event.sponsors) {
      props.event.sponsors = []
    }
    
    props.event.sponsors.push({
      sponsor_file: response.filename,
      id: response.fileid
    })
  }
}

const onRejected = (rejectedEntries) => {
  $q.notify({
    type: 'negative',
    message: 'Only PNG, GIF, JPG and JPEG files allowed'
  })
  deletable.value = false
}
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 500;
}
</style>