<template>
  <q-page padding class="q-pl-md bg-grey-2">
    <q-breadcrumbs class="q-mb-md">
      <q-breadcrumbs-el :to="{ name: 'events' }">Events</q-breadcrumbs-el>
      <q-breadcrumbs-el
        v-if="state.event.id"
        :to="{ name: 'event-details', params: { id: state.event.id } }"
      >
        {{ state.event.title }}
      </q-breadcrumbs-el>
      <q-breadcrumbs-el v-else>New Event</q-breadcrumbs-el>
    </q-breadcrumbs>
    <div class="row">
      <!-- <ActsAsOrg :event="event" /> -->

      <!-- Event Live Status Banner -->
      <div v-if="isEventLive" class="col-12">
        <q-banner rounded class="bg-negative text-white q-ma-md">
          <template v-slot:avatar>
            <q-icon name="warning" color="white" />
          </template>
          This event is live. You are not allowed to make changes once an event
          has gone live.
        </q-banner>
      </div>

      <!-- <div class="col-10 q-mx-auto"> -->
      <div class="col-10 q-mx-auto">
        <StyledCard color="#1976d2" title="Event Information" icon="event">
          <div class="form-group required q-mb-md">
            <label for="evtitle">Event Title</label>
            <q-input
              id="evtitle"
              v-model="state.event.title"
              :readonly="isEventLive"
              :rules="[rules.required]"
              lazy-rules
              @update:model-value="setUpUrl"
              maxlength="150"
              bottom-slots
              :error="!!state.errors.title"
              :error-message="state.errors.title"
            />
          </div>

          <p class="text-weight-bold" v-if="state.event.id">
            Generate your own URL for the event here!
          </p>
          <p class="text-weight-bold" v-if="state.event.id">
            Type the text you would like to add to the URL below.
          </p>

          <div class="form-group q-mb-md" v-if="state.event.id">
            <label for="custom_url">Custom URL for event</label>
            <div class="url-input-group">
              <div class="url-prefix">
                www.servaceevents.co.uk/event/{{ state.event.id }}/
              </div>
              <q-input
                type="text"
                placeholder="my_event"
                id="custom_url"
                v-model="state.customUrlPrev"
                @blur="updateUrl"
                :readonly="isEventLive"
                :rules="[rules.customUrl]"
                lazy-rules
                maxlength="150"
                bottom-slots
                :error="!!state.errors.customUrl"
                :error-message="state.errors.customUrl"
              />
            </div>
          </div>

          <div v-if="state.customUrl" class="form-group q-mb-md">
            <p>Your URL is: {{ state.customUrl }}</p>
          </div>

          <div class="form-group required q-mb-md">
            <label for="eventType">Choose the type of Event</label>
            <q-select
              v-model="state.event.event_type_id"
              id="eventType"
              :options="eventTypeOptions"
              emit-value
              map-options
              :rules="[rules.required]"
              lazy-rules
              @update:model-value="clearEventTypeText"
              :error="!!state.errors.eventType"
              :error-message="state.errors.eventType"
              :readonly="isEventLive"
            />
          </div>

          <div
            v-if="state.event.event_type_id == otherTypeId"
            class="form-group required q-mb-md"
          >
            <label for="eventTypeName">Event Type Description</label>
            <q-input
              type="textarea"
              id="eventTypeName"
              v-model="state.event.event_type_name"
              :rules="[rules.required]"
              lazy-rules
              maxlength="250"
              rows="2"
              bottom-slots
              autogrow
              :error="!!state.errors.eventTypeName"
              :error-message="state.errors.eventTypeName"
              :readonly="isEventLive"
            />
          </div>

          <div class="row q-col-gutter-md">
            <div class="col-md-6 col-12">
              <div class="form-group required q-mb-md">
                <label for="organiser_email">Organiser Email</label>
                <q-input
                  type="email"
                  id="organiser_email"
                  v-model="state.event.organiser_email"
                  :rules="[rules.required, rules.email]"
                  lazy-rules
                  bottom-slots
                  :error="!!state.errors.organiserEmail"
                  :error-message="state.errors.organiserEmail"
                  :readonly="isEventLive"
                />
              </div>
            </div>
            <div class="col-md-6 col-12">
              <div class="form-group required q-mb-md">
                <label for="organiser">Organiser Name</label>
                <q-input
                  type="text"
                  id="organiser"
                  v-model="state.event.organiser"
                  :rules="[rules.required]"
                  lazy-rules
                  bottom-slots
                  :error="!!state.errors.organiser"
                  :error-message="state.errors.organiser"
                  :readonly="isEventLive"
                />
              </div>
            </div>
          </div>
        </StyledCard>

        <StyledCard
          v-if="state.event.ticket_type !== 'virtual'"
          class="q-mt-md"
          color="#00bcd4"
          title="Location & Venue"
          icon="place"
        >
          <div class="row q-col-gutter-md" v-if="!state.event.remove_location">
            <div class="col-12">
              <div class="form-group required q-mb-md">
                <label for="location">Venue Name</label>
                <q-input
                  id="location"
                  v-model="state.event.location"
                  :rules="[rules.required]"
                  lazy-rules
                  maxlength="100"
                  bottom-slots
                  :error="!!state.errors.location"
                  :error-message="state.errors.location"
                  :readonly="isEventLive"
                />
              </div>
            </div>

            <!-- <div class="col-12 col-md-6" v-if="!state.event.international">
              <div class="form-group q-mb-md">
                <label for="houseNo">Building Number</label>
                <q-input id="houseNo" v-model="houseNo" placeholder="Building Number" />
              </div>
            </div> -->

            <template
              v-if="
                (state.event.event_address &&
                  state.event.event_address.address1) ||
                state.event.international ||
                showAddress
              "
            >
              <div class="col-12 col-md-6">
                <div class="form-group required q-mb-md">
                  <label for="address1">Address1</label>
                  <q-input
                    id="address1"
                    v-model="state.event.event_address.address1"
                    :rules="[rules.required]"
                    lazy-rules
                    bottom-slots
                    :error="!!state.errors.address1"
                    :error-message="state.errors.address1"
                    :readonly="isEventLive"
                  />
                </div>
              </div>

              <div class="col-12 col-md-6">
                <div class="form-group q-mb-md">
                  <label for="address2">Address2</label>
                  <q-input
                    id="address2"
                    v-model="state.event.event_address.address2"
                    :readonly="isEventLive"
                  />
                </div>
              </div>

              <div class="col-12 col-md-6">
                <div class="form-group required q-mb-md">
                  <label for="city">Town/City</label>
                  <q-input
                    id="city"
                    v-model="state.event.event_address.city"
                    :rules="[rules.required]"
                    lazy-rules
                    bottom-slots
                    :error="!!state.errors.city"
                    :error-message="state.errors.city"
                    :readonly="isEventLive"
                  />
                </div>
              </div>

              <div class="col-12 col-md-6" v-if="!state.event.international">
                <div class="form-group q-mb-md">
                  <label for="postcode">Postcode</label>
                  <q-input
                    id="postcode"
                    v-model="state.event.event_address.postcode"
                    :rules="[rules.ukPostcode]"
                    lazy-rules
                    @blur="changePostcode"
                    bottom-slots
                    :error="!!state.errors.postcode"
                    :error-message="state.errors.postcode"
                    :readonly="isEventLive"
                  />
                </div>
              </div>

              <!-- <div class="col-12" v-if="!state.event.international && postcodeDetails.length > 1">
              <div class="form-group q-mb-md">
                <label for="postcodeSelect">Select Address</label>
                <q-select id="postcodeSelect" v-model="postcodeAddress" :options="postcodeDetails"
                  @update:model-value="postcodeOption" label="Select Address" />
              </div>
            </div> -->

              <div class="col-12 col-md-6">
                <div class="form-group q-mb-md">
                  <label for="county">County/Region</label>
                  <q-input
                    id="county"
                    v-model="state.event.event_address.county"
                    :readonly="isEventLive"
                  />
                </div>
              </div>

              <template v-if="state.event.international">
                <div class="col-12 col-md-6">
                  <div class="form-group q-mb-md">
                    <label for="postCode">Area Code</label>
                    <q-input
                      id="postcode"
                      v-model="state.event.event_address.postcode"
                      placeholder="Area Code"
                      @blur="changePostcode"
                      :readonly="isEventLive"
                    />
                  </div>
                </div>

                <div class="col-12 col-md-6">
                  <div class="form-group q-mb-md">
                    <label for="countries">Country</label>
                    <q-select
                      v-model="state.event.event_address.country_code"
                      :options="countries"
                      option-label="name"
                      option-value="code"
                      :rules="[rules.required]"
                      lazy-rules
                      emit-value
                      map-options
                      bottom-slots
                      :error="!!state.errors.country"
                      :error-message="state.errors.country"
                      :readonly="isEventLive"
                    />
                  </div>
                </div>
              </template>
            </template>

            <div
              class="col-12 col-md-6"
              v-if="
                !state.event.international &&
                !state.event.remove_location &&
                state.event.event_address.postcode &&
                state.event.event_address.postcode.trim() !== ''
              "
            >
              <div style="width: 100%; height: 300px">
                <Maps :postcode="state.event.event_address.postcode" />
              </div>
            </div>
          </div>
        </StyledCard>

        <StyledCard
          class="q-mt-md"
          color="#4caf50"
          title="Date & Time"
          icon="schedule"
        >
          <div class="row q-col-gutter-sm justify-start items-center">
            <div class="col-12">
              <q-checkbox
                v-model="sameDayEvent"
                label="Same Day?"
                :disable="isEventLive"
              />
            </div>

            <!-- Date Picker -->
            <div class="col-md-6 col-12">
              <div class="text-subtitle1 text-weight-bold">
                {{ sameDayEvent ? "Select Date" : "Select Start Date" }}
              </div>
              <div class="form-group required q-my-md">
                <label :for="sameDayEvent ? 'dateFrom' : 'startDate'">
                  {{ sameDayEvent ? "Event Date" : "Start Date" }}
                </label>
                <VueDatePicker
                  v-model="datefrom"
                  model-type="format"
                  format="dd/MM/yyyy"
                  input-class-name="date-picker"
                  :enable-time-picker="false"
                  :readonly="isEventLive"
                />
                <div
                  v-if="state.errors.startDate"
                  class="text-negative q-mt-sm"
                >
                  {{ state.errors.startDate }}
                </div>
              </div>
            </div>

            <!-- Start Time Picker -->
            <div v-if="sameDayEvent" class="col-md-6 col-12">
              <div class="text-subtitle1 text-weight-bold">
                Select Start Time
              </div>
              <div class="form-group required q-my-md">
                <label for="timeFrom">Start Time</label>
                <VueDatePicker
                  v-model="timefrom"
                  model-type="format"
                  format="HH:mm"
                  input-class-name="date-picker"
                  :enable-time-picker="true"
                  :enable-date-picker="false"
                  :is24="true"
                  minutes-increment="5"
                  time-picker
                  :readonly="isEventLive"
                />
                <div
                  v-if="state.errors.startTime"
                  class="text-negative q-mt-sm"
                >
                  {{ state.errors.startTime }}
                </div>
              </div>
            </div>

            <!-- End Time Picker -->
            <div v-if="sameDayEvent" class="col-md-6 col-12">
              <div class="text-subtitle1 text-weight-bold">Select End Time</div>
              <div class="form-group required q-my-md">
                <label for="timeTo">End Time</label>
                <VueDatePicker
                  v-model="timeto"
                  model-type="format"
                  format="HH:mm"
                  input-class-name="date-picker"
                  :enable-time-picker="true"
                  :enable-date-picker="false"
                  :is24="true"
                  minutes-increment="5"
                  time-picker
                  :readonly="isEventLive"
                />
                <div v-if="state.errors.endTime" class="text-negative q-mt-sm">
                  {{ state.errors.endTime }}
                </div>
              </div>
            </div>

            <!-- End Date Picker -->
            <div v-if="!sameDayEvent" class="col-md-6 col-12">
              <div class="text-subtitle1 text-weight-bold">Select End Date</div>
              <div class="form-group required q-my-md">
                <label for="dateTo">End Date</label>
                <VueDatePicker
                  v-model="dateto"
                  model-type="format"
                  format="dd/MM/yyyy"
                  input-class-name="date-picker"
                  :enable-time-picker="false"
                  :readonly="isEventLive"
                />
                <div v-if="state.errors.endDate" class="text-negative q-mt-sm">
                  {{ state.errors.endDate }}
                </div>
              </div>
            </div>
          </div>
        </StyledCard>

        <StyledCard
          class="q-mt-md"
          color="#673ab7"
          title="Event Description"
          icon="description"
        >
          <div class="col-12" style="padding-left: 0px">
            <h6 class="text-subtitle1 text-weight-bold">
              Enter details about your event here to show on your email
              invitations and event webpage
              <span style="color: red">*</span>
            </h6>
            <q-editor
              v-model="state.event.details"
              min-height="200px"
              :rules="[(val) => !!val || 'Event description is required']"
              :class="{ 'editor-invalid': state.errors.description }"
              :readonly="isEventLive"
            />
            <div v-if="state.errors.description" class="text-negative q-mt-sm">
              {{ state.errors.description }}
            </div>
          </div>
        </StyledCard>

        <EventTags
          :event="state.event"
          class="q-mt-md"
          @update:tags="updateEventTags"
        />

        <StyledCard
          class="q-mt-md hg-topline"
          color="#2e7d32"
          title="Save Event"
          icon="save"
        >
          <div class="text-center">
            <div class="col-md-8 col-12 q-mx-auto q-mb-md">
              <q-btn
                color="primary"
                size="lg"
                :label="isNewEvent ? 'Create Event' : 'Save Changes'"
                @click="validateDetails"
                :loading="state.isSaving"
                :disable="isEventLive"
              />
            </div>
          </div>
        </StyledCard>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useEventStore } from "@/stores/event";
import axios from "axios";
import ActsAsOrg from "./ActsAsOrg.vue";
import EventTags from "./EventTags.vue";
import Maps from "@/common/maps.vue";
import StyledCard from "@/common/StyledCard.vue";
// import Sponsors from "./Sponsors.vue";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import utc from "dayjs/plugin/utc";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { useQuasar } from "quasar";
import { storeToRefs } from "pinia";

// Extend dayjs with required plugins
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(utc);

const router = useRouter();
const route = useRoute();
const eventStore = useEventStore();
const { event } = storeToRefs(eventStore);

const $q = useQuasar();

// State using Context7 pattern
const state = reactive({
  event: {
    id: null,
    title: "",
    event_type_id: "",
    event_type_name: "",
    event_types: [],
    organiser: "",
    organiser_email: "",
    details: "",
    summary: "",
    custom_url: "",
    location: "",
    datetimefrom: null,
    datetimeto: null,
    close_date: null,
    is_public: false,
    live: false,
    temp_flag: false,
    remove_location: false,
    international: false,
    tags: [],
    event_address: {
      id: null,
      address1: "",
      address2: "",
      city: "",
      county: "",
      postcode: "",
      country_code: "",
      latitude: null,
      longitude: null,
    },
  },
  customUrlPrev: "",
  customUrl: "",
  isSaving: false,
  isLoading: true,
  errors: {},
  touchedFields: {},
  formSubmitted: false,
});

const props = defineProps({
  id: {
    type: String,
    default: null,
  },
});

const houseNo = ref(null);
const postcodeDetails = ref([]);
const postcodeAddress = ref(null);
const showAddress = ref(true);
const datefrom = ref(null);
const dateto = ref(null);
const timefrom = ref(null);
const timeto = ref(null);
const sameDayEvent = ref(false);

const rules = {
  required: (val) => !!val || "This field is required",
  email: (val) => {
    const emailPattern =
      /^(?=[a-zA-Z0-9@._%+-]{6,254}$)[a-zA-Z0-9._%+-]{1,64}@(?:[a-zA-Z0-9-]{1,63}\.){1,8}[a-zA-Z]{2,63}$/;
    return emailPattern.test(val) || "Please enter a valid email address";
  },
  ukPostcode: (val) => {
    if (
      !state.event.ticket_type ||
      state.event.ticket_type === "virtual" ||
      state.event.remove_location ||
      state.event.international
    )
      return true;
    if (!val) return "UK postcode is required";
    const postcodePattern = /^[a-z]{1,2}[0-9][a-z0-9]?\s?[0-9][a-z]{2}$/i;
    return postcodePattern.test(val) || "Please enter a valid UK postcode";
  },
  customUrl: (val) => {
    if (!val) return true;
    const urlPattern = /^[a-z0-9-]+$/;
    return (
      urlPattern.test(val) ||
      "URL can only contain lowercase letters, numbers, and hyphens"
    );
  },
  minLength: (val, length) =>
    !val || val.length >= length || `Minimum ${length} characters required`,
  maxLength: (val, length) =>
    !val || val.length <= length || `Maximum ${length} characters allowed`,
  dateNotInPast: (val) => {
    if (!val) return true;
    const now = dayjs();
    const date = dayjs(val);
    return date.isSameOrAfter(now) || "Date cannot be in the past";
  },
};

const markFieldAsTouched = (fieldName) => {
  state.touchedFields[fieldName] = true;
};

const shouldShowError = (fieldName) => {
  return state.formSubmitted || state.touchedFields[fieldName];
};

const isNewEvent = computed(() => !state.event.id);
const isEventLive = computed(() => state.event.live === true);
const advanced = computed(() => eventStore.isAdvancedUser);

const otherTypeId = computed(() => {
  const otherType = state.event?.event_types?.find(
    (type) => type.name === "Other"
  );
  return otherType?.id;
});

const eventTypeOptions = computed(() => {
  const types =
    state.event?.event_types?.map((type) => ({
      value: type.id,
      label: type.name,
    })) || [];

  return [{ value: "", label: "- Please Choose -" }, ...types];
});

const countries = ref([]);

const validateDetails = async () => {
  state.formSubmitted = true;
  state.isSaving = true;
  state.errors = {};

  try {
    updateDates();
    console.log(state.event.datetimefrom, state.event.datetimeto);

    if (!state.event.title) {
      state.errors.title = "Event title is required";
    }

    if (!state.event.event_type_id) {
      state.errors.eventType = "Event type is required";
    } else if (
      state.event.event_type_id === otherTypeId.value &&
      !state.event.event_type_name
    ) {
      state.errors.eventTypeName =
        'Event type description is required for "Other" event types';
    }

    if (!state.event.organiser_email) {
      state.errors.organiserEmail = "Organiser email is required";
    } else {
      const emailValidation = rules.email(state.event.organiser_email);
      if (emailValidation !== true) {
        state.errors.organiserEmail = emailValidation;
      }
    }

    if (!state.event.organiser) {
      state.errors.organiser = "Organiser name is required";
    }

    if (!state.event.details) {
      state.errors.description = "Event description is required";
    }

    if (!datefrom.value) {
      state.errors.startDate = "Start date is required";
    }

    if (!dateto.value) {
      state.errors.endDate = "End date is required";
    }

    const now = dayjs();
    const eventStart = dayjs(state.event.datetimefrom);
    const eventEnd = dayjs(state.event.datetimeto);
    const closeDate = state.event.close_date
      ? dayjs(state.event.close_date)
      : null;

    if (eventEnd.isBefore(eventStart)) {
      state.errors.endDate = "The end date must be after the start date";
    }

    if (!state.event.live) {
      if (eventEnd.isSameOrBefore(now.subtract(1, "day"))) {
        state.errors.endDate = "Event end date can't be in the past";
      }
      if (closeDate && closeDate.isSameOrBefore(now.subtract(1, "day"))) {
        state.errors.closeDate = "Close date can't be in the past";
      }
      if (now.isSameOrAfter(eventStart, "day")) {
        state.errors.startDate = "Start date can't be in the past";
      }
    }

    if (closeDate && closeDate.isSameOrAfter(eventEnd, "day")) {
      state.errors.closeDate = "The close date must be before the event ends";
    }

    if (state.event.ticket_type !== "virtual" && !state.event.remove_location) {
      if (!state.event.location) {
        state.errors.location = "Venue name is required";
      }

      if (!state.event.international) {
        if (!state.event.event_address.postcode) {
          state.errors.postcode = "UK postcode is required";
        } else {
          const postcodeValidation = rules.ukPostcode(
            state.event.event_address.postcode
          );
          if (postcodeValidation !== true) {
            state.errors.postcode = postcodeValidation;
          }
        }
      }

      if (!state.event.event_address.address1) {
        state.errors.address1 = "Address line 1 is required";
      }

      if (!state.event.event_address.city) {
        state.errors.city = "City is required";
      }

      if (
        state.event.international &&
        !state.event.event_address.country_code
      ) {
        state.errors.country = "Country is required";
      }
    }

    if (state.customUrlPrev && !rules.customUrl(state.customUrlPrev)) {
      state.errors.customUrl =
        "URL can only contain lowercase letters, numbers, and hyphens";
    }

    if (Object.keys(state.errors).length > 0) {
      $q.notify({
        type: "negative",
        message: "Please correct the highlighted errors",
        position: "top-right",
        timeout: 3000,
      });
      return;
    }

    const payload = { ...state.event };

    if (state.customUrlPrev) {
      payload.custom_url = state.customUrlPrev;
    }

    if (payload.close_date) {
      payload.close_date = dayjs(payload.close_date)
        .add(1, "hour")
        .toISOString();
    }

    if (payload.event_address) {
      payload.event_address_attributes = {
        id: payload.event_address.id,
        address1: payload.event_address.address1,
        address2: payload.event_address.address2,
        city: payload.event_address.city,
        county: payload.event_address.county,
        country_code: payload.event_address.country_code,
        postcode: payload.event_address.postcode,
        latitude: payload.event_address.latitude,
        longitude: payload.event_address.longitude,
      };
    }

    let response;

    if (isNewEvent.value) {
      response = await axios.post("/event_details.json", { event: payload });
    } else {
      response = await axios.put(`/event_details/${state.event.id}`, {
        event: payload,
      });
    }

    if (response.data && response.data.event) {
      $q.notify({
        type: "positive",
        message: "Event details saved successfully!",
        position: "top-right",
        timeout: 2000,
      });

      eventStore.setEvent(response.data.event);
      setTimeout(() => {
        router.push({
          name: "ticket-creation",
          params: { eventId: response.data.event.id },
        });
      }, 100);
    }
  } catch (error) {
    console.error("Failed to save event details", error);

    if (error.message !== "Validation failed") {
      $q.notify({
        type: "negative",
        message:
          error.response?.data?.error ||
          "Failed to save event details. Please try again.",
        position: "top-right",
        timeout: 3000,
      });
    }
  } finally {
    state.isSaving = false;
  }
};

function toISODate(dateStr, timeOfDay = "start", timeStr) {
  let baseDate = dayjs(dateStr, "DD/MM/YYYY");

  if (!baseDate.isValid()) {
    throw new Error("Invalid date format. Expected DD/MM/YYYY.");
  }

  if (timeStr) {
    const [hour, minute = 0, second = 0] = timeStr.split(":").map(Number);
    baseDate = baseDate.hour(hour).minute(minute).second(second).millisecond(0);
  } else {
    baseDate =
      timeOfDay === "end" ? baseDate.endOf("day") : baseDate.startOf("day");
  }

  return baseDate.utc().toISOString();
}

const updateDates = () => {
  if (sameDayEvent.value) {
    // Combine date and time for same-day events
    if (datefrom.value && timefrom.value) {
      state.event.datetimefrom = toISODate(
        datefrom.value,
        "start",
        timefrom.value
      );
    }
    if (datefrom.value && timeto.value) {
      state.event.datetimeto = toISODate(datefrom.value, "end", timeto.value);
    }
  } else {
    // Set start and end dates with default times for multi-day events
    if (datefrom.value) {
      state.event.datetimefrom = toISODate(datefrom.value, "start");
    }
    if (dateto.value) {
      state.event.datetimeto = toISODate(dateto.value, "end");
    }
  }
};

const changePostcode = () => {
  if (state.event.event_address.postcode) {
    state.event.event_address.postcode =
      state.event.event_address.postcode.toUpperCase();
  }
};

const clearEventTypeText = () => {
  if (state.event.event_type_id !== otherTypeId.value) {
    state.event.event_type_name = null;
  }
};

const updateEventTags = (tags) => {
  state.event.tags = tags;
};

const setUpUrl = () => {
  if (!state.event.title) return;

  const urlFriendlyTitle = state.event.title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");

  state.customUrlPrev = urlFriendlyTitle;
  updateUrl();
};

const updateUrl = () => {
  if (state.customUrlPrev) {
    state.customUrl = `www.servaceevents.co.uk/event/${state.event.id}/${state.customUrlPrev}`;
  } else {
    state.customUrl = "";
  }
};

onMounted(async () => {
  try {
    const eventTypesResponse = await axios.get("/event_details/event_types");
    if (eventTypesResponse.data.event_types) {
      state.event.event_types = eventTypesResponse.data.event_types;
    }

    if (route.name === "create-event") {
      eventStore.clearEvent();
      state.event = {
        ...state.event,
        title: "",
        event_type_id: "",
        organiser_email: "",
        organiser: "",
        event_tags: [],
        ticket_type: "physical",
        event_address: {
          address1: "",
          address2: "",
          city: "",
          county: "",
          postcode: "",
          country_code: "",
        },
      };
      return;
    }

    const eventId = props.eventId || route.params.eventId;
    if (eventId && eventId !== "new") {
      $q.loading.show({
        message: "Loading event details...",
      });
      try {
        // Use eventStore's ensureEventLoaded method which automatically loads tickets
        // This ensures menu items become available immediately
        const success = await eventStore.ensureEventLoaded(eventId);

        if (success) {
          // Get the loaded event from the store
          const storeEvent = JSON.parse(JSON.stringify(event.value));
          if (storeEvent) {
            // Copy event data from store to local state
            // Object.assign(state.event, event);
            state.event = storeEvent;

            // Handle event address
            const eventAddress =
              event.event_address || state.event.event_address;
            if (eventAddress) {
              state.event.event_address = {
                id: eventAddress.id || null,
                address1: eventAddress.address1 || "",
                address2: eventAddress.address2 || "",
                city: eventAddress.city || "",
                county: eventAddress.county || "",
                postcode: eventAddress.postcode || "",
                country_code: eventAddress.country_code || "",
                latitude: eventAddress.latitude || null,
                longitude: eventAddress.longitude || null,
              };
            }

            // Handle URL setup
            if (state.event.custom_url) {
              state.customUrlPrev = state.event.custom_url;
              updateUrl();
            } else {
              setUpUrl();
            }

            // Handle date formatting
            if (state.event.datetimefrom) {
              datefrom.value = dayjs(state.event.datetimefrom).format(
                "DD/MM/YYYY"
              );
              timefrom.value = dayjs(state.event.datetimefrom).format("HH:mm");
            }
            if (state.event.datetimeto) {
              dateto.value = dayjs(state.event.datetimeto).format("DD/MM/YYYY");
              timeto.value = dayjs(state.event.datetimeto).format("HH:mm");
            }
            // Determine if the event is a same-day event
            if (
              state.event.datetimefrom &&
              state.event.datetimeto &&
              dayjs(state.event.datetimefrom).isSame(
                state.event.datetimeto,
                "day"
              )
            ) {
              sameDayEvent.value = true;
            } else {
              sameDayEvent.value = false;
            }
          }
        } else {
          $q.notify({
            type: "negative",
            message: "Failed to load event details",
            position: "top",
            timeout: 5000,
          });
        }
      } catch (error) {
        console.error("Error loading event:", error);
        $q.notify({
          type: "negative",
          message: "Failed to load event details",
          position: "top",
          timeout: 5000,
        });
      } finally {
        $q.loading.hide();
      }
    }

    //TODO: Add an api controller to return countries array
    try {
      const countriesResponse = await axios.get("/api/v1/countries");
      countries.value = countriesResponse.data;
    } catch (error) {
      console.error(
        "Failed to load countries, defaulting to empty list",
        error
      );
      countries.value = [];
    }

    state.isLoading = false;
  } catch (error) {
    console.error("Failed to initialize component", error);
    $q.notify({
      type: "negative",
      message: "Failed to load initial data",
      position: "top-right",
      timeout: 3000,
    });
  }
});

watch(datefrom, () => {
  console.log(datefrom.value, dateto.value);
  if (datefrom.value && !dateto.value && !sameDayEvent.value) {
    dateto.value = dayjs(datefrom.value).add(1, "day").format("YYYY-MM-DD");
  }
});

watch(timefrom, () => {
  console.log(timefrom.value, timeto.value);
  if (timefrom.value && !timeto.value && sameDayEvent.value) {
    timeto.value = dayjs(timefrom.value).add(1, "hour").format("HH:mm");
  }
});
</script>

<style scoped></style>
