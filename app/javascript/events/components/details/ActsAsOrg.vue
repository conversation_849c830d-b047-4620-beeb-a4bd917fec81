<template>
  <q-dialog
    v-model="dialogVisible"
    persistent
    :maximized="$q.screen.lt.sm"
    transition-show="scale"
    transition-hide="scale"
  >
    <q-card style="width: 700px; max-width: 90vw;">
      <q-card-section class="row items-center">
        <div class="text-h6">Do you wish to create an event for another organisation?</div>
        <q-space />
      </q-card-section>

      <q-card-section>
        <q-input
          v-model="orgText"
          label="Search for organisation"
          placeholder="Search for organisation"
          @update:model-value="findOrg"
          maxlength="30"
        >
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>

        <p v-if="organisations" class="q-mt-md">
          Choose one of the organisations from the list below
        </p>
      </q-card-section>

      <q-card-section v-if="organisations" class="scroll" style="max-height: 200px">
        <q-list bordered separator>
          <q-item 
            v-for="org in organisations" 
            :key="org.id" 
            clickable
            @click="chooseOrg(org)"
          >
            <q-item-section>{{ org.name }}</q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-card-section v-if="organisations">
        <div class="text-subtitle2 q-mb-sm">Do you wish this event to be basic or advanced?</div>
        <q-option-group
          v-model="advancedUser"
          :options="[
            { label: 'Basic', value: false },
            { label: 'Advanced', value: true }
          ]"
          color="primary"
          inline
        />
      </q-card-section>

      <q-card-actions align="center" class="q-pa-md">
        <q-btn 
          color="primary" 
          label="Create as Client" 
          @click="createOrg" 
        />
        <q-btn 
          flat 
          color="grey-7" 
          label="Create as HG" 
          @click="cancel" 
          class="q-ml-sm" 
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { useEventBus } from '@vueuse/core'
import { useEventStore } from '@/stores/event'

const $q = useQuasar()
const eventStore = useEventStore()
const { emit: emitEvent } = useEventBus('changeOrgId')

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
})

const dialogVisible = ref(false)
const orgText = ref('')
const org = ref(null)
const organisations = ref(null)
const advancedUser = ref(false)

onMounted(() => {
  if (window.admin_user && props.event.temp_flag && !props.event.org_chosen) {
    showModal()
  }
})

const findOrg = async () => {
  if (orgText.value.length >= 3) {
    try {
      const response = await axios.get(`/get_organisations/${orgText.value}`)
      organisations.value = response.data.organisations
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Please try another organisation name'
      })
    }
  }
}

const chooseOrg = (selectedOrg) => {
  org.value = selectedOrg
  orgText.value = selectedOrg.name
}

const createOrg = () => {
  props.event.show_vat = org.value.vat_enabled
  props.event.organisation_id = org.value.id
  
  $q.notify({
    type: 'positive',
    message: `Building event as ${org.value.name}`
  })

  props.event.org_chosen = true
  eventStore.setAdvancedUser(advancedUser.value)
  hideModal()
  emitEvent('changeOrgId')
}

const showModal = () => {
  dialogVisible.value = true
}

const hideModal = () => {
  dialogVisible.value = false
}

const cancel = () => {
  hideModal()
}
</script>

<style scoped>
.scroll {
  overflow-y: auto;
}
</style>