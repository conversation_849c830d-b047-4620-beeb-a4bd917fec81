<template>
  <q-page padding>
    <q-card
      flat
      bordered
      :style="{ borderLeft: `4px solid ${event?.phcolour || '#1976d2'}` }"
    >
      <q-card-section>
        <div class="text-h5 q-mb-lg">
          <q-icon name="image" class="q-mr-sm" />
          Event Images
        </div>

        <div class="row q-gutter-md">
          <!-- Logo Upload Section -->
          <div class="col-12 col-md-6">
            <q-card flat bordered class="q-pa-md">
              <q-card-section class="q-pa-none">
                <div class="text-h6 q-mb-md">
                  <q-icon name="business" class="q-mr-sm" color="primary" />
                  Event Logo
                </div>

                <q-uploader
                  v-if="event?.id"
                  :url="`/events_logo_upload/${event.id}`"
                  field-name="file"
                  accept="image/*"
                  :headers="headers"
                  label="Event Logo"
                  @removed="deleteLogo"
                  @uploaded="logoAdded"
                  @rejected="onRejectedLogo"
                  @failed="onUploadFailed"
                  max-files="1"
                  :max-file-size="2 * 1024 * 1024"
                  color="primary"
                  flat
                  bordered
                  auto-upload
                  class="full-width"
                />

                <div v-if="event?.image1" class="q-mt-md text-center">
                  <q-img
                    :src="`https://s3-eu-west-1.amazonaws.com/${imageBucket}/${
                      event.id
                    }/${event.image1}?${Math.random()}`"
                    style="max-width: 300px; max-height: 300px"
                    fit="contain"
                    class="rounded-borders"
                  >
                    <div class="absolute-top-right q-pa-xs">
                      <q-btn
                        round
                        color="negative"
                        icon="delete"
                        size="sm"
                        @click="deleteLogo"
                      />
                    </div>
                  </q-img>
                  <div class="text-caption q-mt-sm text-grey-7">
                    Current Logo
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Event Image Upload Section -->
          <div class="col-12 col-md-6">
            <q-card flat bordered class="q-pa-md">
              <q-card-section class="q-pa-none">
                <div class="text-h6 q-mb-md">
                  <q-icon name="photo" class="q-mr-sm" color="secondary" />
                  Event Image
                </div>

                <q-uploader
                  v-if="event?.id"
                  :url="`/events_image_upload/${event.id}`"
                  field-name="file"
                  accept="image/*"
                  :headers="headers"
                  label="Event Image"
                  @removed="deleteImage"
                  @uploaded="imageAdded"
                  @rejected="onRejectedImage"
                  @failed="onUploadFailed"
                  max-files="1"
                  :max-file-size="4 * 1024 * 1024"
                  color="secondary"
                  flat
                  bordered
                  auto-upload
                  class="full-width"
                />

                <div v-if="event?.image2" class="q-mt-md text-center">
                  <q-img
                    :src="`https://s3-eu-west-1.amazonaws.com/${imageBucket}/${
                      event.id
                    }/${event.image2}?${Math.random()}`"
                    style="max-width: 300px; max-height: 300px"
                    fit="contain"
                    class="rounded-borders"
                  >
                    <div class="absolute-top-right q-pa-xs">
                      <q-btn
                        round
                        color="negative"
                        icon="delete"
                        size="sm"
                        @click="deleteImage"
                      />
                    </div>
                  </q-img>
                  <div class="text-caption q-mt-sm text-grey-7">
                    Current Event Image
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useEventStore } from "@/stores/event";
import { useQuasar } from "quasar";
import axios from "axios";

const route = useRoute();
const eventStore = useEventStore();
const $q = useQuasar();

const deletable = ref(true);
const imageBucket = ref(window.appImageBucket);

// Get eventId from route parameters
const eventId = computed(() => route.params.eventId);

// Get event from store
const event = computed(() => eventStore.getEvent);

// Ensure event is loaded when component mounts
onMounted(async () => {
  if (eventId.value) {
    await eventStore.ensureEventLoaded(eventId.value);
    console.log(eventStore.event);
  }
});

const headers = [
  {
    name: "X-CSRF-Token",
    value: document
      .querySelector('meta[name="csrf-token"]')
      ?.getAttribute("content"),
  },
];

const imageAdded = (info) => {
  console.log("Image upload successful:", info);
  const file = info.files[0];
  event.value.image2 = file.name.replace(/[(\/,()\ ]/g, "_");

  $q.notify({
    type: "positive",
    message: "Event image uploaded successfully",
    timeout: 2000,
  });
};

const logoAdded = (info) => {
  console.log("Logo upload successful:", info);
  const file = info.files[0];
  event.value.image1 = file.name.replace(/[(\/,()\ ]/g, "_");

  $q.notify({
    type: "positive",
    message: "Logo uploaded successfully",
    timeout: 2000,
  });
};

const onRejectedLogo = () => {
  $q.notify({
    type: "negative",
    message: "Logo file rejected. Check file type and size.",
    timeout: 3000,
  });
};

const onRejectedImage = () => {
  $q.notify({
    type: "negative",
    message: "Image file rejected. Check file type and size.",
    timeout: 3000,
  });
};

const onUploadFailed = (info) => {
  console.error("Upload failed:", info);
  $q.notify({
    type: "negative",
    message: `Upload failed: ${info.xhr?.statusText || "Unknown error"}`,
    timeout: 5000,
  });
};

const deleteLogo = () => {
  if (event.value.image1 && deletable.value) {
    axios
      .post("/events_image_delete", {
        eventid: event.value.id,
        image_file: event.value.image1,
        logo: true,
      })
      .then(() => {
        event.value.image1 = null;
        $q.notify({
          type: "positive",
          message: "Logo Image Removed",
          timeout: 2000,
        });
      });
  } else {
    deletable.value = true;
  }
};

const deleteImage = () => {
  if (event.value.image2 && deletable.value) {
    axios
      .post("/events_image_delete", {
        eventid: event.value.id,
        image_file: event.value.image2,
      })
      .then(() => {
        event.value.image2 = null;
        $q.notify({
          type: "positive",
          message: "Image Removed",
          timeout: 2000,
        });
      });
  } else {
    deletable.value = true;
  }
};
</script>

<style scoped>
.full-width {
  width: 100%;
}
</style>
