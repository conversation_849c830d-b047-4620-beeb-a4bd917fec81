<template>
  <div class="row" v-if="hasEvent">
    <div class="col-10 q-mx-auto">
      <q-card>
        <q-card-section>
          <div class="q-mb-md">
            <div class="text-subtitle1">Banner Image</div>
            <q-uploader
              class="full-width"
              accept=".jpg, .jpeg, .png"
              :max-file-size="500 * 1024"
              url="/api/upload"
              color="primary"
              flat
              bordered
              @uploaded="handleSuccess"
              @failed="handleError"
              @rejected="handleRejected"
            >
              <template v-slot:header="scope">
                <div class="row no-wrap items-center q-pa-sm">
                  <div class="col text-center">
                    <div class="q-uploader__title">Drop file here or click to upload</div>
                    <div class="q-uploader__subtitle text-grey-7">
                      jpg/png files with a size less than 500kb
                    </div>
                  </div>
                  <q-btn
                    v-if="scope.queuedFiles.length > 0"
                    icon="clear_all"
                    round
                    dense
                    flat
                    @click="scope.removeQueuedFiles"
                  />
                </div>
              </template>
            </q-uploader>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
  <div v-else class="row">
    <div class="col-10 q-mx-auto">
      <q-card>
        <q-card-section class="text-center">
          <h2>Create a New Event</h2>
          <p>Please fill out the event details below to get started.</p>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { eventStore } from '../../stores/event'
import { useQuasar } from 'quasar'

const $q = useQuasar()

// Using Context7-style eventStore instead of Pinia
const event = computed(() => eventStore.getEvent())

// Check if we have a valid event object with an ID
const hasEvent = computed(() => event.value && event.value.id)

const handleSuccess = (info) => {
  // Extract URL from response - adjust this based on your API response structure
  const url = info?.xhr?.response ? JSON.parse(info.xhr.response).url : null
  
  if (url && event.value) {
    // Update event with the new banner URL
    const updatedEvent = { ...event.value, banner: url }
    eventStore.setEvent(updatedEvent)
  }
}

const handleError = (err) => {
  console.error('Upload failed:', err)
  $q.notify({
    color: 'negative',
    message: 'Upload failed. Please try again.',
    icon: 'error'
  })
}

const handleRejected = (rejectedEntries) => {
  // Display why files were rejected (size/type)
  $q.notify({
    type: 'negative',
    message: `${rejectedEntries[0]?.failedPropValidation === 'max-file-size' 
      ? 'Image must be smaller than 500KB!' 
      : 'Please upload only jpg/png images'}`
  })
}
</script>

<style scoped>
.full-width {
  width: 100%;
}
</style>