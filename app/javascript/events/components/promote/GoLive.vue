<template>
  <div v-if="event">
    <div class="col-12 q-mx-auto q-mt-md">
      <q-card v-if="!event.live">
        <q-card-section class="text-h6 hg-underline">
          Would you like this event to be public or private?
        </q-card-section>

        <q-card-section>
          <q-form>
            <q-checkbox
              v-model="event.is_public"
              id="public_event"
              label="Tick the box if you want your event to be public"
              color="primary"
            />
          </q-form>

          <div class="q-mt-md">
            <strong>Event Status:</strong>
            <span :class="event.is_public ? 'text-positive' : 'text-negative'">
              <q-icon :name="event.is_public ? 'people' : 'person'" class="q-mr-xs" />
              {{ event.is_public ? 'Public Event' : 'Private Event' }}
            </span>
          </div>

          <legal-terms></legal-terms>

          <q-form>
            <q-checkbox
              v-model="legalTermsSelected"
              id="legalterms"
              label="Please accept the terms and conditions to make your event live"
              color="primary"
            />
          </q-form>
        </q-card-section>
      </q-card>

      <q-card v-if="event.live">
        <q-card-section class="text-h6 hg-underline">
          This event is currently live!
        </q-card-section>
        <q-card-section>
          <p>
            <strong>Please Note:</strong> No further changes can be made to a live event once an attendee has registered.
          </p>
        </q-card-section>
      </q-card>

      <q-card v-if="event.live && event.is_public">
        <q-card-section class="text-h6 hg-underline">
          Click the buttons below to share your event!
        </q-card-section>
        <q-card-section>
          <social-circles :event="event"></social-circles>
        </q-card-section>
      </q-card>
    </div>

    <div class="col-12 q-mx-auto">
      <q-card class="primary-top-border">
        <q-card-section class="text-center">
          <div class="col-8 q-mx-auto q-mb-md">
            <q-btn
              v-if="!event.live"
              :disable="!legalTermsSelected"
              color="primary"
              size="lg"
              id="make_live"
              @click="goLive"
              label="MAKE EVENT LIVE"
              class="full-width"
            />

            <q-btn-dropdown
              color="primary"
              size="lg"
              label="SAVE"
              class="full-width q-mt-sm"
            >
              <q-list>
                <q-item clickable v-close-popup @click="handleCommand('dashboard')" data-cy="saveAndComplete">
                  <q-item-section>GO TO DASHBOARD</q-item-section>
                </q-item>
                <q-item clickable v-close-popup @click="handleCommand('invites')" v-if="event.live">
                  <q-item-section>SEND INVITES</q-item-section>
                </q-item>
              </q-list>
            </q-btn-dropdown>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'pinia';
import { useQuasar } from 'quasar';
import axios from 'axios';
import LegalTerms from './TermsModal.vue';
import SocialCircles from '@/book/common/social-circles.vue';

const router = useRouter();
const store = useStore();
const $q = useQuasar();

const paymentOptionsSetup = ref(false);
const legalTermsSelected = ref(false);
const event = ref(store.getters.getEvent);
const paymentInfo = ref(store.getters.getPaymentInfo);

const hasPaidTickets = computed(() => {
  return store.getters.getChargeable;
});

onMounted(() => {
  if (!paymentInfo.value) {
    // Duplicate in payment options
    axios.get(`/payment_info/${event.value.id}.json`)
      .then((response) => {
        paymentInfo.value = response.data.payment_info;
        store.commit("setPaymentInfo", paymentInfo.value);
        checkPaymentOptions();
      })
      .catch(() => {
        $q.notify({
          message: "Payment Details Could not be loaded",
          color: "negative",
          icon: "error"
        });
      });
  } else {
    checkPaymentOptions();
  }
});

const checkPaymentOptions = () => {
  if (paymentInfo.value) {
    paymentOptionsSetup.value =
      paymentInfo.value.stripe_enabled ||
      paymentInfo.value.bacs ||
      paymentInfo.value.cheque;
  }
};

const setStep = (stepNo) => {
  event.value.step = Math.max(stepNo, event.value.step);
};

const saveLiveStatus = (status, sendInvites) => {
  axios.put(`/events/${event.value.id}/change_public_status.json`, {
    step: 3.2,
    is_public: event.value.is_public
  })
    .then(() => {
      if (sendInvites) {
        router.push({
          name: "send-invites"
        });
      } else {
        window.location.href = `/dashboard/${event.value.id}`;
      }
    })
    .catch(() => {
      $q.notify({
        message: "Could not change event status",
        color: "negative",
        icon: "error"
      });
    });
};

const handleCommand = (command) => {
  if (command === 'dashboard') {
    saveLiveStatus(event.value.public_event, false);
  } else if (command === 'invites') {
    saveLiveStatus(event.value.public_event, true);
  }
};

const goLive = () => {
  if (!paymentOptionsSetup.value && hasPaidTickets.value) {
    $q.dialog({
      title: "",
      message: "Please add your payment options before going live!",
      color: "warning",
      ok: {
        label: "OK",
        color: "primary"
      }
    }).onOk(() => {
      router.push({
        name: "payment-options"
      });
    });
    return;
  }

  if (event.value.temp_flag) {
    $q.dialog({
      title: "",
      message: "Please complete your event details before going live!",
      color: "negative",
      ok: {
        label: "OK",
        color: "primary"
      }
    }).onOk(() => {
      router.push({
        name: "event-details"
      });
    });
    return;
  }

  if (!event.value.confirm_ticket_options) {
    $q.dialog({
      title: "",
      message: "Please confirm your ticket options!",
      color: "negative",
      ok: {
        label: "OK",
        color: "primary"
      }
    }).onOk(() => {
      router.push({
        name: "ticket-creation"
      });
    });
    return;
  }

  let ticketCount = 0;
  if (event.value.ticket_groups) {
    event.value.ticket_groups.forEach(tg => {
      ticketCount += tg.packages.length;
    });
  }
  ticketCount += event.value.tickets.length;

  if (ticketCount == 0) {
    $q.dialog({
      title: "",
      message: "Please add some tickets!",
      color: "negative",
      ok: {
        label: "OK",
        color: "primary"
      }
    }).onOk(() => {
      router.push({
        name: "ticket-creation"
      });
    });
    return;
  }

  $q.dialog({
    title: "Are you sure?",
    message: "You will not be able to edit this event once an attendee registers.",
    ok: {
      label: "Yes, Make it Live",
      color: "primary"
    },
    cancel: {
      label: "No, Cancel",
      color: "grey"
    },
    persistent: true
  }).onOk(() => {
    event.value.live = true;
    setStep(3.2);
    
    axios.put(`/events/${event.value.id}/change_live_status.json`, {
      step: 3.2,
      is_public: event.value.is_public
    })
    .then(() => {
      $q.dialog({
        title: "Live!",
        message: "Your event is now live",
        color: "positive",
        ok: {
          label: "OK",
          color: "primary"
        }
      });
      event.value.live = true;
      // Add to store
    })
    .catch((error) => {
      $q.notify({
        message: "Event could not be made live, please contact HG",
        color: "negative",
        icon: "error"
      });
      console.log(error);
    });
  });
};
</script>

<style scoped>
.primary-top-border {
  border-top: 4px solid var(--q-primary);
}

.hg-underline {
  border-bottom: 1px solid #ddd;
}
</style>