<template>
	<div class="row">
		<div class="col-12">
			<h5 class="q-mt-md">Please confirm you accept our terms and conditions!</h5>
			<q-btn flat color="primary" label="Click to View" @click="showDialog" />

			<q-dialog v-model="dialogVisible" persistent maximized>
				<q-card>
					<q-card-section class="row items-center">
						<div class="text-h6">Terms and Conditions</div>
						<q-space />
						<q-btn icon="close" flat round dense v-close-popup />
					</q-card-section>

					<q-card-section class="q-pa-md">
						<q-scroll-area style="height: 400px;">
							<div>
								<h4 class="text-center">Terms &amp; Conditions of use - Eventstop</h4>
								<p><strong>Introduction</strong></p>
								<p>This page (together with our Website
									<a href="https://www.hgonestop.co.uk/Terms%20and%20Conditions%20of%20Conferencestop.co.uk%20Website.pdf" target="_blank">Terms of Use</a> and
									<a href="https://www.hgonestop.co.uk/hg_onestop/site_info/privacy" target="_blank">Privacy Policy</a>.) sets out the terms on which Hospitality Guaranteed Limited offers its EventStop services to clients.&nbsp;
								</p>
								
								<!-- Truncated for brevity - the full terms and conditions content would be here -->
								<p>The full terms and conditions content would continue here...</p>
							</div>
						</q-scroll-area>
					</q-card-section>

					<q-card-actions align="right">
						<q-btn flat label="Close" color="primary" v-close-popup />
					</q-card-actions>
				</q-card>
			</q-dialog>
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue';

const dialogVisible = ref(false);

const showDialog = () => {
	dialogVisible.value = true;
};
</script>

<style scoped>
/* No additional styling needed as Quasar components handle the styling */
</style>