<template>
  <div v-if="event">
    <div class="row">
      <div class="col-md-8 q-mx-auto">
        <q-card>
          <q-card-section class="hg-underline">
            Event Terms and Conditions
          </q-card-section>

          <q-card-section>
            <div class="col">
              <div
                v-if="!noredirect"
                class="q-pa-md bg-grey-2 q-mb-md rounded-borders"
              >
                <div class="row items-center">
                  <div>
                    You can set up event specific terms and conditions here.
                    This will show for this event only. You can also import the
                    global terms and conditions and use/edit them.
                  </div>
                  <q-space />
                  <!-- <q-btn round flat color="info" icon="info" size="sm">
                    <q-tooltip
                      anchor="bottom middle"
                      self="top middle"
                      :offset="[0, 8]"
                      max-width="400px"
                    >
                     
                    </q-tooltip>
                  </q-btn> -->
                </div>
              </div>

              <p class="text-weight-bold">
                You can add your event terms and conditions into the text area
                below
              </p>
              <p>
                Please note that while your event is live the Terms and
                Conditions cannot be changed.
              </p>
              <p>
                Your Terms and Conditions will be visible on the confirmation
                email, and when a user signs up to your event.
              </p>

              <!-- DEBUG SECTION - Remove after testing -->
              <div class="q-mb-md q-pa-sm bg-orange-1 rounded-borders">
                <div class="text-caption">
                  <strong>Debug Info:</strong><br />
                  Event Live: {{ event?.live }}<br />
                  Global Terms Exist: {{ globalTermsExist }}<br />
                  Import Button Visible: {{ !event?.live && globalTermsExist
                  }}<br />
                  Event ID: {{ event?.id }}
                </div>
              </div>

              <div v-if="!event.live && globalTermsExist" class="q-mb-md">
                <div class="q-pa-md bg-blue-1 rounded-borders">
                  <div class="row items-center">
                    <div class="col">
                      <div class="text-body2 text-weight-medium">
                        Import Global Terms
                      </div>
                      <div class="text-caption text-grey-7">
                        Use your organization's default terms and conditions as
                        a starting point
                      </div>
                    </div>
                    <div class="col-auto">
                      <q-btn
                        color="primary"
                        icon="download"
                        label="Import"
                        size="sm"
                        outline
                        @click="importGlobalTerms"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <q-editor
                v-if="!event.live"
                :model-value="terms"
                @update:model-value="setTerms"
                :class="{ 'quill-invalid': errors.terms }"
                :toolbar="[
                  ['bold', 'italic', 'strike', 'underline'],
                  ['align', 'unordered', 'ordered'],
                  ['undo', 'redo'],
                ]"
                content-class="editor-content"
              />
              <div
                class="text-negative text-caption q-mt-sm"
                v-if="errors.terms"
              >
                {{ errors.terms }}
              </div>

              <q-card v-if="event.live" class="q-mt-md q-pa-md">
                <div v-html="terms"></div>
              </q-card>
            </div>
          </q-card-section>
        </q-card>

        <div class="text-center q-mt-xl q-mb-lg">
          <q-btn
            color="primary"
            size="lg"
            label="Save Terms and Conditions"
            @click="save"
            class="q-mx-auto"
            :loading="isSaving"
          />
        </div>
      </div>
    </div>

    <!-- Event Progress Dialog -->
    <EventProgressDialog
      v-model="showProgressDialog"
      context="terms"
      :event-id="event?.id"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useEventStore } from "@/stores/event";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import axios from "axios";
import EventProgressDialog from "../shared/EventProgressDialog.vue";

// Props
const props = defineProps({
  eventId: {
    type: [String, Number],
    required: false,
  },
  noredirect: {
    type: Boolean,
    default: false,
  },
});

// Store and router
const eventStore = useEventStore();
const event = computed(() => eventStore.getEvent);
const terms = computed(() =>
  event.value?.legal_term ? event.value.legal_term.terms : ""
);
const router = useRouter();
const $q = useQuasar();

// State
const globalTermsExist = ref(false);
const globalTerms = ref({});
const isSaving = ref(false);
const showProgressDialog = ref(false);

const setTerms = (terms) => {
  if (!event.value.legal_term) {
    event.value.legal_term = { terms };
  }
  event.value.legal_term.terms = terms;
};

const errors = ref({
  terms: null,
});

// Initialize component
onMounted(async () => {
  // Ensure event is loaded if eventId is provided
  if (props.eventId) {
    await eventStore.ensureEventLoaded(props.eventId);
  }

  // Initialize legal_term structure if it doesn't exist
  if (!event.value.legal_term) {
    event.value.legal_term = {
      terms: "",
    };
  }

  try {
    // Load existing event-specific terms first
    const existingTermsResponse = await axios.get(
      `/legal_terms/${event.value.id}`
    );
    if (existingTermsResponse.data && existingTermsResponse.data.terms) {
      // Update the event object with existing terms
      event.value.legal_term = existingTermsResponse.data;
      console.log("Loaded existing terms:", existingTermsResponse.data);
    }
  } catch (error) {
    console.log(
      "No existing terms found for event, will start with empty terms"
    );
  }

  try {
    // Load global/organization terms for import option
    console.log(
      "Attempting to load global terms for event ID:",
      event.value.id
    );
    const response = await axios.get(
      `/legal_terms/${event.value.id}/event_org`
    );
    console.log("Global terms API response:", response.data);
    globalTerms.value = response.data;
    if (globalTerms.value && globalTerms.value.terms) {
      globalTermsExist.value = true;
      console.log(
        "Global terms available for import - globalTermsExist set to true"
      );
    } else {
      console.log("Global terms response received but no terms content found");
    }
  } catch (error) {
    // Don't show error notification if global terms just don't exist
    console.log(
      "No global terms available for import - API error:",
      error.response?.status,
      error.response?.data
    );
    globalTermsExist.value = false;
  }

  // Debug logging for import button visibility
  console.log("=== Import Button Visibility Debug ===");
  console.log("Event live status:", event.value?.live);
  console.log("Global terms exist:", globalTermsExist.value);
  console.log(
    "Import button should be visible:",
    !event.value?.live && globalTermsExist.value
  );
});

// Methods
const setStep = (stepNo) => {
  event.value.step = Math.max(stepNo, event.value.step);
};

const importGlobalTerms = () => {
  // Check if there are existing terms that would be overwritten
  if (
    event.value.legal_term.terms &&
    event.value.legal_term.terms.trim() !== ""
  ) {
    $q.dialog({
      title: "Import Global Terms",
      message:
        "This will replace your current terms with the global terms. Are you sure?",
      cancel: true,
      persistent: true,
    }).onOk(() => {
      event.value.legal_term.terms = globalTerms.value.terms;
      $q.notify({
        type: "positive",
        message: "Global terms imported successfully",
        position: "top",
        timeout: 2000,
      });
    });
  } else {
    // No existing terms, import directly
    event.value.legal_term.terms = globalTerms.value.terms;
    $q.notify({
      type: "positive",
      message: "Global terms imported successfully",
      position: "top",
      timeout: 2000,
    });
  }
};

const save = async () => {
  // Validate terms
  errors.value.terms = null;
  isSaving.value = true;

  console.log("Saving terms:", event.value.legal_term.terms);

  if (
    !event.value.legal_term.terms ||
    event.value.legal_term.terms.trim() === ""
  ) {
    errors.value.terms = "Please add some terms and conditions.";
    $q.notify({
      type: "negative",
      message: "Please add some terms and conditions.",
      position: "top",
    });
    isSaving.value = false;
    return;
  }

  try {
    setStep(1.4);

    const eventDetails = { ...event.value };
    const eventPostable = { event: eventDetails };

    // Update event step
    if (event.value.legal_term.terms) {
      await axios.put(
        `/events/${event.value.id}/update_step.json`,
        eventPostable
      );
    }

    // Save terms
    await axios.post("/event_terms", {
      my_terms: event.value.legal_term,
      event_id: event.value.id,
    });

    $q.notify({
      type: "positive",
      message: "Terms and Conditions Saved",
      position: "top",
      timeout: 2000,
    });

    // Update the store with the saved event
    eventStore.setEvent(event.value);

    // Show the progress dialog after successful save
    showProgressDialog.value = true;

    if (!props.noredirect && !showProgressDialog.value) {
      router.push({ name: "preview" });
    }
  } catch (error) {
    console.error("Error saving terms:", error);
    $q.notify({
      type: "negative",
      message: "Terms and Conditions Not Saved",
      position: "top",
      timeout: 2000,
    });
  } finally {
    isSaving.value = false;
  }
};
</script>

<style scoped>
.hg-underline {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 500;
}

.topline {
  margin-top: 20px;
  border-top: 4px solid #ff9500;
}

.quill-invalid {
  border: 1px solid #c10015;
}
</style>
