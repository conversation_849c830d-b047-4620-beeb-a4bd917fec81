<template>
  <div class="event-preview-page">
    <div class="preview-container">
      <div class="ribbon ribbon-top-right">
        <span>Preview</span>
      </div>

      <div class="row justify-center">
        <!-- Main Content Column -->
        <div class="col-12 col-xl-10 col-lg-11 col-md-11 col-sm-11">
          <!-- Event Info -->
          <info v-if="event" :event="event" class="section-spacing" />

          <!-- Tickets Section -->
          <tickets
            v-if="event"
            :event-booking="mockEventBooking"
            :preview="true"
            class="section-spacing"
          />

          <!-- Booker Form -->
          <booker-form
            v-if="event"
            :event="event"
            :preview="true"
            class="section-spacing"
          />

          <!-- Terms Section -->
          <div v-if="event" class="section-spacing">
            <q-card :style="{ borderTop: `4px solid ${event.phcolour}` }">
              <terms :event="event"></terms>
            </q-card>
          </div>

          <bookButtonPanel v-if="event && !expired" class="section-spacing" />

          <!-- Related Events 
          <org-events
            v-if="event && event.organisation_id != 3251"
            :event="event"
            :is-preview="true"
            class="section-spacing"
          />-->

          <!-- Sponsors -->
          <sponsor-view v-if="event" :event="event" class="section-spacing" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { useEventStore } from "@/stores/event";
import info from "@/bookings/main_booking/info.vue";
import Tickets from "@/bookings/main_booking/tickets.vue";
import BookerForm from "@/bookings/main_booking/booker-form.vue";
import bookButtonPanel from "@/bookings/main_booking/book-button-panel.vue";
import OrgEvents from "@/common/org-events.vue";
import Terms from "@/bookings/main_booking/terms.vue";
import SponsorView from "@/common/sponsor-view.vue";
import dayjs from "dayjs";

const route = useRoute();
const eventStore = useEventStore();

// Get eventId from route parameters
const eventId = computed(() => route.params.eventId);

// Use computed property to get event from store
const event = computed(() => eventStore.getEvent);

// Mock event booking for preview (tickets component expects this)
const mockEventBooking = computed(() => ({
  id: null,
  registered_user_id: null,
  // Add any other properties the tickets component might need
}));

// Check if event is expired (for book button panel)
const expired = ref(false);

const checkEventExpiration = () => {
  if (!event.value || !event.value.datetimeto) return;

  expired.value = dayjs(event.value.datetimeto).isBefore(dayjs(), "hour");

  if (event.value.close_date) {
    const closed = dayjs(event.value.close_date).isBefore(dayjs(), "day");
    if (closed) {
      expired.value = true;
    }
  }
};

// Ensure event is loaded when component mounts
onMounted(async () => {
  if (eventId.value) {
    await eventStore.ensureEventLoaded(eventId.value);
    checkEventExpiration();
  }
});
</script>

<style scoped>
.event-preview-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 0;
  position: relative;
}

.preview-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem 1rem;
  width: 100%;
  position: relative;
}

.section-spacing {
  margin-bottom: 2rem;
}

/* Ribbon styles */
.ribbon {
  width: 150px;
  height: 150px;
  overflow: hidden;
  position: absolute;
  z-index: 999;
}
.ribbon::before,
.ribbon::after {
  position: absolute;
  z-index: -1;
  content: "";
  display: block;
  border: 5px solid #2980b9;
}
.ribbon span {
  position: absolute;
  display: block;
  width: 225px;
  padding: 15px 0;
  background-color: #3498db;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  color: #fff;
  font: 700 18px/1 "Lato", sans-serif;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  text-align: center;
}

/* top right*/
.ribbon-top-right {
  top: -10px;
  right: -10px;
}
.ribbon-top-right::before,
.ribbon-top-right::after {
  border-top-color: transparent;
  border-right-color: transparent;
}
.ribbon-top-right::before {
  top: 0;
  left: 0;
}
.ribbon-top-right::after {
  bottom: 0;
  right: 0;
}
.ribbon-top-right span {
  left: -25px;
  top: 30px;
  transform: rotate(45deg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .preview-container {
    padding: 1rem 0.5rem;
  }

  .section-spacing {
    margin-bottom: 1.5rem;
  }
}

:deep(.q-card) {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: white;
  transition: all 0.3s ease;
}

:deep(.q-card:hover) {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}
</style>
