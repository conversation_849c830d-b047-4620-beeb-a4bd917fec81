<template>
  <q-layout view="hHh lpR fFf" class="bg-grey-1">
    <!-- <q-header elevated class="bg-white text-grey-8" height-hint="68">
      <q-toolbar class="HG__toolbar">
        <q-btn
          flat
          dense
          round
          @click="toggleLeftDrawer"
          aria-label="Menu"
          icon="menu"
          class="q-mr-sm"
        />

        <q-toolbar-title v-if="$q.screen.gt.xs" shrink class="row items-center no-wrap">
          <img src="/images/logo.png" height="30">
          <span class="q-ml-sm">Events</span>
        </q-toolbar-title>

        <q-space />

        <div class="q-gutter-sm row items-center no-wrap">
          <q-btn v-if="$q.screen.gt.sm" round dense flat color="text-grey-7" icon="dashboard">
            <q-tooltip>Dashboard</q-tooltip>
          </q-btn>
          <q-btn round dense flat color="grey-8" icon="notifications">
            <q-tooltip>Notifications</q-tooltip>
          </q-btn>
          <q-btn round flat>
            <q-avatar size="26px">
              <img src="https://cdn.quasar.dev/img/boy-avatar.png">
            </q-avatar>
            <q-tooltip>Account</q-tooltip>
          </q-btn>
        </div>
      </q-toolbar>
    </q-header> -->

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      class="bg-white"
      :width="280"
    >
      <q-scroll-area class="fit">
        <q-list padding class="text-grey-8">
          <div class="q-mb-md q-px-md">
            <q-btn 
              color="primary" 
              class="full-width" 
              label="Create New Event" 
              icon="add" 
              @click="createNewEvent" 
            />
          </div>
          
          <q-item-label header>Event Setup</q-item-label>
          
          <q-item clickable v-ripple @click="navTop('event-details')" class="HG__drawer-item">
            <q-item-section avatar>
              <q-icon name="event" />
            </q-item-section>
            <q-item-section>Create Event</q-item-section>
          </q-item>
          
          <q-item clickable v-ripple @click="navToEventWizard" class="HG__drawer-item" v-if="event && event.id">
            <q-item-section avatar>
              <q-icon name="view_timeline" />
            </q-item-section>
            <q-item-section>Event Wizard</q-item-section>
            <q-item-section side>
              <q-badge color="primary" floating>New</q-badge>
            </q-item-section>
          </q-item>
          
          <q-item clickable v-ripple @click="navTop('customise-invite')" class="HG__drawer-item">
            <q-item-section avatar>
              <q-icon name="email" />
            </q-item-section>
            <q-item-section>Customise Emails</q-item-section>
          </q-item>
          
          <q-item clickable v-ripple @click="navTop('payment-options')" class="HG__drawer-item">
            <q-item-section avatar>
              <q-icon name="payments" />
            </q-item-section>
            <q-item-section>Setup Payments</q-item-section>
          </q-item>
          
          <q-item clickable v-ripple @click="navTop('go-live')" class="HG__drawer-item">
            <q-item-section avatar>
              <q-icon name="publish" />
            </q-item-section>
            <q-item-section>Go Live & Invite</q-item-section>
          </q-item>

          <q-separator inset class="q-my-sm" />

          <q-item-label header>Navigation</q-item-label>
          
          <q-item clickable v-ripple @click="goToDashboard" class="HG__drawer-item">
            <q-item-section avatar>
              <q-icon name="dashboard" />
            </q-item-section>
            <q-item-section>Dashboard</q-item-section>
          </q-item>
          
          <q-item clickable v-ripple class="HG__drawer-item">
            <q-item-section avatar>
              <q-icon name="settings" />
            </q-item-section>
            <q-item-section>Settings</q-item-section>
          </q-item>
          
          <q-item clickable v-ripple class="HG__drawer-item">
            <q-item-section avatar>
              <q-icon name="help" />
            </q-item-section>
            <q-item-section>Help</q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useEventStore } from '@/stores/event.js'
import { routerStore } from '@/events/router'
import { useQuasar } from 'quasar'
// import ActsAsBanner from './ActsAsBanner.vue'

const router = useRouter()
const $q = useQuasar()

// Use Context7-style store instead of Pinia
const event = computed(() => useEventStore().getEvent())

const currentStep = ref(0)
const leftDrawerOpen = ref(false)

// Toggle drawer function
const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

const active = computed(() => {
  const currentRoute = routerStore.state.currentRoute
  if (!currentRoute) return 0

  // Check if the route is part of a specific section
  const isEvent = routerStore.isChildOfRoute('event')
  const isCustomise = routerStore.isChildOfRoute('customise-emails')
  const isPayments = routerStore.isChildOfRoute('payments')
  const isPromote = routerStore.isChildOfRoute('promote')
  
  if (isEvent) return 0
  if (isCustomise) return 1
  if (isPayments) return 2
  if (isPromote) return 3
  
  return 0
})

// Set current step based on route
currentStep.value = active.value

// Function to create a new event
const createNewEvent = () => {
  // Clear existing event data in the store
  eventStore.setEvent({})
  
  // Redirect to the tickets page
  window.location.href = '/events/unified#/tickets/new'
}

const goToDashboard = () => {
  if (event.value?.id) {
    window.location.href = `/dashboard/${event.value.id}`
  } else {
    window.location.href = `/dashboard`
  }
}

const navTop = (pathTo) => {
  const currentRoutedStep = eventStore.state.currentStep || 0

  switch (pathTo) {
    case 'create-event':
      router.push({ name: 'create-event' })
      break
    case 'customise-invite':
      if (currentRoutedStep >= 1.4) {
        router.push({ name: 'customise-invite' })
      }
      break
    case 'payment-options':
      if (currentRoutedStep >= 2.4) {
        router.push({ name: 'payment-options' })
      }
      break
    case 'go-live':
      if (currentRoutedStep >= 3.1) {
        router.push({ name: 'go-live' })
      }
  }
}

const navToEventWizard = () => {
  if (event && event.value?.id) {
    router.push({ name: 'event-wizard', params: { id: event.value.id } })
  }
}
</script>

<style lang="sass">
.HG
  &__toolbar
    height: 64px

  &__drawer-item
    line-height: 24px
    border-radius: 0 24px 24px 0
    margin-right: 12px

    .q-item__section--avatar
      .q-icon
        color: #5f6368

    .q-item__label
      color: #3c4043
      letter-spacing: .01785714em
      font-size: .875rem
      font-weight: 500
      line-height: 1.25rem

.q-card
  background-color: #fff
  border-radius: 4px
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)
  padding: 20px

/* Replace Bootstrap's d-none d-md-block with Quasar's responsive visibility classes */
@media (max-width: 1023px)
  .hide-sm
    display: none
</style>