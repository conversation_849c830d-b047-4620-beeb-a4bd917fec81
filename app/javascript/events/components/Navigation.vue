<!-- Navigation menu with collapsible sidebar -->
<template>
  <AppBar :toggleDrawer="toggleDrawer" />
  <q-drawer
    showIfAbove
    :breakpoint="800"
    v-model="leftDrawerOpen"
    side="left"
    bordered
    :mini="miniState"
    @mouseover="miniState = false"
    @mouseout="miniState = true"
  >
    <q-list padding>
      <q-item clickable @click="goHome">
        <q-item-section avatar>
          <q-icon :name="miniState ? 'home' : 'keyboard_arrow_left'" />
        </q-item-section>
        <q-item-section> Return Home </q-item-section>
      </q-item>
      <q-item
        clickable
        v-ripple
        :to="{ name: 'dash-main', params: { eventId: $route.params.eventId } }"
        @click="navigateToManager"
        :class="{ 'disabled-item': !hasEventContext }"
        active-class="bg-secondary text-white"
        exact
      >
        <q-item-section avatar>
          <q-icon name="list" :color="hasEventContext ? 'default' : 'grey-5'" />
        </q-item-section>
        <q-item-section :class="{ 'text-grey-5': !hasEventContext }"
          >Event Manager</q-item-section
        >
      </q-item>

      <q-item
        clickable
        v-ripple
        :class="{ 'disabled-item': !hasEventContext }"
        active-class="bg-secondary text-white"
        :to="{ name: 'edit-event', params: { eventId: $route.params.eventId } }"
        @click="navigateToEventDetails"
      >
        <q-item-section avatar>
          <q-icon name="edit" :color="hasEventContext ? 'default' : 'grey-5'" />
        </q-item-section>
        <q-item-section>
          <q-item-label :class="{ 'text-grey-5': !hasEventContext }"
            >Event Details</q-item-label
          >
        </q-item-section>
      </q-item>

      <q-item
        clickable
        v-ripple
        :class="{ 'disabled-item': !hasEventContext }"
        active-class="bg-secondary text-white"
        @click="navigateToTickets"
        :to="{
          name: 'ticket-creation',
          params: { eventId: $route.params.eventId },
        }"
      >
        <q-item-section avatar>
          <q-icon
            name="local_activity"
            :color="hasEventContext ? 'default' : 'grey-5'"
          />
        </q-item-section>
        <q-item-section>
          <q-item-label :class="{ 'text-grey-5': !hasEventContext }"
            >Tickets</q-item-label
          >
        </q-item-section>
      </q-item>

      <q-item
        clickable
        v-ripple
        :class="{ 'disabled-item': !hasEventAndTickets }"
        active-class="bg-secondary text-white"
        @click="navigateToAdditional"
        :to="{
          name: 'reg-questions',
          params: { eventId: $route.params.eventId },
        }"
      >
        <q-item-section avatar>
          <q-icon
            name="help"
            :color="hasEventAndTickets ? 'default' : 'grey-5'"
          />
        </q-item-section>
        <q-item-section>
          <q-item-label :class="{ 'text-grey-5': !hasEventAndTickets }"
            >Additional Questions</q-item-label
          >
        </q-item-section>
      </q-item>

      <q-item
        clickable
        v-ripple
        :class="{ 'disabled-item': !hasEventAndTickets }"
        active-class="bg-secondary text-white"
        @click="navigateToTerms"
        :to="{ name: 'terms' }"
      >
        <q-item-section avatar>
          <q-icon
            name="gavel"
            :color="hasEventAndTickets ? 'default' : 'grey-5'"
          />
        </q-item-section>
        <q-item-section>
          <q-item-label :class="{ 'text-grey-5': !hasEventAndTickets }"
            >Terms</q-item-label
          >
        </q-item-section>
      </q-item>

      <q-item
        clickable
        v-ripple
        :class="{ 'disabled-item': !hasEventAndTickets }"
        @click="navigateToEventAttendees"
      >
        <q-item-section avatar>
          <q-icon
            name="people"
            :color="hasEventAndTickets ? 'default' : 'grey-5'"
          />
        </q-item-section>
        <q-item-section>
          <q-item-label :class="{ 'text-grey-5': !hasEventAndTickets }"
            >Event Attendees</q-item-label
          >
        </q-item-section>
      </q-item>

      <q-item
        clickable
        v-ripple
        :class="{ 'disabled-item': !hasEventAndTickets }"
        @click="navigateToEmails"
      >
        <q-item-section avatar>
          <q-icon
            name="email"
            :color="hasEventAndTickets ? 'default' : 'grey-5'"
          />
        </q-item-section>
        <q-item-section>
          <q-item-label :class="{ 'text-grey-5': !hasEventAndTickets }"
            >Email Settings</q-item-label
          >
        </q-item-section>
      </q-item>

      <q-item
        clickable
        v-ripple
        :class="{ 'disabled-item': !hasEventAndTickets }"
        @click="navigateToPreview"
      >
        <q-item-section avatar>
          <q-icon
            name="visibility"
            :color="hasEventAndTickets ? 'default' : 'grey-5'"
          />
        </q-item-section>
        <q-item-section>
          <q-item-label :class="{ 'text-grey-5': !hasEventAndTickets }"
            >Preview</q-item-label
          >
        </q-item-section>
      </q-item>

      <q-separator class="q-my-md" />

      <q-item clickable v-ripple :to="{ name: 'reports' }" exact>
        <q-item-section avatar>
          <q-icon name="report" />
        </q-item-section>
        <q-item-section>Report</q-item-section>
      </q-item>
    </q-list>
  </q-drawer>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useQuasar } from "quasar";
import { useEventStore } from "@/stores/event";
import { prepareForTicketNavigation } from "@/shared/router/ticketRoutesHandler";
import { useMainStore } from "../../stores/main";
import AppBar from "../../shared/components/AppBar.vue";
import { storeToRefs } from "pinia";
// import EventStepper from '../components/EventStepper.vue'

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const eventStore = useEventStore();
const mainStore = useMainStore();
const miniState = ref(true); // Start in mini mode

const { event } = storeToRefs(eventStore);

const { leftDrawerOpen } = storeToRefs(mainStore);

// Context7 MCP: Enhanced context checking that allows initial event loading
const hasEventContext = computed(() => {
  const event = eventStore.getSafeEvent;
  const hasStoreEvent = !!(event && event.id);

  // Allow navigation if there's an event ID in the current route (for initial loads)
  // Check both 'id' and 'eventId' params to handle different route structures
  const hasRouteEventId = !!(
    (route.params.id && route.params.id !== "new") ||
    (route.params.eventId && route.params.eventId !== "new")
  );
  return hasStoreEvent || hasRouteEventId;
});

const currentEventId = computed(() => {
  const event = eventStore.getSafeEvent;
  // Prefer event store, fallback to route params for initial loads
  // Check both 'id' and 'eventId' params
  return event?.id || route.params.id || route.params.eventId || null;
});

// Helper to check if we have a definitive event context (not just route params)
const hasDefinitiveEventContext = computed(() => {
  const event = eventStore.getSafeEvent;
  return !!(event && event.id);
});

// Helper to check if we have both event and tickets created
const hasEventAndTickets = computed(() => {
  if (!event.value || !event.value.id) return false;

  // Check if event has tickets with IDs (saved tickets)
  const hasDirectTickets =
    event.value.tickets &&
    event.value.tickets.some((ticket) => ticket && ticket.id);

  const hasGroupTickets =
    event.value.ticket_groups &&
    event.value.ticket_groups.some(
      (group) =>
        group.packages && group.packages.some((ticket) => ticket && ticket.id)
    );

  return hasDirectTickets || hasGroupTickets;
});

function toggleDrawer() {
  mainStore.setLeftDrawerOpen(mainStore.leftDrawerOpen);
}

const goHome = () => {
  window.location.href = "/dashboard#";
};

// Context7 MCP: Enhanced notification with context-aware messaging
const showEventRequiredNotification = () => {
  const hasRouteId = !!(route.params.id && route.params.id !== "new");

  if (hasRouteId) {
    // Event ID exists in route but not loaded yet - likely still loading
    $q.notify({
      type: "info",
      message: "Event is loading...",
      caption: "Please wait while we load the event details",
      position: "top",
      timeout: 2000,
      spinner: true,
    });
  } else {
    // No event context at all
    $q.notify({
      type: "warning",
      message: "Please create or select an event first",
      caption: "Event context is required for this feature",
      position: "top",
      timeout: 3000,
      actions: [
        {
          label: "Create Event",
          color: "white",
          handler: () => {
            router.push({ name: "create-event" });
          },
        },
      ],
    });
  }
};

// Navigation methods following Context7 MCP principles with enhanced context handling
const navigateToEventDetails = (e, go) => {
  e.preventDefault();
  // Always allow navigation to event details - it handles loading from route params
  if (currentEventId.value && currentEventId.value !== "new") {
    go();
  } else {
    go({ to: { name: "create-event" } });
  }
};

const navigateToManager = (e, go) => {
  e.preventDefault();
  if (!hasEventContext.value) {
    showEventRequiredNotification();
    return;
  }

  console.log(
    `Navigation.vue: Navigating to manager KATE for event ${currentEventId.value}`
  );

  // Add small delay to ensure flags are set
  setTimeout(() => {
    go();
  }, 10);
};

const navigateToTickets = (e, go) => {
  // Use less strict context check - allow if we have event ID from route or store
  e.preventDefault();
  if (!hasEventContext.value) {
    showEventRequiredNotification();
    return;
  }

  console.log(
    `Navigation.vue: Navigating to tickets for event ${currentEventId.value}`
  );
  prepareForTicketNavigation(currentEventId.value);

  // Add small delay to ensure flags are set
  setTimeout(() => {
    go({
      to: {
        name: "ticket-creation",
        params: { eventId: currentEventId.value },
      },
    });
  }, 10);
};

const navigateToAdditional = (e, go) => {
  e.preventDefault();
  if (!hasEventAndTickets.value) {
    if (!hasDefinitiveEventContext.value) {
      showEventRequiredNotification();
    } else {
      $q.notify({
        type: "warning",
        message: "Please create tickets first",
        caption: "Tickets are required before accessing additional features",
        position: "top",
        timeout: 3000,
        actions: [
          {
            label: "Create Tickets",
            color: "white",
            handler: () => {
              navigateToTickets(e, go);
            },
          },
        ],
      });
    }
    return;
  }

  // Navigate to the reg-questions route with current event ID
  setTimeout(() => {
    go();
  }, 10);
};

const navigateToTerms = (e, go) => {
  e.preventDefault();
  if (!hasEventAndTickets.value) {
    if (!hasDefinitiveEventContext.value) {
      showEventRequiredNotification();
    } else {
      $q.notify({
        type: "warning",
        message: "Please create tickets first",
        caption: "Tickets are required before accessing additional features",
        position: "top",
        timeout: 3000,
        actions: [
          {
            label: "Create Tickets",
            color: "white",
            handler: () => {
              navigateToTickets(e, go);
            },
          },
        ],
      });
    }
    return;
  }

  setTimeout(() => {
    go();
  }, 10);
};

const navigateToEventAttendees = () => {
  if (!hasEventAndTickets.value) {
    if (!hasDefinitiveEventContext.value) {
      showEventRequiredNotification();
    } else {
      $q.notify({
        type: "warning",
        message: "Please create tickets first",
        caption: "Tickets are required before accessing additional features",
        position: "top",
        timeout: 3000,
        actions: [
          {
            label: "Create Tickets",
            color: "white",
            handler: () => {
              navigateToTickets(e, go);
            },
          },
        ],
      });
    }
    return;
  }

  setTimeout(() => {
    router.push({
      name: "event-attendees",
      params: { eventId: currentEventId.value },
    });
  }, 10);
};

const navigateToEmails = () => {
  if (!hasEventAndTickets.value) {
    if (!hasDefinitiveEventContext.value) {
      showEventRequiredNotification();
    } else {
      $q.notify({
        type: "warning",
        message: "Please create tickets first",
        caption: "Tickets are required before accessing additional features",
        position: "top",
        timeout: 3000,
        actions: [
          {
            label: "Create Tickets",
            color: "white",
            handler: () => {
              navigateToTickets(e, go);
            },
          },
        ],
      });
    }
    return;
  }

  const eventId = currentEventId.value;
  router.push({
    name: "customise-emails",
    params: { eventId },
  });
};

const navigateToPreview = () => {
  if (!hasEventAndTickets.value) {
    if (!hasDefinitiveEventContext.value) {
      showEventRequiredNotification();
    } else {
      $q.notify({
        type: "warning",
        message: "Please create tickets first",
        caption: "Tickets are required before accessing additional features",
        position: "top",
        timeout: 3000,
        actions: [
          {
            label: "Create Tickets",
            color: "white",
            handler: () => {
              navigateToTickets(e, go);
            },
          },
        ],
      });
    }
    return;
  }

  const eventId = currentEventId.value;
  router.push({
    name: "preview",
    params: { eventId },
  });
};
</script>

<style lang="scss">
.q-drawer {
  .q-item {
    border-radius: 0 32px 32px 0;
    margin: 8px 16px 8px 0;
    &.disabled-item {
      opacity: 0.6;
      cursor: not-allowed !important;

      &:hover {
        background: transparent !important;
      }
    }
  }
}

// Disabled state styling
.text-grey-5 {
  color: #9e9e9e !important;
}
</style>
