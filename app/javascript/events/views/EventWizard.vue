<template>
  <Navigation>
    <EventStepper class="q-mb-lg" />
    <div class="event-wizard q-pa-md">
    
      <wizard-navbar class="q-mb-lg" />
      <router-view></router-view>
    </div>
  </Navigation>
</template>

<script setup>
import Navigation from '../components/Navigation.vue'
import WizardNavbar from '@/common/wizard-navbar.vue'
import EventStepper from '../components/EventStepper.vue'
</script>

<style lang="scss" scoped>
.event-wizard {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 20px;
}
</style>