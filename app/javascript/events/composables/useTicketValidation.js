// A validation composable for tickets following Context7 pattern
import { ref, computed } from 'vue'

export function useTicketValidation() {
  const validationErrors = ref({})
  const validationState = ref({
    isValid: true,
    validatedFields: new Set(),
    touchedFields: new Set()
  })

  /**
   * Validate a single ticket field
   * @param {Object} ticket - The ticket object
   * @param {String} field - The field name to validate
   * @param {Array} rules - Array of validation rule functions
   * @returns {Boolean} - Whether the field is valid
   */
  const validateField = (ticket, field, rules) => {
    if (!rules || !Array.isArray(rules)) return true

    // Mark field as validated
    validationState.value.validatedFields.add(field)
    
    for (const rule of rules) {
      // Pass both the field value and the ticket object to support rules that need context
      const result = rule(ticket[field], ticket)
      if (result !== true) {
        // If rule returns a string, it's an error message
        validationErrors.value[field] = result
        return false
      }
    }

    // Clear any existing errors for this field
    if (validationErrors.value[field]) {
      delete validationErrors.value[field]
    }
    
    return true
  }

  /**
   * Validate an entire ticket
   * @param {Object} ticket - The ticket to validate
   * @param {Object} validationRules - Map of field names to validation rules
   * @returns {Boolean} - Whether the ticket is valid
   */
  const validateTicket = (ticket, validationRules) => {
    if (!ticket) return false

    let isValid = true
    
    // Reset validation state for a new validation run
    validationErrors.value = {}
    validationState.value.isValid = true
    
    // Validate each field with its rules
    for (const [field, rules] of Object.entries(validationRules)) {
      const fieldValid = validateField(ticket, field, rules)
      isValid = isValid && fieldValid
    }
    
    validationState.value.isValid = isValid
    return isValid
  }
  
  /**
   * Mark a field as touched (user has interacted with it)
   * @param {String} field - The field name
   */
  const touchField = (field) => {
    validationState.value.touchedFields.add(field)
  }

  /**
   * Check if a specific field has an error
   * @param {String} field - The field name
   * @returns {String|null} - The error message or null if no error
   */
  const getFieldError = (field) => {
    return validationErrors.value[field] || null
  }

  /**
   * Check if a field should show an error (has error and was touched)
   * @param {String} field - The field name
   * @returns {Boolean} - Whether to show the error
   */
  const shouldShowError = (field) => {
    return !!validationErrors.value[field] && validationState.value.touchedFields.has(field)
  }

  /**
   * Reset all validation state
   */
  const resetValidation = () => {
    validationErrors.value = {}
    validationState.value = {
      isValid: true,
      validatedFields: new Set(),
      touchedFields: new Set()
    }
  }

  // Standard validation rules used across tickets
  const rules = {
    required: (val) => !!val || 'This field is required',
    positiveNumber: (val) => val > 0 || 'Value must be greater than 0',
    minPrice: (minValue) => (val) => val >= minValue || `Price must be at least £${minValue}`,
    url: (val) => !val || /^https?:\/\/.*/.test(val) || 'Must be a valid URL',
    maxValue: (max) => (val) => val <= max || `Value cannot exceed ${max}`,
    notExceedTicketNo: (val, ticket) => !ticket || !ticket.ticket_no || val <= ticket.ticket_no || 'Max tickets cannot exceed total tickets'
  }

  return {
    validateField,
    validateTicket,
    touchField,
    getFieldError,
    shouldShowError,
    resetValidation,
    validationErrors,
    validationState,
    rules
  }
}
