import axios from 'axios';

// Set up CSRF token for axios requests
let token = document.getElementsByName('csrf-token')[0].getAttribute('content');

axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] = 'no-cache,no-store,must-revalidate,max-age=-1,private';

export default {
  async getOrganisations() {
    const response = await api.get('/organisations')
    return response.data
  },
  
  async getOrganisation(id) {
    const response = await api.get(`/organisations/${id}`)
    return response.data
  },
  
  async createOrganisation(name) {
    const response = await api.post('/organisations', {
      organisation: {
        name: name
      }
    })
    return response.data
  },
  
  async updateOrganisation(id, name) {
    const response = await api.put(`/organisations/${id}`, {
      organisation: {
        name: name
      }
    })
    return response.data
  },
  
  async deleteOrganisation(id) {
    const response = await api.delete(`/organisations/${id}`)
    return response.data
  }
}