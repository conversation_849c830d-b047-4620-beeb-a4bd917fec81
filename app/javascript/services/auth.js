import axios from "axios";

// Set up CSRF token for axios requests
let token = document.getElementsByName("csrf-token")[0].getAttribute("content");

axios.defaults.headers.common["X-CSRF-Token"] = token;
axios.defaults.headers.common["Accept"] = "application/json";
axios.defaults.headers.common["Cache-Control"] =
  "no-cache,no-store,must-revalidate,max-age=-1,private";

export default {
  async login(email, password, rememberMe = false) {
    const response = await axios.post("/users/sign_in", {
      user: {
        email: email,
        password: password,
        remember_me: rememberMe,
      },
    });
    return response.data;
  },

  async register(email, password, passwordConfirmation, organisationName) {
    const response = await axios.post("/users", {
      user: {
        email: email,
        password: password,
        password_confirmation: passwordConfirmation,
        organisation_name: organisationName,
      },
    });
    return response.data;
  },

  async logout() {
    const response = await axios.delete("/users/sign_out");
    return response.data;
  },

  async requestPasswordReset(email) {
    const response = await axios.post("/users/password", {
      user: {
        email: email,
      },
    });
    return response.data;
  },

  async validateResetToken(token) {
    const response = await axios.get(
      `/users/password/edit?reset_password_token=${token}`
    );
    return response.data;
  },

  async resetPassword(token, password, passwordConfirmation) {
    const response = await axios.put("/users/password", {
      user: {
        reset_password_token: token,
        password: password,
        password_confirmation: passwordConfirmation,
      },
    });
    return response.data;
  },

  async confirmEmail(token) {
    const response = await axios.get(
      `/users/confirmation?confirmation_token=${token}`
    );
    return response.data;
  },

  async resendConfirmation(email) {
    const response = await axios.post("/users/confirmation", {
      user: {
        email: email,
      },
    });
    return response.data;
  },
};
